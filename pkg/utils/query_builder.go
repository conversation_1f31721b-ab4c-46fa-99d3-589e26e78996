package utils

import (
	"fmt"
	"reflect"

	"gorm.io/gorm"
)

// QueryCondition 查询条件结构
type QueryCondition struct {
	Field    string      // 字段名
	Operator string      // 操作符：=, IN, LIKE, BETWEEN等
	Value    interface{} // 值
}

// QueryBuilder 查询构建器
type QueryBuilder struct {
	conditions []QueryCondition
}

// NewQueryBuilder 创建新的查询构建器
func NewQueryBuilder() *QueryBuilder {
	return &QueryBuilder{
		conditions: make([]QueryCondition, 0),
	}
}

/**
 * 添加等值查询条件
 * @param field 字段名
 * @param value 值
 * @return 查询构建器实例
 */
func (qb *QueryBuilder) Equal(field string, value interface{}) *QueryBuilder {
	if !isZeroValue(value) {
		qb.conditions = append(qb.conditions, QueryCondition{
			Field:    field,
			Operator: "=",
			Value:    value,
		})
	}
	return qb
}

/**
 * 添加IN查询条件
 * @param field 字段名
 * @param values 值列表
 * @return 查询构建器实例
 */
func (qb *QueryBuilder) In(field string, values interface{}) *QueryBuilder {
	if !IsEmptySlice(values) {
		qb.conditions = append(qb.conditions, QueryCondition{
			Field:    field,
			Operator: "IN",
			Value:    values,
		})
	}
	return qb
}

/**
 * 添加LIKE查询条件
 * @param field 字段名
 * @param value 值
 * @return 查询构建器实例
 */
func (qb *QueryBuilder) Like(field string, value interface{}) *QueryBuilder {
	if !isZeroValue(value) {
		qb.conditions = append(qb.conditions, QueryCondition{
			Field:    field,
			Operator: "LIKE",
			Value:    fmt.Sprintf("%%%v%%", value),
		})
	}
	return qb
}

/**
 * 添加BETWEEN查询条件
 * @param field 字段名
 * @param start 开始值
 * @param end 结束值
 * @return 查询构建器实例
 */
func (qb *QueryBuilder) Between(field string, start, end interface{}) *QueryBuilder {
	if !isZeroValue(start) && !isZeroValue(end) {
		qb.conditions = append(qb.conditions, QueryCondition{
			Field:    field,
			Operator: "BETWEEN",
			Value:    []interface{}{start, end},
		})
	}
	return qb
}

/**
 * 添加时间范围查询条件（使用FROM_UNIXTIME）
 * @param field 字段名
 * @param start 开始时间戳
 * @param end 结束时间戳
 * @return 查询构建器实例
 */
func (qb *QueryBuilder) TimeBetween(field string, start, end int64) *QueryBuilder {
	if start != 0 && end != 0 {
		qb.conditions = append(qb.conditions, QueryCondition{
			Field:    field,
			Operator: "TIME_BETWEEN",
			Value:    []interface{}{start, end},
		})
	}
	return qb
}

/**
 * 应用查询条件到GORM查询
 * @param db GORM数据库实例
 * @return 应用条件后的GORM实例
 */
func (qb *QueryBuilder) Apply(db *gorm.DB) *gorm.DB {
	for _, condition := range qb.conditions {
		switch condition.Operator {
		case "=":
			db = db.Where(fmt.Sprintf("%s = ?", condition.Field), condition.Value)
		case "IN":
			db = db.Where(fmt.Sprintf("%s IN (?)", condition.Field), condition.Value)
		case "LIKE":
			db = db.Where(fmt.Sprintf("%s LIKE ?", condition.Field), condition.Value)
		case "BETWEEN":
			values := condition.Value.([]interface{})
			db = db.Where(fmt.Sprintf("%s BETWEEN ? AND ?", condition.Field), values[0], values[1])
		case "TIME_BETWEEN":
			values := condition.Value.([]interface{})
			db = db.Where(fmt.Sprintf("%s BETWEEN FROM_UNIXTIME(?) AND FROM_UNIXTIME(?)", condition.Field), values[0], values[1])
		}
	}
	return db
}

// IsZeroValue 检查值是否为零值
func isZeroValue(value interface{}) bool {
	if value == nil {
		return true
	}

	// 先尝试获取 interface{} 包装的具体类型值
	if v, ok := value.(int32); ok {
		return v == 0
	}
	if v, ok := value.(int64); ok {
		return v == 0
	}
	if v, ok := value.(int); ok {
		return v == 0
	}
	if v, ok := value.(uint); ok {
		return v == 0
	}
	if v, ok := value.(uint32); ok {
		return v == 0
	}
	if v, ok := value.(float32); ok {
		return v == 0
	}
	if v, ok := value.(float64); ok {
		return v == 0
	}

	v := reflect.ValueOf(value)
	switch v.Kind() {
	case reflect.String:
		return v.String() == ""
	case reflect.Slice:
		return v.Len() == 0
	case reflect.Ptr:
		return v.IsNil()
	case reflect.Struct:
		// 遍历结构体的所有字段
		for i := 0; i < v.NumField(); i++ {
			if !isZeroValue(v.Field(i).Interface()) {
				return false
			}
		}
		return true
	case reflect.Interface:
		if v.IsNil() {
			return true
		}
		return isZeroValue(v.Elem().Interface())
	default:
		return reflect.DeepEqual(value, reflect.Zero(reflect.TypeOf(value)).Interface())
	}
}

// IsEmptySlice 检查切片是否为空
func IsEmptySlice(value interface{}) bool {
	switch v := value.(type) {
	case []int64:
		return len(v) == 0
	case []int32:
		return len(v) == 0
	case []string:
		return len(v) == 0
	default:
		return false
	}
}

// IsZero 使用反射检查任意类型的值是否为零值
func IsZero(v interface{}) bool {
	// 如果是 nil，直接返回 true
	if v == nil {
		return true
	}

	// 如果是空接口，检查其底层值
	if vi, ok := v.(interface{}); ok && vi == nil {
		return true
	}

	return false
}
