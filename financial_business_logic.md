# 财务相关业务逻辑梳理

## 1. 概述

本文档旨在梳理代码仓库中与财务相关的业务逻辑，以便更好地理解和维护相关功能。

## 2. 关键文件

*   `idl/api/admin_api/financial_account.proto`：定义了财务账户相关的消息结构。
*   `idl/api/admin_api/financial_enum.proto`：定义了财务相关的枚举类型。
*   `idl/api/admin_api/financial_fund.proto`：定义了财务收款相关的消息结构。
*   `idl/api/admin_api/financial_payment.proto`：定义了财务支付相关的消息结构。
*   `idl/api/admin_api/financial_refund.proto`：定义了财务支款相关的消息结构。
*   `idl/constants/constants.proto`：定义了账户类型等常量。
*   `server/cmd/order/handler_FinancialService.go`：定义了财务服务的处理接口。
*   `server/cmd/order/internal/services/financial_account.go`：实现了财务账户的管理逻辑。
*   `server/cmd/order/internal/services/financial_fund.go`：实现了收款订单的管理逻辑。
*   `server/cmd/order/internal/services/financial_refund.go`：实现了支款订单的管理逻辑。
*   `server/cmd/order/internal/services/financial_payment.go`：实现了财务支付信息的管理逻辑。
*   `pkg/i18n/errno_msg.go`：包含错误消息 "请完善财务账号信息"。
*   `pkg/ctxmeta/auth.go`：定义了财务收款数据和财务支款数据的权限项。
*   `pkg/coderror/error_code_msg_map.go`：包含与财务相关的错误码。
*   `server/cmd/admin_api/internal/services/finanacial_service/fund_service.go`：包含获取财务收款信息的逻辑。
*   `server/cmd/admin_api/internal/services/order_service/order_disbursement_service.go`：包含创建财务退款单的逻辑。
*   `server/cmd/admin_api/internal/services/order_service/order_final_service.go`：包含删除财务尾款收款单的逻辑。
*   `server/cmd/admin_api/internal/services/finanacial_service/refund_service.go`：包含获取财务支款信息的逻辑。
*   `server/cmd/admin_api/internal/services/order_service/order_first_service.go`：包含创建和更新财务收款单的逻辑。
*   `server/cmd/admin_api/internal/services/tools_service/email.go`：包含组装财务邮件收件人的逻辑。
*   `server/cmd/admin_api/internal/services/order_service/order_deposit_service.go`：包含创建财务退款单的逻辑。
*   `server/cmd/admin_api/internal/cron/cron.go`：提及新老客户判断逻辑已调整为财务部分逻辑处理。
*   `server/cmd/admin_api/biz/model/constants/constants.pb.go`：定义了财务操作日志变更类型。
*   `server/cmd/user/internal/task/transfer_task.go`：包含财务转移的逻辑。
*   `server/cmd/standalone/internal/models/cdc_financial_log.go`：包含新增或更新财务操作日志的逻辑。
*   `server/cmd/standalone/internal/process/log_process/cdc_financial_log.go`：包含处理财务日志的逻辑。
*   `server/cmd/standalone/internal/process/log_process/cdc_customer_log.go`：包含同步更新财务用户类型的逻辑。
*   `server/cmd/websocket/biz/model/constants/constants.pb.go`：定义了财务操作日志变更类型。
*   `server/cmd/app_api/biz/model/constants/constants.pb.go`：定义了财务操作日志变更类型。
*   `server/cmd/order/internal/services/order.go`：包含获取订单最新的已完成支款的财务支款信息的逻辑。
*   `server/cmd/order/internal/models/order_transaction.go`：提及更新订单支付信息的逻辑，包括财务审核通过、驳回和完成支款等状态。
*   `server/cmd/order/internal/services/financial_fund.go`：包含财务订单删除和批量更新财务跟进员工的逻辑。
*   `server/cmd/order/internal/models/financial_paid.go`：定义了财务收款记录的结构体。
*   `server/cmd/order/internal/models/order_list.go`：提及通过合同编号模糊查询关联的订单 ID，然后去财务表查询的逻辑。

## 3. 关键业务流程

*   **财务账户管理**：
    *   创建财务账户：`AccountCreate`
    *   查询财务账户：`AccountInfo`
    *   更新财务账户：`AccountUpdate`
    *   删除财务账户：`AccountDelete`
*   **收款订单管理**：
    *   创建收款订单：`FundCreate`
    *   查询收款订单：`FundList`
    *   更新收款订单：`FundUpdate`
    *   删除收款订单：`FundDel`
    *   收款订单状态修改：`FundStatusUpdate`
*   **支款订单管理**：
    *   创建支款订单：`RefundCreate`
    *   查询支款订单：`RefundList`
    *   更新支款订单：`RefundUpdate`
    *   删除支款订单：`RefundDel`
*   **财务支付信息管理**：
    *   创建财务支付信息：`AddFinancialPayment`
    *   更新财务支付信息：`UpdateFinancialPayment`

## 4. 权限控制

*   `pkg/ctxmeta/auth.go` 定义了财务收款数据和财务支款数据的权限项，用于控制用户对财务相关功能的访问权限。

## 5. 错误处理

*   `pkg/coderror/error_code_msg_map.go` 包含了与财务相关的错误码，用于在出现错误时向用户提供清晰的错误提示。

## 6. 数据同步

*   `server/cmd/standalone/internal/process/log_process/cdc_financial_log.go` 和 `server/cmd/standalone/internal/process/log_process/cdc_customer_log.go` 实现了财务相关数据的同步，确保数据的一致性。

## 7. 其他

*   `server/cmd/admin_api/internal/cron/cron.go` 提及新老客户判断逻辑已调整为财务部分逻辑处理，表明财务逻辑与其他业务模块存在一定的关联。

## 8. 总结

本文档对代码仓库中与财务相关的业务逻辑进行了梳理和归纳，希望能帮助开发人员更好地理解和维护相关功能。