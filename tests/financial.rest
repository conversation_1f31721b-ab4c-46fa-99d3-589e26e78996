@baseUrl = http://localhost:9099/api

### 用户登陆(dev)

# @name authlogin
POST {{baseUrl}}/auth/AuthLogin
Content-Type: application/json

{
"account": "**********",
"password": "123123", "device": "Chrome/Windows"
}

#### set token
@token = {{authlogin.response.body.data.token}}

### 用户登陆(test)

# @name authlogin
POST {{baseUrl}}/auth/AuthLogin
Content-Type: application/json

{
"account": "<EMAIL>",
"password": "cs123456", "device": "Chrome/Windows"
}

#### set token
@token = {{authlogin.response.body.data.token}}


### 用户登陆(pre)

# @name authlogin
POST {{baseUrl}}/auth/AuthLogin
Content-Type: application/json

{
"account": "<EMAIL>",
"password": "cs123456", "device": "Chrome/Windows"
}

#### set token
@token = {{authlogin.response.body.data.token}}



### 创建财务支款
POST {{baseUrl}}/order/FinancialRefundCreate
Content-Type: application/json
X-Token: {{token}}

{
    "order_no": "ORD202412250001",
    "fund_no": "FUND202412250001",
    "refund_no": "REF202412250001",
    "customer_id": 1,
    "currency": "CNY",
    "exchange_rate": "1.0",
    "real_amount_other": "5000.00",
    "real_amount_rmb": "5000.00",
    "refund_type": 1,
    "submit_id": 1,
    "refund_receive_account_type": 1,
    "refund_receive_account": "<EMAIL>",
    "refund_reason": "客户申请退款",
    "refund_agreement": [
        {
            "url": "https://example.com/agreement.pdf",
            "name": "退款协议",
            "thumbnail_url": "https://example.com/agreement_thumb.jpg"
        }
    ],
    "scholarship_agreement": [
        {
            "url": "https://example.com/scholarship.pdf",
            "name": "奖学金协议",
            "thumbnail_url": "https://example.com/scholarship_thumb.jpg"
        }
    ],
    "visa": [
        {
            "url": "https://example.com/visa.jpg",
            "name": "签证材料",
            "thumbnail_url": "https://example.com/visa_thumb.jpg"
        }
    ],
    "student_card": [
        {
            "url": "https://example.com/student_card.jpg",
            "name": "学生卡材料",
            "thumbnail_url": "https://example.com/student_card_thumb.jpg"
        }
    ],
    "tuition_payment_proof": [
        {
            "url": "https://example.com/tuition_proof.pdf",
            "name": "学费缴纳证明",
            "thumbnail_url": "https://example.com/tuition_proof_thumb.jpg"
        }
    ],
    "approve_by": 1,
    "order_id": 1,
    "approve_log": [
        {
            "url": "https://example.com/approve_log.pdf",
            "name": "审批记录",
            "thumbnail_url": "https://example.com/approve_log_thumb.jpg"
        }
    ],
    "workflow_no": "WF202412250001",
    "workflow_id": 1,
    "workflow_name": "退款工单",
    "goods_name": "留学服务",
    "goods_specs_name": "英国硕士申请",
    "service_name": "留学申请服务",
    "brand_name": "优弗教育",
    "business_name": "留学业务",
    "num": "1",
    "contract_amount": "10000.00",
    "goods_id": 1,
    "university": "剑桥大学",
    "major": "计算机科学",
    "level": "硕士",
    "enter_time": *************,
    "user_type": 1,
    "refund_deadline": *************
}

### 更新财务支款
POST {{baseUrl}}/order/FinancialRefundUpdate
Content-Type: application/json
X-Token: {{token}}

{
    "financial_refund_id": 1,
    "order_no": "ORD202412250001",
    "fund_no": "FUND202412250001",
    "refund_no": "REF202412250001",
    "customer_id": 1,
    "currency": "CNY",
    "exchange_rate": "1.0",
    "real_amount_other": "4500.00",
    "real_amount_rmb": "4500.00",
    "refund_type": 1,
    "submit_id": 1,
    "refund_receive_account_type": 1,
    "refund_receive_account": "<EMAIL>",
    "refund_reason": "客户申请退款-已更新",
    "refund_agreement": [
        {
            "url": "https://example.com/agreement_updated.pdf",
            "name": "退款协议-已更新",
            "thumbnail_url": "https://example.com/agreement_updated_thumb.jpg"
        }
    ],
    "scholarship_agreement": [
        {
            "url": "https://example.com/scholarship.pdf",
            "name": "奖学金协议",
            "thumbnail_url": "https://example.com/scholarship_thumb.jpg"
        }
    ],
    "visa": [
        {
            "url": "https://example.com/visa.jpg",
            "name": "签证材料",
            "thumbnail_url": "https://example.com/visa_thumb.jpg"
        }
    ],
    "student_card": [
        {
            "url": "https://example.com/student_card.jpg",
            "name": "学生卡材料",
            "thumbnail_url": "https://example.com/student_card_thumb.jpg"
        }
    ],
    "tuition_payment_proof": [
        {
            "url": "https://example.com/tuition_proof.pdf",
            "name": "学费缴纳证明",
            "thumbnail_url": "https://example.com/tuition_proof_thumb.jpg"
        }
    ],
    "approve_status": 2,
    "updated_by": 1,
    "status": 1,
    "account_name": "支付宝账户",
    "transaction_no": [
        {
            "url": "https://example.com/transaction.jpg",
            "name": "交易截图",
            "thumbnail_url": "https://example.com/transaction_thumb.jpg"
        }
    ],
    "approve_comment": "审批通过",
    "order_id": 1,
    "approve_log": [
        {
            "url": "https://example.com/approve_log_updated.pdf",
            "name": "审批记录-已更新",
            "thumbnail_url": "https://example.com/approve_log_updated_thumb.jpg"
        }
    ],
    "workflow_no": "WF202412250001",
    "workflow_id": 1,
    "workflow_name": "退款工单",
    "goods_name": "留学服务",
    "goods_specs_name": "英国硕士申请",
    "service_name": "留学申请服务",
    "brand_name": "优弗教育",
    "business_name": "留学业务",
    "num": "1",
    "should_amount_other": "10000.00",
    "goods_id": 1,
    "approve_by": 1,
    "university": "剑桥大学",
    "major": "计算机科学",
    "level": "硕士",
    "enter_time": *************,
    "user_type": 1,
    "refund_deadline": *************
}

### 获取财务支款列表
POST {{baseUrl}}/financial/RefundList
Content-Type: application/json
X-Token: {{token}}

{
    "approve_status": 0,
    "refund_no": "",
    "order_no": "",
    "customer_id": 0,
    "created_at_start": 0,
    "created_at_end": 0,
    "currency": "",
    "submit_id": [],
    "service_name": [],
    "goods_name": "",
    "brand_name": "",
    "business_name": "",
    "refund_type": 0,
    "payment_account_id": 0,
    "transaction_no": "",
    "pass_time_start": 0,
    "pass_time_end": 0,
    "reject_time_start": 0,
    "reject_time_end": 0,
    "complete_time_start": 0,
    "complete_time_end": 0,
    "order_by": "created_at desc",
    "page_num": 1,
    "page_size": 20,
    "payment_account_type": 1,
    "payment_account_types": [],
    "approve_status_list": [],
    "workflow_id": 0,
    "workflow_no": "",
    "user_type": 0,
    "is_control": 0
    # "refund_deadline_start": 10,
    # "refund_deadline_end": **********
}

### 获取财务支款详情
POST {{baseUrl}}/financial/RefundDetail
Content-Type: application/json
X-Token: {{token}}

{
    "id": 118
    # "refund_no": "REF202412250001",
    # "order_id": 1,
    # "approve_status": 2,
    # "refund_type": 1
}

### 审核财务支款（通过/驳回/回退/完成打款）
### 1. 审核通过
POST {{baseUrl}}/financial/RefundApprove
Content-Type: application/json
X-Token: {{token}}

{
    "financial_refund_id": 1,
    "approve_comment": "审批通过",
    "status": 1,
    "account_name": "支付宝账户",
    "transaction_no": [
        {
            "url": "https://example.com/transaction_complete.jpg",
            "name": "完成打款交易截图",
            "thumbnail_url": "https://example.com/transaction_complete_thumb.jpg"
        }
    ],
    "order_id": 1
}

### 2. 审核驳回
POST {{baseUrl}}/financial/RefundApprove
Content-Type: application/json
X-Token: {{token}}

{
    "financial_refund_id": 2,
    "approve_comment": "材料不齐全，请重新提交完整的退款申请材料",
    "status": 2,
    "order_id": 2
}

### 3. 审核回退
POST {{baseUrl}}/financial/RefundApprove
Content-Type: application/json
X-Token: {{token}}

{
    "financial_refund_id": 3,
    "approve_comment": "需要补充银行账户信息，请完善后重新提交",
    "status": 3,
    "order_id": 3
}

### 4. 完成打款
POST {{baseUrl}}/financial/RefundApprove
Content-Type: application/json
X-Token: {{token}}

{
    "financial_refund_id": 4,
    "approve_comment": "打款完成，请客户查收",
    "status": 4,
    "account_name": "中国银行对公账户",
    "transaction_no": [
        {
            "url": "https://example.com/bank_transfer_receipt.jpg",
            "name": "银行转账回执",
            "thumbnail_url": "https://example.com/bank_transfer_receipt_thumb.jpg"
        },
        {
            "url": "https://example.com/transaction_confirmation.pdf",
            "name": "交易确认书",
            "thumbnail_url": "https://example.com/transaction_confirmation_thumb.jpg"
        }
    ],
    "order_id": 4
}

### 更新财务支款状态
POST {{baseUrl}}/financial/RefundStatusUpdate
Content-Type: application/json
X-Token: {{token}}

{
    "id": 1,
    "approve_status": 2,
    "transaction_no": [
        {
            "url": "https://example.com/transaction_status.jpg",
            "name": "状态更新交易截图",
            "thumbnail_url": "https://example.com/transaction_status_thumb.jpg"
        }
    ],
    "approve_comment": 1,
    "financial_account_id": 1
}

### 删除财务支款
POST {{baseUrl}}/order/FinancialRefundDel
Content-Type: application/json
X-Token: {{token}}

{
    "financial_refund_id": 1
}

### 获取关联支款列表
POST {{baseUrl}}/order/FinancialRefundAssociate
Content-Type: application/json
X-Token: {{token}}

{
    "id": 1,
    "order_no": "ORD202412250001",
    "order_id": 1
}

### 获取支款金额汇总
POST {{baseUrl}}/order/FinancialRefundSum
Content-Type: application/json
X-Token: {{token}}

{
    "customer_id": 1,
    "refund_type": 1,
    "order_id": 1,
    "order_no": "ORD202412250001",
    "approve_status": 3
}

### 创建奖学金支款
POST {{baseUrl}}/order/ScholarshipRefundCreate
Content-Type: application/json
X-Token: {{token}}

{
    "customer_id": 1,
    "currency": "CNY",
    "exchange_rate": "1.0",
    "real_amount_other": "3000.00",
    "submit_id": 1,
    "refund_receive_account_type": 1,
    "refund_receive_account": "<EMAIL>",
    "refund_reason": "奖学金发放",
    "refund_agreement": [
        {
            "url": "https://example.com/scholarship_agreement.pdf",
            "name": "奖学金发放协议",
            "thumbnail_url": "https://example.com/scholarship_agreement_thumb.jpg"
        }
    ],
    "scholarship_agreement": [
        {
            "url": "https://example.com/scholarship_detail.pdf",
            "name": "奖学金协议内容",
            "thumbnail_url": "https://example.com/scholarship_detail_thumb.jpg"
        }
    ],
    "visa": [
        {
            "url": "https://example.com/visa.jpg",
            "name": "签证材料",
            "thumbnail_url": "https://example.com/visa_thumb.jpg"
        }
    ],
    "student_card": [
        {
            "url": "https://example.com/student_card.jpg",
            "name": "学生卡材料",
            "thumbnail_url": "https://example.com/student_card_thumb.jpg"
        }
    ],
    "tuition_payment_proof": [
        {
            "url": "https://example.com/tuition_proof.pdf",
            "name": "学费缴纳证明",
            "thumbnail_url": "https://example.com/tuition_proof_thumb.jpg"
        }
    ],
    "workflow_id": 1,
    "workflow_name": "奖学金发放工单",
    "approve_log": [
        {
            "url": "https://example.com/scholarship_approve.pdf",
            "name": "奖学金审批记录",
            "thumbnail_url": "https://example.com/scholarship_approve_thumb.jpg"
        }
    ],
    "real_amount_rmb": "3000.00",
    "user_type": 1
}

### 创建第三方申请费支款
POST {{baseUrl}}/order/ThirdRefundCreate
Content-Type: application/json
X-Token: {{token}}

{
    "order_no": "ORD202412250001",
    "fund_no": "FUND202412250001",
    "refund_no": "REF202412250002",
    "customer_id": 1,
    "currency": "USD",
    "exchange_rate": "7.2",
    "real_amount_other": "100.00",
    "real_amount_rmb": "720.00",
    "submit_id": 1,
    "refund_receive_account_type": 3,
    "refund_receive_account": "****************",
    "refund_reason": "第三方申请费退款",
    "refund_agreement": [
        {
            "url": "https://example.com/third_party_agreement.pdf",
            "name": "第三方申请费退款协议",
            "thumbnail_url": "https://example.com/third_party_agreement_thumb.jpg"
        }
    ],
    "scholarship_agreement": [],
    "visa": [
        {
            "url": "https://example.com/visa.jpg",
            "name": "签证材料",
            "thumbnail_url": "https://example.com/visa_thumb.jpg"
        }
    ],
    "student_card": [
        {
            "url": "https://example.com/student_card.jpg",
            "name": "学生卡材料",
            "thumbnail_url": "https://example.com/student_card_thumb.jpg"
        }
    ],
    "tuition_payment_proof": [
        {
            "url": "https://example.com/tuition_proof.pdf",
            "name": "学费缴纳证明",
            "thumbnail_url": "https://example.com/tuition_proof_thumb.jpg"
        }
    ],
    "approve_by": 1,
    "order_id": 1,
    "approve_log": [
        {
            "url": "https://example.com/third_party_approve.pdf",
            "name": "第三方申请费审批记录",
            "thumbnail_url": "https://example.com/third_party_approve_thumb.jpg"
        }
    ],
    "workflow_no": "WF202412250002",
    "workflow_id": 2,
    "workflow_name": "第三方申请费退款工单",
    "goods_name": "第三方申请服务",
    "goods_specs_name": "大学申请费",
    "service_name": "申请费代缴服务",
    "brand_name": "优弗教育",
    "business_name": "留学业务",
    "num": "1",
    "contract_amount": "720.00",
    "goods_id": 2,
    "user_type": 1
}

### 获取第三方申请费支款列表
POST {{baseUrl}}/order/GetThirdRefund
Content-Type: application/json
X-Token: {{token}}

{
    "customer_id": 1,
    "workflow_id": 2,
    "workflow_no": "WF202412250002",
    "approve_status": 0,
    "refund_type": 6,
    "page_num": 1,
    "page_size": 20
}

### 根据订单获取支款列表
POST {{baseUrl}}/order/RefundListByOrder
Content-Type: application/json
X-Token: {{token}}

{
    "order_ids": [1, 2, 3]
}

### 导出财务支款列表
POST {{baseUrl}}/financial/AddExportRefundList
Content-Type: application/json
X-Token: {{token}}

{
    "download_fields": {
        "refund_no": "支款单号",
        "order_no": "订单号",
        "real_amount_other": "支款金额",
        "currency": "币种",
        "approve_status": "审批状态"
    },
    "approve_status_list": [1, 2, 3, 4],
    "created_at": [*************, *************],
    "pass_time": [],
    "reject_time": [],
    "complete_time": [],
    "service_name": "",
    "goods_name": "",
    "business_name": "",
    "brand_name": "",
    "refund_type": 0,
    "refund_receive_account_type": 0,
    "download_fields_order": ["refund_no", "order_no", "real_amount_other", "currency", "approve_status"],
    "user_type": 0
}

### 获取客户财务支款列表
POST {{baseUrl}}/customer/FinancialExpenseList
Content-Type: application/json
X-Token: {{token}}

{
    "customer_id": 2689,
    "page_num": 1,
    "page_size": 20
    # "order_id": 1
}

### 创建第三方申请费收款
POST {{baseUrl}}/financial/ThirdFundCreate
Content-Type: application/json
X-Token: {{token}}

{
    "workflow_id": 1,
    "workflow_name": "美国留学申请工单",
    "workflow_no": "WF202412250003",
    "customer_id": 1,
    "currency": "USD",
    "real_amount_other": "150.00",
    "real_amount_rmb": "1080.00",
    "exchange_rate": "7.2",
    "paid_time": *************,
    "financial_paid_info": [
        {
            "payment_account_id": 1,
            "currency": "USD",
            "exchange_rate": "7.2",
            "amount_cny": "1080.00",
            "amount_other": "150.00",
            "images_path": [
                {
                    "url": "https://example.com/payment_proof1.jpg",
                    "name": "支付凭证1",
                    "thumbnail_url": "https://example.com/payment_proof1_thumb.jpg"
                },
                {
                    "url": "https://example.com/payment_proof2.jpg",
                    "name": "支付凭证2", 
                    "thumbnail_url": "https://example.com/payment_proof2_thumb.jpg"
                }
            ],
            "financial_account_name": "美国银行账户",
            "transaction_no": ["TXN202412250001", "TXN202412250002"]
        }
    ],
    "remark": "大学申请费代缴"
}

### 获取第三方申请费详情
POST {{baseUrl}}/financial/FundDetail
Content-Type: application/json
X-Token: {{token}}

{
    "id": 770,
    "fund_type": 4,
    "workflow_id": 1
}

### 获取财务收款详情
POST {{baseUrl}}/financial/FundDetail
Content-Type: application/json
X-Token: {{token}}

{
    "id": 780
    # "fund_type": 1,
    # "workflow_id": 1,
    # "order_id": 1
}

### 获取财务收款列表
POST {{baseUrl}}/financial/FundList
Content-Type: application/json
X-Token: {{token}}

{
    "page_num": 1,
    "page_size": 20,
    "payment_account_types":[3]
}

### 获取客户财务收款列表
POST {{baseUrl}}/customer/FinancialReceiptList
Content-Type: application/json
X-Token: {{token}}

{
    "customer_id": 2689,
    "page_num": 1,
    "page_size": 20,
    "order_id": 0,
    "fund_type": 0,
    "currency": "",
    "created_at_start": 0,
    "created_at_end": 0,
    "paid_time_start": 0,
    "paid_time_end": 0,
    "approve_status": 0,
    "order_by": "created_at desc"
}

### 编辑已审核通过的收款信息
POST {{baseUrl}}/financial/EditApprovedFinancialFund
Content-Type: application/json
X-Token: {{token}}

{
    "financial_fund_id": 780,
    "paid_time": *************,
    "user_type": 2,
    "order_source_ids": [101, 102, 103],
    "contract_info": [
        {
            "url": "https://example.com/updated_contract.pdf",
            "name": "更新后的合同文件",
            "thumbnail_url": "https://example.com/updated_contract_thumb.jpg"
        },
        {
            "url": "https://example.com/additional_agreement.pdf", 
            "name": "补充协议",
            "thumbnail_url": "https://example.com/additional_agreement_thumb.jpg"
        }
    ],
    "other_submit_ids": [101, 102],
    "edit_reason": "根据客户要求更新付款日期和合同信息"
} 