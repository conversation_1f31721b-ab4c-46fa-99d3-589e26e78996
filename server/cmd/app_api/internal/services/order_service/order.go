package order_service

import (
	"context"
	"encoding/json"
	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/customer"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/kitex_gen/server/cmd/user"
	"uofferv2/pkg/ctxmeta"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"
	"uofferv2/server/cmd/app_api/biz/model/server/cmd/app_api"
	"uofferv2/server/cmd/app_api/internal/global"
	"uofferv2/server/cmd/app_api/internal/httputil"

	"github.com/duke-git/lancet/v2/slice"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
)

// GetOrderStatic .
// @router /order/GetOrderStatic [POST]
func GetOrderStatic(ctx context.Context, c *app.RequestContext) {
	funcName := getFuncName(1)

	var err error
	var req app_api.GetOrderStaticReq
	err = c.BindAndValidate(&req)
	if err != nil {
		logger.CtxErrorf(ctx, "%s invalid request param: %v", funcName, err)
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	// 获取 customer_id
	reqRpc := &order.GetOrderStaticReq{
		CustomerId: ctxmeta.MustGetAuth(ctx).CustomerId(),
	}

	respRpc, err := global.OrderClient.GetOrderStatic(ctx, reqRpc)
	if err != nil {
		logger.CtxErrorf(ctx, "%s global.OrderClient.GetOrderStatic rpc error: %v", funcName, err)
		httputil.ResponseInternalErr(c)
		return
	}

	if respRpc.Base.Code != errno.Errno_SUCCESS {
		logger.CtxErrorf(ctx, "%s global.OrderClient.GetOrderStatic rpc base error %v", funcName, respRpc.Base)
		httputil.ResponseErrorCodeWithBase(c, respRpc.Base)
		return
	}

	resp := orderStaticRspSrvToApi(respRpc)

	httputil.ResponseSuccess(c, resp)
}

// GetOrderList .
// @router /order/GetOrderList [POST]
func GetOrderList(ctx context.Context, c *app.RequestContext) {
	funcName := getFuncName(1)

	var err error
	var req app_api.GetOrderListReq
	err = c.BindAndValidate(&req)
	if err != nil {
		logger.CtxErrorf(ctx, "%s invalid request param: %v", funcName, err)
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	query := &order.GetOrderListAppReq{
		PageNum:  req.GetPageNum(),
		PageSize: req.GetPageSize(),
	}

	statuses := make([]order.StatusOrder, 0, len(req.GetStatuses()))
	for _, status := range req.GetStatuses() {
		statuses = append(statuses, order.StatusOrder(status))
	}

	if len(statuses) == 0 || (len(statuses) == 1 && statuses[0] == order.StatusOrder_STATUS_ORDER_FIRST) {
		statuses = []order.StatusOrder{
			order.StatusOrder_STATUS_ORDER_DEPOSIT,
			order.StatusOrder_STATUS_ORDER_FINAL,
			order.StatusOrder_STATUS_ORDER_SUCCESS,
			order.StatusOrder_STATUS_ORDER_DISBURSEMENT,
		}
	}

	query.Statuses = statuses
	query.CustomerId = ctxmeta.MustGetAuth(ctx).CustomerId()

	// 订单列表信息
	respRpc, err := global.OrderClient.GetOrderListApp(ctx, query)
	if err != nil {
		logger.CtxErrorf(ctx, "%s global.OrderClient.GetOrderListApp rpc error: %v", funcName, err)
		httputil.ResponseInternalErr(c)
		return
	}

	if respRpc.Base.Code != errno.Errno_SUCCESS {
		logger.CtxErrorf(ctx, "%s global.OrderClient.GetOrderListApp rpc base error %v", funcName, respRpc.Base)
		httputil.ResponseErrorCodeWithBase(c, respRpc.Base)
		return
	}

	// 客户，操作人，审核人
	customerMap, creatorMap, updaterMap, reviewerMap, returnBreak := getOrderAssociatedUsersInfo(ctx, c, respRpc.GetItems())
	if returnBreak {
		return
	}

	items := make([]*app_api.OrderListItemRsp, 0, len(respRpc.GetItems()))
	for _, item := range respRpc.GetItems() {
		items = append(items, orderListItemRspSrvToApi(item, customerMap, creatorMap, updaterMap, reviewerMap))
	}

	resp := new(app_api.GetOrderListRsp)
	resp.Total = respRpc.GetTotal()
	resp.Items = items

	httputil.ResponseSuccess(c, resp)
}

// GetOrderInfo .
// @router /order/GetOrderInfo [POST]
func GetOrderInfo(ctx context.Context, c *app.RequestContext) {
	funcName := getFuncName(1)

	var err error
	var req app_api.GetOrderInfoReq
	err = c.BindAndValidate(&req)
	if err != nil {
		logger.CtxErrorf(ctx, "%s invalid request param: %v", funcName, err)
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	respRpc, returnBreak := getOrderInfo(ctx, c, req.GetId(), "", funcName)
	if returnBreak {
		return
	}

	resp := new(app_api.GetOrderInfoRsp)
	resp = getOrderInfoRspSrvToApi(respRpc)

	// 用户，操作人，审核人
	customerInfo, creator, updater, reviewer, returnBreak := getOrderAssociatedUserInfo(ctx, c, respRpc)
	if returnBreak {
		return
	}

	/*
		var fundType int32
		if resp.Order.Status == app_api.StatusOrder_STATUS_ORDER_DEPOSIT {
			fundType = 1
		}
		if resp.Order.Status == app_api.StatusOrder_STATUS_ORDER_FIRST {
			fundType = 2
		}
		if resp.Order.Status == app_api.StatusOrder_STATUS_ORDER_FINAL {
			fundType = 3
		}
		fund, _ := global.FinancialClient.FundInfo(ctx, &order.FinancialFundInfoReq{
			OrderId:  req.GetId(),
			FundType: fundType,
		})
	*/
	fund1, _ := global.FinancialClient.FundInfo(ctx, &order.FinancialFundInfoReq{
		OrderId:  req.GetId(),
		FundType: 1,
	})
	fund2, _ := global.FinancialClient.FundInfo(ctx, &order.FinancialFundInfoReq{
		OrderId:  req.GetId(),
		FundType: 2,
	})
	fund3, _ := global.FinancialClient.FundInfo(ctx, &order.FinancialFundInfoReq{
		OrderId:  req.GetId(),
		FundType: 3,
	})
	var contractObject1 []*app_api.ContractObject
	if fund1.Id != 0 {
		if len(fund1.ContractUrl) > 0 {
			_ = json.Unmarshal([]byte(fund1.ContractUrl), &contractObject1)
		}
	}
	var contractObject2 []*app_api.ContractObject
	if fund2.Id != 0 {
		if len(fund2.ContractUrl) > 0 {
			_ = json.Unmarshal([]byte(fund2.ContractUrl), &contractObject2)
		}
	}
	var contractObject3 []*app_api.ContractObject
	if fund3.Id != 0 {
		if len(fund3.ContractUrl) > 0 {
			_ = json.Unmarshal([]byte(fund3.ContractUrl), &contractObject3)
		}
	}
	var contractObject []*app_api.ContractObject
	contractObject = slice.Concat(contractObject1, contractObject2, contractObject3)
	resp.Customer = customerInfo
	resp.Creator = creator
	resp.Updater = updater
	resp.Reviewer = reviewer
	resp.ContractInfo = contractObject

	httputil.ResponseSuccess(c, resp)
}

// GetScholarshipToBeDistributed .
// @router /order/GetScholarshipToBeDistributed [POST]
func GetScholarshipToBeDistributed(ctx context.Context, c *app.RequestContext) {
	funcName := getFuncName(1)

	var err error
	var req app_api.GetScholarshipToBeDistributedReq
	err = c.BindAndValidate(&req)
	if err != nil {
		logger.CtxErrorf(ctx, "%s invalid request param: %v", funcName, err)
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	query := order.GetScholarshipToBeDistributedReq{
		PageNum:    req.GetPageNum(),
		PageSize:   req.GetPageSize(),
		CustomerId: ctxmeta.MustGetAuth(ctx).CustomerId(),
		Base:       nil,
	}

	respRpc, err := global.OrderClient.GetScholarshipToBeDistributed(ctx, &query)
	if err != nil {
		logger.CtxErrorf(ctx, "%s global.OrderClient.GetScholarshipToBeDistributed rpc error: %v", funcName, err)
		httputil.ResponseInternalErr(c)
		return
	}

	if respRpc.Base.Code != errno.Errno_SUCCESS {
		logger.CtxErrorf(ctx, "%s global.OrderClient.GetScholarshipToBeDistributed rpc base error %v", funcName, respRpc.Base)
		httputil.ResponseErrorCodeWithBase(c, respRpc.Base)
		return
	}

	resp := new(app_api.GetScholarshipToBeDistributedRsp)
	resp = getScholarshipToBeDistributedRspSrvToApi(respRpc)

	httputil.ResponseSuccess(c, resp)
}

// GetRefundScholarshipList .
// @router /order/GetRefundScholarshipList [POST]
func GetRefundScholarshipList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req app_api.RefundScholarshipListReq
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(app_api.RefundScholarshipListRsp)
	respRpc, err := global.FinancialClient.RefundList(ctx, &order.FinancialRefundListReq{
		CustomerId:    ctxmeta.MustGetAuth(ctx).CustomerId(),
		RefundType:    3,
		ApproveStatus: 3,
		PageNum:       req.PageNum,
		PageSize:      req.PageSize,
	})
	if len(respRpc.FinancialRefundList) == 0 {
		httputil.ResponseSuccess(c, resp)
		return
	}
	scholarshipList := make([]*app_api.RefundScholarship, 0, len(respRpc.FinancialRefundList))
	for _, item := range respRpc.FinancialRefundList {
		scholarshipList = append(scholarshipList, &app_api.RefundScholarship{
			Id:              item.Id,
			RealAmountOther: item.RealAmountRmb,
			University:      item.University,
			Major:           item.Major,
			Level:           item.Level,
			EnterTime:       item.EnterTime,
			CompleteTime:    item.CompleteTime,
			Currency:        item.Currency,
		})
	}
	amountTotal, _ := global.FinancialClient.RefundScholarshipAmount(ctx, &order.FinancialRefundSumReq{
		CustomerId:    ctxmeta.MustGetAuth(ctx).CustomerId(),
		RefundType:    3,
		ApproveStatus: 3,
	})
	resp.ScholarshipList = scholarshipList
	resp.Total = respRpc.Total
	resp.AmountTotal = amountTotal.AmountTotal
	resp.CurrencyTotal = "100000.00000"
	httputil.ResponseSuccess(c, resp)
}

// GetRefundList .
// @router /order/GetRefundList [POST]
func GetRefundList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req app_api.FinancialRefundListReq
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}
	resp := new(app_api.FinancialRefundListRsp)
	respRpc, err := global.FinancialClient.RefundList(ctx, &order.FinancialRefundListReq{
		OrderId:       req.OrderId,
		OrderNo:       req.OrderNo,
		ApproveStatus: req.ApproveStatus,
		PageNum:       req.PageNum,
		PageSize:      req.PageSize,
	})
	if len(respRpc.FinancialRefundList) == 0 {
		httputil.ResponseSuccess(c, resp)
		return
	}
	returnList := make([]*app_api.FinancialRefundInfo, 0, len(respRpc.FinancialRefundList))
	for _, item := range respRpc.FinancialRefundList {
		returnList = append(returnList, &app_api.FinancialRefundInfo{
			Id:              item.Id,
			RefundNo:        item.RefundNo,
			OrderId:         item.OrderId,
			RealAmountOther: item.RealAmountOther,
			CompleteTime:    item.CompleteTime,
			Currency:        item.Currency,
			RefundType:      item.RefundType,
			ApproveStatus:   item.ApproveStatus,
			RealAmountRmb:   item.RealAmountRmb,
		})
	}
	amountTotal, _ := global.FinancialClient.RefundScholarshipAmount(ctx, &order.FinancialRefundSumReq{
		OrderId:       req.OrderId,
		OrderNo:       req.OrderNo,
		ApproveStatus: req.ApproveStatus,
	})
	resp.Items = returnList
	resp.Total = respRpc.Total
	resp.TotalAmount = amountTotal.AmountTotal
	resp.TotalAmountCurrency = "CNY"
	httputil.ResponseSuccess(c, resp)
}

// GetCurrencyList .
// @router /order/GetCurrencyList [POST]
func GetCurrencyList(ctx context.Context, c *app.RequestContext) {
	funcName := getFuncName(1)

	var err error
	var req app_api.GetCurrencyListReq
	err = c.BindAndValidate(&req)
	if err != nil {
		logger.CtxErrorf(ctx, "%s invalid request param: %v", funcName, err)
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	query := &order.GetCurrencyListReq{}
	respRpc, err := global.OrderClient.GetCurrencyList(ctx, query)
	if err != nil {
		logger.CtxErrorf(ctx, "%s global.OrderClient.GetCurrencyList rpc error: %v", funcName, err)
		httputil.ResponseInternalErr(c)
		return
	}

	if respRpc.GetBase().GetCode() != errno.Errno_SUCCESS {
		logger.CtxErrorf(ctx, "%s global.OrderClient.GetCurrencyList rpc base error %v", funcName, respRpc.GetBase())
		httputil.ResponseErrorCodeWithBase(c, respRpc.GetBase())
		return
	}

	items := make([]*app_api.CurrencyEntity, 0, len(respRpc.GetItems()))
	for _, item := range respRpc.GetItems() {
		items = append(items, currencyEntitySrvToApi(item))
	}

	resp := new(app_api.GetCurrencyListRsp)
	resp.Items = items

	httputil.ResponseSuccess(c, resp)
}

// GetFundList  .
// @router /order/GetFundList [POST]
func GetFundList(ctx context.Context, c *app.RequestContext) {
	var req admin_api.FinancialFundListReq
	if err := c.BindAndValidate(&req); err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	respRpc, err := global.FinancialClient.FundList(ctx, &order.FinancialFundListReq{
		ApproveStatus:   req.ApproveStatus,
		FundNo:          req.FundNo,
		FundType:        req.FundType,
		OrderNo:         req.OrderNo,
		CustomerId:      req.CustomerId,
		GoodsName:       req.GoodsName,
		ServiceName:     req.ServiceName,
		SubmitId:        req.SubmitId,
		CreatedAtStart:  req.CreatedAtStart,
		CreatedAtEnd:    req.CreatedAtEnd,
		PassTimeStart:   req.PassTimeStart,
		PassTimeEnd:     req.PassTimeEnd,
		RejectTimeStart: req.RejectTimeStart,
		RejectTimeEnd:   req.RejectTimeEnd,
		PageNum:         req.PageNum,
		PageSize:        req.PageSize,
	})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	if len(respRpc.FinancialFundList) == 0 {
		httputil.ResponseSuccess(c, &admin_api.FinancialFundListRsp{})
		return
	}
	items := make([]*admin_api.FinancialFundInfo, 0, len(respRpc.FinancialFundList))
	customerIds := make([]int64, 0, len(respRpc.FinancialFundList))
	employeeIds := make([]int64, 0, len(respRpc.FinancialFundList))
	var imagesPath []*admin_api.FundContractInfo
	for _, item := range respRpc.FinancialFundList {
		paidList := make([]*admin_api.FinancialPaidInfo, 0, len(item.FinancialPaiInfo))
		for _, paid := range item.FinancialPaiInfo {
			if len(paid.ImagesPath) > 0 {
				_ = json.Unmarshal([]byte(paid.ImagesPath), &imagesPath)
			}
			paidList = append(paidList, &admin_api.FinancialPaidInfo{
				PaymentAccountId:     paid.PaymentAccountId,
				Currency:             paid.Currency,
				PaidType:             admin_api.PaymentAccountType(paid.PaidType),
				FinancialAccountName: paid.AccountName,
				AmountOther:          paid.AmountOther,
				ImagesPath:           imagesPath,
				FinancialPaidId:      paid.Id,
			})
		}
		items = append(items, &admin_api.FinancialFundInfo{
			Id:     item.Id,
			FundNo: item.FundNo,
			OrderInfo: &admin_api.FinancialOrderInfo{
				OrderId:     item.OrderId,
				OrderNo:     item.OrderNo,
				GoodsName:   item.GoodsName,
				GoodsSpec:   item.GoodsSpec,
				ServiceName: item.ServiceName[0],
				GoodsNum:    item.GoodsNum,
			},
			CustomerId:        item.CustomerId,
			ShouldAmountOther: item.ShouldAmountOther,
			RealAmountOther:   item.RealAmountOther,
			Currency:          item.Currency,
			PaidTime:          item.PaidTime,
			PayType:           item.PayType,
			FundType:          item.FundType,
			ServiceName:       item.ServiceName,
			BrandName:         item.BrandName,
			BusinessName:      item.BusinessName,
			ContractNo:        item.ContractNo,
			CreatedAt:         item.CreatedAt,
			SubmitId:          item.SubmitId,
			FinancialPaiInfo:  paidList,
			PassTime:          item.PassTime,
			RejectTime:        item.RejectTime,
			ApproveStatus:     item.ApproveStatus,
		})
		customerIds = append(customerIds, item.CustomerId)
		employeeIds = append(employeeIds, item.SubmitId)
	}
	employeeResp, err := global.UsersClient.GetEmployeeInfoByIds(ctx, &user.GetEmployeeInfoByIdsReq{
		Id: employeeIds,
	})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	// 获取客户信息
	customerRpcResp, err := global.CustomerProfileClient.GetCustomersByIds(ctx, &customer.GetCustomersByIdsReq{
		CustomerIds: customerIds,
	})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	// 构建客户信息映射并关联到 items
	customerMap := make(map[int64]*admin_api.CustomerWithTag, len(customerRpcResp.GetCustomers()))
	for _, customer := range customerRpcResp.GetCustomers() {
		customerMap[int64(customer.GetId())] = &admin_api.CustomerWithTag{
			Id:   int64(customer.GetId()),
			Name: customer.GetName(),
		}
	}
	// 创建员工信息映射
	type employeeInfo struct {
		name     string
		deptName string
	}
	employeeMap := make(map[int64]employeeInfo)
	for _, emp := range employeeResp.List {
		employeeMap[emp.Id] = employeeInfo{
			name:     emp.Name,
			deptName: emp.DeptName,
		}
	}
	// 更新员工信息
	for i := range items {
		if info, ok := employeeMap[items[i].SubmitId]; ok {
			items[i].SubmitName = info.name
		}
	}
	//更新客户信息
	for i := range items {
		if customer, ok := customerMap[items[i].CustomerId]; ok {
			items[i].CustomerInfo = customer
		}
	}
	//返回
	httputil.ResponseSuccess(c, &admin_api.FinancialFundListRsp{
		Total: respRpc.Total,
		Items: items,
	})
}

func GetThirdRefundList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req app_api.ThirdRefundReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	respRpc, err := global.FinancialClient.GetThirdRefund(ctx, &order.ThirdRefundReq{
		CustomerId:    req.CustomerId,
		WorkflowId:    req.WorkflowId,
		WorkflowNo:    req.WorkflowNo,
		ApproveStatus: req.ApproveStatus,
		RefundType:    6,
		PageNum:       req.PageNum,
		PageSize:      req.PageSize,
	})
	items := make([]*app_api.ThirdRefund, 0, len(respRpc.ThirdRefund))
	for _, item := range respRpc.ThirdRefund {
		items = append(items, &app_api.ThirdRefund{
			Id:              item.Id,
			WorkflowId:      item.WorkflowId,
			WorkflowNo:      item.WorkflowNo,
			RealAmountOther: item.RealAmountOther,
			RealAmountRmb:   item.RealAmountRmb,
			Currency:        item.Currency,
			ApproveStatus:   item.ApproveStatus,
			ExchangeRate:    item.ExchangeRate,
			CompleteTime:    item.CompleteTime,
			RefundNo:        item.RefundNo,
		})
	}

	//返回
	httputil.ResponseSuccess(c, &app_api.ThirdRefundRsp{
		Total:       respRpc.Total,
		ThirdRefund: items,
	})
}

func GetThirdFundByWorkflow(ctx context.Context, c *app.RequestContext) {
	var err error
	var req app_api.ThirdFundByWorkflowReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	respRpc, err := global.FinancialClient.GetThirdFundByWorkflow(ctx, &order.ThirdFundByWorkflowReq{
		WorkflowId: req.WorkflowId,
		WorkflowNo: req.WorkflowNo,
	})
	resp := new(app_api.ThirdFundByWorkflowRsp)
	if respRpc.Id == 0 {
		httputil.ResponseSuccess(c, resp)
		return
	}
	resp.ThirdAmountList = make([]*app_api.AmountInfo, len(respRpc.ThirdAmountList))
	for i, amount := range respRpc.ThirdAmountList {
		resp.ThirdAmountList[i] = &app_api.AmountInfo{
			Amount:   amount.Amount,
			Currency: amount.Currency,
		}
	}
	resp.PaidTime = respRpc.PaidTime
	resp.FundNo = respRpc.FundNo
	resp.Id = respRpc.Id
	resp.RealAmountRmb = respRpc.RealAmountRmb
	resp.RealAmountOther = respRpc.RealAmountOther
	resp.Currency = respRpc.Currency
	resp.WorkflowId = respRpc.WorkflowId
	resp.WorkflowNo = respRpc.WorkflowNo
	resp.WorkflowName = respRpc.WorkflowName
	resp.ApproveStatus = respRpc.ApproveStatus
	httputil.ResponseSuccess(c, resp)
}
