package export_process

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"
	"uofferv2/server/cmd/standalone/internal/pkg/formatutils"
	"uofferv2/server/cmd/standalone/internal/services/finanacial_service"
)

// 支款订单管理的任务数据导出及文件生成
// 参数：
//
//	ctx - 上下文对象
//	task - 导出文件任务模型，包含任务配置信息
//
// 返回值：
//
//	string - 生成的临时文件路径
//	error - 错误对象
func (c *exportProcess) generateRefundTableFile(ctx context.Context, task *model.ExportFileTask) (string, error) {
	// 解析任务参数（下载字段和条件）
	downloadFields, downloadConditions, err := parseCommonConditions(task)
	if err != nil {
		return "", err
	}

	financialRefundListReq := &admin_api.FinancialRefundListReq{}
	approveStatusArr := downloadConditions["approve_status"].GetArrayStringVal().GetValue()
	approveStatusArrInt := make([]int32, len(approveStatusArr))
	for i, s := range approveStatusArr {
		status, _ := strconv.ParseInt(s, 10, 32)
		approveStatusArrInt[i] = int32(status)
	}
	financialRefundListReq.ApproveStatusList = approveStatusArrInt

	createdAtArr := downloadConditions["created_at"].GetArrayStringVal().GetValue()
	if len(createdAtArr) == 2 {
		createStart, _ := strconv.ParseInt(createdAtArr[0], 10, 64)
		createEnd, _ := strconv.ParseInt(createdAtArr[1], 10, 64)
		financialRefundListReq.CreatedAtStart = createStart
		financialRefundListReq.CreatedAtEnd = createEnd
	}

	passTimeArr := downloadConditions["pass_time"].GetArrayStringVal().GetValue()
	if len(passTimeArr) == 2 {
		passTimeStart, _ := strconv.ParseInt(passTimeArr[0], 10, 64)
		passTimeEnd, _ := strconv.ParseInt(passTimeArr[1], 10, 64)
		financialRefundListReq.PassTimeStart = passTimeStart
		financialRefundListReq.PassTimeEnd = passTimeEnd
	}

	rejectTimeArr := downloadConditions["reject_time"].GetArrayStringVal().GetValue()
	if len(rejectTimeArr) == 2 {
		rejectTimeStart, _ := strconv.ParseInt(rejectTimeArr[0], 10, 64)
		rejectTimeEnd, _ := strconv.ParseInt(rejectTimeArr[1], 10, 64)
		financialRefundListReq.RejectTimeStart = rejectTimeStart
		financialRefundListReq.RejectTimeEnd = rejectTimeEnd
	}

	completeTimeArr := downloadConditions["complete_time"].GetArrayStringVal().GetValue()
	if len(completeTimeArr) == 2 {
		completeTimeStart, _ := strconv.ParseInt(completeTimeArr[0], 10, 64)
		completeTimeEnd, _ := strconv.ParseInt(completeTimeArr[1], 10, 64)
		financialRefundListReq.CompleteTimeStart = completeTimeStart
		financialRefundListReq.CompleteTimeEnd = completeTimeEnd
	}

	orderSourceDepart := downloadConditions["order_source_depart"].GetArrayStringVal().GetValue()
	orderSourceDepartInt := make([]int32, len(orderSourceDepart))
	for i, s := range orderSourceDepart {
		depart, _ := strconv.ParseInt(s, 10, 32)
		orderSourceDepartInt[i] = int32(depart)
	}
	financialRefundListReq.OrderSourceDepart = orderSourceDepartInt

	userSourceDepart := downloadConditions["user_source_depart"].GetArrayStringVal().GetValue()
	userSourceDepartInt := make([]int32, len(userSourceDepart))
	for i, s := range userSourceDepart {
		depart, _ := strconv.ParseInt(s, 10, 32)
		userSourceDepartInt[i] = int32(depart)
	}
	financialRefundListReq.UserSourceDepart = userSourceDepartInt

	submitDepart := downloadConditions["submit_depart"].GetArrayStringVal().GetValue()
	submitDepartInt := make([]int32, len(submitDepart))
	for i, s := range submitDepart {
		depart, _ := strconv.ParseInt(s, 10, 32)
		submitDepartInt[i] = int32(depart)
	}
	financialRefundListReq.SubmitDepart = submitDepartInt

	goodsName := downloadConditions["goods_name"].GetStringVal().GetValue()
	financialRefundListReq.GoodsName = goodsName

	serviceName := downloadConditions["service_name"].GetArrayStringVal().GetValue()
	financialRefundListReq.ServiceName = serviceName

	refundType := downloadConditions["refund_type"].GetStringVal().GetValue()
	refundTypeInt, _ := strconv.ParseInt(refundType, 10, 64)
	financialRefundListReq.RefundType = int32(refundTypeInt)

	brandName := downloadConditions["brand_name"].GetStringVal().GetValue()
	financialRefundListReq.BrandName = brandName

	businessName := downloadConditions["business_name"].GetStringVal().GetValue()
	financialRefundListReq.BusinessName = businessName

	userType := downloadConditions["user_type"].GetStringVal().GetValue()
	userTypeInt, _ := strconv.ParseInt(userType, 10, 64)
	financialRefundListReq.UserType = int32(userTypeInt)
	
	financialRefundListReq.PageSize = defaultListPageSize

	dataChan := make(chan []map[string]interface{})
	go func() {
		var (
			page      = 1    // 起始页码
			hasMore   = true // 分页标志
			totalRows = 0    // 调试用总行数
		)

		// 添加协程异常恢复
		defer func() {
			close(dataChan)
			logger.CtxDebugf(ctx, "数据通道已关闭，共发送 %d 个批次", page-1)
			if r := recover(); r != nil {
				logger.CtxErrorf(ctx, "生成协程发生panic: %v", r)
			}
		}()

		for hasMore {
			financialRefundListReq.PageNum = int32(page)

			// 2. 发起RPC调用（带超时控制）
			rpcCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
			reFundList, err := finanacial_service.RefundList(rpcCtx, financialRefundListReq)
			cancel()
			if err != nil {
				logger.CtxErrorf(ctx, "第%d页查询客户列表失败: %v", page, err)
				time.Sleep(1 * time.Second)
				continue
			}
			if len(reFundList.GetItems()) == 0 {
				logger.CtxDebugf(ctx, "第%d页无数据，终止分页", page)
				hasMore = false
				break
			}

			logger.CtxDebugf(ctx, "总数为%d", reFundList.GetTotal())
			logger.CtxDebugf(ctx, "第%d页查询支款订单管理的任务数据成功，共%d条数据", page, len(reFundList.GetItems()))

			var batchData []map[string]interface{}
			for _, item := range reFundList.GetItems() {
				fields := extractFieldsByProtoReflect(item.ProtoReflect(), downloadFields)

				// 订单号 估计作废
				if downloadFields["name"] != nil {
					fields["name"] = fmt.Sprintf("%s-%s-%s*%s\nID:%s",
						item.OrderInfo.ServiceName,
						item.OrderInfo.GoodsName,
						item.OrderInfo.GoodsSpec,
						item.OrderInfo.GoodsNum,
						item.OrderInfo.OrderNo)
				}

				//订单ID
				if downloadFields["order_no"] != nil {
					fields["order_no"] = item.OrderInfo.OrderNo
				}

				//关联订单名称
				if downloadFields["order_name"] != nil {
					fields["order_name"] = fmt.Sprintf("%s-%s-%s*%s\n",
						item.OrderInfo.ServiceName,
						item.OrderInfo.GoodsName,
						item.OrderInfo.GoodsSpec,
						item.OrderInfo.GoodsNum)
				}

				if downloadFields["customer_name"] != nil {
					if item.CustomerInfo != nil {
						fields["customer_name"] = item.CustomerInfo.Name
					}
				}

				if downloadFields["created_at"] != nil {
					fields["created_at"] = formatutils.FormatTime(item.CreatedAt)
				}

				// 支款成功时间
				if downloadFields["complete_time"] != nil {
					fields["complete_time"] = formatutils.FormatTime(item.CompleteTime)
				}

				//审核通过时间
				if downloadFields["pass_time"] != nil {
					fields["pass_time"] = formatutils.FormatTime(item.PassTime)
				}

				// 审核驳回时间
				if downloadFields["reject_time"] != nil {
					fields["reject_time"] = formatutils.FormatTime(item.RejectTime)
				}

				// 用户类型
				if downloadFields["user_type"] != nil {
					switch item.UserType {
					case 1:
						fields["user_type"] = "新客户"
					case 2:
						fields["user_type"] = "老客户"
					case 3:
						fields["user_type"] = "其他"
					default:
						fields["user_type"] = "未知"
					}
				}
				// 用户类型
				if downloadFields["account_type"] != nil {
					switch item.AccountType {
					case 1:
						fields["account_type"] = "支付宝"
					case 2:
						fields["account_type"] = "微信"
					case 3:
						fields["account_type"] = "中国银行账户"
					default:
						fields["account_type"] = ""
					}
				}

				if downloadFields["refund_type"] != nil {
					switch item.RefundType {
					case 1:
						fields["refund_type"] = "退定金"
					case 2:
						fields["refund_type"] = "退服务费"
					case 3:
						fields["refund_type"] = "奖学金"
					case 4:
						fields["refund_type"] = "退差价"
					case 5:
						fields["refund_type"] = "支付违约金"
					case 6:
						fields["refund_type"] = "第三方申请费"
					default:
						fields["refund_type"] = "未知"
					}
				}
				// 成交金额
				if downloadFields["contract_amount"] != nil {
					fields["contract_amount"] = formatutils.FormatMoney(item.ContractAmount, item.Currency)
				}

				//  支款金额
				if downloadFields["real_amount_other"] != nil {
					if item.RefundType == 6 {
						fields["real_amount_other"] = formatutils.FormatMoney(item.RealAmountOther, item.Currency)
					} else {
						fields["real_amount_other"] = formatutils.FormatMoney(item.RealAmountRmb, item.Currency)
					}
				}

				//  实际付款金额
				if downloadFields["paid_amount"] != nil {
					fields["paid_amount"] = formatutils.FormatMoney(item.PaidAmount, item.Currency)
				}

				// submit_name 申请人
				if downloadFields["submit_name"] != nil {
					submitName := fmt.Sprintf("%s(%s)", item.SubmitName, item.SubmitDepartment)
					fields["submit_name"] = submitName
				}

				//客户来源，多个用 /隔开
				if downloadFields["user_source"] != nil {
					userSources := make([]string, 0, len(item.UserSource))
					for _, userSource := range item.UserSource {
						name := fmt.Sprintf("%s(%s)", userSource.Name, userSource.Depart)
						userSources = append(userSources, name)
					}
					fields["user_source"] = strings.Join(userSources, "/")
				}

				//订单来源，多个用 /隔开
				if downloadFields["order_source"] != nil {
					orderSources := make([]string, 0, len(item.OrderSource))
					for _, orderSource := range item.OrderSource {
						name := fmt.Sprintf("%s(%s)", orderSource.Name, orderSource.Depart)
						orderSources = append(orderSources, name)
					}
					fields["order_source"] = strings.Join(orderSources, "/")
				}

				//支款账号类型 1=支付宝 2=微信 3=中国银行账户
				if downloadFields["account_type"] != nil {
					switch item.AccountType {
					case 1:
						fields["account_type"] = "支付宝"
					case 2:
						fields["account_type"] = "微信"
					case 3:
						fields["account_type"] = "中国银行账户"
					default:
						fields["account_type"] = ""
					}
				}

				// 支款账号
				if downloadFields["account_name"] != nil {
					fields["account_name"] = item.AccountName
				}

				//交易单号
				if downloadFields["order_transaction_no"] != nil {
					// OrderTransactionNo 是数组，遍历获取所有value
					transactionNos := make([]string, 0, len(item.OrderTransactionNo))
					for _, value := range item.OrderTransactionNo {
						if value != "" {
							transactionNos = append(transactionNos, value)
						}
					}
					fields["order_transaction_no"] = strings.Join(transactionNos, "/") // 使用/连接所有交易单号
				}

				//申请人
				if downloadFields["submit_name"] != nil {
					submitName := fmt.Sprintf("%s(%s)", item.SubmitName, item.SubmitDepartment)
					fields["submit_name"] = submitName
				}
				//订单原提交人
				if downloadFields["submit_source"] != nil {
					submitSources := make([]string, 0, len(item.SubmitSource))
					for _, submitSource := range item.SubmitSource {
						name := fmt.Sprintf("%s(%s)", submitSource.Name, submitSource.Depart)
						submitSources = append(submitSources, name)
					}
					fields["submit_source"] = strings.Join(submitSources, "/")
				}
				//支款审批人
				if downloadFields["approve_by"] != nil {
					approveName := fmt.Sprintf("%s(%s)", item.ApproveName, item.ApproveDepartment)
					fields["approve_by"] = approveName
				}

				//订单原支付方式
				if downloadFields["account_info"] != nil {
					accountInfos := make([]string, 0, len(item.AccountInfo))
					for _, accountInfo := range item.AccountInfo {
						name := fmt.Sprintf("%s(%s)", accountInfo.Name, accountInfo.Type)
						accountInfos = append(accountInfos, name)
					}
					fields["account_info"] = strings.Join(accountInfos, "/")
				}

				//审批状态(1=待审批;2=待支款;3=支款完成;4=审核驳回);
				if downloadFields["approve_status"] != nil {
					switch item.ApproveStatus {
					case 1:
						fields["approve_status"] = "待审批"
					case 2:
						fields["approve_status"] = "待支款"
					case 3:
						fields["approve_status"] = "支款完成"
					case 4:
						fields["approve_status"] = "审核驳回"
					default:
						fields["order_status"] = ""
					}
				}

				batchData = append(batchData, fields)
			}

			// 6. 带超时的通道发送
			select {
			case dataChan <- batchData:
				logger.CtxDebugf(ctx, "成功发送第%d页数据（共%d条）", page, len(batchData))
			case <-time.After(10 * time.Second):
				logger.CtxErrorf(ctx, "第%d页数据发送超时", page)
				hasMore = false
			}

			// 7. 页数递增
			page++

			// 8. 流控逻辑（按需添加）
			time.Sleep(100 * time.Millisecond) // 控制QPS
		}

		logger.CtxDebugf(ctx, "数据生成完成，共处理%d行", totalRows)
	}()

	downloadFieldsOrder := strings.Split(task.DownloadFieldsOrder, ",")

	fileName, headers := generateExcelFileNameAndHeaders(task, downloadFields, downloadFieldsOrder)

	// 调用Excel生成函数
	filePath, err := generateXLSX(ctx, fileName, headers, dataChan, downloadFieldsOrder)
	if err != nil {
		logger.CtxErrorf(ctx, "生成Excel文件失败: %v", err)
		return "", err
	}

	return filePath, nil
}
