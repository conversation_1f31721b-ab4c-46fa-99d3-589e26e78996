package export_process

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"
	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"
	"uofferv2/server/cmd/standalone/internal/pkg/formatutils"
	"uofferv2/server/cmd/standalone/internal/services/finanacial_service"
)

// 收款订单管理的任务数据导出及文件生成
// 参数：
//
//	ctx - 上下文对象
//	task - 导出文件任务模型，包含任务配置信息
//
// 返回值：
//
//	string - 生成的临时文件路径
//	error - 错误对象
func (c *exportProcess) generateFundTableFile(ctx context.Context, task *model.ExportFileTask) (string, error) {
	//解析task的参数DownloadConditions
	downloadFields, downloadConditions, err := parseCommonConditions(task)
	if err != nil {
		return "", err
	}
	financialFundListReq := &admin_api.FinancialFundListReq{}
	approveStatusArr := downloadConditions["approve_status"].GetArrayStringVal().GetValue()
	approveStatusArrInt := make([]int32, len(approveStatusArr))
	for i, s := range approveStatusArr {
		status, _ := strconv.ParseInt(s, 10, 32)
		approveStatusArrInt[i] = int32(status)
	}
	financialFundListReq.ApproveStatusList = approveStatusArrInt

	createdAtArr := downloadConditions["created_at"].GetArrayStringVal().GetValue()
	if len(createdAtArr) == 2 {
		createStart, _ := strconv.ParseInt(createdAtArr[0], 10, 64)
		createEnd, _ := strconv.ParseInt(createdAtArr[1], 10, 64)
		financialFundListReq.CreatedAtStart = createStart
		financialFundListReq.CreatedAtEnd = createEnd
	}

	passTimeArr := downloadConditions["pass_time"].GetArrayStringVal().GetValue()
	if len(passTimeArr) == 2 {
		passTimeStart, _ := strconv.ParseInt(passTimeArr[0], 10, 64)
		passTimeEnd, _ := strconv.ParseInt(passTimeArr[1], 10, 64)
		financialFundListReq.PassTimeStart = passTimeStart
		financialFundListReq.PassTimeEnd = passTimeEnd
	}

	rejectTimeArr := downloadConditions["reject_time"].GetArrayStringVal().GetValue()
	if len(rejectTimeArr) == 2 {
		rejectTimeStart, _ := strconv.ParseInt(rejectTimeArr[0], 10, 64)
		rejectTimeEnd, _ := strconv.ParseInt(rejectTimeArr[1], 10, 64)
		financialFundListReq.RejectTimeStart = rejectTimeStart
		financialFundListReq.RejectTimeEnd = rejectTimeEnd
	}

	paidTimeArr := downloadConditions["paid_time"].GetArrayStringVal().GetValue()
	if len(paidTimeArr) == 2 {
		paidTimeStart, _ := strconv.ParseInt(paidTimeArr[0], 10, 64)
		paidTimeEnd, _ := strconv.ParseInt(paidTimeArr[1], 10, 64)
		financialFundListReq.PaidTimeStart = paidTimeStart
		financialFundListReq.PaidTimeEnd = paidTimeEnd
	}

	userSourceDepart := downloadConditions["user_source_depart"].GetArrayStringVal().GetValue()
	userSourceDepartInt := make([]int32, len(userSourceDepart))
	for i, s := range userSourceDepart {
		depart, _ := strconv.ParseInt(s, 10, 32)
		userSourceDepartInt[i] = int32(depart)
	}
	financialFundListReq.UserSourceDepart = userSourceDepartInt

	orderSourceDepart := downloadConditions["order_source_depart"].GetArrayStringVal().GetValue()
	orderSourceDepartInt := make([]int32, len(orderSourceDepart))
	for i, s := range orderSourceDepart {
		depart, _ := strconv.ParseInt(s, 10, 32)
		orderSourceDepartInt[i] = int32(depart)
	}
	financialFundListReq.OrderSourceDepart = orderSourceDepartInt

	submitSourceDepart := downloadConditions["submit_source_depart"].GetArrayStringVal().GetValue()
	submitSourceDepartInt := make([]int32, len(submitSourceDepart))
	for i, s := range submitSourceDepart {
		depart, _ := strconv.ParseInt(s, 10, 32)
		submitSourceDepartInt[i] = int32(depart)
	}
	financialFundListReq.SubmitSourceDepart = submitSourceDepartInt

	orderStatus := downloadConditions["order_status"].GetArrayStringVal().GetValue()
	orderStatusInt := make([]int32, len(orderStatus))
	for i, s := range orderStatus {
		status, _ := strconv.ParseInt(s, 10, 32)
		orderStatusInt[i] = int32(status)
	}
	financialFundListReq.OrderStatus = orderStatusInt

	goodsName := downloadConditions["goods_name"].GetStringVal().GetValue()
	financialFundListReq.GoodsName = goodsName

	serviceName := downloadConditions["service_name"].GetArrayStringVal().GetValue()
	financialFundListReq.ServiceName = serviceName

	payType := downloadConditions["pay_type"].GetStringVal().GetValue()
	payTypeInt, _ := strconv.ParseInt(payType, 10, 64)
	financialFundListReq.PayType = payTypeInt

	fundType := downloadConditions["fund_type"].GetStringVal().GetValue()
	fundTypeInt, _ := strconv.ParseInt(fundType, 10, 64)
	financialFundListReq.FundType = int32(fundTypeInt)

	brandName := downloadConditions["brand_name"].GetStringVal().GetValue()
	financialFundListReq.BrandName = brandName

	businessName := downloadConditions["business_name"].GetStringVal().GetValue()
	financialFundListReq.BusinessName = businessName
	userType := downloadConditions["user_type"].GetStringVal().GetValue()
	userTypeInt, _ := strconv.ParseInt(userType, 10, 64)
	financialFundListReq.UserType = int32(userTypeInt)

	financialFundListReq.PageSize = defaultListPageSize

	dataChan := make(chan []map[string]interface{})
	go func() {
		var (
			page      = 1    // 起始页码
			hasMore   = true // 分页标志
			totalRows = 0    // 调试用总行数
		)

		// 添加协程异常恢复
		defer func() {
			close(dataChan)
			logger.CtxDebugf(ctx, "数据通道已关闭，共发送 %d 个批次", page-1)
			if r := recover(); r != nil {
				logger.CtxErrorf(ctx, "生成协程发生panic: %v", r)
			}
		}()

		for hasMore {
			financialFundListReq.PageNum = int32(page)

			// 2. 发起RPC调用（带超时控制）
			rpcCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
			fundList, err := finanacial_service.FundList(rpcCtx, financialFundListReq)
			cancel()

			if err != nil {
				logger.CtxErrorf(ctx, "第%d页查询客户列表失败: %v", page, err)
				time.Sleep(1 * time.Second)
				continue
			}

			if len(fundList.GetItems()) == 0 {
				logger.CtxDebugf(ctx, "第%d页无数据，终止分页", page)
				hasMore = false
				break
			}

			logger.CtxDebugf(ctx, "总数为%d", fundList.GetTotal())
			logger.CtxDebugf(ctx, "第%d页查询收款订单管理的任务数据成功，共%d条数据", page, len(fundList.GetItems()))

			var batchData []map[string]interface{}
			for _, item := range fundList.GetItems() {
				fields := extractFieldsByProtoReflect(item.ProtoReflect(), downloadFields)

				// 订单号 估计作废
				if downloadFields["name"] != nil {
					goodsNum, _ := strconv.ParseFloat(item.OrderInfo.GoodsNum, 64)
					roundedGoodsNum := math.Round(goodsNum)
					fields["name"] = fmt.Sprintf("%s-%s-%s*%d\nID:%s",
						item.OrderInfo.ServiceName,
						item.OrderInfo.GoodsName,
						item.OrderInfo.GoodsSpec,
						int(roundedGoodsNum),
						item.OrderInfo.OrderNo)
				}

				//订单ID
				if downloadFields["order_no"] != nil {
					fields["order_no"] = item.OrderInfo.OrderNo
				}

				//关联订单名称
				if downloadFields["order_name"] != nil {
					goodsNum, _ := strconv.ParseFloat(item.OrderInfo.GoodsNum, 64)
					roundedGoodsNum := math.Round(goodsNum)
					fields["order_name"] = fmt.Sprintf("%s-%s-%s*%d\n",
						item.OrderInfo.ServiceName,
						item.OrderInfo.GoodsName,
						item.OrderInfo.GoodsSpec,
						int(roundedGoodsNum))
				}

				// 付款时间
				if downloadFields["paid_time"] != nil {
					fields["paid_time"] = formatutils.FormatTime(item.PaidTime)
				}

				// 创建时间
				if downloadFields["created_at"] != nil {
					fields["created_at"] = formatutils.FormatTime(item.CreatedAt)
				}

				//审核通过时间
				if downloadFields["pass_time"] != nil {
					fields["pass_time"] = formatutils.FormatTime(item.PassTime)
				}

				// 审核驳回时间
				if downloadFields["reject_time"] != nil {
					fields["reject_time"] = formatutils.FormatTime(item.RejectTime)
				}

				// 客户名称
				if downloadFields["customer_name"] != nil {
					fields["customer_name"] = item.CustomerInfo.Name
				}

				// 付款方式
				if downloadFields["pay_type"] != nil {
					switch item.PayType {
					case 1:
						fields["pay_type"] = "全款"
					case 2:
						fields["pay_type"] = "分期"
					default:
						fields["pay_type"] = "未知"
					}
				}

				// 款项类型
				if downloadFields["fund_type"] != nil {
					switch item.FundType {
					case 1:
						fields["fund_type"] = "定金"
					case 2:
						fields["fund_type"] = "订单首期款"
					case 3:
						fields["fund_type"] = "订单尾款"
					case 4:
						fields["fund_type"] = "第三方申请费"
					default:
						fields["fund_type"] = "未知"
					}
				}

				// 用户类型
				if downloadFields["user_type"] != nil {
					switch item.UserType {
					case 1:
						fields["user_type"] = "新客户"
					case 2:
						fields["user_type"] = "老客户"
					case 3:
						fields["user_type"] = "其他"
					default:
						fields["user_type"] = "未知"
					}
				}
				//客户来源
				if downloadFields["user_source"] != nil {
					//是个数组，遍历获取所有 UserSource，
					//若有多个提交人，用/分隔每个提交人，展示昵称（多层级所在部门）
					userSources := make([]string, 0, len(item.UserSource))
					for _, userSource := range item.UserSource {
						name := fmt.Sprintf("%s(%s)", userSource.Name, userSource.Depart)
						userSources = append(userSources, name)
					}
					fields["user_source"] = strings.Join(userSources, "/")
				}
				//本期应收金额
				if downloadFields["should_amount_rmb"] != nil {
					if item.FundType == 4 {
						var shouldAmountRmb float64
						for _, pai := range item.FinancialPaiInfo {
							amount, _ := strconv.ParseFloat(pai.AmountOther, 64)
							shouldAmountRmb += amount
						}
						fields["should_amount_rmb"] = formatutils.FormatMoney(fmt.Sprintf("%.2f", shouldAmountRmb), item.Currency)
					} else {
						fields["should_amount_rmb"] = formatutils.FormatMoney(item.ShouldAmountRmb, item.Currency)
					}
				}
				//本期实收金额
				if downloadFields["real_amount_rmb"] != nil {
					if item.FundType == 4 {
						var realAmountRmb float64
						for _, pai := range item.FinancialPaiInfo {
							amount, _ := strconv.ParseFloat(pai.AmountOther, 64)
							realAmountRmb += amount
						}
						fields["real_amount_rmb"] = formatutils.FormatMoney(fmt.Sprintf("%.2f", realAmountRmb), item.Currency)
					} else {
						fields["real_amount_rmb"] = formatutils.FormatMoney(item.RealAmountRmb, item.Currency)
					}
				}
				//关联订单状态
				if downloadFields["order_status"] != nil {
					switch item.OrderStatus {
					case 1:
						fields["order_status"] = "已下定金（定金）"
					case 2:
						fields["order_status"] = "支付待确认（首款）"
					case 3:
						fields["order_status"] = "尾款待支付（尾款）"
					case 4:
						fields["order_status"] = "支付成功"
					case 5:
						fields["order_status"] = "支款订单"
					case 6:
						fields["order_status"] = "交易关闭"
					default:
						fields["order_status"] = ""
					}
				}
				//订单来源
				if downloadFields["order_source"] != nil {
					orderSources := make([]string, 0, len(item.OrderSource))
					for _, orderSource := range item.OrderSource {
						name := fmt.Sprintf("%s(%s)", orderSource.Name, orderSource.Depart)
						orderSources = append(orderSources, name)
					}
					fields["order_source"] = strings.Join(orderSources, "/")
				}

				//提交人
				if downloadFields["submit_source"] != nil {
					//是个数组，遍历获取所有 SubmitSource，
					//若有多个提交人，用/分隔每个提交人，展示昵称（多层级所在部门）
					submitSources := make([]string, 0, len(item.SubmitSource))
					for _, submitSource := range item.SubmitSource {
						name := fmt.Sprintf("%s(%s)", submitSource.Name, submitSource.Depart)
						submitSources = append(submitSources, name)
					}
					fields["submit_source"] = strings.Join(submitSources, "/")
				}

				// 收款账号类型
				// if downloadFields["paid_type"] != nil {
				// 	switch item.PaidType {
				// 	case 1:
				// 		fields["paid_type"] = "支付宝"
				// 	case 2:
				// 		fields["paid_type"] = "微信"
				// 	case 3:
				// 		fields["paid_type"] = "中国银行账户"
				// 	case 4:
				// 		fields["paid_type"] = "英国银行账户"
				// 	case 5:
				// 		fields["paid_type"] = "pos机"
				// 	case 6:
				// 		fields["paid_type"] = "paypal"
				// 	case 7:
				// 		fields["paid_type"] = "其他"
				// 	default:
				// 		fields["paid_type"] = ""
				// 	}
				// }

				// 收款账号信息 收款账号类型
				if downloadFields["financial_pai_info"] != nil {
					financialPaidInfos := make([]string, len(item.FinancialPaiInfo))
					paidTypes := make([]int32, len(item.FinancialPaiInfo))
					for i, financialPaidInfo := range item.FinancialPaiInfo {
						financialPaidInfos[i] = financialPaidInfo.FinancialAccountName
						paidTypes[i] = int32(financialPaidInfo.PaidType)
					}
					if len(financialPaidInfos) >= 2 {
						fields["financial_pai_info"] = strings.Join(financialPaidInfos, ",")
						// 收款账号类型
						if downloadFields["paid_type"] != nil {
							paidTypeStr := make([]string, len(paidTypes))
							for i, paidType := range paidTypes {
								switch paidType {
								case 1:
									paidTypeStr[i] = "支付宝"
								case 2:
									paidTypeStr[i] = "微信"
								case 3:
									paidTypeStr[i] = "中国银行账户"
								case 4:
									paidTypeStr[i] = "英国银行账户"
								case 5:
									paidTypeStr[i] = "pos机"
								case 6:
									paidTypeStr[i] = "paypal"
								case 7:
									paidTypeStr[i] = "其他"
								default:
									paidTypeStr[i] = ""
								}
							}
							fields["paid_type"] = strings.Join(paidTypeStr, ",")
						}
					}
					if len(financialPaidInfos) == 1 {
						fields["financial_pai_info"] = financialPaidInfos[0]
						// 收款账号类型
						if downloadFields["paid_type"] != nil {
							switch paidTypes[0] {
							case 1:
								fields["paid_type"] = "支付宝"
							case 2:
								fields["paid_type"] = "微信"
							case 3:
								fields["paid_type"] = "中国银行账户"
							case 4:
								fields["paid_type"] = "英国银行账户"
							case 5:
								fields["paid_type"] = "pos机"
							case 6:
								fields["paid_type"] = "paypal"
							case 7:
								fields["paid_type"] = "其他"
							default:
								fields["paid_type"] = ""
							}
						}
					}

				}

				//合同金额
				if downloadFields["should_amount_other"] != nil {
					fields["should_amount_other"] = formatutils.FormatMoney(item.ShouldAmountOther, item.Currency)
				}

				// 实收金额
				if downloadFields["real_amount_other"] != nil {
					fields["real_amount_other"] = formatutils.FormatMoney(item.RealAmountOther, item.Currency)
				}
				// submit_name 提交人
				if downloadFields["submit_name"] != nil {
					fields["submit_name"] = item.SubmitName
				}
				// transaction_no 交易单号
				if downloadFields["transaction_no"] != nil {
					// FinancialPaiInfo 是数组，遍历获取所有 TransactionNo
					transactionNos := make([]string, 0, len(item.FinancialPaiInfo))
					for _, financialPaidInfo := range item.FinancialPaiInfo {
						if financialPaidInfo.TransactionNo != nil { // 可以选择性地跳过空的交易单号
							transactionNos = append(transactionNos, financialPaidInfo.TransactionNo...)
						}
					}
					fields["transaction_no"] = strings.Join(transactionNos, ",") // 使用逗号连接所有交易单号
				}
				//审批状态(1=待审批;2=审批通过;3=驳回审批;0=全部);
				if downloadFields["approve_status"] != nil {
					switch item.ApproveStatus {
					case 1:
						fields["approve_status"] = "待审批"
					case 2:
						fields["approve_status"] = "审批通过"
					case 3:
						fields["approve_status"] = "驳回审批"
					default:
						fields["order_status"] = ""
					}
				}
				batchData = append(batchData, fields)
			}

			// 6. 带超时的通道发送
			select {
			case dataChan <- batchData:
				logger.CtxDebugf(ctx, "成功发送第%d页数据（共%d条）", page, len(batchData))
			case <-time.After(10 * time.Second):
				logger.CtxErrorf(ctx, "第%d页数据发送超时", page)
				hasMore = false
			}

			// 7. 页数递增
			page++

			// 8. 流控逻辑（按需添加）

			time.Sleep(100 * time.Millisecond) // 控制QPS
		}
		logger.CtxDebugf(ctx, "数据生成完成，共处理%d行", totalRows)

	}()

	downloadFieldsOrder := strings.Split(task.DownloadFieldsOrder, ",")
	fileName, headers := generateExcelFileNameAndHeaders(task, downloadFields, downloadFieldsOrder)

	filePath, err := generateXLSX(ctx, fileName, headers, dataChan, downloadFieldsOrder)
	if err != nil {
		logger.CtxErrorf(ctx, "生成Excel文件失败: %v", err)
		return "", err
	}

	return filePath, nil
}
