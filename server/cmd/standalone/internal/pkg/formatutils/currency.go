package formatutils

import (
	"fmt"

	"github.com/shopspring/decimal"
)

func GetCurrencySymbol(currencyName string) string {
	switch currencyName {
	case "USD":
		return "$"
	case "EUR":
		return "€"
	case "CNY":
		return "¥"
	case "GBP":
		return "£"
	default:
		return "" // 或者返回默认符号
	}
}

func FormatMoney(money string, currencyName string) string {
	// 尝试将输入的 money 字符串解析为 Decimal 类型
	amountDecimal, err := decimal.NewFromString(money)
	if err != nil {
		return "Invalid Amount" // 或者其他错误指示
	}

	// 创建一个表示 100 的 Decimal 值
	hundred := decimal.NewFromInt(100)

	// 使用 Decimal 进行除法运算，保留两位小数
	moneyDecimal := amountDecimal.Div(hundred).Round(5)

	// 获取货币符号
	currencySymbol := GetCurrencySymbol(currencyName)

	return fmt.Sprintf("%s%s", currencySymbol, moneyDecimal.StringFixed(5))
}
