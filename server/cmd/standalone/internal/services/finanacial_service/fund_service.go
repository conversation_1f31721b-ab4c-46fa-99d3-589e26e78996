package finanacial_service

import (
	"context"
	"encoding/json"
	"uofferv2/kitex_gen/server/cmd/customer"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/kitex_gen/server/cmd/user"
	"uofferv2/pkg/utils"
	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"
	"uofferv2/server/cmd/standalone/internal/global"

	"github.com/shopspring/decimal"
)

var TypeNameMap = map[int32]string{
	0: "",
	1: "支付宝",
	2: "微信",
	3: "中国银行账户",
	4: "英国银行账户",
	5: "pos机",
	6: "paypal",
	7: "其他",
}

// FundList 收款单列表.
// @router /financial/FundList [POST]
func FundList(ctx context.Context, req *admin_api.FinancialFundListReq) (*admin_api.FinancialFundListRsp, error) {
	paymentAccountTypes := utils.ConvertSlice(req.PaymentAccountTypes, func(t admin_api.PaymentAccountType) order.PaymentAccountType {
		return order.PaymentAccountType(t)
	})
	respRpc, err := global.FinancialClient.FundList(ctx, &order.FinancialFundListReq{
		ApproveStatus:       req.ApproveStatus,
		ApproveStatusList:   req.ApproveStatusList,
		FundNo:              req.FundNo,
		FundType:            req.FundType,
		OrderNo:             req.OrderNo,
		CustomerId:          req.CustomerId,
		GoodsName:           req.GoodsName,
		ServiceName:         req.ServiceName,
		BrandName:           req.BrandName,
		BusinessName:        req.BusinessName,
		PayType:             req.PayType,
		PaymentAccountId:    req.PaymentAccountId, //暂时保留，兼容用
		PaymentAccountIds:   req.PaymentAccountIds,
		Currency:            req.Currency,
		ContractNo:          req.ContractNo,
		OrderBy:             req.OrderBy,
		SubmitId:            req.SubmitId,
		CreatedAtStart:      req.CreatedAtStart,
		CreatedAtEnd:        req.CreatedAtEnd,
		PassTimeStart:       req.PassTimeStart,
		PassTimeEnd:         req.PassTimeEnd,
		RejectTimeStart:     req.RejectTimeStart,
		RejectTimeEnd:       req.RejectTimeEnd,
		TransactionNo:       req.TransactionNo,
		UserType:            req.UserType,
		PageNum:             req.PageNum,
		PageSize:            req.PageSize,
		CustomerName:        req.CustomerName,
		PaidTimeStart:       req.PaidTimeStart,
		PaidTimeEnd:         req.PaidTimeEnd,
		UserSource:          req.UserSource,
		UserSourceDepart:    req.UserSourceDepart,
		OrderSource:         req.OrderSource,
		OrderSourceDepart:   req.OrderSourceDepart,
		OrderStatus:         req.OrderStatus,
		SubmitSourceDepart:  req.SubmitSourceDepart,
		PaymentAccountTypes: paymentAccountTypes,
	})
	if err != nil {
		return nil, err
	}

	items := make([]*admin_api.FinancialFundInfo, 0, len(respRpc.FinancialFundList))
	customerIds := make([]int64, 0, 0)
	employeeIds := make([]int64, 0, 0)
	for _, item := range respRpc.FinancialFundList {
		thirdAmountList := make([]*admin_api.AmountInfo, 0, 0)
		m := map[string]string{}
		tmp1 := decimal.Decimal{}
		tmp2 := decimal.Decimal{}
		tmp3 := decimal.Decimal{}
		paidList := make([]*admin_api.FinancialPaidInfo, 0, len(item.FinancialPaiInfo))
		for _, paid := range item.FinancialPaiInfo {
			var imagesPath []*admin_api.FundContractInfo
			if len(paid.ImagesPath) > 0 {
				_ = json.Unmarshal([]byte(paid.ImagesPath), &imagesPath)
				for i, _ := range imagesPath {
					imagesPath[i].ThumbnailUrl = imagesPath[i].Url
				}
			}
			accountName := ""
			tmp, ok := TypeNameMap[paid.PaidType]
			if ok {
				accountName = tmp
			}
			paidList = append(paidList, &admin_api.FinancialPaidInfo{
				PaymentAccountId:     paid.PaymentAccountId,
				Currency:             paid.Currency,
				PaidType:             admin_api.PaymentAccountType(paid.PaidType),
				FinancialAccountName: paid.AccountName,
				AmountOther:          paid.AmountOther,
				ImagesPath:           imagesPath,
				FinancialPaidId:      paid.Id,
				AmountCny:            paid.AmountCny,
				ExchangeRate:         paid.ExchangeRate,
				TransactionNo:        paid.TransactionNo,
				AccountTypeName:      accountName,
			})
			val, ok := m[paid.Currency]
			if ok {
				tmp1, _ = decimal.NewFromString(val)
				tmp2, _ = decimal.NewFromString(paid.AmountOther)
				tmp3 = tmp1.Add(tmp2)
				m[paid.Currency] = tmp3.String()
			} else {
				m[paid.Currency] = paid.AmountOther
			}
		}
		for k, v := range m {
			thirdAmountList = append(thirdAmountList, &admin_api.AmountInfo{
				Currency: k,
				Amount:   v,
			})
		}
		var contractInfo []*admin_api.FundContractInfo
		_ = json.Unmarshal([]byte(item.ContractUrl), &contractInfo)
		serviceName := ""
		if len(item.ServiceName) > 0 {
			serviceName = item.ServiceName[0]
		}
		items = append(items, &admin_api.FinancialFundInfo{
			Id:     item.Id,
			FundNo: item.FundNo,
			OrderInfo: &admin_api.FinancialOrderInfo{
				OrderId:     item.OrderId,
				OrderNo:     item.OrderNo,
				GoodsName:   item.GoodsName,
				GoodsSpec:   item.GoodsSpec,
				ServiceName: serviceName,
				GoodsNum:    item.GoodsNum,
			},
			CustomerId:        item.CustomerId,
			ShouldAmountOther: item.ShouldAmountOther,
			ShouldAmountRmb:   item.ShouldAmountRmb,
			RealAmountOther:   item.RealAmountOther,
			Currency:          item.Currency,
			PaidTime:          item.PaidTime,
			PayType:           item.PayType,
			FundType:          item.FundType,
			ServiceName:       item.ServiceName,
			BrandName:         item.BrandName,
			BusinessName:      item.BusinessName,
			ContractNo:        item.ContractNo,
			CreatedAt:         item.CreatedAt,
			SubmitId:          item.SubmitId,
			FinancialPaiInfo:  paidList,
			PassTime:          item.PassTime,
			RejectTime:        item.RejectTime,
			ApproveStatus:     item.ApproveStatus,
			RealAmountRmb:     item.RealAmountRmb,
			ExchangeRate:      item.ExchangeRate,
			ContractInfo:      contractInfo,
			WorkflowNo:        item.WorkflowNo,
			WorkflowId:        item.WorkflowId,
			UserType:          item.UserType,
			ThirdAmountList:   thirdAmountList,
			OrderStatus:       item.OrderStatus,
			Remark:            item.Remark,
		})
		for _, i := range item.UserSource {
			employeeIds = append(employeeIds, i)
		}
		for _, i := range item.OrderSource {
			employeeIds = append(employeeIds, i)
		}
		for _, i := range item.SubmitSource {
			employeeIds = append(employeeIds, i)
		}
		customerIds = append(customerIds, item.CustomerId)
		employeeIds = append(employeeIds, item.SubmitId)
	}
	employeeResp := &user.GetEmployeeInfoByIdsRsp{}
	if len(employeeIds) > 0 {
		employeeResp, err = global.UsersClient.GetEmployeeInfoByIds(ctx, &user.GetEmployeeInfoByIdsReq{
			Id: employeeIds,
		})
		if err != nil {
			return nil, err
		}
	}
	// 获取客户信息
	customerRpcResp, err := global.CustomerProfileClient.GetCustomersByIds(ctx, &customer.GetCustomersByIdsReq{
		CustomerIds: customerIds,
	})
	if err != nil {
		return nil, err
	}
	// 构建客户信息映射并关联到 items
	customerMap := make(map[int64]*admin_api.CustomerWithTag, len(customerRpcResp.GetCustomers()))
	for _, customerInfo := range customerRpcResp.GetCustomers() {
		customerMap[int64(customerInfo.GetId())] = &admin_api.CustomerWithTag{
			Id:        int64(customerInfo.GetId()),
			Name:      customerInfo.GetName(),
			Tags:      convertTags(customerInfo.GetTags()),
			SmartTags: convertTags(customerInfo.GetSmartTags()),
		}
	}
	// 创建员工信息映射
	type employeeInfo struct {
		name         string
		deptName     string
		deptNameTree string
	}
	employeeMap := make(map[int64]employeeInfo)
	for _, emp := range employeeResp.List {
		employeeMap[emp.Id] = employeeInfo{
			name:         emp.Name,
			deptName:     emp.DeptName,
			deptNameTree: emp.DeptNameTree,
		}
	}
	// 更新员工信息
	for i := range items {
		if info, ok := employeeMap[items[i].SubmitId]; ok {
			items[i].SubmitName = info.name
			items[i].SubmitDept = info.deptNameTree
			var smock []*admin_api.IdNameDept
			smock = append(smock, &admin_api.IdNameDept{
				Id:     items[i].SubmitId,
				Name:   info.name,
				Depart: info.deptNameTree,
			})
			items[i].UserSource = smock
			items[i].SubmitSource = smock
		}
	}

	for index, i := range respRpc.FinancialFundList {
		//订单来源人
		var omock []*admin_api.IdNameDept
		for _, e := range i.OrderSource {
			if info, ok := employeeMap[e]; ok {
				omock = append(omock, &admin_api.IdNameDept{
					Id:     e,
					Name:   info.name,
					Depart: info.deptNameTree,
				})
			}
		}
		items[index].OrderSource = omock

		//客户来源人
		var cmock []*admin_api.IdNameDept
		for _, c := range i.UserSource {
			if info, ok := employeeMap[c]; ok {
				cmock = append(cmock, &admin_api.IdNameDept{
					Id:     c,
					Name:   info.name,
					Depart: info.deptNameTree,
				})
			}
		}
		items[index].UserSource = cmock

		//订单共同提交人
		var smock []*admin_api.IdNameDept
		smock = append(smock, &admin_api.IdNameDept{
			Id:     items[index].SubmitId,
			Name:   items[index].SubmitName,
			Depart: items[index].SubmitDept,
		})
		for _, s := range i.SubmitSource {
			if info, ok := employeeMap[s]; ok {
				smock = append(smock, &admin_api.IdNameDept{
					Id:     s,
					Name:   info.name,
					Depart: info.deptNameTree,
				})
			}
		}
		items[index].SubmitSource = smock

	}
	//更新客户信息
	for i := range items {
		if customerInfo, ok := customerMap[items[i].CustomerId]; ok {
			items[i].CustomerInfo = customerInfo
		}
	}
	//返回
	return &admin_api.FinancialFundListRsp{
		Total: respRpc.Total,
		Items: items,
	}, nil
}
