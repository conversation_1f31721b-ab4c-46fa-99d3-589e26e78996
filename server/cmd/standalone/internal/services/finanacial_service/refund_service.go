package finanacial_service

import (
	"context"
	"encoding/json"
	"uofferv2/kitex_gen/server/cmd/customer"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/kitex_gen/server/cmd/user"
	"uofferv2/pkg/utils"
	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"
	"uofferv2/server/cmd/standalone/internal/global"
)

type RelationFundRequest struct {
	OrderId  int64  `json:"order_id"`
	OrderNo  string `json:"order_no"`
	FundType int32  `json:"fund_type"`
}

// RefundList 支款列表.
// @router /financial/RefundList [POST]
func RefundList(ctx context.Context, req *admin_api.FinancialRefundListReq) (*admin_api.FinancialRefundListRsp, error) {
	// 转换 PaymentAccountTypes 类型
	paymentAccountTypes := utils.ConvertSlice(req.PaymentAccountTypes, func(t admin_api.PaymentAccountType) order.PaymentAccountType {
		return order.PaymentAccountType(t)
	})

	respRpc, err := global.FinancialClient.RefundList(ctx, &order.FinancialRefundListReq{
		ApproveStatus:       req.ApproveStatus,
		ApproveStatusList:   req.ApproveStatusList,
		PageNum:             req.PageNum,
		PageSize:            req.PageSize,
		OrderBy:             req.OrderBy,
		CreatedAtStart:      req.CreatedAtStart,
		CreatedAtEnd:        req.CreatedAtEnd,
		PassTimeStart:       req.PassTimeStart,
		PassTimeEnd:         req.PassTimeEnd,
		RejectTimeStart:     req.RejectTimeStart,
		RejectTimeEnd:       req.RejectTimeEnd,
		CompleteTimeStart:   req.CompleteTimeStart,
		CompleteTimeEnd:     req.CompleteTimeEnd,
		RefundNo:            req.RefundNo,
		RefundType:          req.RefundType,
		OrderNo:             req.OrderNo,
		CustomerId:          req.CustomerId,
		GoodsName:           req.GoodsName,
		ServiceName:         req.ServiceName,
		SubmitId:            req.SubmitId,
		BrandName:           req.BrandName,
		BusinessName:        req.BusinessName,
		PaymentAccountId:    req.PaymentAccountId,
		PaymentAccountIds:   req.PaymentAccountIds,
		Currency:            req.Currency,
		WorkflowId:          req.WorkflowId,
		WorkflowNo:          req.WorkflowNo,
		UserType:            req.UserType,
		PaymentAccountTypes: paymentAccountTypes,
		PaymentAccountType:  order.PaymentAccountType(req.PaymentAccountType),
		UserSource:          req.UserSource,
		UserSourceDepart:    req.UserSourceDepart,
		OrderSource:         req.OrderSource,
		OrderSourceDepart:   req.OrderSourceDepart,
		CustomerName:        req.CustomerName,
		SubmitDepart:        req.SubmitDepart,
		TransactionNo:       req.TransactionNo,
		ApproveBy:           req.ApproveBy,
	})
	if err != nil {
		return nil, err
	}
	items := make([]*admin_api.FinancialRefundInfo, 0, len(respRpc.FinancialRefundList))
	customerIds := make([]int64, 0)
	employeeIds := make([]int64, 0)

	for _, item := range respRpc.FinancialRefundList {
		var refundAgreement []*admin_api.ImageInfo
		var approveLog []*admin_api.ImageInfo
		var scholarshipAgreement []*admin_api.ImageInfo
		var visa []*admin_api.ImageInfo
		var studentCard []*admin_api.ImageInfo
		var tuitionPaymentProof []*admin_api.ImageInfo
		var transactionNo []*admin_api.ImageInfo
		if len(item.RefundAgreement) > 0 {
			_ = json.Unmarshal([]byte(item.RefundAgreement), &refundAgreement)
		}
		if len(item.ApproveLog) > 0 {
			_ = json.Unmarshal([]byte(item.ApproveLog), &approveLog)
		}
		if len(item.ScholarshipAgreement) > 0 {
			_ = json.Unmarshal([]byte(item.ScholarshipAgreement), &scholarshipAgreement)
		}
		if len(item.Visa) > 0 {
			_ = json.Unmarshal([]byte(item.Visa), &visa)
		}
		if len(item.StudentCard) > 0 {
			_ = json.Unmarshal([]byte(item.StudentCard), &studentCard)
		}
		if len(item.TuitionPaymentProof) > 0 {
			_ = json.Unmarshal([]byte(item.TuitionPaymentProof), &tuitionPaymentProof)
		}
		if len(item.TransactionNo) > 0 {
			_ = json.Unmarshal([]byte(item.TransactionNo), &transactionNo)
			for i, _ := range transactionNo {
				transactionNo[i].ThumbnailUrl = transactionNo[i].Url
			}
		}
		serviceName := ""
		if len(item.ServiceName) > 0 {
			serviceName = item.ServiceName[0]
		}
		var amock []*admin_api.AccountInfo
		for _, v := range item.AccountInfo {
			amock = append(amock, &admin_api.AccountInfo{
				Type: v.Type,
				Name: v.Name,
			})
		}
		items = append(items, &admin_api.FinancialRefundInfo{
			Id:       item.Id,
			RefundNo: item.RefundNo,
			OrderInfo: &admin_api.RelationOrderInfo{
				OrderId:     item.OrderId,
				OrderNo:     item.OrderNo,
				GoodsName:   item.GoodsName,
				GoodsSpec:   item.GoodsSpecsName,
				ServiceName: serviceName,
				GoodsNum:    item.GoodsNum,
			},
			CustomerId:               item.CustomerId,
			ShouldAmountOther:        item.ShouldAmountOther,
			RealAmountOther:          item.RealAmountOther,
			Currency:                 item.Currency,
			CreatedAt:                item.CreatedAt,
			SubmitId:                 item.SubmitId,
			RefundType:               item.RefundType,
			PaymentAccountId:         item.PaymentAccountId,
			TransactionNo:            transactionNo,
			AccountType:              item.AccountType,
			FundNo:                   item.FundNo,
			ContractAmount:           item.ContractAmount,
			BusinessName:             item.BusinessName,
			BrandName:                item.BrandName,
			ServiceName:              item.ServiceName,
			PassTime:                 item.PassTime,
			RejectTime:               item.RejectTime,
			CompleteTime:             item.CompleteTime,
			AccountName:              item.AccountName,
			ApproveStatus:            item.ApproveStatus,
			RealAmountRmb:            item.RealAmountRmb,
			RefundReason:             item.RefundReason,
			WorkflowNo:               item.WorkflowNo,
			WorkflowId:               item.WorkflowId,
			WorkflowName:             item.WorkflowName,
			RefundAgreement:          refundAgreement,
			ApproveLog:               approveLog,
			ScholarshipAgreement:     scholarshipAgreement,
			Visa:                     visa,
			StudentCard:              studentCard,
			TuitionPaymentProof:      tuitionPaymentProof,
			ExchangeRate:             item.ExchangeRate,
			RefundReceiveAccount:     item.RefundReceiveAccount,
			RefundReceiveAccountType: item.RefundReceiveAccountType,
			ApproveBy:                item.ApproveBy,
			ApproveComment:           item.ApproveComment,
			UserType:                 item.UserType,
			AccountInfo:              amock,
			OrderTransactionNo:       item.OrderTransactionNo,
			PaidAmount:               item.PaidAmount,
		})
		customerIds = append(customerIds, item.CustomerId)
		employeeIds = append(employeeIds, item.SubmitId)
		employeeIds = append(employeeIds, item.ApproveBy)
		for _, i := range item.UserSource {
			employeeIds = append(employeeIds, i)
		}
		for _, i := range item.OrderSource {
			employeeIds = append(employeeIds, i)
		}
		for _, i := range item.SubmitSource {
			employeeIds = append(employeeIds, i)
		}
	}
	// 获取客户信息
	customerRpcResp, err := global.CustomerProfileClient.GetCustomersByIds(ctx, &customer.GetCustomersByIdsReq{
		CustomerIds: customerIds,
	})
	if err != nil {
		return nil, err
	}
	// 构建客户信息映射并关联到 items
	customerMap := make(map[int64]*admin_api.CustomerWithTags, len(customerRpcResp.GetCustomers()))
	for _, customerInfo := range customerRpcResp.GetCustomers() {
		customerMap[int64(customerInfo.GetId())] = &admin_api.CustomerWithTags{
			Id:        int64(customerInfo.GetId()),
			Name:      customerInfo.GetName(),
			Tags:      convertTags2(customerInfo.Tags),
			SmartTags: convertTags2(customerInfo.SmartTags),
		}
	}
	for i := range items {
		if customerInfo, ok := customerMap[items[i].CustomerId]; ok {
			items[i].CustomerInfo = customerInfo
		}
	}
	employeeResp := &user.GetEmployeeInfoByIdsRsp{}
	if len(employeeIds) > 0 {
		employeeResp, err = global.UsersClient.GetEmployeeInfoByIds(ctx, &user.GetEmployeeInfoByIdsReq{
			Id: employeeIds,
		})
		if err != nil {
			return nil, err
		}
	}
	// 创建员工信息映射
	type employeeInfo struct {
		name         string
		deptName     string
		deptNameTree string
	}
	employeeMap := make(map[int64]employeeInfo)
	for _, emp := range employeeResp.List {
		employeeMap[emp.Id] = employeeInfo{
			name:         emp.Name,
			deptName:     emp.DeptName,
			deptNameTree: emp.DeptNameTree,
		}
	}
	// 更新员工信息
	for i := range items {
		if info, ok := employeeMap[items[i].SubmitId]; ok {
			items[i].SubmitName = info.name
			items[i].SubmitDepartment = info.deptNameTree
		}
		if info2, ok2 := employeeMap[items[i].ApproveBy]; ok2 {
			items[i].ApproveName = info2.name
			items[i].ApproveDepartment = info2.deptNameTree
		}
	}

	for index, i := range respRpc.FinancialRefundList {
		//订单来源人
		var omock []*admin_api.IdNameDept
		for _, e := range i.OrderSource {
			if info, ok := employeeMap[e]; ok {
				omock = append(omock, &admin_api.IdNameDept{
					Id:     e,
					Name:   info.name,
					Depart: info.deptNameTree,
				})
			}
		}
		items[index].OrderSource = omock

		//客户来源人
		var cmock []*admin_api.IdNameDept
		for _, c := range i.UserSource {
			if info, ok := employeeMap[c]; ok {
				cmock = append(cmock, &admin_api.IdNameDept{
					Id:     c,
					Name:   info.name,
					Depart: info.deptNameTree,
				})
			}
		}
		items[index].UserSource = cmock

		//订单共同提交人
		var smock []*admin_api.IdNameDept
		smock = append(smock, &admin_api.IdNameDept{
			Id:     items[index].SubmitId,
			Name:   items[index].SubmitName,
			Depart: items[index].SubmitDepartment,
		})
		for _, s := range i.SubmitSource {
			if info, ok := employeeMap[s]; ok {
				smock = append(smock, &admin_api.IdNameDept{
					Id:     s,
					Name:   info.name,
					Depart: info.deptNameTree,
				})
			}
		}
		items[index].SubmitSource = smock
	}

	return &admin_api.FinancialRefundListRsp{
		Total: respRpc.Total,
		Items: items,
	}, nil
}
