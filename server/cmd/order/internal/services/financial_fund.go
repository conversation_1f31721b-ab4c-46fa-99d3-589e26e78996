package services

import (
	"context"
	"errors"
	"time"

	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/pkg/utils"
	"uofferv2/server/cmd/order/internal/global"
	"uofferv2/server/cmd/order/internal/models"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// FundCreate 新增收款订单
func FundCreate(ctx context.Context, req *order.FinancialFundCreateReq) (*order.FinancialFundCreateRsp, error) {
	// 先查询是否存在
	existRefund, err := models.FundInfo(ctx, &models.FundInfoRequest{
		OrderID:  req.OrderId,
		FundType: req.FundType,
	})
	discount := int32(1)
	if req.DiscountRate != "100.00000" {
		discount = int32(2)
	}
	if err == nil && existRefund != nil {
		// 构造更新请求
		updateReq := &order.FinancialFundUpdateReq{
			FinancialFundId:   existRefund.ID,
			OrderNo:           req.OrderNo,
			FundNo:            req.FundNo,
			CustomerId:        req.CustomerId,
			Currency:          req.Currency,
			ExchangeRate:      req.ExchangeRate,
			RealAmountOther:   req.RealAmountOther,
			RealAmountRmb:     req.RealAmountRmb,
			ShouldAmountOther: req.ShouldAmountOther,
			ShouldAmountRmb:   req.ShouldAmountRmb,
			PayType:           req.PayType,
			FundType:          req.FundType,
			Discount:          discount,
			DiscountRate:      req.DiscountRate,
			UrgentSpeed:       req.UrgentSpeed,
			PaidTime:          req.PaidTime,
			ContractNo:        req.ContractNo,
			ContractUrl:       req.ContractUrl,
			UpdatedBy:         req.SubmitId,
			FinancialPaidInfo: req.FinancialPaidInfo,
			GoodsId:           req.GoodsId,
			GoodsName:         req.GoodsName,
			GoodsSpecsName:    req.GoodsSpecsName,
			Num:               req.Num,
			ServiceName:       req.ServiceName,
			BrandName:         req.BrandName,
			BusinessName:      req.BusinessName,
			UserType:          req.UserType,
			Remark:            req.Remark,
		}

		_, err = FundUpdate(ctx, updateReq)
		if err != nil {
			return nil, err
		}
		return &order.FinancialFundCreateRsp{
			Id: existRefund.ID,
		}, nil
	}
	//不存在则新增
	fundNo := generateOrderNo()
	payTime := time.UnixMilli(req.PaidTime)

	var userType int32
	//第三方申请费 固定为 其他类型
	if req.FundType == 4 {
		userType = 3
	} else {
		userType = req.UserType
	}

	fundId, err := models.FundCreate(ctx, &model.FinancialFund{
		OrderNo:           req.OrderNo,
		FundNo:            fundNo,
		CustomerID:        req.CustomerId,
		PayType:           req.PayType,
		FundType:          req.FundType,
		Currency:          req.Currency,
		RealAmountOther:   req.RealAmountOther,
		RealAmountRmb:     req.RealAmountRmb,
		Discount:          req.Discount,
		ShouldAmountOther: req.ShouldAmountOther,
		ShouldAmountRmb:   req.ShouldAmountRmb,
		UrgentSpeed:       req.UrgentSpeed,
		PaidTime:          &payTime,
		ContractNo:        req.ContractNo,
		SubmitID:          req.SubmitId,
		ContractURL:       req.ContractUrl,
		OrderID:           req.OrderId,
		ServiceName:       req.ServiceName,
		BrandName:         req.BrandName,
		BusinessName:      req.BusinessName,
		Num:               req.Num,
		GoodsSpecsName:    req.GoodsSpecsName,
		UpdatedBy:         req.SubmitId,
		GoodsName:         req.GoodsName,
		GoodsID:           req.GoodsId,
		UserType:          userType,
		Remark:            req.Remark,
		HandleBy:          req.SubmitId,
	})
	if err != nil {
		return nil, err
	}
	_, accountList, err := models.AccountAll(ctx, &models.FinancialAccountListRequest{
		PageNum:  1,
		PageSize: 10000,
	})
	type accountInfo struct {
		name        string
		accountType int32
	}
	accountMap := make(map[int64]accountInfo)
	for _, emp := range accountList {
		accountMap[emp.ID] = accountInfo{
			name:        emp.Name,
			accountType: emp.AccountType,
		}
	}
	accountName := ""
	var accountType int32
	relationRecords := make([]*model.FinancialPaid, 0)
	for _, record := range req.FinancialPaidInfo {
		if info, ok := accountMap[record.PaymentAccountId]; ok {
			accountName = info.name
			accountType = info.accountType
		}
		relationRecords = append(relationRecords, &model.FinancialPaid{
			FinancialFundID:  fundId,
			PaymentAccountID: record.PaymentAccountId,
			AmountOther:      record.AmountOther,
			ImagesPath:       record.ImagesPath,
			Currency:         record.Currency,
			ExchangeRate:     record.ExchangeRate,
			AmountCny:        record.AmountCny,
			AccountName:      accountName,
			PaidType:         accountType,
		})
	}
	if q := (&models.FinancialPaidDao{}).BatchCreate(ctx, relationRecords); q != nil {
		return nil, q
	}
	return &order.FinancialFundCreateRsp{
		Id: fundId,
	}, nil
}

// FundList 收款订单列表
func FundList(ctx context.Context, req *order.FinancialFundListReq) (*order.FinancialFundListRsp, error) {
	paymentAccountTypes := utils.ConvertSliceToInt32(req.PaymentAccountTypes)
	// 1. 获取基础数据
	total, fundList, err := models.FundList(ctx, &models.FundListRequest{
		ApproveStatus:       req.ApproveStatus,
		ApproveStatusList:   req.ApproveStatusList,
		FundNo:              req.FundNo,
		OrderNo:             req.OrderNo,
		CustomerId:          req.CustomerId,
		CreatedAtStart:      req.CreatedAtStart,
		CreatedAtEnd:        req.CreatedAtEnd,
		FundType:            req.FundType,
		ContractNo:          req.ContractNo,
		ServiceName:         req.ServiceName,
		GoodsName:           req.GoodsName,
		BrandName:           req.BrandName,
		BusinessName:        req.BusinessName,
		PayType:             int32(req.PayType),
		Currency:            req.Currency,
		SubmitId:            req.SubmitId,
		PaymentAccountId:    req.PaymentAccountId,
		PaymentAccountIds:   req.PaymentAccountIds,
		PassTimeStart:       req.PassTimeStart,
		PassTimeEnd:         req.PassTimeEnd,
		RejectTimeStart:     req.RejectTimeStart,
		RejectTimeEnd:       req.RejectTimeEnd,
		OrderBy:             req.OrderBy,
		OrderId:             req.OrderId,
		PageNum:             req.PageNum,
		PageSize:            req.PageSize,
		TransactionNo:       req.TransactionNo,
		UserType:            req.UserType,
		StaffIds:            req.StaffIds,
		CustomerName:        req.CustomerName,
		PaidTimeStart:       req.PaidTimeStart,
		PaidTimeEnd:         req.PaidTimeEnd,
		UserSource:          req.UserSource,
		UserSourceDepart:    req.UserSourceDepart,
		OrderSource:         req.OrderSource,
		OrderSourceDepart:   req.OrderSourceDepart,
		OrderStatus:         req.OrderStatus,
		SubmitSourceDepart:  req.SubmitSourceDepart,
		PaymentAccountTypes: paymentAccountTypes,
	})
	if err != nil {
		return nil, err
	}

	// 2. 准备返回数据结构
	returnList := make([]*order.FinancialFundInfo, len(fundList))
	fundIds := make([]int64, len(fundList))
	orderIds := make([]int64, 0)
	customerIds := make([]int64, 0)

	// 3. 转换基础数据
	for i, fund := range fundList {
		returnList[i] = convertFundToInfo(fund)
		fundIds = append(fundIds, fund.ID)
		orderIds = append(orderIds, fund.OrderID)
		customerIds = append(customerIds, fund.CustomerID)
	}

	// 4. 获取并处理支付信息
	if err := appendPaidInfo(ctx, fundIds, returnList, fundList); err != nil {
		return nil, err
	}

	// 5. 获取并处理商品信息
	if err := appendGoodsInfo(ctx, orderIds, returnList, fundList); err != nil {
		return nil, err
	}

	// 6. 获取并处理订单来源信息
	if err := appendOrderSourceInfo(ctx, orderIds, returnList); err != nil {
		return nil, err
	}

	// 7. 获取并处理共同提交人信息
	if err := appendSubmitSourceInfo(ctx, orderIds, returnList, fundList); err != nil {
		return nil, err
	}

	// 8. 获取并处理客户来源信息
	if err := appendCustomerSourceInfo(ctx, customerIds, returnList); err != nil {
		return nil, err
	}

	// 9. 获取并处理应收款信息
	if err := appendReceivableInfo(ctx, orderIds, returnList, fundList); err != nil {
		return nil, err
	}

	return &order.FinancialFundListRsp{
		FinancialFundList: returnList,
		Total:             total,
	}, nil
}

// convertFundToInfo 转换基础数据
func convertFundToInfo(fund *models.FundListMod) *order.FinancialFundInfo {
	return &order.FinancialFundInfo{
		Id:                fund.ID,
		OrderNo:           fund.OrderNo,
		FundNo:            fund.FundNo,
		RealAmountOther:   fund.RealAmountOther,
		RealAmountRmb:     fund.RealAmountRmb,
		Currency:          fund.Currency,
		ShouldAmountOther: fund.ShouldAmountOther,
		ShouldAmountRmb:   fund.ShouldAmountRmb,
		SubmitId:          fund.SubmitID,
		CreatedAt:         fund.CreatedAt.UnixMilli(),
		CustomerId:        fund.CustomerID,
		GoodsName:         fund.GoodsName,
		ServiceName:       splitString(fund.ServiceName),
		BrandName:         fund.BrandName,
		BusinessName:      splitString(fund.BusinessName),
		GoodsNum:          fund.Num,
		GoodsSpec:         fund.GoodsSpecsName,
		PassTime:          utils.TimePtrToMilli(fund.PassTime),
		RejectTime:        utils.TimePtrToMilli(fund.RejectTime),
		ContractNo:        fund.ContractNo,
		PayType:           fund.PayType,
		FundType:          fund.FundType,
		ApproveStatus:     fund.ApproveStatus,
		OrderId:           fund.OrderID,
		ExchangeRate:      fund.ExchangeRate,
		Discount:          fund.Discount,
		DiscountRate:      fund.DiscountRate,
		UrgentSpeed:       fund.UrgentSpeed,
		ApproveBy:         fund.ApproveBy,
		ContractUrl:       fund.ContractURL,
		PaidTime:          fund.PaidTime.UnixMilli(),
		ApproveComment:    fund.ApproveComment,
		WorkflowId:        fund.WorkflowID,
		WorkflowNo:        fund.WorkflowNo,
		UserType:          fund.UserType,
		OrderStatus:       fund.Status,
		Remark:            fund.Remark,
	}
}

// appendPaidInfo 添加支付信息
func appendPaidInfo(ctx context.Context, fundIds []int64, returnList []*order.FinancialFundInfo, fundList []*models.FundListMod) error {
	financialPaid, err := (&models.FinancialPaidDao{}).BatchGet(ctx, fundIds)
	if err != nil {
		return err
	}

	financialPaidMap := make(map[int64][]*model.FinancialPaid)
	for _, item := range financialPaid {
		financialPaidMap[item.FinancialFundID] = append(financialPaidMap[item.FinancialFundID], item)
	}

	for i, fund := range fundList {
		paidList := make([]*order.FinancialPaidInfo, 0, len(financialPaidMap[fund.ID]))
		for _, paid := range financialPaidMap[fund.ID] {
			paidList = append(paidList, &order.FinancialPaidInfo{
				PaymentAccountId: paid.PaymentAccountID,
				Currency:         paid.Currency,
				PaidType:         paid.PaidType,
				AccountName:      paid.AccountName,
				AmountCny:        paid.AmountCny,
				AmountOther:      paid.AmountOther,
				ImagesPath:       paid.ImagesPath,
				Id:               paid.ID,
				TransactionNo:    splitString(paid.TransactionNo),
				ExchangeRate:     paid.ExchangeRate,
			})
		}
		returnList[i].FinancialPaiInfo = paidList
	}
	return nil
}

// appendGoodsInfo 添加商品信息
func appendGoodsInfo(ctx context.Context, orderIds []int64, returnList []*order.FinancialFundInfo, fundList []*models.FundListMod) error {
	orderGoodsMods, err := (&models.OrderGoods{}).GetAllOrderGoods(ctx, orderIds)
	if err != nil {
		return err
	}

	orderGoodsModMap := make(map[int64][]*model.OrderGood)
	for _, orderGoodsMod := range orderGoodsMods {
		if orderGoodsModMap[orderGoodsMod.OrderID] == nil {
			orderGoodsModMap[orderGoodsMod.OrderID] = make([]*model.OrderGood, 0)
		}
		orderGoodsModMap[orderGoodsMod.OrderID] = append(orderGoodsModMap[orderGoodsMod.OrderID], orderGoodsMod)
	}

	for i, fund := range fundList {
		serviceName := make([]string, 0)
		businessName := make([]string, 0)
		for _, goods := range orderGoodsModMap[fund.OrderID] {
			serviceName = append(serviceName, goods.ServiceName)
			businessName = append(businessName, goods.BusinessName)
		}
		returnList[i].ServiceName = serviceName
		returnList[i].BusinessName = businessName
	}
	return nil
}

// appendOrderSourceInfo 添加订单来源信息
func appendOrderSourceInfo(ctx context.Context, orderIds []int64, returnList []*order.FinancialFundInfo) error {
	orderRelationMods, err := (&models.Order{}).GetOrderRelationByOrderIds(ctx, orderIds, 7)
	if err != nil {
		return err
	}

	orderRelationMap := make(map[int64][]*model.OrderRelation)
	for _, orderRelationMod := range orderRelationMods {
		orderRelationMap[orderRelationMod.OrderID] = append(orderRelationMap[orderRelationMod.OrderID], orderRelationMod)
	}

	for i, fund := range returnList {
		orderSource := make([]int64, 0)
		for _, orderRelation := range orderRelationMap[fund.OrderId] {
			orderSource = append(orderSource, orderRelation.UserID)
		}
		returnList[i].OrderSource = orderSource
	}
	return nil
}

// appendSubmitSourceInfo 添加共同提交人信息
func appendSubmitSourceInfo(ctx context.Context, orderIds []int64, returnList []*order.FinancialFundInfo, fundList []*models.FundListMod) error {
	orderRelationSubmitMods, err := (&models.Order{}).GetOrderRelationByOrderIds(ctx, orderIds, 6)
	if err != nil {
		return err
	}

	orderRelationSubmitMap := make(map[int64][]*model.OrderRelation)
	for _, orderRelationSubmitMod := range orderRelationSubmitMods {
		if orderRelationSubmitMap[orderRelationSubmitMod.OrderID] == nil {
			orderRelationSubmitMap[orderRelationSubmitMod.OrderID] = make([]*model.OrderRelation, 0)
		}
		orderRelationSubmitMap[orderRelationSubmitMod.OrderID] = append(orderRelationSubmitMap[orderRelationSubmitMod.OrderID], orderRelationSubmitMod)
	}

	for i, fund := range fundList {
		submitSource := make([]int64, 0)
		for _, orderRelation := range orderRelationSubmitMap[fund.OrderID] {
			submitSource = append(submitSource, orderRelation.UserID)
		}
		returnList[i].SubmitSource = submitSource
	}
	return nil
}

// appendCustomerSourceInfo 添加客户来源信息
func appendCustomerSourceInfo(ctx context.Context, customerIds []int64, returnList []*order.FinancialFundInfo) error {
	customerRelationMods, err := (&models.FinancialPaidDao{}).GetCustomerReferralEmployeeIdsByCustomerIds(ctx, customerIds)
	if err != nil {
		return err
	}

	customerRelationMap := make(map[int64][]*model.CustomerReferralRelationship)
	for _, customerRelationMod := range customerRelationMods {
		if customerRelationMap[customerRelationMod.CustomerID] == nil {
			customerRelationMap[customerRelationMod.CustomerID] = make([]*model.CustomerReferralRelationship, 0)
		}
		customerRelationMap[customerRelationMod.CustomerID] = append(customerRelationMap[customerRelationMod.CustomerID], customerRelationMod)
	}

	for i, fund := range returnList {
		customerSource := make([]int64, 0)
		for _, customerRelation := range customerRelationMap[fund.CustomerId] {
			customerSource = append(customerSource, customerRelation.AdminUserID)
		}
		returnList[i].UserSource = customerSource
	}
	return nil
}

// appendReceivableInfo 添加应收款信息
func appendReceivableInfo(ctx context.Context, orderIds []int64, returnList []*order.FinancialFundInfo, fundList []*models.FundListMod) error {
	orderPayMods, err := (&models.FinancialPaidDao{}).GetOrderPayByOrderIds(ctx, orderIds)
	if err != nil {
		return err
	}

	orderPayMap := make(map[int64]*model.OrderPay)
	for _, orderPayMod := range orderPayMods {
		orderPayMap[orderPayMod.OrderID] = orderPayMod
	}

	for i, fund := range fundList {
		if fund.FundType == 1 {
			returnList[i].ShouldAmountRmb = fund.RealAmountRmb
		}
		if fund.FundType == 2 {
			if v, ok := orderPayMap[fund.OrderID]; ok {
				returnList[i].ShouldAmountRmb = v.AmountFirstReceivable
			}
		}
		if fund.FundType == 3 {
			if v, ok := orderPayMap[fund.OrderID]; ok {
				returnList[i].ShouldAmountRmb = v.AmountFinalReceivable
			}
			returnList[i].SubmitSource = []int64{} // 尾款不显示共同提交人
		}
		if fund.FundType == 4 {
			returnList[i].SubmitSource = []int64{} // 第三方收款订单不显示共同提交人
		}
	}
	return nil
}

// FundInfo 收款单详情
func FundInfo(ctx context.Context, req *order.FinancialFundInfoReq) (*order.FinancialFundInfoRsp, error) {
	fund, err := models.FundInfo(ctx, &models.FundInfoRequest{
		Id:         req.Id,
		FundNo:     req.FundNo,
		OrderID:    req.OrderId,
		OrderNo:    req.OrderNo,
		FundType:   req.FundType,
		WorkflowId: req.WorkflowId,
		CustomerId: req.CustomerId,
	})
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return &order.FinancialFundInfoRsp{}, nil
	}
	if err != nil {
		return nil, err
	}

	// 计算 ShouldAmountRmb
	var shouldAmountRmb string
	if fund.FundType == 1 {
		shouldAmountRmb = fund.RealAmountRmb
	} else {
		// 获取订单支付信息
		orderPayMods, err := (&models.FinancialPaidDao{}).GetOrderPayByOrderIds(ctx, []int64{fund.OrderID})
		if err != nil {
			return nil, err
		}

		if len(orderPayMods) > 0 {
			orderPayMod := orderPayMods[0]
			switch fund.FundType {
			case 2:
				shouldAmountRmb = orderPayMod.AmountFirstReceivable
			case 3:
				shouldAmountRmb = orderPayMod.AmountFinalReceivable
			}
		}
	}

	// 获取支付记录
	fundPaidList, err := models.PaidList(ctx, fund.ID)
	if err != nil {
		return nil, err
	}
	returnList := make([]*order.FinancialPaidInfo, len(fundPaidList))
	for i, item := range fundPaidList {
		returnList[i] = &order.FinancialPaidInfo{
			FinancialFundId:  item.FinancialFundID,
			AccountName:      item.AccountName,
			PaymentAccountId: item.PaymentAccountID,
			Currency:         item.Currency,
			PaidType:         item.PaidType,
			ExchangeRate:     item.ExchangeRate,
			AmountCny:        item.AmountCny,
			AmountOther:      item.AmountOther,
			ImagesPath:       item.ImagesPath,
			Id:               item.ID,
			TransactionNo:    splitString(item.TransactionNo),
		}
	}
	orderRelationSubmitMods, _ := (&models.Order{}).GetOrderRelationByOrderIds(ctx, []int64{fund.OrderID}, 6)
	orderRelationSubmitMap := make(map[int64][]*model.OrderRelation)
	for _, orderRelationSubmitMod := range orderRelationSubmitMods {
		orderRelationSubmitMap[orderRelationSubmitMod.OrderID] = append(orderRelationSubmitMap[orderRelationSubmitMod.OrderID], orderRelationSubmitMod)
	}
	otherSubmitIds := make([]int64, 0)
	for _, orderRelation := range orderRelationSubmitMap[fund.OrderID] {
		otherSubmitIds = append(otherSubmitIds, orderRelation.UserID)
	}
	orderRelationSourceMods, _ := (&models.Order{}).GetOrderRelationByOrderIds(ctx, []int64{fund.OrderID}, 7)
	orderRelationSourceMap := make(map[int64][]*model.OrderRelation)
	for _, orderRelationSourceMod := range orderRelationSourceMods {
		orderRelationSourceMap[orderRelationSourceMod.OrderID] = append(orderRelationSourceMap[orderRelationSourceMod.OrderID], orderRelationSourceMod)
	}
	orderSourceIds := make([]int64, 0)
	for _, orderRelation := range orderRelationSourceMap[fund.OrderID] {
		orderSourceIds = append(orderSourceIds, orderRelation.UserID)
	}

	return &order.FinancialFundInfoRsp{
		Id:                fund.ID,
		OrderId:           fund.OrderID,
		CustomerId:        fund.CustomerID,
		OrderNo:           fund.OrderNo,
		FundNo:            fund.FundNo,
		RealAmountOther:   fund.RealAmountOther,
		Currency:          fund.Currency,
		ExchangeRate:      fund.ExchangeRate,
		ShouldAmountOther: fund.ShouldAmountOther,
		RealAmountRmb:     fund.RealAmountRmb,
		CreatedAt:         fund.CreatedAt.UnixMilli(),
		FundType:          fund.FundType,
		PayType:           fund.PayType,
		ContractNo:        fund.ContractNo,
		ApproveStatus:     fund.ApproveStatus,
		Discount:          fund.Discount,
		DiscountRate:      fund.DiscountRate,
		UrgentSpeed:       fund.UrgentSpeed,
		PaidTime:          fund.PaidTime.UnixMilli(),
		ContractUrl:       fund.ContractURL,
		FinancialPaiInfo:  returnList,
		SubmitId:          fund.SubmitID,
		ApproveBy:         fund.ApproveBy,
		ApproveComment:    fund.ApproveComment,
		UserType:          order.FinancialCustomerType(fund.UserType),
		Remark:            fund.Remark,
		ShouldAmountRmb:   shouldAmountRmb,
		OtherSubmitIds:    otherSubmitIds,
		OrderSourceIds:    orderSourceIds,
	}, nil
}

// FundStatusUpdate 收款订单状态修改
func FundStatusUpdate(ctx context.Context, req *order.FinancialFundStatusUpdateReq) (*order.FinancialFundStatusUpdateRsp, error) {
	updateMod := &model.FinancialFund{
		ID:            req.FinancialFundId,
		ApproveStatus: req.Status,
		ApproveBy:     req.UpdatedBy,
		UpdatedBy:     req.UpdatedBy,
	}

	if req.ApproveComment != "" {
		updateMod.ApproveComment = req.ApproveComment
	}

	currentTime := time.Now()
	if req.Status == 2 {
		updateMod.PassTime = &currentTime
		fundInfo, _ := models.FundInfo(ctx, &models.FundInfoRequest{
			Id: req.FinancialFundId,
		})
		// 只有定金/首期款/尾款 才需要更新, 之前是新用户才有必要更新
		if fundInfo.UserType == 1 && (fundInfo.FundType == 1 || fundInfo.FundType == 2 || fundInfo.FundType == 3) {
			// 创建的时候就已经是审批通过了, 所以这里需要排除掉当前的收款单
			old, _ := models.FundInfoForUserType(ctx,
				fundInfo.CustomerID,
				[]int64{fundInfo.ID},
			)
			if old != nil {
				updateMod.UserType = 2
			}
			//1.0 历史数据
			old2, err := models.FundInfoForCustomerUserType(ctx, fundInfo.CustomerID)
			if err == nil && old2 != nil {
				updateMod.UserType = 2
			}
		}
	}
	if req.Status == 3 {
		updateMod.RejectTime = &currentTime
	}
	fund, err := models.FundStatusUpdate(ctx, updateMod)
	if err != nil {
		return nil, err
	}
	return &order.FinancialFundStatusUpdateRsp{
		FinancialFundId: fund.ID,
		Status:          fund.ApproveStatus,
	}, nil
}

// FundPaidList 收款支付记录
func FundPaidList(ctx context.Context, financialFundId, orderId int64, fundType int32) (*order.FinancialPaidListRsp, error) {
	if financialFundId == 0 {
		fundInfo, err := models.FundInfo(ctx, &models.FundInfoRequest{
			OrderID:  orderId,
			FundType: fundType,
		})
		if err != nil {
			return nil, err
		}
		if fundInfo.ID == 0 {
			return nil, err
		}
		financialFundId = fundInfo.ID
	}
	fundPaidList, err := models.PaidList(ctx, financialFundId)
	if err != nil {
		return nil, err
	}
	returnList := make([]*order.FinancialPaidInfo, len(fundPaidList))
	for i, item := range fundPaidList {
		returnList[i] = &order.FinancialPaidInfo{
			FinancialFundId:  item.FinancialFundID,
			AccountName:      item.AccountName,
			PaymentAccountId: item.PaymentAccountID,
			Currency:         item.Currency,
			PaidType:         item.PaidType,
			ExchangeRate:     item.ExchangeRate,
			AmountCny:        item.AmountCny,
			AmountOther:      item.AmountOther,
			ImagesPath:       item.ImagesPath,
			Id:               item.ID,
			TransactionNo:    splitString(item.TransactionNo),
		}
	}
	return &order.FinancialPaidListRsp{
		PaidList: returnList,
	}, nil
}

// FundDraftCreate 保存收款草稿
func FundDraftCreate(ctx context.Context, req *order.FinancialFundDraftReq) (*order.FinancialFundDraftRsp, error) {
	_, err := models.FundDraftCreate(ctx, &model.FinancialFundDraft{
		OrderID:      req.OrderId,
		FundType:     req.FundType,
		Content:      req.Content,
		ContractInfo: req.ContractInfo,
		CreatedBy:    req.CreatedBy,
		Remark:       req.Remark,
	})
	if err != nil {
		return nil, err
	}
	return &order.FinancialFundDraftRsp{}, nil
}

// GetFundDraft 获取收款草稿
func GetFundDraft(ctx context.Context, req *order.GetFinancialFundDraftReq) (*order.GetFinancialFundDraftRsp, error) {
	res, err := models.GetFundDraft(ctx, req.GetOrderId(), req.GetFundType())
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return &order.GetFinancialFundDraftRsp{}, nil
	}
	if err != nil {
		return nil, err
	}
	return &order.GetFinancialFundDraftRsp{
		OrderId:      res.OrderID,
		FundType:     res.FundType,
		Content:      res.Content,
		ContractInfo: res.ContractInfo,
		Remark:       res.Remark,
	}, nil
}

// FundUpdate 更新全部信息时使用此方法
func FundUpdate(ctx context.Context, req *order.FinancialFundUpdateReq) (*order.FinancialFundUpdateRsp, error) {
	payTime := time.UnixMilli(req.PaidTime)
	updateFields := make(map[string]interface{})

	// 只更新非零值字段
	if req.OrderNo != "" {
		updateFields["order_no"] = req.OrderNo
	}
	if req.FundNo != "" {
		updateFields["fund_no"] = req.FundNo
	}
	if req.CustomerId != 0 {
		updateFields["customer_id"] = req.CustomerId
	}
	if req.ApproveStatus != 0 {
		updateFields["approve_status"] = req.ApproveStatus
	}
	if req.PayType != 0 {
		updateFields["pay_type"] = req.PayType
	}
	if req.FundType != 0 {
		updateFields["fund_type"] = req.FundType
	}
	if req.Currency != "" {
		updateFields["currency"] = req.Currency
	}
	if req.ExchangeRate != "" {
		updateFields["exchange_rate"] = req.ExchangeRate
	}
	if req.RealAmountOther != "" {
		updateFields["real_amount_other"] = req.RealAmountOther
	}
	if req.RealAmountRmb != "" {
		updateFields["real_amount_rmb"] = req.RealAmountRmb
	}
	if req.Discount != 0 {
		updateFields["discount"] = req.Discount
	}
	if req.DiscountRate != "" {
		updateFields["discount_rate"] = req.DiscountRate
	}
	if req.ShouldAmountOther != "" {
		updateFields["should_amount_other"] = req.ShouldAmountOther
	}
	if req.ShouldAmountRmb != "" {
		updateFields["should_amount_rmb"] = req.ShouldAmountRmb
	}
	if req.ContractNo != "" {
		updateFields["contract_no"] = req.ContractNo
	}
	if req.UpdatedBy != 0 {
		updateFields["updated_by"] = req.UpdatedBy
	}
	if req.UrgentSpeed != "" {
		updateFields["urgent_speed"] = req.UrgentSpeed
	}
	if req.PaidTime != 0 {
		updateFields["paid_time"] = payTime
	}
	if req.BusinessName != "" {
		updateFields["business_name"] = req.BusinessName
	}
	if req.GoodsName != "" {
		updateFields["goods_name"] = req.GoodsName
	}
	if req.BrandName != "" {
		updateFields["brand_name"] = req.BrandName
	}
	if req.ServiceName != "" {
		updateFields["service_name"] = req.ServiceName
	}
	if req.GoodsSpecsName != "" {
		updateFields["goods_specs_name"] = req.GoodsSpecsName
	}
	if req.Num != "" {
		updateFields["num"] = req.Num
	}
	if req.GoodsId != 0 {
		updateFields["goods_id"] = req.GoodsId
	}
	if req.UserType != 0 {
		updateFields["user_type"] = req.UserType
	}
	contractUrl := "[]"
	if len(req.ContractUrl) > 0 {
		contractUrl = req.ContractUrl
	}
	updateFields["contract_url"] = contractUrl
	updateFields["remark"] = req.Remark

	err := models.FundUpdateFields(ctx, req.FinancialFundId, updateFields)
	if err != nil {
		return nil, err
	}

	// 2. 更新支付信息表
	// 先删除原有的支付记录
	if err := (&models.FinancialPaidDao{}).DeleteByFundID(ctx, req.FinancialFundId); err != nil {
		return nil, err
	}

	// 添加新的支付记录
	_, accountList, err := models.AccountAll(ctx, &models.FinancialAccountListRequest{
		PageNum:  1,
		PageSize: 10000,
	})
	type accountInfo struct {
		name        string
		accountType int32
	}
	accountMap := make(map[int64]accountInfo)
	for _, emp := range accountList {
		accountMap[emp.ID] = accountInfo{
			name:        emp.Name,
			accountType: emp.AccountType,
		}
	}
	accountName := ""
	var accountType int32
	relationRecords := make([]*model.FinancialPaid, 0)
	for _, record := range req.FinancialPaidInfo {
		if info, ok := accountMap[record.PaymentAccountId]; ok {
			accountName = info.name
			accountType = info.accountType
		}
		relationRecords = append(relationRecords, &model.FinancialPaid{
			FinancialFundID:  req.FinancialFundId,
			PaymentAccountID: record.PaymentAccountId,
			Currency:         record.Currency,
			ExchangeRate:     record.ExchangeRate,
			AmountCny:        record.AmountCny,
			AmountOther:      record.AmountOther,
			ImagesPath:       record.ImagesPath,
			AccountName:      accountName,
			PaidType:         accountType,
		})
	}

	if err := (&models.FinancialPaidDao{}).BatchCreate(ctx, relationRecords); err != nil {
		return nil, err
	}

	return &order.FinancialFundUpdateRsp{}, nil
}

// FundDel 财务订单删除
func FundDel(ctx context.Context, req *order.FinancialFundDelReq) (*order.FinancialFundDelRsp, error) {
	var id int64
	if req.FinancialFundId != 0 {
		id = req.FinancialFundId
	} else {
		fund, _ := models.FundInfo(ctx, &models.FundInfoRequest{
			OrderID:  req.OrderId,
			FundType: req.FundType,
		})
		id = fund.ID
	}
	// 1. 删除支付记录
	if err := (&models.FinancialPaidDao{}).DeleteByFundID(ctx, id); err != nil {
		return nil, err
	}
	// 2. 删除主表记录
	if err := models.FundDelete(ctx, id); err != nil {
		return nil, err
	}
	return &order.FinancialFundDelRsp{}, nil
}

// ThirdFundCreate 第三方申请费
func ThirdFundCreate(ctx context.Context, req *order.ThirdFundCreateReq) (*order.ThirdFundCreateRsp, error) {
	//不存在则新增
	fundNo := generateOrderNo()
	payTime := time.UnixMilli(req.PaidTime)
	fundId, err := models.FundCreate(ctx, &model.FinancialFund{
		FundNo:          fundNo,
		CustomerID:      req.CustomerId,
		FundType:        4,
		Currency:        req.Currency,
		RealAmountOther: req.RealAmountOther,
		RealAmountRmb:   req.RealAmountRmb,
		PaidTime:        &payTime,
		SubmitID:        req.SubmitId,
		ServiceName:     req.ServiceName,
		BrandName:       req.BrandName,
		BusinessName:    req.BusinessName,
		Num:             req.Num,
		GoodsSpecsName:  req.GoodsSpecsName,
		GoodsName:       req.GoodsName,
		GoodsID:         req.GoodsId,
		WorkflowID:      req.WorkflowId,
		WorkflowName:    req.WorkflowName,
		WorkflowNo:      req.WorkflowNo,
		ContractURL:     "[]",
		OrderNo:         req.OrderNo,
		OrderID:         req.OrderId,
		UserType:        3,
		HandleBy:        req.SubmitId,
		Remark:          req.Remark,
	})
	if err != nil {
		return nil, err
	}
	relationRecords := make([]*model.FinancialPaid, 0)
	// 添加新的支付记录
	_, accountList, err := models.AccountAll(ctx, &models.FinancialAccountListRequest{
		PageNum:  1,
		PageSize: 10000,
	})
	type accountInfo struct {
		name        string
		accountType int32
	}
	accountMap := make(map[int64]accountInfo)
	for _, emp := range accountList {
		accountMap[emp.ID] = accountInfo{
			name:        emp.Name,
			accountType: emp.AccountType,
		}
	}
	accountName := ""
	var accountType int32
	for _, record := range req.FinancialPaidInfo {
		if info, ok := accountMap[record.PaymentAccountId]; ok {
			accountName = info.name
			accountType = info.accountType
		}
		relationRecords = append(relationRecords, &model.FinancialPaid{
			FinancialFundID:  fundId,
			PaymentAccountID: record.PaymentAccountId,
			AmountOther:      record.AmountOther,
			ImagesPath:       record.ImagesPath,
			Currency:         record.Currency,
			ExchangeRate:     record.ExchangeRate,
			AmountCny:        record.AmountCny,
			AccountName:      accountName,
			PaidType:         accountType,
		})
	}
	if q := (&models.FinancialPaidDao{}).BatchCreate(ctx, relationRecords); q != nil {
		return nil, q
	}
	return &order.ThirdFundCreateRsp{
		Id: fundId,
	}, nil
}

// GetRelationExchangeRate 获取收款汇率
func GetRelationExchangeRate(ctx context.Context, req *order.RelationExchangeRateReq) (*order.RelationExchangeRateRsp, error) {
	exchangeRate, err := models.GetRelationExchangeRate(ctx, &models.FundExchangeRateRequest{
		OrderID:    req.OrderId,
		OrderNo:    req.OrderNo,
		Currency:   req.Currency,
		WorkflowID: req.WorkflowId,
	})
	if err != nil {
		return nil, err
	}
	return &order.RelationExchangeRateRsp{
		ExchangeRate: exchangeRate,
	}, nil
}

// GetWorkflowFund 获取工单关联收款信息
func GetWorkflowFund(ctx context.Context, req *order.WorkflowFundReq) (*order.WorkflowFundRsp, error) {
	total, fundList, err := models.GetWorkflowFund(ctx, &models.WorkflowFundRequest{
		WorkflowNo:    req.WorkflowNo,
		WorkflowName:  req.WorkflowName,
		CustomerId:    req.CustomerId,
		PageSize:      req.PageSize,
		PageNum:       req.PageNum,
		WorkflowId:    req.WorkflowId,
		ApproveStatus: req.ApproveStatus,
	})
	if err != nil {
		return nil, err
	}
	returnList := make([]*order.WorkflowFund, len(fundList))
	for i, fund := range fundList {
		returnList[i] = &order.WorkflowFund{
			Id:            fund.ID,
			FundNo:        fund.FundNo,
			WorkflowNo:    fund.WorkflowNo,
			WorkflowName:  fund.WorkflowName,
			Currency:      fund.Currency,
			RealAmountRmb: fund.RealAmountRmb,
			PaidTime:      fund.PaidTime.UnixMilli(),
		}
	}

	return &order.WorkflowFundRsp{
		WorkflowFund: returnList,
		Total:        total,
	}, nil
}

// GetThirdFundByWorkflow  获取工单关联第三方申请费收款信息
func GetThirdFundByWorkflow(ctx context.Context, req *order.ThirdFundByWorkflowReq) (*order.ThirdFundByWorkflowRsp, error) {
	fund, err := models.GetThirdFundByWorkflow(ctx, &models.WorkflowFundRequest{
		WorkflowNo: req.WorkflowNo,
		WorkflowId: req.WorkflowId,
	})
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return &order.ThirdFundByWorkflowRsp{}, nil
	}
	if err != nil {
		return nil, err
	}
	fundPaidList, err := models.PaidList(ctx, fund.ID)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return &order.ThirdFundByWorkflowRsp{}, nil
	}
	thirdAmountList := make([]*order.AmountInfo, 0, 0)
	m := map[string]string{}
	tmp1 := decimal.Decimal{}
	tmp2 := decimal.Decimal{}
	tmp3 := decimal.Decimal{}
	for _, item := range fundPaidList {
		val, ok := m[item.Currency]
		if ok {
			tmp1, _ = decimal.NewFromString(val)
			tmp2, _ = decimal.NewFromString(item.AmountOther)
			tmp3 = tmp1.Add(tmp2)
			m[item.Currency] = tmp3.String()
		} else {
			m[item.Currency] = item.AmountOther
		}
	}
	for k, v := range m {
		thirdAmountList = append(thirdAmountList, &order.AmountInfo{
			Currency: k,
			Amount:   v,
		})
	}
	return &order.ThirdFundByWorkflowRsp{
		Id:              fund.ID,
		WorkflowId:      fund.WorkflowID,
		WorkflowNo:      fund.WorkflowNo,
		WorkflowName:    fund.WorkflowName,
		Currency:        fund.Currency,
		RealAmountOther: fund.RealAmountOther,
		RealAmountRmb:   fund.RealAmountRmb,
		ApproveStatus:   fund.ApproveStatus,
		PaidTime:        fund.PaidTime.UnixMilli(),
		FundNo:          fund.FundNo,
		ThirdAmountList: thirdAmountList,
	}, nil
}

// UpdateFinancialFundEmployee 批量更新财务跟进员工
func UpdateFinancialFundEmployee(ctx context.Context, req *order.UpdateFinancialFundEmployeeReq) (*order.UpdateFinancialFundEmployeeRsp, error) {
	resp := &order.UpdateFinancialFundEmployeeRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}
	if len(req.GetCustomerIds()) == 0 || req.GetBefore() <= 0 || req.GetAfter() <= 0 {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}
	if err := models.UpdateFinancialFundEmployee(ctx, req.Before, req.After, req.CustomerIds); err != nil {
		return &order.UpdateFinancialFundEmployeeRsp{}, err
	}
	return resp, nil
}

/**
 * EditApprovedFinancialFund 编辑已审核通过的收款信息
 * 支持部分字段的重新编辑功能
 */
func EditApprovedFinancialFund(ctx context.Context, req *order.FinancialFundApprovedEditReq) (*order.FinancialFundApprovedEditRsp, error) {
	// 1. 详细参数验证
	if req.FinancialFundId <= 0 {
		logger.CtxErrorf(ctx, "EditApprovedFinancialFund invalid financial_fund_id: %d", req.FinancialFundId)
		return &order.FinancialFundApprovedEditRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_FinancialFundNotFound),
		}, nil
	}
	if req.UpdatedBy <= 0 {
		logger.CtxErrorf(ctx, "EditApprovedFinancialFund invalid updated_by: %d", req.UpdatedBy)
		return &order.FinancialFundApprovedEditRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_FinancialFundInvalidOperator),
		}, nil
	}

	// 验证是否有可编辑的字段
	hasEditableFields := req.PaidTime > 0 || req.UserType > 0 || req.ContractInfo != "" ||
		len(req.OrderSourceIds) >= 0 || len(req.OtherSubmitIds) >= 0
	if !hasEditableFields {
		logger.CtxErrorf(ctx, "EditApprovedFinancialFund no editable fields provided for fund_id: %d", req.FinancialFundId)
		return &order.FinancialFundApprovedEditRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_FinancialFundNoEditableFields),
		}, nil
	}

	// 验证客户类型有效性
	if req.UserType > 0 && (req.UserType < 1 || req.UserType > 3) {
		logger.CtxErrorf(ctx, "EditApprovedFinancialFund invalid user_type: %d for fund_id: %d", req.UserType, req.FinancialFundId)
		return &order.FinancialFundApprovedEditRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_FinancialFundInvalidUserType),
		}, nil
	}

	// 2. 获取收款单信息，验证状态
	fundInfo, err := models.FundInfo(ctx, &models.FundInfoRequest{
		Id: req.FinancialFundId,
	})
	if errors.Is(err, gorm.ErrRecordNotFound) {
		logger.CtxErrorf(ctx, "EditApprovedFinancialFund fund not found for id: %d", req.FinancialFundId)
		return &order.FinancialFundApprovedEditRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_FinancialFundNotFound),
		}, nil
	}
	if err != nil {
		logger.CtxErrorf(ctx, "EditApprovedFinancialFund get fund info error: %v for id: %d", err, req.FinancialFundId)
		return nil, err
	}

	// 3. 验证收款单状态是否为已审核通过
	if fundInfo.ApproveStatus != 2 {
		logger.CtxErrorf(ctx, "EditApprovedFinancialFund invalid approve status: %d for fund_id: %d, expected: 2(已审核通过)",
			fundInfo.ApproveStatus, req.FinancialFundId)
		return &order.FinancialFundApprovedEditRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_FinancialFundStatusNotAllowEdit),
		}, nil
	}

	// 4. 构建更新字段
	updateFields := make(map[string]interface{})
	updateFields["updated_by"] = req.UpdatedBy

	// 实际付款日期
	if req.PaidTime > 0 {
		payTime := time.UnixMilli(req.PaidTime)
		updateFields["paid_time"] = payTime
		oldPaidTime := fundInfo.PaidTime.UnixMilli()
		logger.CtxInfof(ctx, "EditApprovedFinancialFund updating paid_time from %d to %d for fund_id: %d",
			oldPaidTime, req.PaidTime, req.FinancialFundId)
	}

	// 客户类型
	if req.UserType > 0 {
		updateFields["user_type"] = req.UserType
		logger.CtxInfof(ctx, "EditApprovedFinancialFund updating user_type from %d to %d for fund_id: %d",
			fundInfo.UserType, req.UserType, req.FinancialFundId)
	}

	// 合同文件信息
	if req.ContractInfo != "" {
		updateFields["contract_url"] = req.ContractInfo
		logger.CtxInfof(ctx, "EditApprovedFinancialFund updating contract_info for fund_id: %d, new length: %d",
			req.FinancialFundId, len(req.ContractInfo))
	}

	// 5. 更新收款单主表
	if len(updateFields) > 1 { // 除了 updated_by 之外还有其他字段需要更新
		logger.CtxInfof(ctx, "EditApprovedFinancialFund updating fund fields: %+v for fund_id: %d",
			updateFields, req.FinancialFundId)
		err = models.FundUpdateFields(ctx, req.FinancialFundId, updateFields)
		if err != nil {
			logger.CtxErrorf(ctx, "EditApprovedFinancialFund update fund fields error: %v for fund_id: %d",
				err, req.FinancialFundId)
			return nil, err
		}
	}

	// 6. 开启事务处理订单关系更新
	tx := global.DB.WithContext(ctx).Begin()
	defer func() {
		if err != nil {
			logger.CtxErrorf(ctx, "EditApprovedFinancialFund transaction rollback for fund_id: %d, error: %v",
				req.FinancialFundId, err)
			tx.Rollback()
		} else {
			logger.CtxInfof(ctx, "EditApprovedFinancialFund transaction commit success for fund_id: %d",
				req.FinancialFundId)
			tx.Commit()
		}
	}()

	// 7. 更新订单来源关系
	if len(req.OrderSourceIds) >= 0 { // 允许为空数组（清空关系）
		logger.CtxInfof(ctx, "EditApprovedFinancialFund updating order_source_ids: %v for order_id: %d, fund_id: %d",
			req.OrderSourceIds, fundInfo.OrderID, req.FinancialFundId)
		_, err = models.UpdateOrderRelation(ctx, tx, "EditApprovedFinancialFund", fundInfo.OrderID, 7, req.OrderSourceIds)
		if err != nil {
			logger.CtxErrorf(ctx, "EditApprovedFinancialFund update order source relation error: %v for order_id: %d",
				err, fundInfo.OrderID)
			return nil, err
		}
	}
	ops := &models.RelationOperationSet{}
	// 8. 更新共同提交人关系
	if len(req.OtherSubmitIds) >= 0 { // 允许为空数组（清空关系）
		logger.CtxInfof(ctx, "EditApprovedFinancialFund updating other_submit_ids: %v for order_id: %d, fund_id: %d",
			req.OtherSubmitIds, fundInfo.OrderID, req.FinancialFundId)
		ops, err = models.UpdateOrderRelation(ctx, tx, "EditApprovedFinancialFund", fundInfo.OrderID, 6, req.OtherSubmitIds)
		if err != nil {
			logger.CtxErrorf(ctx, "EditApprovedFinancialFund update other submit relation error: %v for order_id: %d",
				err, fundInfo.OrderID)
			return nil, err
		}
	}

	// 9. 记录操作日志（如果有编辑原因）
	if req.EditReason != "" {
		logger.CtxInfof(ctx, "EditApprovedFinancialFund edit reason: %s for fund_id: %d by user: %d",
			req.EditReason, req.FinancialFundId, req.UpdatedBy)
	}

	logger.CtxInfof(ctx, "EditApprovedFinancialFund completed successfully for fund_id: %d by user: %d",
		req.FinancialFundId, req.UpdatedBy)

	return &order.FinancialFundApprovedEditRsp{
		FinancialFundId: req.FinancialFundId,
		UpdatedAt:       time.Now().UnixMilli(),
		ToDelete:        ops.ToDelete,
		ToAdd:           ops.ToAdd,
		CustomerId:      fundInfo.CustomerID,
		Base:            coderror.MakeSuccessBaseRsp(),
	}, nil
}
