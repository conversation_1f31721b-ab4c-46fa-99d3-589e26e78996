package services

import (
	"context"

	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/i18n"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/order/internal/models"
)

// BatchUpdateOrderOwnId implements the ServiceImpl interface.
func (o *OrderService) BatchUpdateOrderOwnId(ctx context.Context, req *order.BatchUpdateOrderOwnIdReq) (resp *order.BatchUpdateOrderOwnIdRsp, err error) {
	funcName := getFuncName(1)

	resp = &order.BatchUpdateOrderOwnIdRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	if len(req.GetCustomerIds()) == 0 || req.GetBefore() <= 0 || req.GetAfter() <= 0 {
		logger.CtxErrorf(ctx, "%s invalid params err: %+v, customer_ids: %+v, before: %+v, after: %+v", funcName, i18n.InvalidParams, req.GetCustomerIds(), req.GetBefore(), req.GetAfter())
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return
	}

	err = (&models.OrderTransaction{}).BatchUpdateOrderOwnId(ctx, req.GetCustomerIds(), req.GetBefore(), req.GetAfter())
	if err != nil {
		logger.CtxErrorf(ctx, "%s db err: %+v, customer_ids: %+v, before: %+v, after: %+v", funcName, err, req.GetCustomerIds(), req.GetBefore(), req.GetAfter())
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return
	}

	// 资产转移
	logger.CtxInfof(ctx, "%s success before: %+v, after: %+v, customer_ids: %+v", funcName, req.GetBefore(), req.GetAfter(), req.GetCustomerIds())

	return
}
