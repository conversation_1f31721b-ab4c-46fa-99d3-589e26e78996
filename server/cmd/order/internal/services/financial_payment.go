package services

import (
	"context"
	"errors"
	"gorm.io/gorm"
	"time"
	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/pkg/utils"
	"uofferv2/server/cmd/order/internal/models"
)

type FinancialPaymentService struct{}

// AddFinancialPayment 创建支付
func (f *FinancialPaymentService) AddFinancialPayment(ctx context.Context, req *order.FinancialPaymentCreateReq) (*order.FinancialPaymentCreateRsp, error) {
	resp := &order.FinancialPaymentCreateRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}
	paymentAccountIds := make([]int64, 0)
	for _, info := range req.PaymentAccountInfo {
		paymentAccountIds = append(paymentAccountIds, info.PaymentAccountId)
	}
	accountInfos, err := models.AccountListByIds(ctx, paymentAccountIds)
	if err != nil {
		return nil, err
	}

	accountInfoMap := make(map[int64]*model.FinancialAccount)
	for _, account := range accountInfos {
		if account.SupportLink == 1 {
			accountInfoMap[account.ID] = account
		}
	}
	outTradeNo := make([]string, 0)
	records := make([]*model.FinancialCustomerPayment, 0)
	for _, item := range req.PaymentAccountInfo {
		if accountInfo, exists := accountInfoMap[item.PaymentAccountId]; exists {
			tmpOutTradeNo := utils.GetUniqueNo()
			outTradeNo = append(outTradeNo, tmpOutTradeNo)
			var paymentMethod int32
			var brandId int64
			//1支付宝
			if accountInfo.AccountType == 1 {
				paymentMethod = 4
			}
			//2微信
			if accountInfo.AccountType == 2 {
				paymentMethod = 5
			}

			logger.CtxInfof(ctx, "accountInfo AccountType: %v, paymentMethod: %v", accountInfo.AccountType, paymentMethod)
			brandId = accountInfo.BrandID
			records = append(records, &model.FinancialCustomerPayment{
				CustomerID:           req.CustomerId,
				CustomerName:         req.CustomerName,
				OrderID:              req.OrderId,
				OrderNo:              req.OrderNo,
				OutTradeNo:           tmpOutTradeNo,
				GoodsName:            req.GoodsName,
				ServiceName:          req.ServiceName,
				BrandName:            req.BrandName,
				BusinessName:         req.BusinessName,
				Num:                  req.Num,
				Amount:               item.AmountCny,
				RecipientAccountID:   item.PaymentAccountId,
				PaymentMethod:        paymentMethod,
				OperatorID:           req.OperatorId,
				CreatedBy:            req.OperatorId,
				RecipientAccountName: item.AccountName,
				BrandID:              brandId,
			})
		}
	}

	if len(records) == 0 {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_AccountNotSupportLink)
		return resp, nil
	}

	err = (&models.FinancialPayment{}).PaymentCreate(ctx, records)
	if err != nil {
		return nil, err
	}
	return &order.FinancialPaymentCreateRsp{
		OutTradeNo: outTradeNo,
	}, nil
}

// UpdateFinancialPayment 更新支付
func (f *FinancialPaymentService) UpdateFinancialPayment(ctx context.Context, req *order.FinancialPaymentUpdateReq) (*order.FinancialPaymentUpdateRsp, error) {
	mod := &model.FinancialCustomerPayment{
		ID:            req.Id,
		PaymentStatus: req.PaymentStatus,
	}
	if req.PaymentTime != 0 {
		paymentTime := time.UnixMilli(req.PaymentTime)
		mod.PaymentTime = &paymentTime
	}
	if req.PaymentDeadline != 0 {
		paymentDeadline := time.UnixMilli(req.PaymentDeadline)
		mod.PaymentDeadline = &paymentDeadline
	}
	resp, err := (&models.FinancialPayment{}).PaymentUpdate(ctx, mod)
	if err != nil {
		return nil, err
	}
	return &order.FinancialPaymentUpdateRsp{
		Id: resp.ID,
	}, nil
}

// FinancialPaymentInfo  获取支付信息
func (f *FinancialPaymentService) FinancialPaymentInfo(ctx context.Context, req *order.FinancialPaymentInfoReq) (*order.FinancialPaymentInfoRsp, error) {
	mod, err := (&models.FinancialPayment{}).PaymentInfo(ctx, req.OutTradeNo)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return &order.FinancialPaymentInfoRsp{}, nil
	}
	if err != nil {
		return nil, err
	}
	return &order.FinancialPaymentInfoRsp{
		Id:                   mod.ID,
		CustomerId:           mod.CustomerID,
		CustomerName:         mod.CustomerName,
		OrderNo:              mod.OrderNo,
		Amount:               mod.Amount,
		OutTradeNo:           mod.OutTradeNo,
		OrderId:              mod.OrderID,
		ServiceName:          mod.ServiceName,
		GoodsName:            mod.GoodsName,
		BrandName:            mod.BrandName,
		BusinessName:         mod.BusinessName,
		RecipientAccountId:   mod.RecipientAccountID,
		RecipientAccountName: mod.RecipientAccountName,
		PaymentMethod:        mod.PaymentMethod,
		Num:                  mod.Num,
		BrandId:              mod.BrandID,
		PaymentStatus:        mod.PaymentStatus,
		PaymentTime:          utils.TimePtrToMilli(mod.PaymentTime),
		PaymentDeadline:      utils.TimePtrToMilli(mod.PaymentDeadline),
	}, nil
}
