package services

import (
	"context"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/pkg/dao/model"
	"uofferv2/server/cmd/order/internal/models"
)

type FinancialRefundApproveService struct{}

// RefundApproveCreate 新增支款订单审核记录
func (f *FinancialRefundApproveService) RefundApproveCreate(ctx context.Context, req *order.FinancialRefundApproveCreateReq) (*order.FinancialRefundApproveCreateRsp, error) {
	RefundApproveId, err := (&models.FinancialRefundApproveLog{}).RefundApproveCreate(ctx, &model.FinancialRefundApproveLog{
		FinancialRefundID: req.FinancialRefundId,
		OrderID:           req.OrderId,
		ApproveBy:         req.ApproveBy,
		ApproveComment:    req.ApproveComment,
		Status:            req.Status,//审批状态 1通过,2驳回,3回退,4完成打款
	})
	if err != nil {
		return nil, err
	}
	return &order.FinancialRefundApproveCreateRsp{
		Id: RefundApproveId,
	}, nil
}

// RefundApproveInfo 收款单审核详情
func (f *FinancialRefundApproveService) RefundApproveInfo(ctx context.Context, req *order.FinancialRefundApproveInfoReq) (*order.FinancialRefundApproveInfo, error) {
	r, err := (&models.FinancialRefundApproveLog{}).RefundApproveInfo(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &order.FinancialRefundApproveInfo{
		Id:                 r.ID,
		Financial_RefundId: r.FinancialRefundID,
		OrderNo:            r.OrderNo,
		ApproveBy:          r.ApproveBy,
		ApproveComment:     r.ApproveComment,
		Status:             r.Status,
	}, nil
}

// RefundApproveList zhi款单审核列表
func (f *FinancialRefundApproveService) RefundApproveList(ctx context.Context, req *order.FinancialRefundApproveListReq) (*order.FinancialRefundApproveListRsp, error) {
	total, RefundApproveList, err := (&models.FinancialRefundApproveLog{}).RefundApproveList(ctx, &models.FinancialRefundApproveListRequest{
		FinancialRefundId: req.FinancialRefundId,
		PageSize:          req.PageSize,
		PageNum:           req.PageNum,
	})
	if err != nil {
		return nil, err
	}
	returnList := make([]*order.FinancialRefundApproveInfo, len(RefundApproveList))
	for i, r := range RefundApproveList {
		returnList[i] = &order.FinancialRefundApproveInfo{
			Id:                 r.ID,
			Financial_RefundId: r.FinancialRefundID,
			OrderNo:            r.OrderNo,
			ApproveBy:          r.ApproveBy,
			ApproveComment:     r.ApproveComment,
			Status:             r.Status,
			CreatedAt:          r.CreatedAt.UnixMilli(),
		}
	}
	return &order.FinancialRefundApproveListRsp{
		FinancialRefundApproveInfo: returnList,
		Total:                       total,
	}, nil
}
