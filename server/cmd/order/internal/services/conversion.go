package services

import (
	"time"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/pkg/dao/model"
	"uofferv2/server/cmd/order/internal/models"
)

func pointerTimeToInt64(t *time.Time) int64 {
	if t == nil {
		return 0
	}

	return t.UnixMilli()
}

func cloneTime(t *time.Time) time.Time {
	if t == nil {
		return time.UnixMilli(0)
	}
	return time.UnixMilli(t.UnixMilli())
}

func getOrderBy(pb []*order.OrderBy) []*models.OrderBy {
	orderBySli := make([]*models.OrderBy, 0)

	if len(pb) > 0 {
		for _, item := range pb {
			orderByItem := &models.OrderBy{}
			orderByItem.Field = models.GetOrderByField(item.GetField())
			orderByItem.Type = models.OrderByDesc
			if item.GetType() == order.OrderByType_ORDER_BY_ASC {
				orderByItem.Type = models.OrderByAsc
			}
			orderBySli = append(orderBySli, orderByItem)
		}
	}

	return orderBySli
}

func GetOrderListReq(pb *order.GetOrderListReq) *models.GetOrderListReq {
	if pb == nil {
		return nil
	}

	disbursementTypes := make([]models.DisbursementType, len(pb.GetDisbursementTypes()))
	for i, v := range pb.GetDisbursementTypes() {
		disbursementTypes[i] = models.DisbursementType(v.Number())
	}

	statuses := make([]models.StatusOrder, len(pb.GetStatuses()))
	for i, v := range pb.GetStatuses() {
		statuses[i] = models.StatusOrder(v.Number())
	}

	statusReviews := make([]models.StatusReview, len(pb.GetStatusReviews()))
	for i, v := range pb.GetStatusReviews() {
		statusReviews[i] = models.StatusReview(v.Number())
	}

	statusOrderWorkflow := make([]models.StatusOrderWorkflow, len(pb.GetStatusWorkflows()))
	for i, v := range pb.GetStatusWorkflows() {
		statusOrderWorkflow[i] = models.StatusOrderWorkflow(v.Number())
	}

	statusPayDeposits := make([]models.StatusPay, len(pb.GetStatusPayDeposits()))
	for i, v := range pb.GetStatusPayDeposits() {
		statusPayDeposits[i] = models.StatusPay(v.Number())
	}

	statusPayFirsts := make([]models.StatusPay, len(pb.GetStatusPayFirsts()))
	for i, v := range pb.GetStatusPayFirsts() {
		statusPayFirsts[i] = models.StatusPay(v.Number())
	}

	statusPayFinals := make([]models.StatusPay, len(pb.GetStatusPayFinals()))
	for i, v := range pb.GetStatusPayFinals() {
		statusPayFinals[i] = models.StatusPay(v.Number())
	}

	statusPayDisbursements := make([]models.StatusPay, len(pb.GetStatusPayDisbursements()))
	for i, v := range pb.GetStatusPayDisbursements() {
		statusPayDisbursements[i] = models.StatusPay(v.Number())
	}

	return &models.GetOrderListReq{
		PageNum:                pb.GetPageNum(),
		PageSize:               pb.GetPageSize(),
		OrderNo:                pb.GetOrderNo(),
		ContractNo:             pb.GetContractNo(),
		CustomerIds:            pb.GetCustomerIds(),
		UpdaterIds:             pb.GetUpdaterIds(),
		ReviewerIds:            pb.GetReviewerIds(),
		ExecutorIds:            pb.GetExecutorIds(),
		ClosedIds:              pb.GetClosedIds(),
		BrandIds:               pb.GetBrandIds(),
		BusinessIds:            pb.GetBusinessIds(),
		ServiceIds:             pb.GetServiceIds(),
		InstallmentType:        models.InstallmentType(pb.GetInstallmentType()),
		DisbursementTypes:      disbursementTypes,
		Statuses:               statuses,
		StatusReviews:          statusReviews,
		StatusOrderWorkflow:    statusOrderWorkflow,
		StatusPayDeposits:      statusPayDeposits,
		StatusPayFirsts:        statusPayFirsts,
		StatusPayFinals:        statusPayFinals,
		StatusPayDisbursements: statusPayDisbursements,
		PayDepositAt:           pb.GetPayDepositAt(),
		PayFirstAt:             pb.GetPayFirstAt(),
		PayFinalAt:             pb.GetPayFinalAt(),
		PayDisbursementAt:      pb.GetPayDisbursementAt(),
		ApplyDepositAt:         pb.GetApplyDepositAt(),
		ApplyFirstAt:           pb.GetApplyFirstAt(),
		ApplyFinalAt:           pb.GetApplyFinalAt(),
		ApplyDisbursementAt:    pb.GetApplyDisbursementAt(),
		PassDepositAt:          pb.GetPassDepositAt(),
		PassFirstAt:            pb.GetPassFirstAt(),
		PassFinalAt:            pb.GetPassFinalAt(),
		PassDisbursementAt:     pb.GetPassDisbursementAt(),
		RejectAt:               pb.GetRejectAt(),
		CreatedAt:              pb.GetCreatedAt(),
		UpdatedAt:              pb.GetUpdatedAt(),
		ClosedAt:               pb.GetClosedAt(),
		BrandName:              pb.GetBrandName(),
		BusinessName:           pb.GetBusinessName(),
		ServiceName:            pb.GetServiceName(),
		GoodsName:              pb.GetGoodsName(),
		ForeignCurrency:        models.StatusYesNo(pb.GetForeignCurrency()),
		UrgentService:          models.StatusYesNo(pb.GetUrgentService()),
		ExemptFinal:            models.StatusYesNo(pb.GetExemptFinal()),
		HasScholarship:         models.StatusYesNo(pb.GetHasScholarship()),
		AutoSchedule:           models.StatusYesNo(pb.GetAutoSchedule()),
		OrderBy:                getOrderBy(pb.GetOrderBy()),
	}
}

func getOrderGoodsListReqToMod(pb *order.GetOrderGoodsListReq) *models.GetOrderGoodsListReq {
	if pb == nil {
		return nil
	}

	return &models.GetOrderGoodsListReq{
		PageNum:   pb.GetPageNum(),
		PageSize:  pb.GetPageSize(),
		OrderIds:  pb.GetOrderIds(),
		GoodsType: models.GoodsType(pb.GetGoodsType()),
		GoodsName: pb.GetGoodsName(),
		OrderBy:   getOrderBy(pb.GetOrderBy()),
	}
}

func getOrderOperationLogListReqToMod(pb *order.GetOrderOperationLogListReq) *models.GetOrderOperationLogListReq {
	if pb == nil {
		return nil
	}

	return &models.GetOrderOperationLogListReq{
		PageNum:  pb.GetPageNum(),
		PageSize: pb.GetPageSize(),
		OrderIds: pb.GetOrderIds(),
		OrderNos: pb.GetOrderNos(),
		OrderBy:  getOrderBy(pb.GetOrderBy()),
	}
}

func currencyModToPb(mod *model.Currency) *order.CurrencyEntity {
	if mod == nil {
		return nil
	}

	return &order.CurrencyEntity{
		Id:         mod.ID,
		Code:       mod.Code,
		Name:       mod.Name,
		Symbol:     mod.Symbol,
		Rate:       mod.Rate,
		RateRemark: mod.RateMark,
		Remark:     mod.Remark,
		Status:     order.StatusYesNo(mod.Status),
	}
}

func scholarshipModToPb(mod *models.ScholarshipToBeDistributed) *order.ScholarshipToBeDistributed {
	if mod == nil {
		return nil
	}

	return &order.ScholarshipToBeDistributed{
		CustomerId:          mod.CustomerId,
		OrderId:             mod.OrderId,
		OrderNo:             mod.OrderNo,
		BrandId:             mod.BrandId,
		BusinessId:          mod.BusinessId,
		ServiceId:           mod.ServiceId,
		BrandName:           mod.BrandName,
		BusinessName:        mod.BusinessName,
		ServiceName:         mod.ServiceName,
		AmountTotal:         mod.AmountTotal,
		AmountScholarship:   mod.AmountScholarship,
		CurrencyTotal:       mod.CurrencyTotal,
		CurrencyScholarship: mod.CurrencyScholarship,
	}
}

func updateUpdaterIdPbToMod(pb []*order.UpdateUpdaterIdItem) (mods []models.UpdateUpdaterId) {
	mods = make([]models.UpdateUpdaterId, 0, len(pb))
	if pb == nil {
		return
	}

	for _, item := range pb {
		mods = append(mods, models.UpdateUpdaterId{
			Before: item.GetBefore(),
			After:  item.GetAfter(),
		})
	}

	return
}

func ConvertRefundDeadline(timestamp int64) *time.Time {
	if timestamp <= 0 {
		return nil // 返回零值
	}

	// 项目中主要使用毫秒时间戳
	t := time.UnixMilli(timestamp)
	return &t
}
