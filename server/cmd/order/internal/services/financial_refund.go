package services

import (
	"context"
	"strings"
	"time"
	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/pkg/utils"
	"uofferv2/server/cmd/order/internal/models"
)

// RefundCreate 新增支款订单
func RefundCreate(ctx context.Context, req *order.FinancialRefundCreateReq) (*order.FinancialRefundCreateRsp, error) {
	resp := &order.FinancialRefundCreateRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}
	refundNo := generateOrderNo()
	refundID, err := models.RefundCreate(ctx, &model.FinancialRefund{
		OrderNo:                  req.OrderNo,
		OrderID:                  req.OrderId,
		FundNo:                   req.FundNo,
		RefundNo:                 refundNo,
		CustomerID:               req.CustomerId,
		Currency:                 req.Currency,
		ExchangeRate:             req.ExchangeRate,
		RealAmountOther:          req.RealAmountOther,
		RealAmountRmb:            req.RealAmountRmb,
		RefundType:               req.RefundType,
		SubmitID:                 req.SubmitId,
		UpdatedBy:                req.SubmitId,
		RefundReceiveAccountType: req.RefundReceiveAccountType,
		RefundReceiveAccount:     req.RefundReceiveAccount,
		RefundReason:             req.RefundReason,
		RefundAgreement:          req.RefundAgreement,
		ScholarshipAgreement:     req.ScholarshipAgreement,
		Visa:                     req.Visa,
		StudentCard:              req.StudentCard,
		TuitionPaymentProof:      req.TuitionPaymentProof,
		ApproveLog:               req.ApproveLog,
		WorkflowName:             req.WorkflowName,
		WorkflowID:               req.WorkflowId,
		WorkflowNo:               req.WorkflowNo,
		BusinessName:             req.BusinessName,
		BrandName:                req.BrandName,
		ServiceName:              req.ServiceName,
		GoodsName:                req.GoodsName,
		GoodsSpecsName:           req.GoodsSpecsName,
		Num:                      req.Num,
		GoodsID:                  req.GoodsId,
		ContractAmount:           req.ContractAmount,
		University:               req.University,
		Major:                    req.Major,
		Level:                    req.Level,
		EnterTime:                req.EnterTime,
		UserType:                 req.UserType,
		HandleBy:                 req.SubmitId,
		RefundDeadline:           utils.MilliToTimePtr(req.RefundDeadline),
	})
	if err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	resp.Id = refundID
	return resp, nil
}

// RefundList 支款订单列表
func RefundList(ctx context.Context, req *order.FinancialRefundListReq) (*order.FinancialRefundListRsp, error) {
	// 转换 PaymentAccountType 到 int32
	accountTypes := utils.ConvertSliceToInt32(req.PaymentAccountTypes)

	total, refundList, err := models.RefundList(ctx, &models.FinancialRefundListRequest{
		OrderNo:             req.OrderNo,
		RefundNo:            req.RefundNo,
		SubmitId:            req.SubmitId,
		CustomerId:          req.CustomerId,
		PageNum:             req.PageNum,
		PageSize:            req.PageSize,
		RefundType:          req.RefundType,
		OrderId:             req.OrderId,
		ApproveStatus:       req.ApproveStatus,
		ApproveStatusList:   req.ApproveStatusList,
		PaymentAccountTypes: accountTypes,
		TransactionNo:       req.TransactionNo,
		Currency:            req.Currency,
		PaymentAccountId:    req.PaymentAccountId,
		PaymentAccountIds:   req.PaymentAccountIds,
		PaymentAccountType:  int32(req.PaymentAccountType),
		CreatedAtStart:      req.CreatedAtStart,
		CreatedAtEnd:        req.CreatedAtEnd,
		CompleteTimeStart:   req.CompleteTimeStart,
		CompleteTimeEnd:     req.CompleteTimeEnd,
		RejectTimeStart:     req.RejectTimeStart,
		RejectTimeEnd:       req.RejectTimeEnd,
		ServiceName:         req.ServiceName,
		BrandName:           req.BrandName,
		BusinessName:        req.BusinessName,
		WorkflowId:          req.WorkflowId,
		WorkflowNo:          req.WorkflowNo,
		UserType:            req.UserType,
		StaffIds:            req.StaffIds,
		OrderBy:             req.OrderBy,
		PassTimeEnd:         req.PassTimeEnd,
		PassTimeStart:       req.PassTimeStart,
		UserSource:          req.UserSource,
		UserSourceDepart:    req.UserSourceDepart,
		OrderSource:         req.OrderSource,
		OrderSourceDepart:   req.OrderSourceDepart,
		CustomerName:        req.CustomerName,
		SubmitDepart:        req.SubmitDepart,
		ApproveBy:           req.ApproveBy,
		RefundDeadlineStart: req.RefundDeadlineStart,
		RefundDeadlineEnd:   req.RefundDeadlineEnd,
	})
	if err != nil {
		return nil, err
	}
	returnList := make([]*order.FinancialRefundInfo, len(refundList))
	orderIds := make([]int64, 0)
	customerIds := make([]int64, 0)
	accountMap, err := models.AccountMapAll(ctx)
	if err != nil {
		return nil, err
	}
	for i, refund := range refundList {
		accountType := refund.RefundReceiveAccountType
		// 安全访问方式
		if account, exists := accountMap[refund.PaymentAccountID]; exists && account != nil {
			accountType = account.AccountType
			// 使用 accountType
		}
		returnList[i] = &order.FinancialRefundInfo{
			Id:                       refund.ID,
			ApproveStatus:            refund.ApproveStatus,
			OrderNo:                  refund.OrderNo,
			RefundNo:                 refund.RefundNo,
			RealAmountOther:          refund.RealAmountOther,
			RealAmountRmb:            refund.RealAmountRmb,
			Currency:                 refund.Currency,
			SubmitId:                 refund.SubmitID,
			CreatedAt:                refund.CreatedAt.UnixMilli(),
			CustomerId:               refund.CustomerID,
			ShouldAmountOther:        refund.ShouldAmountOther,
			RefundType:               refund.RefundType,
			ServiceName:              splitString(refund.ServiceName),
			BrandName:                refund.BrandName,
			BusinessName:             splitString(refund.BusinessName),
			AccountType:              accountType, // 支款账户类型
			GoodsName:                refund.GoodsName,
			GoodsSpecsName:           refund.GoodsSpecsName,
			GoodsNum:                 refund.Num,
			PassTime:                 utils.TimePtrToMilli(refund.PassTime),
			RejectTime:               utils.TimePtrToMilli(refund.RejectTime),
			CompleteTime:             utils.TimePtrToMilli(refund.CompleteTime),
			TransactionNo:            refund.TransactionNo,
			PaymentAccountId:         refund.PaymentAccountID,
			FundNo:                   refund.FundNo,
			ContractAmount:           refund.ContractAmount,
			AccountName:              refund.AccountName,
			OrderId:                  refund.OrderID,
			RefundReceiveAccount:     refund.RefundReceiveAccount,
			RefundReceiveAccountType: refund.RefundReceiveAccountType,
			RefundAgreement:          refund.RefundAgreement,
			ApproveLog:               refund.ApproveLog,
			ScholarshipAgreement:     refund.ScholarshipAgreement,
			Visa:                     refund.Visa,
			StudentCard:              refund.StudentCard,
			TuitionPaymentProof:      refund.TuitionPaymentProof,
			WorkflowName:             refund.WorkflowName,
			ApproveComment:           refund.ApproveComment,
			ApproveBy:                refund.ApproveBy,
			University:               refund.University,
			Major:                    refund.Major,
			Level:                    refund.Level,
			EnterTime:                refund.EnterTime,
			ExchangeRate:             refund.ExchangeRate,
			WorkflowNo:               refund.WorkflowNo,
			WorkflowId:               refund.WorkflowID,
			RefundReason:             refund.RefundReason,
			UserType:                 refund.UserType,
			RefundDeadline:           utils.TimePtrToMilli(refund.RefundDeadline),
		}
		orderIds = append(orderIds, refund.OrderID)
		customerIds = append(customerIds, refund.CustomerID)
	}
	//服务项目和业务名称
	orderGoodsMods, err := (&models.OrderGoods{}).GetAllOrderGoods(ctx, orderIds)
	if err != nil {
		return nil, err
	}
	orderGoodsModMap := make(map[int64][]*model.OrderGood)
	for _, orderGoodsMod := range orderGoodsMods {
		if orderGoodsModMap[orderGoodsMod.OrderID] == nil || len(orderGoodsModMap[orderGoodsMod.OrderID]) == 0 {
			orderGoodsModMap[orderGoodsMod.OrderID] = make([]*model.OrderGood, 0)
		}
		orderGoodsModMap[orderGoodsMod.OrderID] = append(orderGoodsModMap[orderGoodsMod.OrderID], orderGoodsMod)
	}
	for i, fund := range returnList {
		serviceName := make([]string, 0, 0)
		businessName := make([]string, 0, 0)
		for _, goods := range orderGoodsModMap[fund.OrderId] {
			serviceName = append(serviceName, goods.ServiceName)
			businessName = append(businessName, goods.BusinessName)
		}
		returnList[i].ServiceName = serviceName
		returnList[i].BusinessName = businessName
	}

	//订单来源
	orderRelationMods, err := (&models.Order{}).GetOrderRelationByOrderIds(ctx, orderIds, 7)
	if err != nil {
		return nil, err
	}
	orderRelationMap := make(map[int64][]*model.OrderRelation)
	for _, orderRelationMod := range orderRelationMods {
		orderRelationMap[orderRelationMod.OrderID] = append(orderRelationMap[orderRelationMod.OrderID], orderRelationMod)
	}
	for i, fund := range returnList {
		orderSource := make([]int64, 0, 0)
		for _, orderRelation := range orderRelationMap[fund.OrderId] {
			orderSource = append(orderSource, orderRelation.UserID)
		}
		returnList[i].OrderSource = orderSource
	}

	orderPayMods, err := (&models.FinancialPaidDao{}).GetOrderPayByOrderIds(ctx, orderIds)
	if err != nil {
		return nil, err
	}

	orderPayMap := make(map[int64]*model.OrderPay)
	for _, orderPayMod := range orderPayMods {
		orderPayMap[orderPayMod.OrderID] = orderPayMod
	}
	for i, fund := range returnList {
		if v, ok := orderPayMap[fund.OrderId]; ok {
			returnList[i].PaidAmount = v.AmountTotal
		}
	}

	//共同提交人
	orderRelationSubmitMods, err := (&models.Order{}).GetOrderRelationByOrderIds(ctx, orderIds, 6)
	if err != nil {
		return nil, err
	}
	orderRelationSubmitMap := make(map[int64][]*model.OrderRelation)
	for _, orderRelationSubmitMod := range orderRelationSubmitMods {
		if orderRelationSubmitMap[orderRelationSubmitMod.OrderID] == nil || len(orderRelationSubmitMap[orderRelationSubmitMod.OrderID]) == 0 {
			orderRelationSubmitMap[orderRelationSubmitMod.OrderID] = make([]*model.OrderRelation, 0)
		}
		orderRelationSubmitMap[orderRelationSubmitMod.OrderID] = append(orderRelationSubmitMap[orderRelationSubmitMod.OrderID], orderRelationSubmitMod)
	}
	for i, fund := range returnList {
		submitSource := make([]int64, 0, 0)
		for _, orderRelation := range orderRelationSubmitMap[fund.OrderId] {
			submitSource = append(submitSource, orderRelation.UserID)
		}
		returnList[i].SubmitSource = submitSource
	}

	//客户来源
	customerRelationMods, err := (&models.FinancialPaidDao{}).GetCustomerReferralEmployeeIdsByCustomerIds(ctx, customerIds)
	if err != nil {
		return nil, err
	}
	customerRelationMap := make(map[int64][]*model.CustomerReferralRelationship)
	for _, customerRelationMod := range customerRelationMods {
		if customerRelationMap[customerRelationMod.CustomerID] == nil || len(customerRelationMap[customerRelationMod.CustomerID]) == 0 {
			customerRelationMap[customerRelationMod.CustomerID] = make([]*model.CustomerReferralRelationship, 0)
		}
		customerRelationMap[customerRelationMod.CustomerID] = append(customerRelationMap[customerRelationMod.CustomerID], customerRelationMod)
	}
	for i, fund := range returnList {
		customerSource := make([]int64, 0, 0)
		for _, customerRelation := range customerRelationMap[fund.CustomerId] {
			customerSource = append(customerSource, customerRelation.AdminUserID)
		}
		returnList[i].UserSource = customerSource
	}

	//交易单号和账号信息
	financialPaidMods, err := (&models.FinancialPaidDao{}).GetAllByOrderIds(ctx, orderIds)
	if err != nil {
		return nil, err
	}
	//logger.CtxInfof(ctx, "%v financialPaidMods ",financialPaidMods)
	financialPaidMap := make(map[int64][]*models.FinancialPaidMod)
	for _, financialPaidMod := range financialPaidMods {
		if financialPaidMap[financialPaidMod.OrderID] == nil || len(financialPaidMap[financialPaidMod.OrderID]) == 0 {
			financialPaidMap[financialPaidMod.OrderID] = make([]*models.FinancialPaidMod, 0)
		}
		financialPaidMap[financialPaidMod.OrderID] = append(financialPaidMap[financialPaidMod.OrderID], financialPaidMod)
	}
	logger.CtxInfof(ctx, "%v financialPaidMap ", financialPaidMap)
	typeMap := map[int32]string{1: "支付宝", 2: "微信", 3: "中国银行账户", 4: "英国银行账户", 5: "pos机", 6: "paypal", 7: "其他"}
	for i, fund := range returnList {
		transactionNo := make([]string, 0, 0)
		accountInfo := make([]*order.AccountInfo, 0, 0)
		for _, financialPaid := range financialPaidMap[fund.OrderId] {
			typeName := ""
			typeTpm, ok := typeMap[financialPaid.PaidType]
			if ok {
				typeName = typeTpm
			}
			if financialPaid.TransactionNo != "" {
				transactionNo = append(transactionNo, financialPaid.TransactionNo)
			}
			accountInfo = append(accountInfo, &order.AccountInfo{
				Type: typeName,
				Name: financialPaid.AccountName,
			})
		}
		returnList[i].OrderTransactionNo = transactionNo
		returnList[i].AccountInfo = accountInfo
	}

	return &order.FinancialRefundListRsp{
		FinancialRefundList: returnList,
		Total:               total,
	}, nil
}

// RefundInfo 支款订单详情
func RefundInfo(ctx context.Context, req *order.FinancialRefundInfoReq) (*order.FinancialRefundInfoRsp, error) {
	refund, err := models.RefundInfo(ctx, req)
	if err != nil {
		return nil, err
	}
	return &order.FinancialRefundInfoRsp{
		Id:                       refund.ID,
		OrderNo:                  refund.OrderNo,
		RefundNo:                 refund.RefundNo,        //支款单ID
		RealAmountOther:          refund.RealAmountOther, //支款总额
		RealAmountRmb:            refund.RealAmountRmb,
		Currency:                 refund.Currency,
		SubmitId:                 refund.SubmitID,
		CreatedAt:                refund.CreatedAt.UnixMilli(),
		RefundType:               refund.RefundType, //支款类型
		CustomerId:               refund.CustomerID,
		OrderId:                  refund.OrderID,
		RefundReceiveAccount:     refund.RefundReceiveAccount,               //支款接收账户
		RefundReceiveAccountType: refund.RefundReceiveAccountType,           //支款接收账户类型
		CompleteTime:             utils.TimePtrToMilli(refund.CompleteTime), //支款完成时间
		RefundReason:             refund.RefundReason,
		ApproveStatus:            refund.ApproveStatus,
		AccountName:              refund.AccountName,
		RefundAgreement:          refund.RefundAgreement,
		ApproveLog:               refund.ApproveLog,
		ExchangeRate:             refund.ExchangeRate,
		WorkflowNo:               refund.WorkflowNo,
		WorkflowName:             refund.WorkflowName,
		WorkflowId:               refund.WorkflowID,
		ApproveComment:           refund.ApproveComment,
		StudentCard:              refund.StudentCard,
		ScholarshipAgreement:     refund.ScholarshipAgreement,
		Visa:                     refund.Visa,
		TuitionPaymentProof:      refund.TuitionPaymentProof,
		UserType:                 refund.UserType,
		TransactionNo:            refund.TransactionNo,
		RefundDeadline:           utils.TimePtrToMilli(refund.RefundDeadline),
	}, nil
}

// RefundUpdate 更新支款单
func RefundUpdate(ctx context.Context, req *order.FinancialRefundUpdateReq) (*order.FinancialRefundUpdateRsp, error) {
	// 定义字段映射关系（结构体字段名 -> 数据库字段名）
	fieldMapping := map[string]string{
		"OrderNo":                  "order_no",
		"FundNo":                   "fund_no",
		"RefundNo":                 "refund_no",
		"CustomerId":               "customer_id",
		"Currency":                 "currency",
		"ExchangeRate":             "exchange_rate",
		"RealAmountOther":          "real_amount_other",
		"RealAmountRmb":            "real_amount_rmb",
		"RefundType":               "refund_type",
		"UpdatedBy":                "updated_by",
		"ApproveBy":                "approve_by",
		"RefundReceiveAccountType": "refund_receive_account_type",
		"RefundReceiveAccount":     "refund_receive_account",
		"RefundReason":             "refund_reason",
		"ApproveLog":               "approve_log",
		"RefundAgreement":          "refund_agreement",
		"ScholarshipAgreement":     "scholarship_agreement",
		"Visa":                     "visa",
		"StudentCard":              "student_card",
		"TuitionPaymentProof":      "tuition_payment_proof",
		"WorkflowNo":               "workflow_no",
		"WorkflowId":               "workflow_id",
		"WorkflowName":             "workflow_name",
		"University":               "university",
		"Major":                    "major",
		"Level":                    "level",
		"EnterTime":                "enter_time",
		"BrandName":                "brand_name",
		"ServiceName":              "service_name",
		"BusinessName":             "business_name",
		"GoodsName":                "goods_name",
		"Num":                      "num",
		"GoodsSpecsName":           "goods_specs_name",
		"ApproveStatus":            "approve_status",
		"AccountName":              "account_name",
		"PaymentAccountId":         "payment_account_id",
		"TransactionNo":            "transaction_no",
		"ApproveComment":           "approve_comment",
		"UserType":                 "user_type",
		"FinancialRefundId":        "", // 排除此字段
		"Status":                   "", // 排除此字段，需要特殊处理
		"RefundDeadline":           "refund_deadline",
	}

	// 使用工具函数构建更新字段，排除需要特殊处理的字段
	excludeFields := []string{"FinancialRefundId", "Status"}
	updateFields := utils.BuildUpdateFieldsWithExcludes(req, fieldMapping, excludeFields)

	// 特殊处理 Status 字段
	if req.Status != 0 {
		//1=待审批;2=待支款;3=支款完成;4=审核驳回)
		switch req.Status {
		case 1:
			updateFields["approve_status"] = 2 //1通过
			currentTime := time.Now()
			updateFields["pass_time"] = &currentTime //通过时间
		case 2:
			updateFields["approve_status"] = 4 //2驳回
			currentTime := time.Now()
			updateFields["reject_time"] = &currentTime //驳回时间
		case 3:
			updateFields["approve_status"] = 1 //3回退
		case 4:
			updateFields["approve_status"] = 3 //4完成打款
			currentTime := time.Now()
			updateFields["complete_time"] = &currentTime //完成打款时间
		}
	}

	// 处理 FinancialRefundId
	if req.FinancialRefundId == 0 {
		info, err := models.RefundInfoByOrder(ctx, &models.RefundInfoRequest{
			OrderId: req.OrderId,
		})
		req.FinancialRefundId = info.ID
		if err != nil {
			return nil, err
		}
	}

	err := models.RefundUpdateFields(ctx, req.FinancialRefundId, updateFields)
	if err != nil {
		return nil, err
	}
	return &order.FinancialRefundUpdateRsp{}, nil
}

// RefundDel 删除支款单
func RefundDel(ctx context.Context, req *order.FinancialRefundDelReq) (*order.FinancialRefundDelRsp, error) {
	err := models.RefundDel(ctx, req.FinancialRefundId)
	if err != nil {
		return nil, err
	}
	return &order.FinancialRefundDelRsp{}, nil
}

// RefundAssociateList 关联支款单列表
func RefundAssociateList(ctx context.Context, req *order.FinancialRefundAssociateReq) (*order.FinancialRefundAssociateRsp, error) {
	refundList, err := models.RefundAssociateList(ctx, &models.FinancialRefundListRequest{
		OrderId: req.OrderId,
		Id:      req.Id,
	})
	if err != nil {
		return nil, err
	}
	returnList := make([]*order.FinancialRefundAssociate, len(refundList))
	for i, refund := range refundList {
		returnList[i] = &order.FinancialRefundAssociate{
			Id:                       refund.ID,
			OrderNo:                  refund.OrderNo,
			RefundNo:                 refund.RefundNo,
			RealAmountOther:          refund.RealAmountOther,
			RealAmountRmb:            refund.RealAmountRmb,
			Currency:                 refund.Currency,
			SubmitId:                 refund.SubmitID,
			CreatedAt:                utils.TimePtrToMilli(&refund.CreatedAt),
			RefundType:               refund.RefundType,
			CustomerId:               refund.CustomerID,
			RefundReceiveAccountType: refund.RefundReceiveAccountType,
			RefundReceiveAccount:     refund.RefundReceiveAccount,
			CompleteTime:             utils.TimePtrToMilli(refund.CompleteTime),
			RefundReason:             refund.RefundReason,
			RefundAgreement:          refund.RefundAgreement,
			ApproveLog:               refund.ApproveLog,
			ExchangeRate:             refund.ExchangeRate,
		}
	}
	return &order.FinancialRefundAssociateRsp{
		RefundList: returnList,
	}, nil
}

// splitString 处理逗号分隔的字符串转数组
func splitString(s string) []string {
	if s == "" {
		return []string{}
	}
	return strings.Split(s, ",")
}

func RefundScholarshipAmount(ctx context.Context, req *order.FinancialRefundSumReq) (resp *order.FinancialRefundSumRsp, err error) {
	resp = &order.FinancialRefundSumRsp{}
	amountTotal, err := models.RefundScholarshipAmount(ctx, &models.RefundSumRequest{
		OrderNo:       req.OrderNo,
		OrderId:       req.OrderId,
		CustomerId:    req.CustomerId,
		ApproveStatus: req.ApproveStatus,
		RefundType:    req.RefundType,
	})
	if err != nil {
		return nil, err
	}
	resp.AmountTotal = amountTotal
	return resp, nil
}

// ScholarshipRefundCreate 奖学金支款
func ScholarshipRefundCreate(ctx context.Context, req *order.ScholarshipRefundCreateReq) (*order.ScholarshipRefundCreateRsp, error) {
	resp := &order.ScholarshipRefundCreateRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}
	refundNo := generateOrderNo()
	id, err := models.RefundCreate(ctx, &model.FinancialRefund{
		RefundNo:                 refundNo,
		CustomerID:               req.CustomerId,
		Currency:                 req.Currency,
		ExchangeRate:             req.ExchangeRate,
		RealAmountOther:          req.RealAmountOther,
		RealAmountRmb:            req.RealAmountRmb,
		RefundType:               3,
		SubmitID:                 req.SubmitId,
		RefundReceiveAccountType: req.RefundReceiveAccountType,
		RefundReceiveAccount:     req.RefundReceiveAccount,
		RefundReason:             req.RefundReason,
		RefundAgreement:          req.RefundAgreement,
		ScholarshipAgreement:     req.ScholarshipAgreement,
		Visa:                     req.Visa,
		StudentCard:              req.StudentCard,
		TuitionPaymentProof:      req.TuitionPaymentProof,
		WorkflowID:               req.WorkflowId,
		WorkflowName:             req.WorkflowName,
		ApproveLog:               req.ApproveLog,
	})
	if err != nil {
		resp.Id = id
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	return resp, nil
}

// ThirdRefundCreate 第三方申请费支款
func ThirdRefundCreate(ctx context.Context, req *order.ThirdRefundCreateReq) (*order.ThirdRefundCreateRsp, error) {
	resp := &order.ThirdRefundCreateRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}
	refundNo := generateOrderNo()
	id, err := models.RefundCreate(ctx, &model.FinancialRefund{
		RefundNo:                 refundNo,
		CustomerID:               req.CustomerId,
		Currency:                 req.Currency,
		ExchangeRate:             req.ExchangeRate,
		RealAmountOther:          req.RealAmountOther,
		RealAmountRmb:            req.RealAmountRmb,
		RefundType:               6,
		SubmitID:                 req.SubmitId,
		RefundReceiveAccountType: req.RefundReceiveAccountType,
		RefundReceiveAccount:     req.RefundReceiveAccount,
		RefundReason:             req.RefundReason,
		RefundAgreement:          req.RefundAgreement,
		WorkflowID:               req.WorkflowId,
		WorkflowName:             req.WorkflowName,
		ApproveLog:               req.ApproveLog,
		UserType:                 req.UserType,
		HandleBy:                 req.SubmitId,
		RefundDeadline:           utils.MilliToTimePtr(req.RefundDeadline),
	})
	if err != nil {
		resp.Id = id
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	return resp, nil
}

// GetThirdRefund  支款订单列表
func GetThirdRefund(ctx context.Context, req *order.ThirdRefundReq) (*order.ThirdRefundRsp, error) {
	total, refundList, err := models.RefundList(ctx, &models.FinancialRefundListRequest{
		CustomerId:    req.CustomerId,
		PageNum:       req.PageNum,
		PageSize:      req.PageSize,
		WorkflowId:    req.WorkflowId,
		WorkflowNo:    req.WorkflowNo,
		RefundType:    req.RefundType,
		ApproveStatus: req.ApproveStatus,
	})
	if err != nil {
		return nil, err
	}
	returnList := make([]*order.ThirdRefund, len(refundList))
	for i, refund := range refundList {
		returnList[i] = &order.ThirdRefund{
			Id:              refund.ID,
			RealAmountRmb:   refund.RealAmountRmb,
			Currency:        refund.Currency,
			CompleteTime:    refund.CompleteTime.UnixMilli(),
			WorkflowId:      refund.WorkflowID,
			RefundNo:        refund.RefundNo,
			RealAmountOther: refund.RealAmountOther,
			ExchangeRate:    refund.ExchangeRate,
			ApproveStatus:   refund.ApproveStatus,
			RefundDeadline:  utils.TimePtrToMilli(refund.RefundDeadline),
		}
	}
	return &order.ThirdRefundRsp{
		ThirdRefund: returnList,
		Total:       total,
	}, nil
}

// RefundListByOrder 订单关联支款单最新一条已经完成支款
func RefundListByOrder(ctx context.Context, req *order.RefundListByOrderReq) (*order.RefundListByOrderRsp, error) {
	refundList, err := models.RefundListByOrder(ctx, req.OrderIds)
	if err != nil {
		return nil, err
	}
	returnList := make([]*order.RefundListByOrderInfo, len(refundList))
	for i, refund := range refundList {
		returnList[i] = &order.RefundListByOrderInfo{
			Id:            refund.ID,
			RealAmountRmb: refund.RealAmountRmb,
			RefundType:    refund.RefundType,
			OrderId:       refund.OrderID,
		}
	}
	return &order.RefundListByOrderRsp{
		RefundList: returnList,
	}, nil
}
