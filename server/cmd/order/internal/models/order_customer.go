package models

import (
	"context"

	"uofferv2/pkg/dao/model"
	"uofferv2/server/cmd/order/internal/global"
)

// GetTaggedRefundHighRiskCustomers
// 定时任务：用户退款高危标签更新
//
//	@Description: 获取已经打上高风险标签的用户
//
// 退款高风险客户标签 customer_tags tag_id -1
//
// @receiver o
// @param ctx
func (o *Order) GetTaggedRefundHighRiskCustomers(ctx context.Context) (customerIds []int64, err error) {
	customerIds = make([]int64, 0)

	refundHighRiskTagId := "-1"

	err = global.DB.WithContext(ctx).Debug().
		Model(&model.CustomerTag{}).
		Select(model.CustomerTagColumns.CustomerID()).
		Where(model.CustomerTagColumns.TagID(), refundHighRiskTagId).
		Group(model.CustomerTagColumns.CustomerID()).
		Pluck(model.CustomerTagColumns.CustomerID(), &customerIds).
		Error
	if err != nil {
		return
	}

	return
}

// GetTaggedOldCustomers
// 定时任务：用户新/老客户标签更新
//
//	@Description: 获取已经打上老用户标签的用户
//
// 新/老 客户标签 customers.user_type 用户类型（1新客户，2老客户）
//
//	@receiver o
//	@param ctx
//	@return customerIds
//	@return err
func (o *Order) GetTaggedOldCustomers(ctx context.Context) (customerIds []int64, err error) {
	customerIds = make([]int64, 0)

	// user_type 用户类型（1新客户，2老客户）
	userType := 2

	err = global.DB.WithContext(ctx).Debug().
		Model(&model.Customer{}).
		Select(model.CustomerColumns.ID()).
		Where(model.CustomerColumns.UserType(), userType).
		Group(model.CustomerColumns.ID()).
		Pluck(model.CustomerColumns.ID(), &customerIds).
		Error
	if err != nil {
		return
	}

	return
}

// GetTaggedRedLineRiskCustomers
// 定时任务：用户退款红色横线标签更新
//
//	@Description: 获取已经打上退款红色横线标签的用户
//
// 退款红色横线标签  customers.refund_flag 退款标记1是2否
//
//	@receiver o
//	@param ctx
//	@return customerIds
//	@return err
func (o *Order) GetTaggedRedLineRiskCustomers(ctx context.Context) (customerIds []int64, err error) {
	customerIds = make([]int64, 0)

	// 退款红色横线标签 customers.refund_flag 退款标记1是2否
	refundFlag := 1

	err = global.DB.WithContext(ctx).Debug().
		Model(&model.Customer{}).
		Select(model.CustomerColumns.ID()).
		Where(model.CustomerColumns.RefundFlag(), refundFlag).
		Group(model.CustomerColumns.ID()).
		Pluck(model.CustomerColumns.ID(), &customerIds).
		Error
	if err != nil {
		return
	}

	return
}
