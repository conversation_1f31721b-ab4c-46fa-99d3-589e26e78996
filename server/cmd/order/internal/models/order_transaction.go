package models

import (
	"context"
	"time"

	"gorm.io/gorm"

	"uofferv2/pkg/dao/model"
	"uofferv2/server/cmd/order/internal/global"
)

type OrderTransaction struct{}

func (o *OrderTransaction) CreateOrder(ctx context.Context, newOrderMod *model.Order, newOrderPayMod *model.OrderPay, goodsMods []*model.OrderGood,
	operationLogMods []*model.OrderOperationLog, relationMods []*model.OrderRelation) (orderId int64, err error) {
	err = global.DB.WithContext(ctx).Debug().Transaction(func(tx *gorm.DB) error {
		// 订单
		err = global.DB.WithContext(ctx).Debug().
			Model(&model.Order{}).
			Create(&newOrderMod).
			Error
		if err != nil {
			return err
		}

		orderId = newOrderMod.ID
		orderNo := newOrderMod.OrderNo

		newOrderPayMod.OrderID = orderId
		// 订单付款
		err = global.DB.WithContext(ctx).Debug().
			Model(&model.OrderPay{}).
			Create(&newOrderPayMod).
			Error
		if err != nil {
			return err
		}

		if len(goodsMods) > 0 {
			for _, goodMod := range goodsMods {
				goodMod.OrderID = orderId
			}

			// 订单商品
			err = tx.Model(&model.OrderGood{}).
				CreateInBatches(&goodsMods, len(goodsMods)).
				Error
			if err != nil {
				return err
			}
		}

		if len(operationLogMods) > 0 {
			for _, operationLogMod := range operationLogMods {
				operationLogMod.OrderID = orderId
				operationLogMod.OrderNo = orderNo
			}

			// 订单操作日志
			err = tx.Model(&model.OrderOperationLog{}).
				CreateInBatches(&operationLogMods, len(operationLogMods)).
				Error
			if err != nil {
				return err
			}
		}

		if len(relationMods) > 0 {
			for _, relationMod := range relationMods {
				relationMod.OrderID = orderId
			}

			err = tx.Model(&model.OrderRelation{}).
				CreateInBatches(&relationMods, len(relationMods)).
				Error
			if err != nil {
				return err
			}
		}

		return nil
	})

	return
}

func (o *OrderTransaction) UpdateOrder(ctx context.Context, orderId int64, newOrderMod *model.Order, newOrderPayMod *model.OrderPay,
	goodsMods []*model.OrderGood, operationLogMods []*model.OrderOperationLog, relationMods []*model.OrderRelation, sourceIds, submissionIds []int64) (err error) {

	funcName := "UpdateOrder"

	err = global.DB.WithContext(ctx).Debug().Transaction(func(tx *gorm.DB) error {
		// 订单
		err = tx.Model(&model.Order{}).
			Where(model.OrderColumns.ID(), orderId).
			Updates(&newOrderMod).
			Error
		if err != nil {
			return err
		}

		// 订单付款
		err = tx.Model(&model.OrderPay{}).
			Where(model.OrderPayColumns.OrderID(), orderId).
			Updates(&newOrderPayMod).
			Error
		if err != nil {
			return err
		}

		// 订单商品
		err = tx.Model(&model.OrderGood{}).
			Where(model.OrderGoodColumns.OrderID(), orderId).
			Delete(&model.OrderGood{}).
			Error
		if err != nil {
			return err
		}

		if len(goodsMods) > 0 {
			// 订单商品
			err = tx.Model(&model.OrderGood{}).
				CreateInBatches(&goodsMods, len(goodsMods)).
				Error
			if err != nil {
				return err
			}
		}

		if len(operationLogMods) > 0 {
			// 订单日志
			err = tx.Model(&model.OrderOperationLog{}).
				CreateInBatches(&operationLogMods, len(operationLogMods)).
				Error
			if err != nil {
				return err
			}
		}

		// 共同提交
		_, err = UpdateOrderRelation(ctx, tx, funcName, orderId, int32(OrderRelationActionSubmission), submissionIds)
		if err != nil {
			return err
		}

		// 订单来源
		_, err = UpdateOrderRelation(ctx, tx, funcName, orderId, int32(OrderRelationActionSource), sourceIds)
		if err != nil {
			return err
		}

		// 编辑
		if len(relationMods) > 0 {
			for _, relationMod := range relationMods {
				relationMod.OrderID = orderId
			}

			err = tx.Model(&model.OrderRelation{}).
				CreateInBatches(&relationMods, len(relationMods)).
				Error
			if err != nil {
				return err
			}
		}

		return err
	})

	if err != nil {
		return err
	}

	return nil
}

func (o *OrderTransaction) UpdateOrderStatus(ctx context.Context, orderId int64, updatesMap map[string]any,
	operationLogMods []*model.OrderOperationLog, updatesOrderPay StatusYesNo, orderPayUpdatesMap map[string]any, relationMods []*model.OrderRelation) (err error) {
	return global.DB.WithContext(ctx).Debug().Transaction(func(tx *gorm.DB) error {

		// 订单
		err = tx.Model(&model.Order{}).Where(model.OrderColumns.ID(), orderId).Updates(updatesMap).Error
		if err != nil {
			return err
		}

		// 更新订单支付信息
		// 财务审核通过
		// 财务审核驳回
		// 财务完成支款
		// 财务审核通过后-回退
		if updatesOrderPay == StatusYesNoYes {
			err = tx.Model(&model.OrderPay{}).
				Where(model.OrderPayColumns.OrderID(), orderId).
				Updates(orderPayUpdatesMap).
				Error
			if err != nil {
				return err
			}
		}

		if len(operationLogMods) > 0 {
			// 订单日志
			err = tx.Model(&model.OrderOperationLog{}).
				CreateInBatches(&operationLogMods, len(operationLogMods)).
				Error
			if err != nil {
				return err
			}
		}

		if len(relationMods) > 0 {
			err = tx.Model(&model.OrderRelation{}).
				CreateInBatches(&relationMods, len(relationMods)).
				Error
			if err != nil {
				return err
			}
		}

		return nil
	})
}

// BatchUpdateUpdaterId
//
//	@Description: 批量更新 owned_by，资产转移
//
// 订单:资产归属 = 1:1
// 操作：先删除，后创建
//
//	@receiver o
//	@param ctx
//	@param items
//	@return err
func (o *OrderTransaction) BatchUpdateUpdaterId(ctx context.Context, customerId int64, items []UpdateUpdaterId) (err error) {
	if customerId <= 0 || len(items) == 0 {
		return nil
	}

	var (
		timeNow   = time.Now()
		batchSize = 100
		action    = OrderRelationActionOwn // 资产归属
	)

	return global.DB.WithContext(ctx).Debug().Transaction(func(tx *gorm.DB) error {

		for _, item := range items {
			var orderIds []int64

			// 获取需要更新的订单ID
			err = tx.Model(&model.Order{}).
				Where(model.OrderColumns.CustomerID(), customerId).
				Where(model.OrderColumns.OwnedBy(), item.Before).
				Pluck(model.OrderColumns.ID(), &orderIds).
				Error
			if err != nil {
				return err
			}

			if len(orderIds) == 0 {
				continue
			}

			updatesMap := make(map[string]any)
			updatesMap[model.OrderColumns.OwnedBy()] = item.After
			updatesMap[model.OrderColumns.UpdatedAt()] = timeNow

			// 更新订单资产所属人
			err = tx.Model(&model.Order{}).
				Where(model.OrderColumns.ID()+" IN (?) ", orderIds).
				Updates(updatesMap).
				Error

			// 删除资产归属在 orderIds 中的订单（订单:资产归属 = 1:1）
			// UNIQUE KEY `udx_order_user_action` (`order_id`,`user_id`,`action`,`deleted_at`) USING BTREE,
			err = tx.Model(&model.OrderRelation{}).
				Where(model.OrderRelationColumns.OrderID()+" IN (?) ", orderIds).
				Where(model.OrderRelationColumns.Action(), action). // 资产归属
				Where(model.OrderRelationColumns.DeletedAt(), 0).
				UpdateColumn(model.OrderRelationColumns.DeletedAt(), timeNow.Unix()).
				Error
			if err != nil {
				return err
			}

			// 新增资产归属 after 的订单
			orderRelationSli := make([]*model.OrderRelation, 0, len(orderIds))
			for _, orderId := range orderIds {
				orderRelation := &model.OrderRelation{
					ID:        0,
					OrderID:   orderId,
					UserID:    item.After,
					Action:    int32(action), // 资产归属
					Status:    int32(StatusYesNoYes),
					CreatedAt: timeNow,
					UpdatedAt: timeNow,
					DeletedAt: 0,
				}

				orderRelationSli = append(orderRelationSli, orderRelation)
			}

			err = tx.Model(&model.OrderRelation{}).CreateInBatches(&orderRelationSli, batchSize).Error

			if err != nil {
				return err
			}
		}

		return nil
	})
}

// BatchUpdateOrderOwnId
//
//	Description: 资产交接（多用户）
//
// 订单:资产归属 = 1:1
// 操作：先删除，后创建
//
//	receiver o
//	param ctx
//	param customerIds
//	param before
//	param after
//	return err
func (o *OrderTransaction) BatchUpdateOrderOwnId(ctx context.Context, customerIds []int64, before, after int64) (err error) {
	if len(customerIds) == 0 || before <= 0 || after <= 0 {
		return nil
	}

	var (
		timeNow   = time.Now()
		batchSize = 100
		action    = OrderRelationActionOwn // 资产归属
	)

	return global.DB.WithContext(ctx).Debug().Transaction(func(tx *gorm.DB) error {
		var orderIds []int64

		// 获取需要更新的订单ID
		err = tx.Model(&model.Order{}).
			Where(model.OrderColumns.CustomerID()+" IN (?) ", customerIds).
			Where(model.OrderColumns.OwnedBy(), before).
			Pluck(model.OrderColumns.ID(), &orderIds).
			Error
		if err != nil {
			return err
		}

		if len(orderIds) == 0 {
			return nil
		}

		updatesMap := make(map[string]any)
		updatesMap[model.OrderColumns.OwnedBy()] = after
		updatesMap[model.OrderColumns.UpdatedAt()] = time.Now()

		// 更新订单资产所属人
		err = tx.Model(&model.Order{}).
			Where(model.OrderColumns.ID()+" IN (?) ", orderIds).
			Updates(updatesMap).
			Error
		if err != nil {
			return err
		}

		// 删除资产归属在 orderIds 中的订单（订单:资产归属 = 1:1）
		// UNIQUE KEY `udx_order_user_action` (`order_id`,`user_id`,`action`,`deleted_at`) USING BTREE,
		err = tx.Model(&model.OrderRelation{}).
			Where(model.OrderRelationColumns.OrderID()+" IN (?) ", orderIds).
			Where(model.OrderRelationColumns.Action(), action). // 资产归属
			Where(model.OrderRelationColumns.DeletedAt(), 0).
			UpdateColumn(model.OrderRelationColumns.DeletedAt(), timeNow.Unix()).
			Error
		if err != nil {
			return err
		}

		// 新增资产归属 after 的订单
		orderRelationSli := make([]*model.OrderRelation, 0, len(orderIds))
		for _, orderId := range orderIds {
			orderRelation := &model.OrderRelation{
				ID:        0,
				OrderID:   orderId,
				UserID:    after,
				Action:    int32(action), // 资产归属
				Status:    int32(StatusYesNoYes),
				CreatedAt: timeNow,
				UpdatedAt: timeNow,
				DeletedAt: 0,
			}

			orderRelationSli = append(orderRelationSli, orderRelation)
		}

		err = tx.Model(&model.OrderRelation{}).CreateInBatches(&orderRelationSli, batchSize).Error
		if err != nil {
			return err
		}

		return nil
	})
}

// BatchUpdateWorkflowStatus
//
//	@Description: 批量更新ids的工单状态
//	@receiver o
//	@param ctx
//	@param orderIds
//	@param workflow
//	@return err
func (o *OrderTransaction) BatchUpdateWorkflowStatus(ctx context.Context, orderIds []int64, workflow StatusOrderWorkflow) (err error) {
	if len(orderIds) == 0 {
		return nil
	}

	updatesMap := make(map[string]any)
	updatesMap[model.OrderColumns.StatusWorkflow()] = workflow
	updatesMap[model.OrderColumns.UpdatedAt()] = time.Now()

	return global.DB.WithContext(ctx).Debug().Transaction(func(tx *gorm.DB) error {

		err = tx.Model(&model.Order{}).
			Where(model.OrderColumns.ID(), orderIds).
			Updates(updatesMap).
			Error

		if err != nil {
			return err
		}

		return nil
	})
}

// ResetCompleteWorkflowStatus
//
//	@Description: 重制已完成订单的工单状态为服务中
//	@receiver o
//	@param ctx
//	@return error
func (o *Order) ResetCompleteWorkflowStatus(ctx context.Context, orderIds []int64) error {
	if len(orderIds) == 0 {
		return nil
	}

	statusWorkflowSli := []StatusOrderWorkflow{
		StatusOrderWorkflowCompleted,
		StatusOrderWorkflowTerminated,
	}

	updatesMap := make(map[string]any)
	updatesMap[model.OrderColumns.StatusWorkflow()] = StatusOrderWorkflowInService
	updatesMap[model.OrderColumns.UpdatedAt()] = time.Now()

	err := global.DB.WithContext(ctx).Debug().
		Model(&model.Order{}).
		Where(model.OrderColumns.ID()+" IN (?)", orderIds).
		Where(model.OrderColumns.StatusWorkflow()+" IN (?)", statusWorkflowSli).
		Updates(updatesMap).
		Error

	return err
}
