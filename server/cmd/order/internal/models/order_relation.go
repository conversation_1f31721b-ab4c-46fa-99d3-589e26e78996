package models

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/order/internal/global"
)

// GetOrderRelationsByOrderId 获取订单关联关系
func (o *Order) GetOrderRelationsByOrderId(ctx context.Context, orderID int64) ([]*model.OrderRelation, error) {
	var (
		err  error
		mods = make([]*model.OrderRelation, 0)
	)

	err = global.DB.WithContext(ctx).Debug().
		Model(&model.OrderRelation{}).
		Where(model.OrderRelationColumns.OrderID(), orderID).
		Where(model.OrderRelationColumns.Status(), StatusYesNoYes).
		Where(model.OrderRelationColumns.DeletedAt(), 0).
		Find(&mods).
		Error

	return mods, err
}

// GetOrderRelationByActionUserId 获取订单关联关系
func (o *Order) GetOrderRelationByActionUserId(ctx context.Context, orderID, userID int64, action OrderRelationAction) (*model.OrderRelation, error) {

	var (
		err error
		mod *model.OrderRelation
	)

	err = global.DB.WithContext(ctx).Debug().
		Model(&model.OrderRelation{}).
		Where(model.OrderRelationColumns.OrderID(), orderID).
		Where(model.OrderRelationColumns.UserID(), userID).
		Where(model.OrderRelationColumns.Action(), action).
		Where(model.OrderRelationColumns.Status(), StatusYesNoYes).
		Where(model.OrderRelationColumns.DeletedAt(), 0).
		First(&mod).
		Error

	return mod, err
}

// UpdateOrderRelation 更新订单关联关系
//
// 功能描述：
//  1. 处理无跟进人情况：删除该订单下所有工单跟进类型的关联记录
//  2. 处理有跟进人情况：
//     a. 查询现有关系记录
//     b. 将跟进人分类为待删除/待恢复/待新增三类
//     c. 批量执行数据库操作
//
// 参数说明：
//   - ctx          : 上下文对象，用于传递请求上下文信息
//   - tx           : 数据库事务对象，所有操作将在该事务中执行
//   - funcName     : 调用方函数名，用于日志记录
//   - orderID      : 订单ID，需要更新的订单标识
//   - action       : 操作类型，此处固定为工单跟进(6)
//   - processBySli : 当前需要关联的用户ID列表（已去重）
//
// 返回值：
//   - error : 操作失败时返回错误信息，成功返回nil
//
// 注意事项：
//  1. 所有操作都是幂等的，可安全重试
//  2. 软删除使用时间戳标记（DeletedAt）
//  3. 新增记录状态默认为有效(Status=1)
//  4. 批量操作使用CreateInBatches提高性能
func UpdateOrderRelation(ctx context.Context, tx *gorm.DB, funcName string, orderID int64, action int32, processBySli []int64) error {
	// 状态常量定义
	const StatusValid = 1 // 状态#1%有效|2%失效

	var (
		err         error
		timeNow     = time.Now()     // 统一使用当前时间
		timeNowUnix = timeNow.Unix() // 时间戳格式
		batchSize   = 100            // 批量插入大小
	)

	// 1. 处理无跟进人的特殊情况
	if len(processBySli) == 0 {
		logger.CtxInfof(ctx, "%s no valid followers found", funcName)

		// 删除所有跟进类型的关联记录（软删除）
		// 条件：指定订单 + 跟进类型 + 未删除的记录
		err = tx.Model(&model.OrderRelation{}).
			Where(model.OrderRelationColumns.OrderID(), orderID).
			Where(model.OrderRelationColumns.Action(), action).
			Where(model.OrderRelationColumns.DeletedAt(), 0).
			UpdateColumn(model.OrderRelationColumns.DeletedAt(), timeNowUnix).
			Error
		if err != nil {
			return fmt.Errorf("failed to delete order_relation: %w", err)
		}

		return nil
	}

	// 2. 获取现有关系记录
	// 条件：指定订单 + 跟进类型（包含已软删除的记录）
	var existingRelations []model.OrderRelation
	err = tx.Model(&model.OrderRelation{}).
		Where(model.OrderRelationColumns.OrderID(), orderID).
		Where(model.OrderRelationColumns.Action(), action).
		Find(&existingRelations).Error
	if err != nil {
		return fmt.Errorf("failed to get existing relations: %w", err)
	}

	// 3. 分类操作：将用户分为待删除/待恢复/待新增三类
	ops, err := ClassifyRelationOperations(processBySli, existingRelations)
	if err != nil {
		return fmt.Errorf("classification failed: %w", err)
	}

	// 4. 批量执行数据库操作
	// 4.1 处理待删除记录（软删除）
	if len(ops.ToDelete) > 0 {
		logger.CtxInfof(ctx, "%s deleting %d relations", funcName, len(ops.ToDelete))

		// 条件：指定订单 + 跟进类型 + 用户ID列表 + 未删除的记录
		err = tx.Model(&model.OrderRelation{}).
			Where(model.OrderRelationColumns.OrderID(), orderID).
			Where(model.OrderRelationColumns.Action(), action).
			Where(model.OrderRelationColumns.UserID(), ops.ToDelete).
			Where(model.OrderRelationColumns.DeletedAt(), 0).
			UpdateColumn(model.OrderRelationColumns.DeletedAt(), timeNowUnix).
			Error
		if err != nil {
			return fmt.Errorf("batch delete failed: %w", err)
		}
	}

	// 4.2 处理待恢复记录（取消软删除）
	if len(ops.ToRecover) > 0 {
		logger.CtxInfof(ctx, "%s recovering %d relations", funcName, len(ops.ToRecover))

		// 条件：指定订单 + 跟进类型 + 用户ID列表 + 已删除的记录
		err = tx.Model(&model.OrderRelation{}).
			Where(model.OrderRelationColumns.OrderID(), orderID).
			Where(model.OrderRelationColumns.Action(), action).
			Where(model.OrderRelationColumns.UserID(), ops.ToRecover).
			Where(model.OrderRelationColumns.DeletedAt()+" > ? ", 0).
			UpdateColumn(model.OrderRelationColumns.DeletedAt(), 0).
			Error
		if err != nil {
			return fmt.Errorf("batch recover failed: %w", err)
		}
	}

	// 4.3 处理待新增记录
	if len(ops.ToAdd) > 0 {
		logger.CtxInfof(ctx, "%s adding %d relations", funcName, len(ops.ToAdd))

		// 准备批量插入数据
		relations := make([]model.OrderRelation, 0, len(ops.ToAdd))
		for _, userID := range ops.ToAdd {
			relations = append(relations, model.OrderRelation{
				OrderID:   orderID,
				UserID:    userID,
				Action:    action,
				Status:    StatusValid, // 状态默认为有效
				CreatedAt: timeNow,     // 设置创建时间
				UpdatedAt: timeNow,     // 设置更新时间
			})
		}

		// 批量插入（每batchSize条一个批次）
		if err = tx.CreateInBatches(relations, batchSize).Error; err != nil {
			return fmt.Errorf("batch create failed: %w", err)
		}
	}

	return nil
}

// RelationOperationSet 关系操作集合
type RelationOperationSet struct {
	ToDelete  []int64 // 待删除的用户ID（需满足：不在当前跟进列表且未软删除）
	ToRecover []int64 // 待恢复的用户ID（需满足：在当前跟进列表且已软删除）
	ToAdd     []int64 // 待新增的用户ID（需满足：在当前跟进列表且不存在于数据库）
}

// ClassifyRelationOperations 分类关系操作
// 参数:
//   - retainIDs: 当前需要保留的用户ID集合
//   - existingRelations: 数据库中已存在的关系记录
//
// 返回值:
//   - 操作集合（删除/恢复/新增）
//   - 错误信息
func ClassifyRelationOperations(retainIDs []int64, existingRelations []model.OrderRelation) (*RelationOperationSet, error) {
	// 参数校验
	if len(retainIDs) == 0 && len(existingRelations) == 0 {
		return &RelationOperationSet{}, nil
	}

	// 1. 构建保留集（预分配内存优化性能）
	retainSet := make(map[int64]struct{}, len(retainIDs))
	for _, id := range retainIDs {
		if id <= 0 {
			return nil, fmt.Errorf("invalid user ID: %d", id)
		}
		retainSet[id] = struct{}{}
	}

	// 2. 构建现有关系查找表
	existingMap := make(map[int64]model.OrderRelation, len(existingRelations))
	var ops RelationOperationSet

	// 3. 遍历现有记录进行分类
	for _, rel := range existingRelations {
		existingMap[rel.UserID] = rel

		if _, retain := retainSet[rel.UserID]; retain {
			if rel.DeletedAt != 0 {
				ops.ToRecover = append(ops.ToRecover, rel.UserID)
			}
			// 保留且未删除的记录无需处理
		} else if rel.DeletedAt == 0 {
			ops.ToDelete = append(ops.ToDelete, rel.UserID)
		}
	}

	// 4. 确定新增记录
	for id := range retainSet {
		if _, exists := existingMap[id]; !exists {
			ops.ToAdd = append(ops.ToAdd, id)
		}
	}

	return &ops, nil
}

// GetOrderRelationByOrderIds 获取订单关联关系
func (o *Order) GetOrderRelationByOrderIds(ctx context.Context, orderIDs []int64, action int32) ([]*model.OrderRelation, error) {
	var (
		err error
		mods = make([]*model.OrderRelation, 0)
	)
	err = global.DB.WithContext(ctx).Debug().
		Model(&model.OrderRelation{}).
		Where(model.OrderRelationColumns.OrderID()+" IN (?)", orderIDs).
		Where(model.OrderRelationColumns.Action(), action).
		Where(model.OrderRelationColumns.Status(), StatusYesNoYes).
		Where(model.OrderRelationColumns.DeletedAt(), 0).
		Find(&mods).
		Error

	return mods, err
}
