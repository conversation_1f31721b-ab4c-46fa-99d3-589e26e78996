package models

import (
	"context"
	"strconv"
	"time"
	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/utils"
	"uofferv2/server/cmd/order/internal/global"

	"gorm.io/gorm"
	"gorm.io/plugin/soft_delete"
)

type FundInfoRequest struct {
	Id         int64  `json:"id"`
	FundNo     string `json:"fund_no"`
	OrderID    int64  `json:"order_id"`
	OrderNo    string `json:"order_no"`
	FundType   int32  `json:"fund_type"`
	WorkflowId int64  `json:"workflow_id"`
	CustomerId int64  `json:"customer_id"`
}

type FundInfoUserRequest struct {
	Id         int64 `json:"id"`
	CustomerId int64 `json:"customer_id"` //客户ID
}

type FundListRequest struct {
	ApproveStatus       int32    `json:"approve_status"`        //审批状态(1=待审批;2=审批通过;3=驳回审批);
	ApproveStatusList   []int32  `json:"approve_status_list"`   //审批状态(1=待审批;2=审批通过;3=驳回审批);
	FundNo              string   `json:"fund_no"`               //收款单号
	OrderNo             string   `json:"order_no"`              //订单号
	CustomerId          int64    `json:"customer_id"`           //客户ID
	CreatedAtStart      int64    `json:"created_at_start"`      //创建日期开始时间
	CreatedAtEnd        int64    `json:"created_at_end"`        //创建日期结束时间
	FundType            int32    `json:"fund_type"`             //款项类型
	ContractNo          string   `json:"contract_no"`           //合同编码
	ServiceName         []string `json:"service_name"`          //服务项目名称
	GoodsName           string   `json:"goods_name"`            //商品名称
	BrandName           string   `json:"brand_name"`            //品牌
	BusinessName        string   `json:"business_name"`         //业务名称
	PayType             int32    `json:"pay_type"`              //付款方式(1=一次性;2=分期)
	Currency            string   `json:"currency"`              //币种
	SubmitId            []int64  `json:"submit_id"`             //提交人
	PaymentAccountId    int64    `json:"payment_account_id"`    //收款账户ID
	PassTimeStart       int64    `json:"pass_time_start"`       //通过日期开始时间
	PassTimeEnd         int64    `json:"pass_time_end"`         //通过日期结束时间
	RejectTimeStart     int64    `json:"reject_time_start"`     //驳回日期开始时间
	RejectTimeEnd       int64    `json:"reject_time_end"`       //驳回日期结束时间
	OrderBy             string   `json:"order_by"`              //排序字段 created_at desc
	OrderId             int64    `json:"order_id"`              //订单ID
	PageNum             int32    `json:"page_num"`              //页数
	PageSize            int32    `json:"page_size"`             //每页几条
	TransactionNo       string   `json:"transaction_no"`        //交易单号
	UserType            int32    `json:"user_type"`             //用户类型（1新客户，2老客户)
	StaffIds            []int64  `json:"staff_ids"`             // 可选 员工ID列表, 调用rpc获取
	CustomerName        string   `json:"customer_name"`         //客户姓名
	PaidTimeStart       int64    `json:"paid_time_start"`       //实际付款日期开始时间
	PaidTimeEnd         int64    `json:"paid_time_end"`         //实际付款日期结束时间
	UserSource          []int64  `json:"user_source"`           // 客户来源
	UserSourceDepart    []int32  `json:"user_source_depart"`    // 客户来源部门
	OrderSource         []int64  `json:"order_source"`          // 订单来源
	OrderSourceDepart   []int32  `json:"order_source_depart"`   // 订单来源部门
	OrderStatus         []int32  `json:"order_status"`          // 订单状态
	SubmitSourceDepart  []int32  `json:"submit_source_depart"`  // 提交人源部门
	PaymentAccountIds   []int64  `json:"payment_account_ids"`   // 收款账户ID列表
	PaymentAccountTypes []int32  `json:"payment_account_types"` // 使用基础类型 int32
}

type FundListMod struct {
	ID                  int64                 `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	OrderNo             string                `gorm:"column:order_no;type:varchar(255);not null;comment:订单号" json:"order_no"`                                        // 订单号
	OrderID             int64                 `gorm:"column:order_id;type:bigint unsigned;not null;comment:订单ID" json:"order_id"`                                    // 订单ID
	FundNo              string                `gorm:"column:fund_no;type:varchar(255);not null;comment:收款单号" json:"fund_no"`                                         // 收款单号
	CustomerID          int64                 `gorm:"column:customer_id;type:bigint unsigned;not null;comment:客户id" json:"customer_id"`                              // 客户id
	GoodsID             int64                 `gorm:"column:goods_id;type:bigint unsigned;not null;comment:商品ID" json:"goods_id"`                                    // 商品ID
	GoodsName           string                `gorm:"column:goods_name;type:varchar(255);not null;comment:商品名称" json:"goods_name"`                                   // 商品名称
	GoodsSpecsName      string                `gorm:"column:goods_specs_name;type:varchar(255);not null;comment:商品规格名称" json:"goods_specs_name"`                     // 商品规格名称
	ServiceName         string                `gorm:"column:service_name;type:varchar(255);not null;comment:服务项目名称" json:"service_name"`                             // 服务项目名称
	BrandName           string                `gorm:"column:brand_name;type:varchar(255);not null;comment:品牌名称" json:"brand_name"`                                   // 品牌名称
	BusinessName        string                `gorm:"column:business_name;type:varchar(255);not null;comment:业务线名称" json:"business_name"`                            // 业务线名称
	Num                 string                `gorm:"column:num;type:decimal(20,5);not null;default:0.00000;comment:数量" json:"num"`                                  // 数量
	ApproveStatus       int32                 `gorm:"column:approve_status;type:tinyint;not null;default:1;comment:审批状态(1=待审批;2=审批通过;3=驳回审批)" json:"approve_status"` // 审批状态(1=待审批;2=审批通过;3=驳回审批)
	PayType             int32                 `gorm:"column:pay_type;type:tinyint;not null;default:1;comment:付款方式(1=全款;2=分期)" json:"pay_type"`                       // 付款方式(1=全款;2=分期)
	FundType            int32                 `gorm:"column:fund_type;type:tinyint;not null;default:1;comment:款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)" json:"fund_type"`  // 款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)
	UserType            int32                 `gorm:"column:user_type;type:tinyint unsigned;not null;default:1;comment:用户类型（1新客户，2老客户）" json:"user_type"`            // 用户类型（1新客户，2老客户）
	Currency            string                `gorm:"column:currency;type:varchar(16);not null;default:CNY;comment:币种" json:"currency"`                              // 币种
	ExchangeRate        string                `gorm:"column:exchange_rate;type:decimal(20,5);not null;default:1.00000;comment:汇率" json:"exchange_rate"`              // 汇率
	RealAmountOther     string                `gorm:"column:real_amount_other;type:decimal(20,5);not null;default:0.00000" json:"real_amount_other"`
	RealAmountRmb       string                `gorm:"column:real_amount_rmb;type:decimal(20,5);not null;default:0.00000" json:"real_amount_rmb"`
	Discount            int32                 `gorm:"column:discount;type:int;not null;default:1;comment:折扣 1无 2有" json:"discount"`                        // 折扣 1无 2有
	DiscountRate        string                `gorm:"column:discount_rate;type:decimal(20,5);not null;default:100.00000;comment:折扣率" json:"discount_rate"` // 折扣率
	ShouldAmountOther   string                `gorm:"column:should_amount_other;type:decimal(20,5);not null;default:0.00000" json:"should_amount_other"`
	ShouldAmountRmb     string                `gorm:"column:should_amount_rmb;type:decimal(20,5);not null;default:0.00000" json:"should_amount_rmb"`
	UrgentSpeed         string                `gorm:"column:urgent_speed;type:decimal(20,5);not null;default:1.00000" json:"urgent_speed"`
	ApproveBy           int64                 `gorm:"column:approve_by;type:bigint;not null;comment:审批人id" json:"approve_by"`                          // 审批人id
	ApproveComment      string                `gorm:"column:approve_comment;type:text;comment:审批备注" json:"approve_comment"`                            // 审批备注
	PaidTime            *time.Time            `gorm:"column:paid_time;type:datetime(3);comment:付款时间" json:"paid_time"`                                 // 付款时间
	ContractNo          string                `gorm:"column:contract_no;type:varchar(255);not null;comment:合同编号" json:"contract_no"`                   // 合同编号
	ContractURL         string                `gorm:"column:contract_url;type:json;comment:合同url" json:"contract_url"`                                 // 合同url
	SubmitID            int64                 `gorm:"column:submit_id;type:bigint;not null;comment:提交者ID" json:"submit_id"`                            // 提交者ID
	UpdatedBy           int64                 `gorm:"column:updated_by;type:bigint;not null;comment:更新人" json:"updated_by"`                            // 更新人
	PassTime            *time.Time            `gorm:"column:pass_time;type:datetime(3);comment:通过时间" json:"pass_time"`                                 // 通过时间
	RejectTime          *time.Time            `gorm:"column:reject_time;type:datetime(3);comment:驳回时间" json:"reject_time"`                             // 驳回时间
	WorkflowID          int64                 `gorm:"column:workflow_id;type:bigint;not null;comment:工单ID" json:"workflow_id"`                         // 工单ID
	WorkflowNo          string                `gorm:"column:workflow_no;type:varchar(255);not null;comment:工单编号" json:"workflow_no"`                   // 工单编号
	WorkflowName        string                `gorm:"column:workflow_name;type:varchar(255);not null;comment:工单名称" json:"workflow_name"`               // 工单名称
	CreatedAt           time.Time             `gorm:"column:created_at;type:datetime(3);not null;autoCreateTime:milli;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt           time.Time             `gorm:"column:updated_at;type:datetime(3);not null;autoUpdateTime:milli;comment:修改时间" json:"updated_at"` // 修改时间
	DeletedAt           soft_delete.DeletedAt `gorm:"column:deleted_at;type:bigint;not null;default:0;softDelete:milli" json:"deleted_at"`
	GroupServiceName    string                `gorm:"-" json:"group_service_name"`                                                                                                               // 服务项目名称
	GroupBusinessName   string                `gorm:"-" json:"group_business_name"`                                                                                                              // 业务线名称
	GroupCustomerUserId string                `gorm:"-" json:"group_customer_user_id"`                                                                                                           // 客户来源
	GroupOrderUserId    string                `gorm:"-" json:"group_order_user_id"`                                                                                                              // 订单来源
	HandleBy            int64                 `gorm:"column:handle_by;type:bigint;not null;comment:资产处理人" json:"handle_by"`                                                                      // 资产处理人
	Remark              string                `gorm:"column:remark;type:varchar(2000);not null;comment:备注" json:"remark"`                                                                        // 备注
	Status              int32                 `gorm:"column:status;type:tinyint unsigned;not null;default:2;comment:订单状态#1%已下定金（定金）|2%支付待确认（首款）|3%尾款待支付（尾款）|4%支付成功|5%支款订单|6%交易关闭" json:"status"` // 订单状态#1%已下定金（定金）|2%支付待确认（首款）|3%尾款待支付（尾款）|4%支付成功|5%支款订单|6%交易关闭
}

type FundExchangeRateRequest struct {
	OrderID    int64  `json:"order_id"`
	OrderNo    string `json:"order_no"`
	Currency   string `json:"currency"`
	WorkflowID int64  `json:"workflow_id"`
}

type WorkflowFundRequest struct {
	WorkflowNo    string `json:"workflow_no"`
	WorkflowName  string `json:"workflow_name"`
	ApproveStatus int32  `json:"approve_status"`
	CustomerId    int64  `json:"customer_id"` //客户ID
	WorkflowId    int64  `json:"workflow_id"` //工单ID
	PageNum       int32  `json:"page_num"`    //页数
	PageSize      int32  `json:"page_size"`   //每页几条
}

// FundCreate 新增收款单
func FundCreate(ctx context.Context, req *model.FinancialFund) (int64, error) {
	if req.ContractURL == "" {
		req.ContractURL = "[]"
	}

	if err := global.DB.WithContext(ctx).Create(&req).Error; err != nil {
		return req.ID, err
	}
	return req.ID, nil
}

// FundInfo 收款单详情
func FundInfo(ctx context.Context, req *FundInfoRequest) (*model.FinancialFund, error) {
	mod := &model.FinancialFund{}
	idl := global.DB.WithContext(ctx).Model(&model.FinancialFund{})
	flag := 1
	if req.Id != 0 {
		idl.Where(model.FinancialFundColumns.ID(), req.Id)
		flag = 0
	}
	if req.OrderNo != "" {
		idl.Where(model.FinancialFundColumns.OrderNo(), req.OrderNo)
		flag = 0
	}
	if req.FundType != 0 {
		idl.Where(model.FinancialFundColumns.FundType(), req.FundType)
		flag = 0
	}
	if req.FundNo != "" {
		idl.Where(model.FinancialFundColumns.FundNo(), req.FundNo)
		flag = 0
	}
	if req.OrderID != 0 {
		idl.Where(model.FinancialFundColumns.OrderID(), req.OrderID)
		flag = 0
	}
	if req.WorkflowId != 0 {
		idl.Where(model.FinancialFundColumns.WorkflowID(), req.WorkflowId)
		flag = 0
	}
	if req.CustomerId != 0 {
		idl.Where(model.FinancialFundColumns.CustomerID(), req.CustomerId)
		flag = 0
	}
	if flag == 1 {
		return mod, nil
	}
	err := idl.Order("id desc").First(&mod).Error
	if err != nil {
		return mod, err
	}
	return mod, nil
}

// FundList 收款单列表
func FundList(ctx context.Context, req *FundListRequest) (int64, []*FundListMod, error) {
	var total int64
	fundList := make([]*FundListMod, 0)

	// 创建基础查询
	db := global.DB.WithContext(ctx).Model(&model.FinancialFund{})

	// 跟踪表连接状态
	var (
		joinedOrderGoods       bool
		joinedCustomers        bool
		joinedOrderRelation    bool
		joinedFinancialPaid    bool
		joinedCustomerReferral bool
	)

	// 使用 NewQueryBuilder 构建查询条件
	qb := utils.NewQueryBuilder()
	// 因为需要返回orders.status，所以需要先连接orders表
	db = db.Joins("LEFT JOIN orders ON financial_fund.order_id = orders.id")

	// 基础字段查询
	qb.Like("financial_fund.order_no", req.OrderNo).
		Like("financial_fund.fund_no", req.FundNo).
		In("financial_fund.submit_id", req.SubmitId).
		Equal("financial_fund.order_id", req.OrderId).
		Equal("financial_fund.customer_id", req.CustomerId).
		Equal("financial_fund.fund_type", req.FundType).
		Equal("financial_fund.approve_status", req.ApproveStatus).
		Equal("financial_fund.brand_name", req.BrandName).
		Equal("financial_fund.business_name", req.BusinessName).
		Equal("financial_fund.pay_type", req.PayType).
		Equal("financial_fund.user_type", req.UserType).
		Like("financial_fund.contract_no", req.ContractNo).
		In("financial_fund.handle_by", req.StaffIds).
		In("orders.status", req.OrderStatus)

	// 特殊处理审批状态列表
	if len(req.ApproveStatusList) > 0 && len(req.ApproveStatusList) < 4 && req.ApproveStatus == 0 {
		qb.In("financial_fund.approve_status", req.ApproveStatusList)
	}

	// 处理服务名称查询
	if len(req.ServiceName) != 0 {
		if !joinedOrderGoods {
			db = db.Joins("LEFT JOIN order_goods ON financial_fund.order_id = order_goods.order_id")
			joinedOrderGoods = true
		}
		qb.In("order_goods.service_name", req.ServiceName)
	}

	// 处理客户名称查询
	if req.CustomerName != "" {
		if !joinedCustomers {
			db = db.Joins("LEFT JOIN customers ON financial_fund.customer_id = customers.id")
			joinedCustomers = true
		}
		qb.Like("customers.real_name", req.CustomerName)
	}

	// 处理用户来源查询
	if len(req.UserSource) != 0 {
		if !joinedCustomerReferral {
			db = db.Joins("LEFT JOIN customer_referral_relationship ON financial_fund.customer_id = customer_referral_relationship.customer_id")
			joinedCustomerReferral = true
		}
		qb.In("customer_referral_relationship.admin_user_id", req.UserSource)
	}

	// 处理用户来源部门查询
	if len(req.UserSourceDepart) != 0 {
		if !joinedCustomerReferral {
			db = db.Joins("LEFT JOIN customer_referral_relationship ON financial_fund.customer_id = customer_referral_relationship.customer_id")
			joinedCustomerReferral = true
		}
		db = db.Joins("LEFT JOIN sys_employee AS emp_source ON customer_referral_relationship.admin_user_id = emp_source.id")
		qb.In("emp_source.dept_id", req.UserSourceDepart)
	}

	// 处理订单来源查询
	if len(req.OrderSource) != 0 {
		db = db.Joins("LEFT JOIN order_relation ON financial_fund.order_id = order_relation.order_id AND order_relation.action = ?", OrderRelationActionSource)
		joinedOrderRelation = true
		qb.In("order_relation.user_id", req.OrderSource)
	}

	// 处理订单来源部门查询
	if len(req.OrderSourceDepart) != 0 {
		if !joinedOrderRelation {
			db = db.Joins("LEFT JOIN order_relation ON financial_fund.order_id = order_relation.order_id AND order_relation.action = ?", OrderRelationActionSource)
			joinedOrderRelation = true
		}
		db = db.Joins("LEFT JOIN sys_employee AS emp_order ON order_relation.user_id = emp_order.id")
		qb.In("emp_order.dept_id", req.OrderSourceDepart)
	}

	// 处理提交部门查询
	if len(req.SubmitSourceDepart) > 0 {
		db = db.Joins("LEFT JOIN sys_employee AS emp_submit ON financial_fund.submit_id = emp_submit.id")
		qb.In("emp_submit.dept_id", req.SubmitSourceDepart)
	}

	// 处理商品名称/ID查询
	if req.GoodsName != "" {
		goodsId, err := strconv.Atoi(req.GoodsName)
		if err != nil {
			qb.Like("financial_fund.goods_name", req.GoodsName)
		} else {
			qb.Equal("financial_fund.goods_id", goodsId)
		}
	}

	// 处理币种查询
	if req.Currency != "" {
		if !joinedFinancialPaid {
			db = db.Joins("LEFT JOIN financial_paid ON financial_fund.id = financial_paid.financial_fund_id").
				Where("financial_paid.deleted_at = ?", 0)
			joinedFinancialPaid = true
		}
		qb.Equal("financial_paid.currency", req.Currency)
	}

	// 处理交易号查询
	if req.TransactionNo != "" {
		if !joinedFinancialPaid {
			db = db.Joins("LEFT JOIN financial_paid ON financial_fund.id = financial_paid.financial_fund_id").
				Where("financial_paid.deleted_at = ?", 0)
			joinedFinancialPaid = true
		}
		qb.Like("financial_paid.transaction_no", req.TransactionNo)
	}

	// 处理支付账号查询
	if req.PaymentAccountId != 0 {
		req.PaymentAccountIds = []int64{req.PaymentAccountId}
	}
	if len(req.PaymentAccountIds) > 0 || len(req.PaymentAccountTypes) > 0 {
		if !joinedFinancialPaid {
			db = db.Joins("LEFT JOIN financial_paid ON financial_fund.id = financial_paid.financial_fund_id").
				Where("financial_paid.deleted_at = ?", 0)
			joinedFinancialPaid = true
		}
		if len(req.PaymentAccountIds) > 0 {
			qb.In("financial_paid.payment_account_id", req.PaymentAccountIds)
		}
		if len(req.PaymentAccountTypes) > 0 {
			// 用快照查询。页面显示的快照
			qb.In("financial_paid.paid_type", req.PaymentAccountTypes)
		}
	}

	// 处理时间范围查询
	qb.TimeBetween("financial_fund.created_at", req.CreatedAtStart, req.CreatedAtEnd).
		TimeBetween("financial_fund.pass_time", req.PassTimeStart, req.PassTimeEnd).
		TimeBetween("financial_fund.reject_time", req.RejectTimeStart, req.RejectTimeEnd).
		TimeBetween("financial_fund.paid_time", req.PaidTimeStart, req.PaidTimeEnd)

	// 应用查询条件
	db = qb.Apply(db)

	// 处理排序
	if req.OrderBy != "" {
		db = db.Order("financial_fund." + req.OrderBy)
	} else {
		db = db.Order("financial_fund.id desc")
	}

	// 分组
	db = db.Group("financial_fund.id")

	// 执行查询
	err := db.Count(&total).
		Offset(int((req.PageNum - 1) * req.PageSize)).
		Limit(int(req.PageSize)).
		Select("financial_fund.*,orders.status").
		Find(&fundList).Error

	if err != nil {
		return 0, nil, err
	}

	return total, fundList, nil
}

// FundStatusUpdate 更新收款单状态
func FundStatusUpdate(ctx context.Context, req *model.FinancialFund) (*model.FinancialFund, error) {
	if err := global.DB.WithContext(ctx).Where(model.FinancialFundColumns.ID(), req.ID).Updates(req).Error; err != nil {
		return req, err
	}
	return req, nil
}

// PaidList 支付记录列表
func PaidList(ctx context.Context, financialFundId int64) ([]*model.FinancialPaid, error) {
	financialPaid := make([]*model.FinancialPaid, 0)
	err := global.DB.WithContext(ctx).Model(&model.FinancialPaid{}).Where(model.FinancialPaidColumns.FinancialFundID(), financialFundId).Order("id desc").Find(&financialPaid).Error
	if err != nil {
		return financialPaid, err
	}
	return financialPaid, nil
}

// FundDraftCreate 新增收款记录草稿
func FundDraftCreate(ctx context.Context, req *model.FinancialFundDraft) (int64, error) {
	if err := global.DB.WithContext(ctx).Create(&req).Error; err != nil {
		return req.ID, err
	}
	return req.ID, nil
}

// GetFundDraft 获取收款记录草稿
func GetFundDraft(ctx context.Context, orderId int64, fundType int32) (*model.FinancialFundDraft, error) {
	mod := &model.FinancialFundDraft{}
	idl := global.DB.WithContext(ctx).Model(&model.FinancialFundDraft{})
	if orderId != 0 {
		idl.Where(model.FinancialFundDraftColumns.OrderID(), orderId)
	}
	if fundType != 0 {
		idl.Where(model.FinancialFundDraftColumns.FundType(), fundType)
	}
	err := idl.Order("id desc").First(&mod).Error
	if err != nil {
		return mod, err
	}
	return mod, nil
}

// FundUpdate 更新收款单
func FundUpdate(ctx context.Context, req *model.FinancialFund) (*model.FinancialFund, error) {

	if err := global.DB.WithContext(ctx).Where(model.FinancialFundColumns.ID(), req.ID).Updates(req).Error; err != nil {
		return req, err
	}
	return req, nil
}

// FundDelete 删除收款单
func FundDelete(ctx context.Context, id int64) error {
	return global.DB.WithContext(ctx).Where(model.FinancialFundColumns.ID(), id).Delete(&model.FinancialFund{}).Error
}

// FundUpdateFields 更新指定字段
func FundUpdateFields(ctx context.Context, id int64, fields map[string]interface{}) error {
	return global.DB.WithContext(ctx).Model(&model.FinancialFund{}).Where("id = ?", id).Updates(fields).Error
}

// GetRelationExchangeRate 获取汇率
func GetRelationExchangeRate(ctx context.Context, req *FundExchangeRateRequest) (string, error) {
	mod := &model.FinancialFund{}
	exchangeRate := ""
	idl := global.DB.WithContext(ctx).Model(&model.FinancialFund{})

	if req.OrderNo != "" {
		idl.Where(model.FinancialFundColumns.OrderNo(), req.OrderNo)
	}
	if req.OrderID != 0 {
		idl.Where(model.FinancialFundColumns.OrderID(), req.OrderID)
	}
	if req.Currency != "" {
		idl.Where(model.FinancialFundColumns.Currency(), req.Currency)
	}
	if req.WorkflowID != 0 {
		idl.Where(model.FinancialFundColumns.WorkflowID(), req.WorkflowID)
	}
	idl.Order("id desc")
	err := idl.First(&mod).Error
	if err != nil {
		return mod.ExchangeRate, err
	}
	if mod == nil {
		return exchangeRate, nil
	}
	return mod.ExchangeRate, nil
}

// GetWorkflowFund  获取工单关联收款信息
func GetWorkflowFund(ctx context.Context, req *WorkflowFundRequest) (int64, []*model.FinancialFund, error) {
	var total int64
	fundList := make([]*model.FinancialFund, 0)
	idl := global.DB.WithContext(ctx).Model(&model.FinancialFund{})
	if req.WorkflowNo != "" {
		idl.Where(model.FinancialFundColumns.WorkflowNo(), req.WorkflowNo)
	}
	if req.WorkflowName != "" {
		idl.Where(model.FinancialFundColumns.WorkflowName()+" like ?", "%"+req.WorkflowName+"%")
	}
	if req.CustomerId != 0 {
		idl.Where(model.FinancialFundColumns.CustomerID(), req.CustomerId)
	}
	if req.WorkflowId != 0 {
		idl.Where(model.FinancialFundColumns.WorkflowID(), req.WorkflowId)
	}
	if req.ApproveStatus != 0 {
		idl.Where(model.FinancialFundColumns.ApproveStatus(), req.ApproveStatus)
	} else {
		idl.Where(model.FinancialFundColumns.ApproveStatus(), 2)
	}
	err := idl.Count(&total).Offset(int((req.PageNum-1)*req.PageSize)).Where("fund_type", 4).Limit(int(req.PageSize)).Find(&fundList).Error

	if err != nil {
		return 0, nil, err
	}
	return total, fundList, nil
}

// GetThirdFundByWorkflow 获取工单关联第三方申请费收款信息
func GetThirdFundByWorkflow(ctx context.Context, req *WorkflowFundRequest) (*model.FinancialFund, error) {
	fund := &model.FinancialFund{}
	idl := global.DB.WithContext(ctx).Model(&model.FinancialFund{})
	if req.WorkflowNo != "" {
		idl.Where(model.FinancialFundColumns.WorkflowNo(), req.WorkflowNo)
	}
	if req.WorkflowId != 0 {
		idl.Where(model.FinancialFundColumns.WorkflowID(), req.WorkflowId)
	}
	err := idl.Where("fund_type", 4).First(&fund).Error

	if err != nil {
		return nil, err
	}
	return fund, nil
}

// UpdateFinancialFundEmployee 批量更新员工
func UpdateFinancialFundEmployee(ctx context.Context, before, after int64, customerIds []int64) error {
	updateFields := make(map[string]interface{})
	updateFields["handle_by"] = after
	if err := global.DB.WithContext(ctx).
		Model(&model.FinancialFund{}).
		Where(model.FinancialFundColumns.HandleBy(), before).
		Where(model.FinancialFundColumns.CustomerID()+" IN (?) ", customerIds).
		Updates(updateFields).Error; err != nil {
		return err
	}
	if err := global.DB.WithContext(ctx).
		Model(&model.FinancialRefund{}).
		Where(model.FinancialRefundColumns.HandleBy(), before).
		Where(model.FinancialRefundColumns.CustomerID()+" IN (?) ", customerIds).
		Updates(updateFields).Error; err != nil {
		return err
	}
	return nil
}

func FundInfoForUserType(ctx context.Context, customerId int64, excludeFundIds []int64) (*model.FinancialFund, error) {
	mod := &model.FinancialFund{}
	idl := global.DB.WithContext(ctx).Model(&model.FinancialFund{})

	idl.Where(model.FinancialFundColumns.CustomerID(), customerId)
	idl.Where(model.FinancialFundColumns.ApproveStatus(), 2)

	fundType := []int32{2, 3}

	idl.Where(model.FinancialFundColumns.FundType()+" in (?)", fundType)
	if len(excludeFundIds) > 0 {
		idl.Where(model.FinancialFundColumns.ID()+" not in (?)", excludeFundIds)
	}

	var appTime int64
	appTime = time.Now().Unix()
	// appTime = appTime - 3600

	idl.Where(model.FinancialFundColumns.PassTime()+" < FROM_UNIXTIME(?)", appTime)

	err := idl.Order("id desc").First(&mod).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return mod, nil
}

func FundInfoForCustomerUserType(ctx context.Context, customerId int64) (*model.Customer, error) {
	mod := &model.Customer{}
	idl := global.DB.WithContext(ctx).Model(&model.Customer{})

	idl.Where(model.CustomerColumns.ID(), customerId)

	err := idl.First(&mod).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return mod, err
	}
	if mod != nil && mod.FundNum == 0 {
		return nil, nil
	}
	return mod, nil
}

func UpdateCustomerUserType(ctx context.Context, id int64, fields map[string]interface{}) error {
	return global.DB.WithContext(ctx).Model(&model.Customer{}).Where("id = ?", id).Updates(fields).Error
}
