package models

import (
	"context"
	"strconv"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/utils"
	"uofferv2/server/cmd/order/internal/global"
)

type FinancialRefundListRequest struct {
	Id                  int64    `json:"id"`
	OrderId             int64    `json:"order_id"`
	OrderNo             string   `json:"order_no"`
	RefundNo            string   `json:"refund_no"`
	SubmitId            []int64  `json:"submit_id"`
	CustomerId          int64    `json:"customer_id"`
	PageNum             int32    `json:"page_num"`
	PageSize            int32    `json:"page_size"`
	RefundType          int32    `json:"refund_type"`           // 款项类型(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
	ApproveStatus       int32    `json:"approve_status"`        //审批状态(1=待审批;2=待支款;3=支款完成;4=审核驳回);
	TransactionNo       string   `json:"transaction_no"`        //交易单号
	Currency            string   `json:"currency"`              //币种
	PaymentAccountId    int64    `json:"payment_account_id"`    //支款账户ID
	CreatedAtStart      int64    `json:"created_at_start"`      //创建日期开始时间
	CreatedAtEnd        int64    `json:"created_at_end"`        //创建日期结束时间
	PassTimeStart       int64    `json:"pass_time_start"`       //通过日期开始时间
	PassTimeEnd         int64    `json:"pass_time_end"`         //通过日期结束时间
	RejectTimeStart     int64    `json:"reject_time_start"`     //驳回日期开始时间
	RejectTimeEnd       int64    `json:"reject_time_end"`       //驳回日期结束时间
	CompleteTimeStart   int64    `json:"complete_time_start"`   //支款完成结束时间
	CompleteTimeEnd     int64    `json:"complete_time_end"`     //支款完成开始时间
	ServiceName         []string `json:"service_name"`          //服务项目名称
	GoodsName           string   `json:"goods_name"`            //商品名称
	BrandName           string   `json:"brand_name"`            //品牌
	BusinessName        string   `json:"business_name"`         //业务名称
	WorkflowId          int64    `json:"workflow_id"`           //工单ID
	WorkflowNo          string   `json:"workflow_no"`           //工单编号
	WorkflowName        string   `json:"workflow_name"`         //工单名称
	OrderBy             string   `json:"order_by"`              //排序字段 created_at desc
	ApproveStatusList   []int32  `json:"approve_status_list"`   //审批状态(1=待审批;2=待支款;3=支款完成;4=审核驳回);
	PaymentAccountType  int32    `json:"payment_account_type"`  //支款账号类型 1=支付宝 2=微信 3=银行卡
	UserType            int32    `json:"user_type"`             //用户类型（1新客户，2老客户)
	StaffIds            []int64  `json:"staff_ids"`             // 可选 员工ID列表, 调用rpc获取
	UserSource          []int64  `json:"user_source"`           // 客户来源
	UserSourceDepart    []int32  `json:"user_source_depart"`    // 客户来源部门
	OrderSource         []int64  `json:"order_source"`          // 订单来源
	OrderSourceDepart   []int32  `json:"order_source_depart"`   // 订单来源部门
	SubmitDepart        []int32  `json:"submit_depart"`         // 申请人部门
	ApproveBy           int64    `json:"approve_by"`            // 审批人ID
	CustomerName        string   `json:"customer_name"`         //客户姓名
	PaymentAccountIds   []int64  `json:"payment_account_ids"`   //收款账户ID列表
	PaymentAccountTypes []int32  `json:"payment_account_types"` //收款账户类型列表
	RefundDeadlineStart int64    `json:"refund_deadline_start"` //支款截止时间开始时间
	RefundDeadlineEnd   int64    `json:"refund_deadline_end"`   //支款截止时间结束时间
}

type RefundSumRequest struct {
	OrderId       int64  `json:"order_id"`
	OrderNo       string `json:"order_no"`
	CustomerId    int64  `json:"customer_id"`
	ApproveStatus int32  `json:"approve_status"`
	RefundType    int32  `json:"refund_type"`
}

type RefundInfoRequest struct {
	OrderId int64 `json:"order_id"`
}

// RefundCreate 新增收款单
func RefundCreate(ctx context.Context, req *model.FinancialRefund) (int64, error) {
	if req.ApproveLog == "" {
		req.ApproveLog = "[]"
	}
	if req.ScholarshipAgreement == "" {
		req.ScholarshipAgreement = "[]"
	}
	if req.Visa == "" {
		req.Visa = "[]"
	}
	if req.StudentCard == "" {
		req.StudentCard = "[]"
	}
	if req.TuitionPaymentProof == "" {
		req.TuitionPaymentProof = "[]"
	}
	if req.RefundAgreement == "" {
		req.RefundAgreement = "[]"
	}
	if req.TransactionNo == "" {
		req.TransactionNo = "[]"
	}
	//这里写死老客户
	req.UserType = 2
	if err := global.DB.WithContext(ctx).Create(&req).Error; err != nil {
		return req.ID, err
	}
	return req.ID, nil
}

// RefundList 支款单列表
func RefundList(ctx context.Context, req *FinancialRefundListRequest) (int64, []*model.FinancialRefund, error) {
	var total int64
	refundList := make([]*model.FinancialRefund, 0)
	idl := global.DB.Debug().WithContext(ctx).Model(&model.FinancialRefund{})

	// JOIN标记，避免重复JOIN
	var (
		joinedFinancialFund             = false
		joinedFinancialPaid             = false
		joinedOrderGoods                = false
		joinedCustomers                 = false
		joinedCustomerReferralRelation  = false
		joinedSysEmployeeForUserSource  = false
		joinedOrderRelation             = false
		joinedSysEmployeeForOrderSource = false
		joinedSysEmployeeForSubmit      = false
	)

	// 特殊业务逻辑：当查询特定订单ID时，排除第三方申请费
	if req.OrderId != 0 {
		idl.Where("financial_refund.order_id = ?", req.OrderId)
		idl.Where("financial_refund.refund_type != ?", 6) // 排除第三方申请费
	}

	// 使用查询构建器简化其他基础条件
	qb := utils.NewQueryBuilder()
	qb.Equal("financial_refund.id", req.Id).
		Like("financial_refund.order_no", req.OrderNo). // 修正为模糊匹配
		Like("financial_refund.refund_no", req.RefundNo).
		In("financial_refund.submit_id", req.SubmitId).
		Equal("financial_refund.customer_id", req.CustomerId).
		Equal("financial_refund.refund_type", req.RefundType).
		Equal("financial_refund.approve_status", req.ApproveStatus).
		In("financial_refund.approve_status", req.ApproveStatusList).
		Equal("financial_refund.workflow_id", req.WorkflowId).
		Equal("financial_refund.workflow_no", req.WorkflowNo).
		Equal("financial_refund.currency", req.Currency).
		Equal("financial_refund.approve_by", req.ApproveBy).
		Equal("financial_refund.payment_account_id", req.PaymentAccountId).
		Equal("financial_refund.brand_name", req.BrandName).
		Equal("financial_refund.business_name", req.BusinessName).
		TimeBetween("financial_refund.created_at", req.CreatedAtStart, req.CreatedAtEnd).
		TimeBetween("financial_refund.pass_time", req.PassTimeStart, req.PassTimeEnd).
		TimeBetween("financial_refund.reject_time", req.RejectTimeStart, req.RejectTimeEnd).
		TimeBetween("financial_refund.complete_time", req.CompleteTimeStart, req.CompleteTimeEnd).
		TimeBetween("financial_refund.refund_deadline", req.RefundDeadlineStart, req.RefundDeadlineEnd).
		In("financial_refund.handle_by", req.StaffIds).
		Equal("financial_refund.user_type", req.UserType)

	// 应用基础查询条件
	idl = qb.Apply(idl)

	// 处理需要JOIN financial_fund和financial_paid的条件
	needFinancialJoin := len(req.PaymentAccountIds) != 0 || req.TransactionNo != "" || len(req.PaymentAccountTypes) > 0
	if needFinancialJoin {
		if !joinedFinancialFund {
			idl.Joins("LEFT JOIN financial_fund ON financial_refund.order_id = financial_fund.order_id")
			joinedFinancialFund = true
		}
		if !joinedFinancialPaid {
			idl.Joins("LEFT JOIN financial_paid ON financial_paid.financial_fund_id = financial_fund.id").
				Where("financial_paid.deleted_at = ?", 0)
			joinedFinancialPaid = true
		}

		// 原收款账号id列表查询
		if len(req.PaymentAccountIds) != 0 {
			idl.Where("financial_paid.payment_account_id IN (?)", req.PaymentAccountIds)
		}
		// 原收款账号类型查询
		if len(req.PaymentAccountTypes) > 0 {
			// 用快照查询。页面显示的快照
			idl.Where("financial_paid.paid_type IN (?)", req.PaymentAccountTypes)
		}

		// 交易单号查询
		if req.TransactionNo != "" {
			idl.Where("financial_paid.transaction_no = ?", req.TransactionNo)
		}
	}

	// 支款账号类型查询
	if req.PaymentAccountType != 0 {
		idl.Joins("LEFT JOIN financial_account ON financial_refund.payment_account_id = financial_account.id").
			Where("financial_account.account_type = ?", req.PaymentAccountType)
	}

	// 商品名称特殊处理（可能是ID或名称）
	if req.GoodsName != "" {
		goodsId, err := strconv.Atoi(req.GoodsName)
		if err != nil {
			idl.Where("financial_refund.goods_name like ?", "%"+req.GoodsName+"%")
		} else {
			idl.Where("financial_refund.goods_id = ?", goodsId)
		}
	}

	// 服务项目查询
	if len(req.ServiceName) != 0 {
		if !joinedOrderGoods {
			idl.Joins("LEFT JOIN order_goods ON financial_refund.order_id = order_goods.order_id")
			joinedOrderGoods = true
		}
		idl.Where("order_goods.service_name in (?)", req.ServiceName)
	}

	// 客户姓名查询
	if req.CustomerName != "" {
		if !joinedCustomers {
			idl.Joins("LEFT JOIN customers ON financial_refund.customer_id = customers.id")
			joinedCustomers = true
		}
		idl.Where("customers.real_name like ?", "%"+req.CustomerName+"%")
	}

	// 处理客户来源相关查询
	needCustomerReferralJoin := len(req.UserSource) != 0 || len(req.UserSourceDepart) != 0
	if needCustomerReferralJoin {
		if !joinedCustomerReferralRelation {
			idl.Joins("LEFT JOIN customer_referral_relationship ON financial_refund.customer_id = customer_referral_relationship.customer_id")
			joinedCustomerReferralRelation = true
		}

		if len(req.UserSource) != 0 {
			idl.Where("customer_referral_relationship.admin_user_id in (?)", req.UserSource)
		}

		if len(req.UserSourceDepart) != 0 {
			if !joinedSysEmployeeForUserSource {
				idl.Joins("LEFT JOIN sys_employee AS emp_user_source ON customer_referral_relationship.admin_user_id = emp_user_source.id")
				joinedSysEmployeeForUserSource = true
			}
			idl.Where("emp_user_source.dept_id in (?)", req.UserSourceDepart)
		}
	}

	// 处理订单来源相关查询
	needOrderRelationJoin := len(req.OrderSource) != 0 || len(req.OrderSourceDepart) != 0
	if needOrderRelationJoin {
		if !joinedOrderRelation {
			idl.Joins("LEFT JOIN order_relation ON financial_refund.order_id = order_relation.order_id AND order_relation.action = ?", 7)
			joinedOrderRelation = true
		}

		if len(req.OrderSource) != 0 {
			idl.Where("order_relation.user_id in (?)", req.OrderSource)
		}

		if len(req.OrderSourceDepart) != 0 {
			if !joinedSysEmployeeForOrderSource {
				idl.Joins("LEFT JOIN sys_employee AS emp_order_source ON order_relation.user_id = emp_order_source.id")
				joinedSysEmployeeForOrderSource = true
			}
			idl.Where("emp_order_source.dept_id in (?)", req.OrderSourceDepart)
		}
	}

	// 申请人部门查询
	if len(req.SubmitDepart) != 0 {
		if !joinedSysEmployeeForSubmit {
			idl.Joins("LEFT JOIN sys_employee AS emp_submit ON financial_refund.submit_id = emp_submit.id")
			joinedSysEmployeeForSubmit = true
		}
		idl.Where("emp_submit.dept_id in (?)", req.SubmitDepart)
	}

	if req.OrderBy != "" {
		idl.Order("financial_refund." + req.OrderBy)
	} else {
		idl.Order("financial_refund.id DESC")
	}

	// 使用DISTINCT确保结果唯一性
	idl.Group("financial_refund.id")
	err := idl.Count(&total).Offset(int((req.PageNum - 1) * req.PageSize)).Limit(int(req.PageSize)).Find(&refundList).Error
	if err != nil {
		return 0, nil, err
	}
	return total, refundList, nil
}

// RefundInfo 支款单详情
func RefundInfo(ctx context.Context, req *order.FinancialRefundInfoReq) (*model.FinancialRefund, error) {
	Refund := &model.FinancialRefund{}
	idl := global.DB.WithContext(ctx).Model(&model.FinancialRefund{})
	flag := 1
	if req.GetRefundNo() != "" {
		idl.Where("refund_no = ?", req.GetRefundNo())
		flag = 0
	}
	if req.GetId() != 0 {
		idl.Where("id = ?", req.GetId())
		flag = 0
	}
	if req.GetOrderId() != 0 {
		idl.Where("order_id = ?", req.GetOrderId())
		flag = 0
	}
	if req.GetApproveStatus() != 0 {
		idl.Where("approve_status = ?", req.GetApproveStatus())
		flag = 0
	}
	if req.GetRefundType() != 0 {
		idl.Where("refund_type = ?", req.GetRefundType())
		flag = 0
	}
	if flag == 1 {
		return Refund, nil
	}
	err := idl.Order("id desc").First(&Refund).Error
	if err != nil {
		return Refund, err
	}
	return Refund, nil
}

// RefundInfoByOrder  支款订单单详情
func RefundInfoByOrder(ctx context.Context, req *RefundInfoRequest) (*model.FinancialRefund, error) {
	Refund := &model.FinancialRefund{}
	err := global.DB.WithContext(ctx).
		Model(&model.FinancialRefund{}).
		Where("order_id = ?", req.OrderId).
		Where("approve_status = ?", 4).
		Order("id desc").
		First(&Refund).
		Error
	if err != nil {
		return Refund, err
	}
	return Refund, nil
}

// RefundUpdate 更新支款单
func RefundUpdate(ctx context.Context, req *model.FinancialRefund) error {
	return global.DB.WithContext(ctx).Where("id = ?", req.ID).Updates(req).Error
}

// RefundUpdateFields 更新指定字段
func RefundUpdateFields(ctx context.Context, id int64, fields map[string]interface{}) error {
	return global.DB.WithContext(ctx).Model(&model.FinancialRefund{}).Where("id = ?", id).Updates(fields).Error
}

// RefundDel 删除支款单
func RefundDel(ctx context.Context, id int64) error {
	return global.DB.WithContext(ctx).Where("id = ?", id).Delete(&model.FinancialRefund{}).Error
}

// RefundAssociateList 关联支款单列表
func RefundAssociateList(ctx context.Context, req *FinancialRefundListRequest) ([]*model.FinancialRefund, error) {
	refundList := make([]*model.FinancialRefund, 0)
	idl := global.DB.WithContext(ctx).Model(&model.FinancialRefund{})
	if req.OrderId != 0 {
		idl.Where("Order_id = ?", req.OrderId)
	}
	if req.Id != 0 {
		idl.Where("id <> ?", req.Id)
	}
	err := idl.Order("id desc").Find(&refundList).Error
	if err != nil {
		return nil, err
	}
	return refundList, nil
}

// RefundScholarshipAmount 获取已经退款总额
func RefundScholarshipAmount(ctx context.Context, req *RefundSumRequest) (string, error) {
	amountTotal := ""
	idl := global.DB.WithContext(ctx).Model(&model.FinancialRefund{})
	if req.OrderId != 0 {
		idl.Where("order_id = ?", req.OrderId)
	}
	if req.CustomerId != 0 {
		idl.Where("customer_id = ?", req.CustomerId)
	}
	if req.ApproveStatus != 0 {
		idl.Where("approve_status = ?", req.ApproveStatus)
	}
	if req.RefundType != 0 {
		idl.Where("refund_type = ?", req.RefundType)
	}
	if req.OrderNo != "" {
		idl.Where("order_no = ?", req.OrderNo)
	}
	err := idl.Select("IFNULL(SUM(real_amount_rmb),'0.00000') as amount_total").
		Scan(&amountTotal).
		Error
	return amountTotal, err
}

// RefundListByOrder 获取订单最新的已经支款完成的
func RefundListByOrder(ctx context.Context, OrderIDs []int64) ([]*model.FinancialRefund, error) {
	refundList := make([]*model.FinancialRefund, 0)
	idl := global.DB.WithContext(ctx)
	err := idl.Raw("SELECT * FROM financial_refund WHERE id IN (SELECT MAX(id) AS max_id FROM financial_refund where order_id in ? and refund_type<>6 and approve_status = 3 GROUP BY order_id)", OrderIDs).Find(&refundList).Error
	if err != nil {
		return nil, err
	}
	return refundList, nil
}

// RefundInfoByTransactionNo 支款单详情
func RefundInfoByTransactionNo(ctx context.Context, transactionNo string) (*model.FinancialRefund, error) {
	Refund := &model.FinancialRefund{}
	idl := global.DB.WithContext(ctx).Model(&model.FinancialRefund{})
	idl.Where("transaction_no = ?", transactionNo)
	err := idl.Order("id desc").First(&Refund).Error
	if err != nil {
		return Refund, err
	}
	return Refund, nil
}
