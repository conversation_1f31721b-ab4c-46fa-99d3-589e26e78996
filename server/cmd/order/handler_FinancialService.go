package main

import (
	"context"
	order "uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/server/cmd/order/internal/services"
)

// FinancialServiceImpl implements the last service interface defined in the IDL.
type FinancialServiceImpl struct{}

// AccountCreate implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) AccountCreate(ctx context.Context, req *order.FinancialAccountCreateReq) (resp *order.FinancialAccountCreateRsp, err error) {
	return services.AccountCreate(ctx, req)
}

// AccountInfo implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) AccountInfo(ctx context.Context, req *order.FinancialAccountInfoReq) (resp *order.FinancialAccountInfoRsp, err error) {
	return services.AccountInfo(ctx, req)
}

// AccountList implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) AccountList(ctx context.Context, req *order.FinancialAccountListReq) (resp *order.FinancialAccountListRsp, err error) {
	return services.AccountList(ctx, req)
}

// AccountDelete implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) AccountDelete(ctx context.Context, req *order.FinancialAccountDeleteReq) (resp *order.FinancialAccountDeleteRsp, err error) {
	return services.AccountDelete(ctx, req)
}

// AccountUpdate implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) AccountUpdate(ctx context.Context, req *order.FinancialAccountUpdateReq) (resp *order.FinancialAccountUpdateRsp, err error) {
	return services.AccountUpdate(ctx, req)
}

// FundCreate implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) FundCreate(ctx context.Context, req *order.FinancialFundCreateReq) (resp *order.FinancialFundCreateRsp, err error) {
	return services.FundCreate(ctx, req)
}

// FundList implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) FundList(ctx context.Context, req *order.FinancialFundListReq) (resp *order.FinancialFundListRsp, err error) {
	return services.FundList(ctx, req)
}

// FundInfo implements the FinancialServiceImpl interface. 这个服务应该只提供收款单信息还是加上收款信息
func (s *FinancialServiceImpl) FundInfo(ctx context.Context, req *order.FinancialFundInfoReq) (resp *order.FinancialFundInfoRsp, err error) {
	return services.FundInfo(ctx, req)
}

// FundApproveCreate implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) FundApproveCreate(ctx context.Context, req *order.FinancialFundApproveCreateReq) (resp *order.FinancialFundApproveCreateRsp, err error) {
	return services.FundApproveCreate(ctx, req)
}

// FundStatusUpdate implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) FundStatusUpdate(ctx context.Context, req *order.FinancialFundStatusUpdateReq) (resp *order.FinancialFundStatusUpdateRsp, err error) {
	return services.FundStatusUpdate(ctx, req)
}

// FundApproveInfo implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) FundApproveInfo(ctx context.Context, req *order.FinancialFundApproveInfoReq) (resp *order.FinancialFundApproveInfo, err error) {
	return services.FundApproveInfo(ctx, req)
}

// FundApproveList implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) FundApproveList(ctx context.Context, req *order.FinancialFundApproveListReq) (resp *order.FinancialFundApproveListRsp, err error) {
	return services.FundApproveList(ctx, req)
}

// RefundCreate implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) RefundCreate(ctx context.Context, req *order.FinancialRefundCreateReq) (resp *order.FinancialRefundCreateRsp, err error) {
	return services.RefundCreate(ctx, req)
}

// RefundList implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) RefundList(ctx context.Context, req *order.FinancialRefundListReq) (resp *order.FinancialRefundListRsp, err error) {
	return services.RefundList(ctx, req)
}

// RefundInfo implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) RefundInfo(ctx context.Context, req *order.FinancialRefundInfoReq) (resp *order.FinancialRefundInfoRsp, err error) {
	return services.RefundInfo(ctx, req)
}

// RefundApproveCreate implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) RefundApproveCreate(ctx context.Context, req *order.FinancialRefundApproveCreateReq) (resp *order.FinancialRefundApproveCreateRsp, err error) {
	return (&services.FinancialRefundApproveService{}).RefundApproveCreate(ctx, req)
}

// RefundStatusUpdate implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) RefundStatusUpdate(ctx context.Context, req *order.FinancialRefundStatusUpdateReq) (resp *order.FinancialRefundStatusUpdateRsp, err error) {
	return
}

// RefundApproveInfo implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) RefundApproveInfo(ctx context.Context, req *order.FinancialRefundApproveInfoReq) (resp *order.FinancialRefundApproveInfo, err error) {
	return (&services.FinancialRefundApproveService{}).RefundApproveInfo(ctx, req)
}

// RefundApproveList implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) RefundApproveList(ctx context.Context, req *order.FinancialRefundApproveListReq) (resp *order.FinancialRefundApproveListRsp, err error) {
	return (&services.FinancialRefundApproveService{}).RefundApproveList(ctx, req)
}

// RefundOperationLogCreate implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) RefundOperationLogCreate(ctx context.Context, req *order.FinancialOperationLogCreateReq) (resp *order.FinancialOperationLogCreateRsp, err error) {
	return (&services.FinancialOperationLogService{}).FinancialOperationLogCreate(ctx, req)
}

// RefundOperationLogList implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) RefundOperationLogList(ctx context.Context, req *order.FinancialOperationLogListReq) (resp *order.FinancialOperationLogListRsp, err error) {
	return (&services.FinancialOperationLogService{}).FinancialOperationLogList(ctx, req)
}

// FundPaidList implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) FundPaidList(ctx context.Context, req *order.FinancialPaidListReq) (resp *order.FinancialPaidListRsp, err error) {
	return services.FundPaidList(ctx, req.FinancialFundId, req.OrderId, req.FundType)
}

// FundDraftCreate implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) FundDraftCreate(ctx context.Context, req *order.FinancialFundDraftReq) (resp *order.FinancialFundDraftRsp, err error) {
	return services.FundDraftCreate(ctx, req)
}

// GetFundDraft implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) GetFundDraft(ctx context.Context, req *order.GetFinancialFundDraftReq) (resp *order.GetFinancialFundDraftRsp, err error) {
	return services.GetFundDraft(ctx, req)
}

// FundUpdate implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) FundUpdate(ctx context.Context, req *order.FinancialFundUpdateReq) (resp *order.FinancialFundUpdateRsp, err error) {
	return services.FundUpdate(ctx, req)
}

// FundDel implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) FundDel(ctx context.Context, req *order.FinancialFundDelReq) (resp *order.FinancialFundDelRsp, err error) {
	return services.FundDel(ctx, req)
}

// RefundUpdate implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) RefundUpdate(ctx context.Context, req *order.FinancialRefundUpdateReq) (resp *order.FinancialRefundUpdateRsp, err error) {
	return services.RefundUpdate(ctx, req)
}

// RefundDel implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) RefundDel(ctx context.Context, req *order.FinancialRefundDelReq) (resp *order.FinancialRefundDelRsp, err error) {
	return services.RefundDel(ctx, req)
}

// RefundAssociateList implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) RefundAssociateList(ctx context.Context, req *order.FinancialRefundAssociateReq) (resp *order.FinancialRefundAssociateRsp, err error) {
	return services.RefundAssociateList(ctx, req)
}

// RefundScholarshipAmount implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) RefundScholarshipAmount(ctx context.Context, req *order.FinancialRefundSumReq) (resp *order.FinancialRefundSumRsp, err error) {
	return services.RefundScholarshipAmount(ctx, req)
}

// FinancialOperationLogList implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) FinancialOperationLogList(ctx context.Context, req *order.FinancialOperationLogListReq) (resp *order.FinancialOperationLogListRsp, err error) {
	return (&services.FinancialOperationLogService{}).FinancialOperationLogList(ctx, req)
}

// AddFinancialPayment implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) AddFinancialPayment(ctx context.Context, req *order.FinancialPaymentCreateReq) (resp *order.FinancialPaymentCreateRsp, err error) {
	return (&services.FinancialPaymentService{}).AddFinancialPayment(ctx, req)
}

// UpdateFinancialPayment implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) UpdateFinancialPayment(ctx context.Context, req *order.FinancialPaymentUpdateReq) (resp *order.FinancialPaymentUpdateRsp, err error) {
	return (&services.FinancialPaymentService{}).UpdateFinancialPayment(ctx, req)
}

// FinancialPaymentInfo implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) FinancialPaymentInfo(ctx context.Context, req *order.FinancialPaymentInfoReq) (resp *order.FinancialPaymentInfoRsp, err error) {
	return (&services.FinancialPaymentService{}).FinancialPaymentInfo(ctx, req)
}

// ThirdFundCreate implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) ThirdFundCreate(ctx context.Context, req *order.ThirdFundCreateReq) (resp *order.ThirdFundCreateRsp, err error) {
	return services.ThirdFundCreate(ctx, req)
}

// ScholarshipRefundCreate implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) ScholarshipRefundCreate(ctx context.Context, req *order.ScholarshipRefundCreateReq) (resp *order.ScholarshipRefundCreateRsp, err error) {
	return services.ScholarshipRefundCreate(ctx, req)
}

// ThirdRefundCreate implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) ThirdRefundCreate(ctx context.Context, req *order.ThirdRefundCreateReq) (resp *order.ThirdRefundCreateRsp, err error) {
	return services.ThirdRefundCreate(ctx, req)
}

// GetRelationExchangeRate implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) GetRelationExchangeRate(ctx context.Context, req *order.RelationExchangeRateReq) (resp *order.RelationExchangeRateRsp, err error) {
	return services.GetRelationExchangeRate(ctx, req)
}

// GetWorkflowFund implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) GetWorkflowFund(ctx context.Context, req *order.WorkflowFundReq) (resp *order.WorkflowFundRsp, err error) {
	return services.GetWorkflowFund(ctx, req)
}

// GetThirdRefund implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) GetThirdRefund(ctx context.Context, req *order.ThirdRefundReq) (resp *order.ThirdRefundRsp, err error) {
	return services.GetThirdRefund(ctx, req)
}

// GetThirdFundByWorkflow implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) GetThirdFundByWorkflow(ctx context.Context, req *order.ThirdFundByWorkflowReq) (resp *order.ThirdFundByWorkflowRsp, err error) {
	return services.GetThirdFundByWorkflow(ctx, req)
}

// RefundListByOrder implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) RefundListByOrder(ctx context.Context, req *order.RefundListByOrderReq) (resp *order.RefundListByOrderRsp, err error) {
	return services.RefundListByOrder(ctx, req)
}

// UpdateFinancialFundEmployee implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) UpdateFinancialFundEmployee(ctx context.Context, req *order.UpdateFinancialFundEmployeeReq) (resp *order.UpdateFinancialFundEmployeeRsp, err error) {
	return services.UpdateFinancialFundEmployee(ctx, req)
}

// EditApprovedFinancialFund implements the FinancialServiceImpl interface.
func (s *FinancialServiceImpl) EditApprovedFinancialFund(ctx context.Context, req *order.FinancialFundApprovedEditReq) (resp *order.FinancialFundApprovedEditRsp, err error) {
	return services.EditApprovedFinancialFund(ctx, req)
}
