package main

import (
	"context"

	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/server/cmd/order/internal/services"
)

// ServiceImpl implements the last service interface defined in the IDL.
type ServiceImpl struct{}

// GetOrderList implements the ServiceImpl interface.
func (s *ServiceImpl) GetOrderList(ctx context.Context, req *order.GetOrderListReq) (resp *order.GetOrderListRsp, err error) {
	return (&services.OrderService{}).GetOrderList(ctx, req)
}

// GetOrderInfoByIds implements the ServiceImpl interface.
func (s *ServiceImpl) GetOrderInfoByIds(ctx context.Context, req *order.GetOrderInfoByIdsReq) (resp *order.GetOrderInfoByIdsRsp, err error) {
	return (&services.OrderService{}).GetOrderInfoByIds(ctx, req)
}

// GetOrderInfo implements the ServiceImpl interface.
func (s *ServiceImpl) GetOrderInfo(ctx context.Context, req *order.GetOrderInfoReq) (resp *order.GetOrderInfoRsp, err error) {
	return (&services.OrderService{}).GetOrderInfo(ctx, req)
}

// SaveOrder implements the ServiceImpl interface.
func (s *ServiceImpl) SaveOrder(ctx context.Context, req *order.SaveOrderReq) (resp *order.SaveOrderRsp, err error) {
	return (&services.OrderService{}).SaveOrder(ctx, req)
}

// UpdateOrderStatus implements the ServiceImpl interface.
func (s *ServiceImpl) UpdateOrderStatus(ctx context.Context, req *order.UpdateOrderStatusReq) (resp *order.UpdateOrderStatusRsp, err error) {
	return (&services.OrderService{}).UpdateOrderStatus(ctx, req)
}

// GetOrderStatic implements the ServiceImpl interface.
func (s *ServiceImpl) GetOrderStatic(ctx context.Context, req *order.GetOrderStaticReq) (resp *order.GetOrderStaticRsp, err error) {
	return (&services.OrderService{}).GetOrderStatic(ctx, req)
}

// GetCurrencyList implements the ServiceImpl interface.
func (s *ServiceImpl) GetCurrencyList(ctx context.Context, req *order.GetCurrencyListReq) (resp *order.GetCurrencyListRsp, err error) {
	return (&services.OrderService{}).GetCurrencyList(ctx, req)
}

// GetScholarshipToBeDistributed implements the ServiceImpl interface.
func (s *ServiceImpl) GetScholarshipToBeDistributed(ctx context.Context, req *order.GetScholarshipToBeDistributedReq) (resp *order.GetScholarshipToBeDistributedRsp, err error) {
	return (&services.OrderService{}).GetScholarshipToBeDistributed(ctx, req)
}

// GetScholarshipToBeDistributedAmount implements the ServiceImpl interface.
func (s *ServiceImpl) GetScholarshipToBeDistributedAmount(ctx context.Context, req *order.GetScholarshipToBeDistributedAmountReq) (resp *order.GetScholarshipToBeDistributedAmountRsp, err error) {
	return (&services.OrderService{}).GetScholarshipToBeDistributedAmount(ctx, req)
}

// BatchUpdateUpdaterId implements the ServiceImpl interface.
func (s *ServiceImpl) BatchUpdateUpdaterId(ctx context.Context, req *order.BatchUpdateUpdaterIdReq) (resp *order.BatchUpdateUpdaterIdRsp, err error) {
	return (&services.OrderService{}).BatchUpdateUpdaterId(ctx, req)
}

// BatchUpdateWorkflowStatus implements the ServiceImpl interface.
func (s *ServiceImpl) BatchUpdateWorkflowStatus(ctx context.Context, req *order.BatchUpdateWorkflowStatusReq) (resp *order.BatchUpdateWorkflowStatusRsp, err error) {
	return (&services.OrderService{}).BatchUpdateWorkflowStatus(ctx, req)
}

// GetRefundHighRiskCustomers implements the ServiceImpl interface.
func (s *ServiceImpl) GetRefundHighRiskCustomers(ctx context.Context, req *order.GetRefundHighRiskCustomersReq) (resp *order.GetRefundHighRiskCustomersRsp, err error) {
	return (&services.OrderService{}).GetRefundHighRiskCustomers(ctx, req)
}

// GetWorkflowCompleteOrders implements the ServiceImpl interface.
func (s *ServiceImpl) GetWorkflowCompleteOrders(ctx context.Context, req *order.GetWorkflowCompleteOrdersReq) (resp *order.GetWorkflowCompleteOrdersRsp, err error) {
	return (&services.OrderService{}).GetWorkflowCompleteOrders(ctx, req)
}

// GetLatestOrderOperationLogByOrderId implements the ServiceImpl interface.
func (s *ServiceImpl) GetLatestOrderOperationLogByOrderId(ctx context.Context, req *order.GetLatestOrderOperationLogByOrderIdReq) (resp *order.GetLatestOrderOperationLogByOrderIdRsp, err error) {
	return (&services.OrderService{}).GetLatestOrderOperationLogByOrderId(ctx, req)
}

// GetLatestOrderInfoByCustomerIds implements the ServiceImpl interface.
func (s *ServiceImpl) GetLatestOrderInfoByCustomerIds(ctx context.Context, req *order.GetLatestOrderInfoByCustomerIdsReq) (resp *order.GetLatestOrderInfoByCustomerIdsRsp, err error) {
	return (&services.OrderService{}).GetLatestOrderInfoByCustomerIds(ctx, req)
}

// GetRedLineRiskCustomers implements the ServiceImpl interface.
func (s *ServiceImpl) GetRedLineRiskCustomers(ctx context.Context, req *order.GetRedLineRiskCustomersReq) (resp *order.GetRedLineRiskCustomersRsp, err error) {
	return (&services.OrderService{}).GetRedLineRiskCustomers(ctx, req)
}

// GetOldNewCustomers implements the ServiceImpl interface.
func (s *ServiceImpl) GetOldNewCustomers(ctx context.Context, req *order.GetOldNewCustomersReq) (resp *order.GetOldNewCustomersRsp, err error) {
	return (&services.OrderService{}).GetOldNewCustomers(ctx, req)
}

// GetOrderCountByCustomerId implements the ServiceImpl interface.
func (s *ServiceImpl) GetOrderCountByCustomerId(ctx context.Context, req *order.GetOrderCountByCustomerIdReq) (resp *order.GetOrderCountByCustomerIdRsp, err error) {
	return (&services.OrderService{}).GetOrderCountByCustomerId(ctx, req)
}

// ResetCompleteWorkflowStatus implements the ServiceImpl interface.
func (s *ServiceImpl) ResetCompleteWorkflowStatus(ctx context.Context, req *order.ResetCompleteWorkflowStatusReq) (resp *order.ResetCompleteWorkflowStatusRsp, err error) {
	return (&services.OrderService{}).ResetCompleteWorkflowStatus(ctx, req)
}

// GetOrderCountByCustomerIds implements the ServiceImpl interface.
func (s *ServiceImpl) GetOrderCountByCustomerIds(ctx context.Context, req *order.GetOrderCountByCustomerIdsReq) (resp *order.GetOrderCountByCustomerIdsRsp, err error) {
	return (&services.OrderService{}).GetOrderCountByCustomerIds(ctx, req)
}

// GetOrderOperationLogList implements the ServiceImpl interface.
func (s *ServiceImpl) GetOrderOperationLogList(ctx context.Context, req *order.GetOrderOperationLogListReq) (resp *order.GetOrderOperationLogListRsp, err error) {
	return (&services.OrderOperationLogService{}).GetOrderOperationLogList(ctx, req)
}

// GetOrderListApp implements the ServiceImpl interface.
func (s *ServiceImpl) GetOrderListApp(ctx context.Context, req *order.GetOrderListAppReq) (resp *order.GetOrderListAppRsp, err error) {
	return (&services.OrderService{}).GetOrderListApp(ctx, req)
}

// BatchUpdateOrderOwnId implements the ServiceImpl interface.
func (s *ServiceImpl) BatchUpdateOrderOwnId(ctx context.Context, req *order.BatchUpdateOrderOwnIdReq) (resp *order.BatchUpdateOrderOwnIdRsp, err error) {
	return (&services.OrderService{}).BatchUpdateOrderOwnId(ctx, req)
}

// UpdateOrderRelation implements the ServiceImpl interface.
func (s *ServiceImpl) UpdateOrderRelation(ctx context.Context, req *order.UpdateOrderRelationReq) (resp *order.UpdateOrderRelationRsp, err error) {
	return (&services.OrderService{}).UpdateOrderRelation(ctx, req)
}
