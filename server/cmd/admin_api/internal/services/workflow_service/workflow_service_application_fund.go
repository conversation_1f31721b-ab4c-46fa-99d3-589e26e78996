package workflow_service

import (
	"context"
	"encoding/json"

	"uofferv2/kitex_gen/server/cmd/customer"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	log "github.com/sirupsen/logrus"

	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/kitex_gen/server/cmd/workflow"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/ctxmeta"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"
	"uofferv2/server/cmd/admin_api/internal/global"
	"uofferv2/server/cmd/admin_api/internal/services/httputil"
	"uofferv2/server/cmd/admin_api/internal/services/tools_service"
)

// --------------------------------------第三方申请费相关

// UpdateWorkflowApplyFundCurrencyInfo .
// @router /workflow/UpdateWorkflowApplyFundCurrencyInfo [POST]
func UpdateWorkflowApplyFundCurrencyInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.UpdateWorkflowApplyFundCurrencyInfoReq
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}
	// 判断申请在不在
	rpcResp, err := global.WorkflowClient.GetWorkflowApply(ctx, &workflow.GetWorkflowApplyReq{
		WorkflowId: req.GetWorkflowId(),
	})
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil || rpcResp.WorkflowId == 0 {
		logger.CtxErrorf(ctx, "GetWorkflowApply rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	// 判断能否变更 todo 临时取消限制，已开会沟通，后面等产品方案确定限制
	//fundInfo := rpcResp.GetFundInfo()
	//if fundInfo.FundBindType != 1 && fundInfo.FundBindType != 2 {
	//	httputil.ResponseParamsErr(c, "非新增类型或绑定类型，不能填写相关金额")
	//	return
	//}
	// 调用WorkflowClient修改
	UpdateWorkflowApplyRpcResp, err := global.WorkflowClient.UpdateWorkflowApply(ctx, &workflow.UpdateWorkflowApplyReq{
		WorkflowId: req.GetWorkflowId(),
		FundCurrencyInfo: &workflow.FundCurrencyInfo{
			FundCurrencyCode:  req.GetFundCurrencyCode(),
			FundCurrencyValue: req.GetFundCurrencyValue(),
		},
	})
	errWrap = coderror.Warp(UpdateWorkflowApplyRpcResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "UpdateWorkflowApply rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	resp := new(admin_api.UpdateWorkflowApplyFundCurrencyInfoRsp)
	httputil.ResponseSuccess(c, resp)
}

// AddFundWithWorkflowApply 处理添加资金申请
func AddFundWithWorkflowApply(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.AddFundWithWorkflowApplyReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	// 判断申请在不在
	rpcResp, err := global.WorkflowClient.GetWorkflowApply(ctx, &workflow.GetWorkflowApplyReq{
		WorkflowId: req.GetWorkflowId(),
	})
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil || rpcResp.WorkflowId == 0 {
		logger.CtxErrorf(ctx, "GetWorkflowApply rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	// 获取工单详情信息
	wfRpcResp, err := global.WorkflowClient.GetWorkflowDetail(ctx, &workflow.GetWorkflowDetailReq{
		WorkflowId: req.GetWorkflowId(),
	})
	errWrap = coderror.Warp(wfRpcResp, err)
	if errWrap != nil || rpcResp.WorkflowId == 0 {
		logger.CtxErrorf(ctx, "GetWorkflowDetail rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}

	// 获取工单详情信息
	orderRpcResp, err := global.OrderClient.GetOrderInfo(ctx, &order.GetOrderInfoReq{
		Id:      req.OrderId,
		OrderNo: req.OrderNo,
	})
	errWrap = coderror.Warp(wfRpcResp, err)
	if errWrap != nil || rpcResp.WorkflowId == 0 {
		logger.CtxErrorf(ctx, "GetOrderInfo rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	// 判断能否变更
	//fundInfo := rpcResp.GetFundInfo()
	//if fundInfo.FundBindType > 0 {
	//	httputil.ResponseParamsErr(c, "请判断当前节点状态，之前已操作，不可再新增")
	//	return
	//}
	// 调用order rpc 新建收款单
	// 调用WorkflowClient修改免审理由
	employeeId := ctxmeta.MustGetAuth(ctx).EmployeeId()
	customerRpcResp, err := global.CustomerProfileClient.GetCustomerDetail(ctx, &customer.GetCustomerDetailReq{
		Id: req.CustomerId,
	})
	errWrap = coderror.Warp(customerRpcResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "GetCustomerDetail rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	customerInfo := customerRpcResp.GetCustomerInfo() //客户信息
	rpcReq := &order.ThirdFundCreateReq{
		CustomerId:        req.CustomerId,
		Currency:          req.Currency,
		ExchangeRate:      req.ExchangeRate,
		RealAmountOther:   req.RealAmountOther,
		PaidTime:          req.PaidTime,
		SubmitId:          employeeId,
		FinancialPaidInfo: []*order.FinancialPaidInfo{},
		ServiceName:       req.ServiceName,
		GoodsName:         req.GoodsName,
		BrandName:         req.BrandName,
		BusinessName:      req.BusinessName,
		GoodsSpecsName:    req.GoodsSpecsName,
		Num:               req.Num,
		WorkflowId:        req.GetWorkflowId(),
		WorkflowName:      wfRpcResp.Workflow.WorkflowName,
		RealAmountRmb:     req.RealAmountRmb,
		WorkflowNo:        wfRpcResp.Workflow.WorkflowNo,
		OrderId:           req.OrderId,
		OrderNo:           req.OrderNo,
		UserType:          int32(customerInfo.UserType),
		Remark:            req.Remark,
	}
	for _, paidInfo := range req.FinancialPaidInfo {
		addInfo := &order.FinancialPaidInfo{
			FinancialFundId:  paidInfo.FinancialFundId,
			PaymentAccountId: paidInfo.PaymentAccountId,
			PaidType:         paidInfo.PaidType,
			Currency:         paidInfo.Currency,
			ExchangeRate:     paidInfo.ExchangeRate,
			AmountCny:        paidInfo.AmountCny,
			AmountOther:      paidInfo.AmountOther,
			ImagesPath:       "",
			AccountName:      paidInfo.AccountName,
			TransactionNo:    paidInfo.TransactionNo,
			Id:               paidInfo.Id,
		}
		images := paidInfo.ImagesPath
		if len(images) > 0 {
			str, marErr := json.Marshal(images)
			if marErr == nil {
				addInfo.ImagesPath = string(str)
			}
		}
		rpcReq.FinancialPaidInfo = append(rpcReq.FinancialPaidInfo, addInfo)
	}
	rpcReq.SubmitId = GetUserId(ctx, req.SubmitId)
	fundRpcInfo, err := global.FinancialClient.ThirdFundCreate(ctx, rpcReq)
	errWrap = coderror.Warp(fundRpcInfo, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "ThirdFundCreate rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}

	//发邮件方法调整
	var sendEmailReq tools_service.FinancialEmailReq
	sendEmailReq.CustomerId = req.CustomerId
	sendEmailReq.EmployeeId = employeeId
	sendEmailReq.OrderNo = req.OrderNo
	sendEmailReq.PayTime = req.PaidTime
	sendEmailReq.ContractAmount = orderRpcResp.OrderPay.AmountContract //
	sendEmailReq.FundType = 4
	// todo order那边是没有名字的
	sendEmailReq.OrderName = orderRpcResp.Order.ServiceName
	sendEmailReq.AmountInfo = rpcReq.FinancialPaidInfo
	sendEmailReq.ServiceName = "第三方申请费"

	// TODO 发邮件应该不阻塞主干
	err = tools_service.SendFinancialEmail(ctx, sendEmailReq)
	if err != nil {
		logger.CtxErrorf(ctx, "%s global.tools.SendReq rpc error: %v", errWrap.Error())
	}

	// 调用WorkflowClient修改免审理由
	updateResp, err := global.WorkflowClient.UpdateWorkflowApply(ctx, &workflow.UpdateWorkflowApplyReq{
		WorkflowId: req.GetWorkflowId(),
		FundInfo: &workflow.FundInfo{
			FundId:         fundRpcInfo.Id,
			FreeFundReason: "",
			// todo 拉出去声明
			FundBindType: 1,
		},
	})
	errWrap = coderror.Warp(updateResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "UpdateWorkflowApply rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}

	// todo fundInfo 绑定到
	resp := new(admin_api.AddFundWithWorkflowApplyRsp)
	// 这里添加处理逻辑

	httputil.ResponseSuccess(c, resp)
}

// BindFundWithWorkflowApply 处理绑定资金申请
func BindFundWithWorkflowApply(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.BindFundWithWorkflowApplyReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	// 判断申请在不在
	rpcResp, err := global.WorkflowClient.GetWorkflowApply(ctx, &workflow.GetWorkflowApplyReq{
		WorkflowId: req.GetWorkflowId(),
	})
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil || rpcResp.WorkflowId == 0 {
		logger.CtxErrorf(ctx, "GetWorkflowApply rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	// 判断能否变更
	fundInfo := rpcResp.GetFundInfo()
	if fundInfo.FundBindType == 1 || fundInfo.FundBindType == 3 {
		httputil.ResponseParamsErr(c, "非绑定类型操作，不能换绑")
		return
	}
	// 调用WorkflowClient修改免审理由
	updateResp, err := global.WorkflowClient.UpdateWorkflowApply(ctx, &workflow.UpdateWorkflowApplyReq{
		WorkflowId: req.GetWorkflowId(),
		FundInfo: &workflow.FundInfo{
			FundId:         req.FinancialFundId,
			FreeFundReason: "",
			// todo 拉出去声明
			FundBindType: 2,
		},
	})
	errWrap = coderror.Warp(updateResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "UpdateWorkflowApply rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	// todo 看返回什么
	resp := new(admin_api.BindFundWithWorkflowApplyRsp)

	// 这里添加处理逻辑
	httputil.ResponseSuccess(c, resp)
}

// CancelFundWithWorkflowApply 处理取消资金申请
func CancelFundWithWorkflowApply(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.CancelFundWithWorkflowApplyReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	// 判断申请在不在
	rpcResp, err := global.WorkflowClient.GetWorkflowApply(ctx, &workflow.GetWorkflowApplyReq{
		WorkflowId: req.GetWorkflowId(),
	})
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil || rpcResp.WorkflowId == 0 {
		logger.CtxErrorf(ctx, "GetWorkflowApply rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	// 判断能否变更
	fundInfo := rpcResp.GetFundInfo()
	if fundInfo.FundBindType == 1 || fundInfo.FundBindType == 2 {
		httputil.ResponseParamsErr(c, "已绑定收款单，不能免费")
		return
	}
	// 调用WorkflowClient修改免审理由
	updateResp, err := global.WorkflowClient.UpdateWorkflowApply(ctx, &workflow.UpdateWorkflowApplyReq{
		WorkflowId: req.GetWorkflowId(),
		FundInfo: &workflow.FundInfo{
			// todo 拉出去声明
			FundId:         -1,
			FreeFundReason: req.GetFreeReason(),
			FundBindType:   3,
		},
	})
	errWrap = coderror.Warp(updateResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "UpdateWorkflowApply rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	// todo 看返回什么
	resp := new(admin_api.CancelFundWithWorkflowApplyRsp)
	// 这里添加处理逻辑
	httputil.ResponseSuccess(c, resp)
}

// GetApplyWorkflowFundInfo .
// @router /workflow/GetApplyWorkflowFundInfo [POST]
func GetApplyWorkflowFundInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.GetApplyWorkflowFundInfoReq
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}
	rpcResp, err := global.WorkflowClient.GetWorkflowApply(ctx, &workflow.GetWorkflowApplyReq{
		WorkflowId: req.GetWorkflowId(),
	})
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "GetWorkflowApply rpc internal error: %v", err)
		httputil.ResponseInternalErr(c)
		return
	}
	fundInfo := rpcResp.GetFundInfo()
	apiResp := admin_api.WorkflowFundEntity{
		FundId:            fundInfo.FundId,
		FreeReason:        fundInfo.FreeFundReason,
		FundBindType:      fundInfo.FundBindType,
		FundCurrencyCode:  fundInfo.FundCurrencyCode,
		FundCurrencyValue: fundInfo.FundCurrencyValue,
	}

	httputil.ResponseSuccess(c, &apiResp)
}

// AddFinancialRefundWithWorkflowApply .
// @router /workflow/AddFinancialRefundWithWorkflowApply [POST]
func AddFinancialRefundWithWorkflowApply(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.AddFinancialRefundWithWorkflowApplyReq
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}
	// 判断申请在不在
	rpcResp, err := global.WorkflowClient.GetWorkflowApply(ctx, &workflow.GetWorkflowApplyReq{
		WorkflowId: req.GetWorkflowId(),
	})
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil || rpcResp.WorkflowId == 0 {
		logger.CtxErrorf(ctx, "GetWorkflowApply rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	// 获取工单详情信息
	wfRpcResp, err := global.WorkflowClient.GetWorkflowDetail(ctx, &workflow.GetWorkflowDetailReq{
		WorkflowId: req.GetWorkflowId(),
	})
	errWrap = coderror.Warp(wfRpcResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "GetWorkflowDetail rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	// 判断能否变更 todo 这里前端之前说临时放开
	//fundInfo := rpcResp.GetFundInfo()
	//if fundInfo.FundBindType > 0 {
	//	httputil.ResponseParamsErr(c, "请判断当前节点状态，之前已操作，不可再新增")
	//	return
	//}

	fundInfo, err := global.FinancialClient.FundInfo(ctx, &order.FinancialFundInfoReq{OrderId: req.OrderId})
	if err != nil {
		logger.CtxErrorf(ctx, "global.FinancialClient.FundInfo rpc error: %v", err)
		httputil.ResponseInternalErr(c)
		return
	}
	if fundInfo == nil {
		logger.CtxErrorf(ctx, "global.FinancialClient.FundInfo rpc error: %v", err)
		httputil.ResponseInternalErr(c)
		return
	}
	approveLog := "[]"
	if len(req.ApproveLog) > 0 {
		newApproveLog, err := GenerateThumbnail(ctx, req.ApproveLog)
		if err != nil {
			log.Errorf("GenerateThumbnail error: %v", err)
		} else {
			req.ApproveLog = newApproveLog
		}
		if jsonBytes, err := json.Marshal(req.ApproveLog); err == nil {
			approveLog = string(jsonBytes)
		}
	}
	refundAgreement := "[]"
	if len(req.RefundAgreement) > 0 {
		newRefundAgreement, err := GenerateThumbnail(ctx, req.RefundAgreement)
		if err != nil {
			log.Errorf("GenerateThumbnail error: %v", err)
		} else {
			req.RefundAgreement = newRefundAgreement
		}
		if jsonBytes, err := json.Marshal(req.RefundAgreement); err == nil {
			refundAgreement = string(jsonBytes)
		}
	}

	// 根据订单ID，获取订单信息
	respGetOrderInfo, err := global.OrderClient.GetOrderInfo(ctx, &order.GetOrderInfoReq{
		Id: req.OrderId,
	})
	errWrap = coderror.Warp(respGetOrderInfo, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "GetOrderInfo rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}

	// 调用order rpc 新建收款单
	// 调用WorkflowClient修改免审理由
	employeeId := ctxmeta.MustGetAuth(ctx).EmployeeId()
	customerRpcResp, err := global.CustomerProfileClient.GetCustomerDetail(ctx, &customer.GetCustomerDetailReq{
		Id: fundInfo.CustomerId,
	})
	errWrap = coderror.Warp(customerRpcResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "GetCustomerDetail rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	customerInfo := customerRpcResp.GetCustomerInfo() //客户信息
	createInfo, err := global.FinancialClient.RefundCreate(ctx, &order.FinancialRefundCreateReq{
		OrderNo:         req.OrderNo,
		OrderId:         req.OrderId,
		CustomerId:      fundInfo.CustomerId,
		Currency:        req.Currency,
		RealAmountOther: req.RealAmountOther,
		//RealAmountRmb:            req.AmountDisbursementReview,
		ExchangeRate: "1.000000",
		// 固定6 第三方
		RefundType:               6,
		SubmitId:                 employeeId,
		ApproveBy:                employeeId,
		RefundReceiveAccountType: req.RefundReceiveAccountType,
		RefundReceiveAccount:     req.RefundReceiveAccount,
		RefundReason:             req.RefundReason,
		RefundAgreement:          refundAgreement,
		ApproveLog:               approveLog,
		//ScholarshipAgreement:     scholarshipAgreement,
		//Visa:                     visa,
		//StudentCard:              studentCard,
		//TuitionPaymentProof:      tuitionPaymentProof,
		WorkflowNo:     wfRpcResp.Workflow.WorkflowNo,
		WorkflowId:     req.WorkflowId,
		WorkflowName:   wfRpcResp.Workflow.WorkflowName,
		ContractAmount: respGetOrderInfo.OrderPay.AmountContract,
		BrandName:      respGetOrderInfo.GetOrder().GetBrandName(),
		ServiceName:    respGetOrderInfo.GetOrder().GetServiceName(),
		BusinessName:   respGetOrderInfo.GetOrder().GetBusinessName(),
		GoodsName:      respGetOrderInfo.GetOrderGoods()[0].GoodsName,
		GoodsSpecsName: respGetOrderInfo.GetOrderGoods()[0].GoodsSpec,
		Num:            respGetOrderInfo.GetOrderGoods()[0].GoodsNum,
		GoodsId:        respGetOrderInfo.GetOrderGoods()[0].GoodsId,
		UserType:       int32(customerInfo.UserType),
		RefundDeadline: req.DeadlineTime,
	})
	errWrap = coderror.Warp(createInfo, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "RefundCreate rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}

	// 绑定
	var refundIds []int64 = make([]int64, 0)
	if jsonErr := json.Unmarshal([]byte(rpcResp.FundInfo.RefundIdsJson), &refundIds); jsonErr != nil {
		logger.CtxErrorf(ctx, "json.Unmarshal error: %v", jsonErr)
		refundIds = []int64{createInfo.Id}
	} else {
		refundIds = append(refundIds, createInfo.Id)
	}
	var refundIdsJson []byte
	refundIdsJson, _ = json.Marshal(refundIds)
	updateResp, err := global.WorkflowClient.UpdateWorkflowApply(ctx, &workflow.UpdateWorkflowApplyReq{
		WorkflowId: req.WorkflowId,
		RefundInfo: &workflow.RefundInfo{
			RefundIdsJson: string(refundIdsJson),
		},
	})
	errWrap = coderror.Warp(updateResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "UpdateWorkflowApply rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	//refundInfo.
	resp := new(admin_api.AddFinancialRefundWithWorkflowApplyRsp)
	httputil.ResponseSuccess(c, resp)
}

// UpdateFinancialRefundWithWorkflowApply .
// @router /workflow/UpdateFinancialRefundWithWorkflowApply [POST]
func UpdateFinancialRefundWithWorkflowApply(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.UpdateFinancialRefundWithWorkflowApplyReq
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}
	// 判断申请在不在
	rpcResp, err := global.WorkflowClient.GetWorkflowApply(ctx, &workflow.GetWorkflowApplyReq{
		WorkflowId: req.GetWorkflowId(),
	})
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil || rpcResp.WorkflowId == 0 {
		logger.CtxErrorf(ctx, "GetWorkflowApply rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	// 判断能否变更 todo 这里前端之前说临时放开
	//fundInfo := rpcResp.GetFundInfo()
	//if fundInfo.FundBindType > 0 {
	//	httputil.ResponseParamsErr(c, "请判断当前节点状态，之前已操作，不可再新增")
	//	return
	//}
	approveLog := "[]"
	if len(req.ApproveLog) > 0 {
		if jsonBytes, err := json.Marshal(req.ApproveLog); err == nil {
			approveLog = string(jsonBytes)
		}
	}
	refundAgreement := "[]"
	if len(req.RefundAgreement) > 0 {
		if jsonBytes, err := json.Marshal(req.RefundAgreement); err == nil {
			refundAgreement = string(jsonBytes)
		}
	}

	// 调用order rpc 新建收款单
	// 调用WorkflowClient修改免审理由
	//employeeId := ctxmeta.MustGetAuth(ctx).EmployeeId()
	updateResp, err := global.FinancialClient.RefundUpdate(ctx, &order.FinancialRefundUpdateReq{
		//OrderNo:                  respSaveOrder.GetOrderNo(), rpc自动生成
		//OrderId:                  respSaveOrder.GetId(), rpc自动生成
		//CustomerId:      fundInfo.CustomerId,
		Currency:        req.Currency,
		RealAmountOther: req.RealAmountOther,
		ApproveStatus:   1,
		//RealAmountRmb:            req.AmountDisbursementReview,
		//ExchangeRate: "1.000000",
		// 固定6 第三方
		RefundType: 6,
		//SubmitId:                 employeeId,
		//ApproveBy:                employeeId,
		RefundReceiveAccountType: req.RefundReceiveAccountType,
		RefundReceiveAccount:     req.RefundReceiveAccount,
		RefundReason:             req.RefundReason,
		RefundAgreement:          refundAgreement,
		ApproveLog:               approveLog,
		//ScholarshipAgreement:     scholarshipAgreement,
		//Visa:                     visa,
		//StudentCard:              studentCard,
		//TuitionPaymentProof:      tuitionPaymentProof,
		//WorkflowNo:     req.WorkflowNo,
		//WorkflowId:     req.WorkflowId,
		//WorkflowName:   req.WorkflowName,
		//ContractAmount: respGetOrderInfo.OrderPay.AmountContract,
		//BrandName:      respGetOrderInfo.GetOrder().GetBrandName(),
		//ServiceName:    respGetOrderInfo.GetOrder().GetServiceName(),
		//BusinessName:   respGetOrderInfo.GetOrder().GetBusinessName(),
		//GoodsName:      respGetOrderInfo.GetOrderGoods()[0].GoodsName,
		//GoodsSpecsName: respGetOrderInfo.GetOrderGoods()[0].GoodsSpec,
		//Num:            respGetOrderInfo.GetOrderGoods()[0].GoodsNum,
		//GoodsId:        respGetOrderInfo.GetOrderGoods()[0].GoodsId,
		RefundDeadline: req.RefundDeadline,
	})
	errWrap = coderror.Warp(updateResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "RefundUpdate rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}

	resp := new(admin_api.UpdateFinancialRefundWithWorkflowApplyRsp)
	httputil.ResponseSuccess(c, resp)
}

// DeleteFinancialRefundWithWorkflowApply .
// @router /workflow/DeleteFinancialRefundWithWorkflowApply [POST]
func DeleteFinancialRefundWithWorkflowApply(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.DeleteFinancialRefundWithWorkflowApplyReq
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}
	// 判断申请在不在
	rpcResp, err := global.WorkflowClient.GetWorkflowApply(ctx, &workflow.GetWorkflowApplyReq{
		WorkflowId: req.GetWorkflowId(),
	})
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil || rpcResp.WorkflowId == 0 {
		logger.CtxErrorf(ctx, "GetWorkflowApply rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	// 删除收款单
	// 绑定
	var refundIds []int64 = make([]int64, 0)
	jsonErr := json.Unmarshal([]byte(rpcResp.FundInfo.RefundIdsJson), &refundIds)
	if jsonErr != nil {
		logger.CtxErrorf(ctx, "绑定的workflow订单转换失败: %v", jsonErr)
		httputil.ResponseInternalErr(c)
		return
	}

	var newRefundIds []int64 = make([]int64, 0)
	for _, v := range refundIds {
		if v != req.GetRefundId() {
			newRefundIds = append(newRefundIds, v)
		}
	}
	if len(refundIds) == len(newRefundIds) {
		httputil.ResponseParamsErr(c, "工单没有绑定该支款单")
		return
	}

	var refundIdsJson []byte
	refundIdsJson, _ = json.Marshal(newRefundIds)
	delResp, err := global.FinancialClient.RefundDel(ctx, &order.FinancialRefundDelReq{
		FinancialRefundId: req.RefundId,
	})
	errWrap = coderror.Warp(delResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "RefundDel rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}

	updateResp, err := global.WorkflowClient.UpdateWorkflowApply(ctx, &workflow.UpdateWorkflowApplyReq{
		WorkflowId: req.WorkflowId,
		RefundInfo: &workflow.RefundInfo{
			RefundIdsJson: string(refundIdsJson),
		},
	})
	errWrap = coderror.Warp(updateResp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "UpdateWorkflowApply rpc error: %v", errWrap.Error())
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}

	resp := new(admin_api.DeleteFinancialRefundWithWorkflowApplyRsp)
	httputil.ResponseSuccess(c, resp)
}
