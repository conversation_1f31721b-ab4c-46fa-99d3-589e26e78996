package convert

import (
	"context"
	"encoding/json"
	"time"

	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/customer"
	"uofferv2/kitex_gen/server/cmd/message"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/kitex_gen/server/cmd/user"
	"uofferv2/kitex_gen/server/cmd/workflow"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/admin_api/biz/model/constants"
	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"
	"uofferv2/server/cmd/admin_api/internal/global"

	"github.com/shopspring/decimal"
)

func EducationInfo2Api(educationInfo *customer.EducationInfo) *admin_api.EducationInfo {
	if educationInfo == nil {
		return nil
	}
	schoolList := make([]*admin_api.SchoolInfo, len(educationInfo.Schools))
	if len(schoolList) != 0 {
		for i, customerEducationalExperience := range educationInfo.Schools {
			schoolList[i] = &admin_api.SchoolInfo{
				Id:     customerEducationalExperience.Id,
				Name:   customerEducationalExperience.Name,
				Major:  customerEducationalExperience.Major,
				Degree: customerEducationalExperience.Degree,
			}
		}
	}
	otherInfo := &admin_api.OtherInfo{}
	if educationInfo.OtherInfo != nil {
		otherInfo = &admin_api.OtherInfo{
			IsAcceptCrossMajor:    educationInfo.OtherInfo.IsAcceptCrossMajor,
			CurrentCountry:        educationInfo.OtherInfo.CurrentCountry,
			IsDualDegree:          educationInfo.OtherInfo.IsDualDegree,
			IntentionalSchoolRank: educationInfo.OtherInfo.IntentionalSchoolRank,
			IntentionalSchool:     educationInfo.OtherInfo.IntentionalSchool,
			Remark:                educationInfo.OtherInfo.Remark,
		}
	}
	studentInfo := &admin_api.StudentInfo{}
	if educationInfo.StudentInfo != nil {
		studentInfo = &admin_api.StudentInfo{
			Resume:                     ConvertFileInfo2Api(educationInfo.StudentInfo.Resume),
			Passport:                   ConvertFileInfo2Api(educationInfo.StudentInfo.Passport),
			Transcript:                 ConvertFileInfo2Api(educationInfo.StudentInfo.Transcript),
			UkBiometricResidencePermit: ConvertFileInfo2Api(educationInfo.StudentInfo.UkBiometricResidencePermit),
			UkVisa:                     ConvertFileInfo2Api(educationInfo.StudentInfo.UkVisa),
			WorkCertificate:            ConvertFileInfo2Api(educationInfo.StudentInfo.WorkCertificate),
			PersonalStatement:          ConvertFileInfo2Api(educationInfo.StudentInfo.PersonalStatement),
			Portfolio:                  ConvertFileInfo2Api(educationInfo.StudentInfo.Portfolio),
			GraduationCertificate:      ConvertFileInfo2Api(educationInfo.StudentInfo.GraduationCertificate),
			InSchoolCertificate:        ConvertFileInfo2Api(educationInfo.StudentInfo.InSchoolCertificate),
			LanguageCertificate:        ConvertFileInfo2Api(educationInfo.StudentInfo.LanguageCertificate),
			OtherInfo:                  ConvertFileInfo2Api(educationInfo.StudentInfo.OtherInfo),
		}
	}
	return &admin_api.EducationInfo{
		Schools:     schoolList,
		OtherInfo:   otherInfo,
		StudentInfo: studentInfo,
	}
}

func CustomerDetail2Api(ctx context.Context, info *customer.CustomerInfo, customerTags []*customer.CustomerTag) *admin_api.CustomerInfo {
	if info == nil {
		return nil
	}
	//将 customerTags安装 type和customer_id分组
	customerTagMap := make(map[string][]*admin_api.SimpleTag)
	for _, customerTag := range customerTags {
		key := string(customerTag.CustomerId) + string(customerTag.Type)
		customerTagMap[key] = append(customerTagMap[key], &admin_api.SimpleTag{
			Id:   customerTag.Id,
			Name: customerTag.Name,
			Icon: customerTag.Icon,
		})
	}
	isAllowChangeIdentity := constants.YesNo_Yes
	//绑定了人家长或者学生就不能切换身份了
	if len(info.BindCustomers) > 0 {
		isAllowChangeIdentity = constants.YesNo_No
	} else {
		// 订单列表信息
		respRpc, err := global.OrderClient.GetOrderList(ctx, &order.GetOrderListReq{
			CustomerIds: []int64{int64(info.Id)},
			PageNum:     1,
			PageSize:    10,
		})
		if err != nil {
			logger.CtxErrorf(ctx, "global.OrderClient.GetOrderList rpc error: %v", err)
		}
		if respRpc.GetBase().GetCode() != errno.Errno_SUCCESS {
			logger.CtxErrorf(ctx, "global.OrderClient.GetOrderList rpc base error %v", respRpc.GetBase())
		}
		if len(respRpc.GetItems()) > 0 {
			isAllowChangeIdentity = constants.YesNo_No
		}
	}
	//to do调用订单接口，判断是否有订单
	return &admin_api.CustomerInfo{
		Id:                    info.Id,
		Name:                  info.Name,
		Identity:              admin_api.IdentityType(info.Identity),
		BindCustomers:         SimpleCustomerInfoList2Api(info.BindCustomers),
		CountryId:             info.CountryId,
		CountryCode:           info.CountryCode,
		Phone:                 info.Phone,
		Email:                 info.Email,
		Tags:                  customerTagMap[string(info.Id)+string(customer.TagType_TAG_TYPE_MANUAL)],
		SmartTags:             customerTagMap[string(info.Id)+string(customer.TagType_TAG_TYPE_SMART)],
		PhoneVerified:         constants.YesNo(info.PhoneVerified),
		EmailVerified:         constants.YesNo(info.EmailVerified),
		EmployeeId:            info.EmployeeId,
		Profile:               info.Profile,
		UserType:              admin_api.CustomerType(info.UserType),
		IsAllowChangeIdentity: isAllowChangeIdentity,
		Remark:                info.Remark,
	}
}

// CustomerInfo2Api 将 RPC CustomerInfo 转换为 API CustomerInfo
func CustomerInfo2Api(info *customer.CustomerInfo) *admin_api.CustomerInfo {
	if info == nil {
		return nil
	}
	return &admin_api.CustomerInfo{
		Id:            info.Id,
		Name:          info.Name,
		Identity:      admin_api.IdentityType(info.Identity),
		BindCustomers: SimpleCustomerInfoList2Api(info.BindCustomers),
		CountryId:     info.CountryId,
		CountryCode:   info.CountryCode,
		Phone:         info.Phone,
		Email:         info.Email,
		Tags:          SimpleTagList2Api(info.Tags),
		SmartTags:     SimpleTagList2Api(info.SmartTags),
		PhoneVerified: constants.YesNo(int32(info.PhoneVerified)),
		EmailVerified: constants.YesNo(int32(info.EmailVerified)),
		// FollowEmployees: info.FollowEmployees,
		// Employee:      info.EmployeeId,
		Profile: info.Profile,
	}
}

func SimpleCustomerInfoList2Api(infos []*customer.SimpleCustomerInfo) []*admin_api.SimpleCustomerInfo {
	if infos == nil {
		return nil
	}
	result := make([]*admin_api.SimpleCustomerInfo, 0, len(infos))
	for _, info := range infos {
		result = append(result, &admin_api.SimpleCustomerInfo{
			Id:       info.Id,
			Name:     info.Name,
			Identity: admin_api.IdentityType(info.Identity),
		})
	}
	return result
}

func SimpleTagList2Api(tags []*customer.SimpleTag) []*admin_api.SimpleTag {
	if tags == nil {
		return nil
	}
	result := make([]*admin_api.SimpleTag, 0, len(tags))
	for _, tag := range tags {
		result = append(result, &admin_api.SimpleTag{
			Id:   tag.Id,
			Name: tag.Name,
			Icon: tag.Icon,
		})
	}
	return result
}

func ConvertCustomerOption2Api(items []*customer.SimpleCustomerInfo) []*admin_api.SimpleCustomerInfo {
	if items == nil {
		return nil
	}
	result := make([]*admin_api.SimpleCustomerInfo, 0, len(items))
	for _, item := range items {
		result = append(result, &admin_api.SimpleCustomerInfo{
			Id:    item.Id,
			Name:  item.Name,
			Phone: item.Phone,
			Email: item.Email,
		})
	}
	return result
}
func ConvertCustomerOptionWithAvatar2Api(items []*customer.SimpleCustomerInfo, avatarItems map[int64]*message.AvatarItem) []*admin_api.SimpleCustomerInfo {
	if items == nil {
		return nil
	}
	result := make([]*admin_api.SimpleCustomerInfo, 0, len(items))
	for _, item := range items {
		avatar := ""
		if avatarItems[item.Id] != nil {
			avatar = avatarItems[item.Id].Avatar
		}
		result = append(result, &admin_api.SimpleCustomerInfo{
			Id:     item.Id,
			Name:   item.Name,
			Phone:  item.Phone,
			Avatar: avatar,
			Email:  item.Email,
		})
	}
	return result
}
func ConvertCustomerList2Api(infos []*customer.CustomerInfo,
	customerTags []*customer.CustomerTag,
	staffMap map[int64]*admin_api.EmployeeInfo,
	customerReferralEmployeeMap map[int64][]*admin_api.CustomerReferralEmployee,
	customerWorkflowListNewestMap map[int64]*admin_api.CustomerWorkOrderInfo,
	latestOrderInfoByCustomerIdsMap map[int64]*admin_api.LastOrderInfo,
	transferingUser []*user.TransferObject,
) []*admin_api.ListCustomerItem {
	if infos == nil {
		return nil
	}
	//将 customerTags安装 type和customer_id分组
	customerTagMap := make(map[string][]*admin_api.SimpleTag)
	for _, customerTag := range customerTags {
		key := string(customerTag.CustomerId) + string(customerTag.Type)
		customerTagMap[key] = append(customerTagMap[key], &admin_api.SimpleTag{
			Id:   customerTag.Id,
			Name: customerTag.Name,
			Icon: customerTag.Icon,
		})
	}
	//资产交接中的用户
	transferingUserMap := make(map[int64]*admin_api.TransferObject)
	if transferingUser != nil {
		for _, item := range transferingUser {
			transferingUserMap[item.UserId] = &admin_api.TransferObject{
				UserId:                   item.UserId,
				ToEmployeeId:             item.ToEmployeeId,
				ToEmployeeDepartmentName: item.ToEmployeeName,
				ToEmployeeName:           item.ToEmployeeName,
			}
		}
	}

	result := make([]*admin_api.ListCustomerItem, 0, len(infos))
	for _, info := range infos {
		name := ""
		if staff, ok := staffMap[info.UpdatedBy]; ok {
			name = staff.Name
		}
		employeeList := make([]*admin_api.CustomerReferralEmployee, 0)
		if staffList, ok := customerReferralEmployeeMap[int64(info.Id)]; ok {
			employeeList = staffList
		}
		latestWorkOrder := &admin_api.CustomerWorkOrderInfo{}
		if item, ok := customerWorkflowListNewestMap[int64(info.Id)]; ok {
			latestWorkOrder = item
		}
		latestOrder := &admin_api.LastOrderInfo{}
		if item, ok := latestOrderInfoByCustomerIdsMap[int64(info.Id)]; ok {
			latestOrder = item
		}
		transferObject := &admin_api.TransferObject{}
		if item, ok := transferingUserMap[int64(info.Id)]; ok {
			transferObject = item
		}
		result = append(result, &admin_api.ListCustomerItem{
			Id:             info.Id,
			Name:           info.Name,
			Identity:       admin_api.IdentityType(info.Identity),
			Tags:           customerTagMap[string(info.Id)+string(customer.TagType_TAG_TYPE_MANUAL)],
			SmartTags:      customerTagMap[string(info.Id)+string(customer.TagType_TAG_TYPE_SMART)],
			CustomerType:   admin_api.CustomerType(info.UserType),
			CustomerSource: admin_api.CustomerSource(info.CustomerSource),
			CustomerStatus: admin_api.CustomerStatus(info.Status),
			LatestEditTime: info.LastEditTime,
			Operator: &admin_api.SimpleEmployeeInfo{
				Id:   int32(info.UpdatedBy),
				Name: name,
			},
			OrderCount:             info.OrderCount,
			WorkOrderCount:         info.WorkfowCount,
			EmployeeList:           employeeList,
			ServiceStatus:          admin_api.CustomerWorkOrderStatus(info.ServiceStatus),
			LatestOrder:            latestOrder,
			LatestWorkOrder:        latestWorkOrder,
			LatestStatusChangeTime: info.LatestStatusChangeTime,
			RefundFlag:             constants.YesNo(info.RefundFlag),
			Email:                  info.Email,
			Phone:                  info.Phone,
			TransferObject:         transferObject,
		})
	}
	return result
}

func ConvertCustomerInfo2Api(infos []*customer.CustomerInfo) []*admin_api.CustomerInfo {
	if infos == nil {
		return nil
	}
	result := make([]*admin_api.CustomerInfo, 0, len(infos))
	for _, info := range infos {
		result = append(result, CustomerInfo2Api(info))
	}
	return result
}
func ConvertEducationExperience2Api(items []*customer.SchoolInfo) []*admin_api.SchoolInfo {
	if items == nil {
		return nil
	}
	result := make([]*admin_api.SchoolInfo, 0, len(items))
	for _, item := range items {
		result = append(result, &admin_api.SchoolInfo{
			Id:           item.Id,
			Country:      item.Country,
			Category:     item.Category,
			Name:         item.Name,
			Major:        item.Major,
			MajorType:    item.MajorType,
			Degree:       item.Degree,
			StartTime:    item.StartTime,
			EndTime:      item.EndTime,
			Gpa:          item.Gpa,
			AverageScore: item.AverageScore,
			SchoolId:     item.SchoolId,
			CountryId:    item.CountryId,
			MajorCate:    item.MajorCate,
			MajorSubcate: item.MajorSubcate,
		})
	}
	return result
}
func ConvertAwardExperience2Api(items []*customer.AwardExperience) []*admin_api.AwardExperience {
	if items == nil {
		return nil
	}
	result := make([]*admin_api.AwardExperience, 0, len(items))
	for _, item := range items {
		result = append(result, &admin_api.AwardExperience{
			Id:            item.Id,
			AwardName:     item.AwardName,
			AwardDate:     item.AwardDate,
			AwardLocation: item.AwardLocation,
			AwardLevel:    item.AwardLevel,
			AwardDesc:     item.AwardDesc,
		})
	}
	return result
}
func ConvertWorkInternshipExperience2Api(items []*customer.WorkInternshipExperience) []*admin_api.WorkInternshipExperience {
	if items == nil {
		return nil
	}
	result := make([]*admin_api.WorkInternshipExperience, 0, len(items))
	for _, item := range items {
		result = append(result, &admin_api.WorkInternshipExperience{
			Id:          item.Id,
			Position:    item.Position,
			Workplace:   item.Workplace,
			StartTime:   item.StartTime,
			EndTime:     item.EndTime,
			WorkContent: item.WorkContent,
		})
	}
	return result
}
func ConvertReferrerInformation2Api(items []*customer.ReferrerInformation) []*admin_api.ReferrerInformation {
	if items == nil {
		return nil
	}
	result := make([]*admin_api.ReferrerInformation, 0, len(items))
	for _, item := range items {
		result = append(result, &admin_api.ReferrerInformation{
			Id:       item.Id,
			Name:     item.Name,
			Position: item.Position,
			Email:    item.Email,
			Tel:      item.Tel,
			Postcode: item.Postcode,
			Address:  item.Address,
		})
	}
	return result
}
func ConvertStudentAppeal2Api(item *customer.StudentAppeal) *admin_api.StudentAppeal {
	if item == nil {
		return nil
	}
	return &admin_api.StudentAppeal{
		CurrentCountry:          item.CurrentCountry,
		IntentionalApplyCountry: item.IntentionalApplyCountry,
		IntentionalSchool:       item.IntentionalSchool,
		IntentionalSchoolRank:   item.IntentionalSchoolRank,
		IntentionalMajor:        item.IntentionalMajor,
		IsDualDegree:            item.IsDualDegree,
		IsAcceptCrossMajor:      item.IsAcceptCrossMajor,
		EmergencyContact:        item.EmergencyContact,
		EmergencyContactTel:     item.EmergencyContactTel,
		EmergencyContactTelCode: item.EmergencyContactTelCode,
		Remark:                  item.Remark,
	}
}
func ConvertLanguageAchievement2Api(item *customer.LanguageAchievement) *admin_api.LanguageAchievement {
	if item == nil {
		return nil
	}
	return &admin_api.LanguageAchievement{
		ExamType:             item.ExamType,
		ExamDate:             item.ExamDate,
		ScoreInquiryLink:     item.ScoreInquiryLink,
		ScoreInquiryUsername: item.ScoreInquiryUsername,
		ScoreInquiryPassword: item.ScoreInquiryPassword,
		ExamTotalScore:       item.ExamTotalScore,
		ExamListeningScore:   item.ExamListeningScore,
		ExamOralScore:        item.ExamOralScore,
		ExamReadingScore:     item.ExamReadingScore,
		ExamWritingScore:     item.ExamWritingScore,
	}
}
func ConvertStudentInfo2Api(item *customer.StudentInfo) *admin_api.StudentInfo {
	if item == nil {
		return nil
	}
	return &admin_api.StudentInfo{
		Resume:                     ConvertFileInfo2Api(item.Resume),
		Passport:                   ConvertFileInfo2Api(item.Passport),
		Transcript:                 ConvertFileInfo2Api(item.Transcript),
		UkBiometricResidencePermit: ConvertFileInfo2Api(item.UkBiometricResidencePermit),
		UkVisa:                     ConvertFileInfo2Api(item.UkVisa),
		WorkCertificate:            ConvertFileInfo2Api(item.WorkCertificate),
		PersonalStatement:          ConvertFileInfo2Api(item.PersonalStatement),
		Portfolio:                  ConvertFileInfo2Api(item.Portfolio),
		GraduationCertificate:      ConvertFileInfo2Api(item.GraduationCertificate),
		InSchoolCertificate:        ConvertFileInfo2Api(item.InSchoolCertificate),
		OtherInfo:                  ConvertFileInfo2Api(item.OtherInfo),
		LanguageCertificate:        ConvertFileInfo2Api(item.LanguageCertificate),
		RecommendationLetter:       ConvertFileInfo2Api(item.RecommendationLetter),
		CourseList:                 ConvertFileInfo2Api(item.CourseList),
	}
}
func ConvertInternalOutputDocument2Api(item *customer.InternalOutputDocument) *admin_api.InternalOutputDocument {
	if item == nil {
		return nil
	}
	return &admin_api.InternalOutputDocument{
		InternalOutputCv:    ConvertFileInfo2Api(item.InternalOutputCv),
		InternalOutputPs:    ConvertFileInfo2Api(item.InternalOutputPs),
		InternalOutputRl:    ConvertFileInfo2Api(item.InternalOutputRl),
		InternalOutputOther: ConvertFileInfo2Api(item.InternalOutputOther),
		InternalOutputSqclb: ConvertFileInfo2Api(item.InternalOutputSqclb),
		InternalOutputTcx:   ConvertFileInfo2Api(item.InternalOutputTcx),
	}
}
func ConvertResultDocument2Api(item *customer.ResultDocument) *admin_api.ResultDocument {
	if item == nil {
		return nil
	}
	return &admin_api.ResultDocument{
		ResultFileOffer:           ConvertFileInfo2Api(item.ResultFileOffer),
		ResultFileOther:           ConvertFileInfo2Api(item.ResultFileOther),
		ResultFileRejectionLetter: ConvertFileInfo2Api(item.ResultFileRejectionLetter),
		ResultFileVisa:            ConvertFileInfo2Api(item.ResultFileVisa),
	}
}
func ConvertProcessDocument2Api(item *customer.ProcessDocument) *admin_api.ProcessDocument {
	if item == nil {
		return nil
	}
	return &admin_api.ProcessDocument{
		ProcessDocument: ConvertFileInfo2Api(item.ProcessDocument),
	}
}

// 支款
func ConvertCustomerFinancialExpenseList2Api(ctx context.Context, items []*order.FinancialRefundInfo) []*admin_api.CustomerFinancialRefundInfo {
	if items == nil {
		return nil
	}

	// 1. 收集所有提交者和审批者ID
	employeeIds := make([]int64, 0)
	for _, item := range items {
		if item.SubmitId > 0 {
			employeeIds = append(employeeIds, item.SubmitId)
		}
		if item.ApproveBy > 0 {
			employeeIds = append(employeeIds, item.ApproveBy)
		}
	}

	// 2. 批量查询员工信息
	type employeeInfo struct {
		name     string
		deptName string
	}
	employeeMap := make(map[int64]employeeInfo)
	if len(employeeIds) > 0 {
		employeeResp, err := global.UsersClient.GetEmployeeInfoByIds(ctx, &user.GetEmployeeInfoByIdsReq{
			Id: employeeIds,
		})
		if err != nil {
			return nil
		}
		for _, emp := range employeeResp.List {
			employeeMap[emp.Id] = employeeInfo{
				name:     emp.Name,
				deptName: emp.DeptName,
			}
		}
	}

	// 3. 在循环中使用map获取员工信息
	result := make([]*admin_api.CustomerFinancialRefundInfo, 0, len(items))

	for _, item := range items {
		// 获取提交者信息
		submitName := ""
		submitDepartment := ""
		if submitInfo, ok := employeeMap[item.SubmitId]; ok {
			submitName = submitInfo.name
			submitDepartment = submitInfo.deptName
		}
		// 获取审批者信息
		approveName := ""
		approveDepartment := ""
		if approveInfo, ok := employeeMap[item.ApproveBy]; ok {
			approveName = approveInfo.name
			approveDepartment = approveInfo.deptName
		}
		var approveLog []*admin_api.FundContractInfo
		var refundAgreement []*admin_api.FundContractInfo
		var scholarshipAgreement []*admin_api.FundContractInfo
		var studentCard []*admin_api.FundContractInfo
		var visa []*admin_api.FundContractInfo
		var tuitionPaymentProof []*admin_api.FundContractInfo
		var transactionNo []*admin_api.FundContractInfo
		if len(item.ApproveLog) > 0 {
			_ = json.Unmarshal([]byte(item.ApproveLog), &approveLog)
		}
		if len(item.RefundAgreement) > 0 {
			_ = json.Unmarshal([]byte(item.RefundAgreement), &refundAgreement)
		}
		if len(item.ScholarshipAgreement) > 0 {
			_ = json.Unmarshal([]byte(item.ScholarshipAgreement), &scholarshipAgreement)
		}
		if len(item.Visa) > 0 {
			_ = json.Unmarshal([]byte(item.Visa), &visa)
		}
		if len(item.StudentCard) > 0 {
			_ = json.Unmarshal([]byte(item.StudentCard), &studentCard)
		}
		if len(item.TuitionPaymentProof) > 0 {
			_ = json.Unmarshal([]byte(item.TuitionPaymentProof), &tuitionPaymentProof)
		}
		if len(item.TransactionNo) > 0 {
			_ = json.Unmarshal([]byte(item.TransactionNo), &transactionNo)
			for i, _ := range transactionNo {
				transactionNo[i].ThumbnailUrl = transactionNo[i].Url
			}
		}
		result = append(result, &admin_api.CustomerFinancialRefundInfo{
			Id:                       item.Id,
			RefundNo:                 item.RefundNo,
			RealAmountOther:          item.RealAmountOther,
			RefundType:               item.RefundType,
			CreatedAt:                item.CreatedAt,
			Currency:                 item.Currency,
			ApproveStatus:            item.ApproveStatus,
			PassTime:                 item.PassTime,
			RejectTime:               item.RejectTime,
			CompleteTime:             item.CompleteTime,
			TransactionNo:            transactionNo,
			RefundReceiveAccountType: item.RefundReceiveAccountType,
			RefundReceiveAccount:     item.RefundReceiveAccount,
			RefundReason:             item.RefundReason,
			AccountName:              item.AccountName,
			OrderId:                  item.OrderId,
			OrderNo:                  item.OrderNo,
			CustomerId:               item.CustomerId,
			RefundAgreement:          refundAgreement,
			ApproveLog:               approveLog,
			ScholarshipAgreement:     scholarshipAgreement,
			Visa:                     visa,
			StudentCard:              studentCard,
			TuitionPaymentProof:      tuitionPaymentProof,
			WorkflowName:             item.WorkflowName,
			SubmitId:                 item.SubmitId,
			SubmitName:               submitName,
			SubmitDepartment:         submitDepartment,
			ApproveBy:                item.ApproveBy,
			ApproveName:              approveName,
			ApproveDepartment:        approveDepartment,
			ApproveComment:           item.ApproveComment,
			RealAmountRmb:            item.RealAmountRmb,
			ExchangeRate:             item.ExchangeRate,
			WorkflowId:               item.WorkflowId,
			WorkflowNo:               item.WorkflowNo,
			RefundDeadline:           item.RefundDeadline,
		})
	}
	return result
}

func ConvertCustomerFinancialReceiptList2Api(ctx context.Context, items []*order.FinancialFundInfo) []*admin_api.CustomerFinancialFundInfo {
	if items == nil {
		return nil
	}
	employeeIds := make([]int64, 0, len(items))
	for _, item := range items {
		employeeIds = append(employeeIds, item.SubmitId)
		employeeIds = append(employeeIds, item.ApproveBy)
		for _, u := range item.UserSource {
			employeeIds = append(employeeIds, u)
		}
		for _, o := range item.OrderSource {
			employeeIds = append(employeeIds, o)
		}
		for _, i := range item.SubmitSource {
			employeeIds = append(employeeIds, i)
		}
	}
	employeeResp, err := global.UsersClient.GetEmployeeInfoByIds(ctx, &user.GetEmployeeInfoByIdsReq{
		Id: employeeIds,
	})
	if err != nil {
		return nil
	}
	type employeeInfo struct {
		name     string
		deptName string
	}
	employeeMap := make(map[int64]employeeInfo)
	for _, emp := range employeeResp.List {
		employeeMap[emp.Id] = employeeInfo{
			name:     emp.Name,
			deptName: emp.DeptName,
		}
	}

	result := make([]*admin_api.CustomerFinancialFundInfo, 0, len(items))
	for _, item := range items {
		var contractInfo []*admin_api.FundContractInfo
		if len(item.ContractUrl) > 0 {
			_ = json.Unmarshal([]byte(item.ContractUrl), &contractInfo)
		}
		paidList := make([]*admin_api.FinancialPaidInfo, 0, len(item.FinancialPaiInfo))
		thirdAmountList := make([]*admin_api.AmountInfo, 0, 0)
		m := map[string]string{}
		tmp1 := decimal.Decimal{}
		tmp2 := decimal.Decimal{}
		tmp3 := decimal.Decimal{}
		for _, paid := range item.FinancialPaiInfo {
			var imagesPath []*admin_api.FundContractInfo
			if len(paid.ImagesPath) > 0 {
				_ = json.Unmarshal([]byte(paid.ImagesPath), &imagesPath)
				for i, _ := range imagesPath {
					imagesPath[i].ThumbnailUrl = imagesPath[i].Url
				}
			}
			paidList = append(paidList, &admin_api.FinancialPaidInfo{
				FinancialPaidId:      paid.Id,
				PaymentAccountId:     paid.PaymentAccountId,
				Currency:             paid.Currency,
				PaidType:             admin_api.PaymentAccountType(paid.PaidType),
				AmountOther:          paid.AmountOther,
				ImagesPath:           imagesPath,
				TransactionNo:        paid.TransactionNo,
				FinancialAccountName: paid.AccountName,
				ExchangeRate:         paid.ExchangeRate,
				AmountCny:            paid.AmountCny,
			})
			val, ok := m[paid.Currency]
			if ok {
				tmp1, _ = decimal.NewFromString(val)
				tmp2, _ = decimal.NewFromString(paid.AmountOther)
				tmp3 = tmp1.Add(tmp2)
				m[paid.Currency] = tmp3.String()
			} else {
				m[paid.Currency] = paid.AmountOther
			}
		}
		for k, v := range m {
			thirdAmountList = append(thirdAmountList, &admin_api.AmountInfo{
				Currency: k,
				Amount:   v,
			})
		}
		// 获取提交者信息
		submitName := ""
		submitDepartment := ""
		if submitInfo, ok := employeeMap[item.SubmitId]; ok {
			submitName = submitInfo.name
			submitDepartment = submitInfo.deptName
		}

		// 获取审批者信息
		approveName := ""
		approveDepartment := ""
		if approveInfo, ok := employeeMap[item.ApproveBy]; ok {
			approveName = approveInfo.name
			approveDepartment = approveInfo.deptName
		}

		result = append(result, &admin_api.CustomerFinancialFundInfo{
			Id:                item.Id,
			CustomerId:        item.CustomerId,
			OrderId:           item.OrderId,
			OrderNo:           item.OrderNo,
			FundNo:            item.FundNo,
			RealAmountOther:   item.RealAmountOther,
			Currency:          item.Currency,
			ShouldAmountOther: item.ShouldAmountOther,
			SubmitId:          item.SubmitId,
			CreatedAt:         item.CreatedAt,
			FundType:          item.FundType,
			PayType:           item.PayType,
			ContractNo:        item.ContractNo,
			ExchangeRate:      item.ExchangeRate,
			ApproveStatus:     item.ApproveStatus,
			Discount:          item.Discount,
			DiscountRate:      item.DiscountRate,
			UrgentSpeed:       item.UrgentSpeed,
			PaidTime:          item.PaidTime,
			PassTime:          item.PassTime,
			RejectTime:        item.RejectTime,
			ApproveBy:         item.ApproveBy,
			ContractInfo:      contractInfo,
			FinancialPaidList: paidList,
			SubmitName:        submitName,
			SubmitDepartment:  submitDepartment,
			ApproveName:       approveName,
			ApproveDepartment: approveDepartment,
			ApproveComment:    item.ApproveComment,
			RealAmountRmb:     item.RealAmountRmb,
			ThirdAmountList:   thirdAmountList,
			Remark:            item.Remark,
			ShouldAmountRmb:   item.ShouldAmountRmb,
		})
	}

	for index, i := range items {
		//订单来源人
		var omock []*admin_api.IdNameDept
		for _, e := range i.OrderSource {
			if info, ok := employeeMap[e]; ok {
				omock = append(omock, &admin_api.IdNameDept{
					Id:     e,
					Name:   info.name,
					Depart: info.deptName,
				})
			}
		}
		result[index].OrderSource = omock

		//客户来源人
		var cmock []*admin_api.IdNameDept
		for _, c := range i.UserSource {
			if info, ok := employeeMap[c]; ok {
				cmock = append(cmock, &admin_api.IdNameDept{
					Id:     c,
					Name:   info.name,
					Depart: info.deptName,
				})
			}
		}
		result[index].UserSource = cmock

		//订单共同提交人
		var smock []*admin_api.IdNameDept
		smock = append(smock, &admin_api.IdNameDept{
			Id:     result[index].SubmitId,
			Name:   result[index].SubmitName,
			Depart: result[index].SubmitDepartment,
		})
		for _, s := range i.SubmitSource {
			if info, ok := employeeMap[s]; ok {
				smock = append(smock, &admin_api.IdNameDept{
					Id:     s,
					Name:   info.name,
					Depart: info.deptName,
				})
			}
		}
		result[index].SubmitSource = smock

	}
	return result
}

func ConvertCustomerOrderList2Api(items []*order.OrderListItem) []*admin_api.OrderInfo {
	if items == nil {
		return nil
	}
	result := make([]*admin_api.OrderInfo, 0, len(items))
	for _, item := range items {
		result = append(result, &admin_api.OrderInfo{
			Id: item.Order.Id,
		})
	}
	return result
}
func ConvertCustomerSource(sources []customer.CustomerSource) []int32 {
	result := make([]int32, len(sources))
	for i, source := range sources {
		result[i] = int32(source)
	}
	return result
}

func ConvertFileInfo2Api(items []*customer.File) []*admin_api.File {
	if items == nil {
		return nil
	}
	result := make([]*admin_api.File, 0, len(items))
	for _, item := range items {
		result = append(result, &admin_api.File{
			Name:         item.Name,
			Url:          item.Url,
			ThumbnailUrl: item.ThumbnailUrl,
			UploadTime:   item.UploadTime,
		})
	}
	return result
}
func ConvertFileInfo2Rpc(ctx context.Context, items []*admin_api.File) []*customer.File {
	if items == nil {
		return nil
	}
	result := make([]*customer.File, 0, len(items))
	for _, item := range items {
		uploadTime := time.Now().UnixMilli()
		if item.UploadTime > 0 {
			uploadTime = item.UploadTime
		}
		result = append(result, &customer.File{
			Name:         item.Name,
			Url:          item.Url,
			ThumbnailUrl: item.ThumbnailUrl,
			UploadTime:   uploadTime,
		})
	}
	return result
}

func ConvertBindCustomers2Api(items []*customer.SimpleCustomerInfo, avatarItems map[int64]*message.AvatarItem) []*admin_api.GroupChatInfo {
	if items == nil {
		return nil
	}

	result := make([]*admin_api.GroupChatInfo, 0, len(items))
	for _, item := range items {
		avatar := ""
		if avatarItems[item.Id] != nil {
			avatar = avatarItems[item.Id].Avatar
		}
		result = append(result, &admin_api.GroupChatInfo{
			Id:     int32(item.Id),
			Name:   item.Name,
			Avatar: avatar,
		})
	}
	return result
}

func CustomerWorkflowListNewestToMap(items []*workflow.CustomerNewestWorkflowInfo) map[int64]*admin_api.CustomerWorkOrderInfo {
	if items == nil {
		return nil
	}
	//创建一个map
	result := make(map[int64]*admin_api.CustomerWorkOrderInfo)
	for _, item := range items {
		result[item.CustomerId] = &admin_api.CustomerWorkOrderInfo{
			Id:          0,
			WorkOrderNo: item.WorkflowNo,
			Name:        item.WorkflowName,
			Status:      admin_api.WorkflowStatus(item.Status),
		}
	}
	return result
}
func LatestOrderInfoByCustomerIdsToMap(items []*order.SingleOrderInfoItem) map[int64]*admin_api.LastOrderInfo {
	if items == nil {
		return nil
	}
	//创建一个map
	result := make(map[int64]*admin_api.LastOrderInfo)
	for _, item := range items {

		result[item.Order.CustomerId] = &admin_api.LastOrderInfo{
			OrderNo:               item.Order.OrderNo,
			ServiceName:           item.Order.ServiceName,
			Status:                admin_api.StatusOrder(item.Order.Status),
			StatusReview:          admin_api.StatusOrderReview(item.Order.StatusReview),
			GoodsContent:          item.Order.ServiceName + "-" + item.OrderGoods.GoodsName + "-" + item.OrderGoods.GoodsSpec + "*" + item.OrderGoods.GoodsNum,
			StatusWorkflow:        admin_api.StatusOrderWorkflow(item.Order.StatusWorkflow),
			StatusPayDisbursement: admin_api.StatusOrderPay(item.Order.StatusPayDisbursement),
		}
	}
	return result
}
