package order_service

import (
	"context"
	"encoding/json"
	"strings"
	"time"
	"uofferv2/kitex_gen/base"
	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/customer"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/kitex_gen/server/cmd/workflow"
	"uofferv2/pkg/ctxmeta"
	"uofferv2/pkg/i18n"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"
	"uofferv2/server/cmd/admin_api/internal/global"
	"uofferv2/server/cmd/admin_api/internal/services/httputil"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/shopspring/decimal"
)

// GetOrderDisbursementList 支款订单列表（支款订单）
func GetOrderDisbursementList(ctx context.Context, c *app.RequestContext) {
	funcName := getFuncName(1)

	var err error
	var req admin_api.GetOrderListReq
	err = c.BindAndValidate(&req)
	if err != nil {
		logger.CtxErrorf(ctx, "%s invalid request param: %v", funcName, err)
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	query := orderListReqApiToSrv(&req)
	statuses := []order.StatusOrder{
		order.StatusOrder_STATUS_ORDER_DISBURSEMENT, // 5%支款（支款订单）
	}
	query.Statuses = statuses

	// TODO LIUSHUANG 数据权限
	// 数据权限
	employeeId := ctxmeta.MustGetAuth(ctx).EmployeeId()
	if employeeId > 0 {
		_, _, _, customerIds, employeeIds, returnBreak := getDataPermissionIds(ctx, c, funcName, employeeId)
		if returnBreak {
			return
		}

		if len(req.GetUpdaterIds()) == 0 {
			query.UpdaterIds = employeeIds
		}

		if len(req.GetCustomerIds()) == 0 {
			query.CustomerIds = customerIds
		}
	}

	respRpc, err := global.OrderClient.GetOrderList(ctx, query)
	if err != nil {
		logger.CtxErrorf(ctx, "%s global.OrderClient.GetOrderList rpc error: %v", funcName, err)
		httputil.ResponseInternalErr(c)
		return
	}

	if respRpc.GetBase().GetCode() != errno.Errno_SUCCESS {
		logger.CtxErrorf(ctx, "%s global.OrderClient.GetOrderList rpc base error %v", funcName, respRpc.GetBase())
		httputil.ResponseErrorCodeWithBase(c, respRpc.GetBase())
		return
	}

	customerMap, creatorMap, updaterMap, reviewerMap, closerMap, returnBreak := getOrderAssociatedUsersInfo(ctx, c, respRpc.GetItems())
	if returnBreak {
		return
	}

	items := make([]*admin_api.OrderListItemRsp, 0, len(respRpc.Items))
	for _, item := range respRpc.Items {
		items = append(items, orderListItemRspSrvToApi(item, customerMap, creatorMap, updaterMap, reviewerMap, closerMap))
	}

	resp := new(admin_api.GetOrderDisbursementListRsp)
	resp.Total = respRpc.Total
	resp.Items = items

	httputil.ResponseSuccess(c, resp)
}

// UpdateOrderDisbursement 编辑支款订单-提交支款（支款订单）
func UpdateOrderDisbursement(ctx context.Context, c *app.RequestContext) {
	funcName := getFuncName(1)

	var err error
	var req admin_api.UpdateOrderDisbursementReq
	err = c.BindAndValidate(&req)
	if err != nil {
		logger.CtxErrorf(ctx, "%s invalid request param: %v", funcName, err)
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	// 获取订单信息
	respRpc, returnBreak := getOrderInfo(ctx, c, req.GetId(), "", funcName)
	if returnBreak {
		return
	}

	// -> 支款待审核
	query := saveOrderDisbursementReqSrvToApi(ctx, c, &req)
	// 发起支款时，冗余当前的订单状态
	// 订单状态
	// 审核状态
	query.Order.StatusPrev = respRpc.GetOrder().GetStatus()
	query.Order.StatusReviewPrev = respRpc.GetOrder().GetStatusReview()
	// 商品信息，共同提交人，订单来源不做更新
	query.OrderGoods = respRpc.GetOrderGoods()
	query.SubmissionIds = respRpc.GetSubmissionIds()
	query.SourceIds = respRpc.GetSourceIds()

	// 编辑订单-支款
	respSaveOrder, err := global.OrderClient.SaveOrder(ctx, query)
	if err != nil {
		logger.CtxErrorf(ctx, "%s global.OrderClient.SaveOrder rpc error: %v", funcName, err)
		httputil.ResponseInternalErr(c)
		return
	}

	if respSaveOrder.GetBase().GetCode() != errno.Errno_SUCCESS {
		logger.CtxErrorf(ctx, "%s global.OrderClient.SaveOrder rpc base error %v", funcName, respSaveOrder.GetBase())
		httputil.ResponseErrorCodeWithBase(c, respSaveOrder.GetBase())
		return
	}

	resp := new(admin_api.UpdateOrderDisbursementRsp)
	resp.Id = req.GetId()

	// 当前订单为草稿待审核 -> 新增
	if query.Order.StatusReview == order.StatusOrderReview_STATUS_REVIEW_DRAFT_AUDIT {
		//创建财务退款单

		authInfo := httputil.MustGetAuthInfoFromContext(ctx, c)
		refundAgreement := "[]"
		if len(req.RefundAgreement) > 0 {
			files, err := GenerateThumbnail(ctx, req.RefundAgreement)
			if err != nil {
				return
			}
			//遍历转换files
			refundAgreementTmp := make([]*admin_api.ContractObj, 0, len(req.RefundAgreement))
			for _, item := range files {
				refundAgreementTmp = append(refundAgreementTmp, &admin_api.ContractObj{
					ThumbnailUrl: item.ThumbnailUrl,
					Url:          item.Url,
					Name:         item.Name,
					CreatedAt:    time.Now().UnixMilli(),
				})

			}
			if jsonBytes, err := json.Marshal(refundAgreementTmp); err == nil {
				refundAgreement = string(jsonBytes)
			}
		}
		approveLog := "[]"
		if len(req.ApproveLog) > 0 {
			approveLogFiles, err := GenerateThumbnail(ctx, req.ApproveLog)
			if err != nil {
				return
			}
			//遍历转换files
			approveLogTmp := make([]*admin_api.ContractObj, 0, len(req.ApproveLog))
			for _, item := range approveLogFiles {
				approveLogTmp = append(approveLogTmp, &admin_api.ContractObj{
					ThumbnailUrl: item.ThumbnailUrl,
					Url:          item.Url,
					Name:         item.Name,
					CreatedAt:    time.Now().UnixMilli(),
				})
			}
			if jsonBytes, err := json.Marshal(approveLogTmp); err == nil {
				approveLog = string(jsonBytes)
			}
		}
		scholarshipAgreement := "[]"
		if len(req.ScholarshipAgreement) > 0 {
			ScholarshipAgreementFiles, err := GenerateThumbnail(ctx, req.ScholarshipAgreement)
			if err != nil {
				return
			}
			//遍历转换files
			scholarshipAgreementTmp := make([]*admin_api.ContractObj, 0, len(req.ScholarshipAgreement))
			for _, item := range ScholarshipAgreementFiles {
				scholarshipAgreementTmp = append(scholarshipAgreementTmp, &admin_api.ContractObj{
					ThumbnailUrl: item.ThumbnailUrl,
					Url:          item.Url,
					Name:         item.Name,
					CreatedAt:    time.Now().UnixMilli(),
				})
			}
			if jsonBytes, err := json.Marshal(scholarshipAgreementTmp); err == nil {
				scholarshipAgreement = string(jsonBytes)
			}
		}
		visa := "[]"
		if len(req.Visa) > 0 {
			visaFiles, err := GenerateThumbnail(ctx, req.Visa)
			if err != nil {
				return
			}
			//遍历转换files
			visaTmp := make([]*admin_api.ContractObj, 0, len(req.Visa))
			for _, item := range visaFiles {
				visaTmp = append(visaTmp, &admin_api.ContractObj{
					ThumbnailUrl: item.ThumbnailUrl,
					Url:          item.Url,
					Name:         item.Name,
					CreatedAt:    time.Now().UnixMilli(),
				})
			}
			if jsonBytes, err := json.Marshal(visaTmp); err == nil {
				visa = string(jsonBytes)
			}
		}
		studentCard := "[]"
		if len(req.StudentCard) > 0 {
			StudentCardFiles, err := GenerateThumbnail(ctx, req.StudentCard)
			if err != nil {
				return
			}
			//遍历转换files
			studentCardTmp := make([]*admin_api.ContractObj, 0, len(req.StudentCard))
			for _, item := range StudentCardFiles {
				studentCardTmp = append(studentCardTmp, &admin_api.ContractObj{
					ThumbnailUrl: item.ThumbnailUrl,
					Url:          item.Url,
					Name:         item.Name,
					CreatedAt:    time.Now().UnixMilli(),
				})
			}
			if jsonBytes, err := json.Marshal(studentCardTmp); err == nil {
				studentCard = string(jsonBytes)
			}
		}
		tuitionPaymentProof := "[]"
		if len(req.TuitionPaymentProof) > 0 {
			TuitionPaymentProofFiles, err := GenerateThumbnail(ctx, req.TuitionPaymentProof)
			if err != nil {
				return
			}
			//遍历转换files
			tuitionPaymentProofTmp := make([]*admin_api.ContractObj, 0, len(req.TuitionPaymentProof))
			for _, item := range TuitionPaymentProofFiles {
				tuitionPaymentProofTmp = append(tuitionPaymentProofTmp, &admin_api.ContractObj{
					ThumbnailUrl: item.ThumbnailUrl,
					Url:          item.Url,
					Name:         item.Name,
					CreatedAt:    time.Now().UnixMilli(),
				})
			}
			if jsonBytes, err := json.Marshal(tuitionPaymentProofTmp); err == nil {
				tuitionPaymentProof = string(jsonBytes)
			}
		}
		fundInfo, err := global.FinancialClient.FundInfo(ctx, &order.FinancialFundInfoReq{OrderId: req.Id})
		if err != nil {
			logger.CtxErrorf(ctx, "%s global.FinancialClient.FundInfo rpc error: %v", funcName, err)
			httputil.ResponseInternalErr(c)
			return
		}
		if fundInfo == nil {
			logger.CtxErrorf(ctx, "%s global.FinancialClient.FundInfo rpc error: %v", funcName, err)
			httputil.ResponseInternalErr(c)
			return
		}
		refundType := query.Order.DisbursementType
		reqGetOrderInfo := &order.GetOrderInfoReq{
			Id: respSaveOrder.GetId(),
		}
		realAmountRmb := req.AmountDisbursementReview
		realAmountOther := req.AmountDisbursementForeigin
		if refundType == order.OrderDisbursementType_DISBURSEMENT_TYPE_SCHOLARSHIP {
			//奖学金
			realAmountRmb = req.AmountBursaryReview
		}
		if refundType == order.OrderDisbursementType_DISBURSEMENT_TYPE_LIQUIDATED_DAMAGES {
			//违约金
			realAmountRmb = req.AmountLiquidatedReview
		}
		// 根据订单ID，获取订单信息
		respGetOrderInfo, _ := global.OrderClient.GetOrderInfo(ctx, reqGetOrderInfo)
		university := ""
		major := ""
		level := ""
		mapLevel := map[int32]string{1: "本科预科", 2: "本科", 3: "硕士预科", 4: "硕士", 5: "博士", 6: "其他"}
		var enterTime int64
		if req.WorkflowId != 0 {
			respGetWorkInfo, _ := global.WorkflowClient.WorkflowDetail(ctx, &workflow.WorkflowDetailReq{
				Id: req.WorkflowId,
			})
			university = respGetWorkInfo.ApplicationInfo.SelectionUniversityZh
			major = respGetWorkInfo.ApplicationInfo.SelectionMajorZh
			levelInt := respGetWorkInfo.ApplicationInfo.SelectionCourseLevel
			value, ok := mapLevel[levelInt]
			if ok {
				level = value
			}
			enterTime = respGetWorkInfo.ApplicationInfo.SelectionExpectedEntry
		}
		customerRpcResp, err := global.CustomerProfileClient.GetCustomerDetail(ctx, &customer.GetCustomerDetailReq{
			Id: fundInfo.CustomerId,
		})
		customerInfo := customerRpcResp.GetCustomerInfo() //客户信息
		_, err = global.FinancialClient.RefundCreate(ctx, &order.FinancialRefundCreateReq{
			OrderNo:                  respSaveOrder.GetOrderNo(),
			OrderId:                  respSaveOrder.GetId(),
			CustomerId:               fundInfo.CustomerId,
			Currency:                 req.Currency,
			RealAmountOther:          realAmountOther,
			RealAmountRmb:            realAmountRmb,
			ExchangeRate:             req.ExchangeRate,
			RefundType:               int32(refundType),
			SubmitId:                 authInfo.Id,
			RefundReceiveAccountType: req.RefundReceiveAccountType,
			RefundReceiveAccount:     req.RefundReceiveAccount,
			RefundReason:             req.RefundReason,
			RefundAgreement:          refundAgreement,
			ApproveLog:               approveLog,
			ScholarshipAgreement:     scholarshipAgreement,
			Visa:                     visa,
			StudentCard:              studentCard,
			TuitionPaymentProof:      tuitionPaymentProof,
			WorkflowNo:               req.WorkflowNo,
			WorkflowId:               req.WorkflowId,
			WorkflowName:             req.WorkflowName,
			ContractAmount:           respGetOrderInfo.OrderPay.AmountContract,
			BrandName:                respGetOrderInfo.GetOrder().GetBrandName(),
			ServiceName:              respGetOrderInfo.GetOrder().GetServiceName(),
			BusinessName:             respGetOrderInfo.GetOrder().GetBusinessName(),
			GoodsName:                respGetOrderInfo.GetOrderGoods()[0].GoodsName,
			GoodsSpecsName:           respGetOrderInfo.GetOrderGoods()[0].GoodsSpec,
			Num:                      respGetOrderInfo.GetOrderGoods()[0].GoodsNum,
			GoodsId:                  respGetOrderInfo.GetOrderGoods()[0].GoodsId,
			University:               university,
			Major:                    major,
			Level:                    level,
			EnterTime:                enterTime,
			UserType:                 int32(customerInfo.UserType),
			RefundDeadline:           req.RefundDeadline,
		})
		if err != nil {
			logger.CtxErrorf(ctx, "%s global.FinancialClient.RefundCreate rpc error: %v", funcName, err)
			httputil.ResponseInternalErr(c)
			return
		}
	}

	// 当前订单为审核驳回待审核 -> 编辑
	if query.Order.StatusReview == order.StatusOrderReview_STATUS_REVIEW_REJECT_AUDIT {
		//编辑支款
		refundInfo, _ := global.FinancialClient.RefundInfo(ctx, &order.FinancialRefundInfoReq{
			OrderId:       respSaveOrder.GetId(),
			ApproveStatus: 4,
		})
		if refundInfo.Id > 0 {
			authInfo := httputil.MustGetAuthInfoFromContext(ctx, c)
			refundAgreement := "[]"
			if len(req.RefundAgreement) > 0 {
				files, err := GenerateThumbnail(ctx, req.RefundAgreement)
				if err != nil {
					return
				}
				//遍历转换files
				refundAgreementTmp := make([]*admin_api.ContractObj, 0, len(req.RefundAgreement))
				for _, item := range files {
					refundAgreementTmp = append(refundAgreementTmp, &admin_api.ContractObj{
						ThumbnailUrl: item.ThumbnailUrl,
						Url:          item.Url,
						Name:         item.Name,
						CreatedAt:    time.Now().UnixMilli(),
					})

				}
				if jsonBytes, err := json.Marshal(refundAgreementTmp); err == nil {
					refundAgreement = string(jsonBytes)
				}
			}
			approveLog := "[]"
			if len(req.ApproveLog) > 0 {
				approveLogFiles, err := GenerateThumbnail(ctx, req.ApproveLog)
				if err != nil {
					return
				}
				//遍历转换files
				approveLogTmp := make([]*admin_api.ContractObj, 0, len(req.ApproveLog))
				for _, item := range approveLogFiles {
					approveLogTmp = append(approveLogTmp, &admin_api.ContractObj{
						ThumbnailUrl: item.ThumbnailUrl,
						Url:          item.Url,
						Name:         item.Name,
						CreatedAt:    time.Now().UnixMilli(),
					})
				}
				if jsonBytes, err := json.Marshal(approveLogTmp); err == nil {
					approveLog = string(jsonBytes)
				}
			}
			scholarshipAgreement := "[]"
			if len(req.ScholarshipAgreement) > 0 {
				ScholarshipAgreementFiles, err := GenerateThumbnail(ctx, req.ScholarshipAgreement)
				if err != nil {
					return
				}
				//遍历转换files
				scholarshipAgreementTmp := make([]*admin_api.ContractObj, 0, len(req.ScholarshipAgreement))
				for _, item := range ScholarshipAgreementFiles {
					scholarshipAgreementTmp = append(scholarshipAgreementTmp, &admin_api.ContractObj{
						ThumbnailUrl: item.ThumbnailUrl,
						Url:          item.Url,
						Name:         item.Name,
						CreatedAt:    time.Now().UnixMilli(),
					})
				}
				if jsonBytes, err := json.Marshal(scholarshipAgreementTmp); err == nil {
					scholarshipAgreement = string(jsonBytes)
				}
			}
			visa := "[]"
			if len(req.Visa) > 0 {
				visaFiles, err := GenerateThumbnail(ctx, req.Visa)
				if err != nil {
					return
				}
				//遍历转换files
				visaTmp := make([]*admin_api.ContractObj, 0, len(req.Visa))
				for _, item := range visaFiles {
					visaTmp = append(visaTmp, &admin_api.ContractObj{
						ThumbnailUrl: item.ThumbnailUrl,
						Url:          item.Url,
						Name:         item.Name,
						CreatedAt:    time.Now().UnixMilli(),
					})
				}
				if jsonBytes, err := json.Marshal(visaTmp); err == nil {
					visa = string(jsonBytes)
				}
			}
			studentCard := "[]"
			if len(req.StudentCard) > 0 {
				StudentCardFiles, err := GenerateThumbnail(ctx, req.StudentCard)
				if err != nil {
					return
				}
				//遍历转换files
				studentCardTmp := make([]*admin_api.ContractObj, 0, len(req.StudentCard))
				for _, item := range StudentCardFiles {
					studentCardTmp = append(studentCardTmp, &admin_api.ContractObj{
						ThumbnailUrl: item.ThumbnailUrl,
						Url:          item.Url,
						Name:         item.Name,
						CreatedAt:    time.Now().UnixMilli(),
					})
				}
				if jsonBytes, err := json.Marshal(studentCardTmp); err == nil {
					studentCard = string(jsonBytes)
				}
			}
			tuitionPaymentProof := "[]"
			if len(req.TuitionPaymentProof) > 0 {
				TuitionPaymentProofFiles, err := GenerateThumbnail(ctx, req.TuitionPaymentProof)
				if err != nil {
					return
				}
				//遍历转换files
				tuitionPaymentProofTmp := make([]*admin_api.ContractObj, 0, len(req.TuitionPaymentProof))
				for _, item := range TuitionPaymentProofFiles {
					tuitionPaymentProofTmp = append(tuitionPaymentProofTmp, &admin_api.ContractObj{
						ThumbnailUrl: item.ThumbnailUrl,
						Url:          item.Url,
						Name:         item.Name,
						CreatedAt:    time.Now().UnixMilli(),
					})
				}
				if jsonBytes, err := json.Marshal(tuitionPaymentProofTmp); err == nil {
					tuitionPaymentProof = string(jsonBytes)
				}
			}
			fundInfo, err := global.FinancialClient.FundInfo(ctx, &order.FinancialFundInfoReq{OrderId: req.Id})
			if err != nil {
				logger.CtxErrorf(ctx, "%s global.FinancialClient.FundInfo rpc error: %v", funcName, err)
				httputil.ResponseInternalErr(c)
				return
			}
			if fundInfo == nil {
				logger.CtxErrorf(ctx, "%s global.FinancialClient.FundInfo rpc error: %v", funcName, err)
				httputil.ResponseInternalErr(c)
				return
			}
			refundType := query.Order.DisbursementType
			reqGetOrderInfo := &order.GetOrderInfoReq{
				Id: respSaveOrder.GetId(),
			}
			realAmountRmb := req.AmountDisbursementReview
			realAmountOther := req.AmountDisbursementForeigin
			if refundType == order.OrderDisbursementType_DISBURSEMENT_TYPE_SCHOLARSHIP {
				//奖学金
				realAmountRmb = req.AmountBursaryReview
			}
			if refundType == order.OrderDisbursementType_DISBURSEMENT_TYPE_LIQUIDATED_DAMAGES {
				//违约金
				realAmountRmb = req.AmountLiquidatedReview
			}
			// 根据订单ID，获取订单信息
			respGetOrderInfo, _ := global.OrderClient.GetOrderInfo(ctx, reqGetOrderInfo)
			university := ""
			major := ""
			level := ""
			mapLevel := map[int32]string{1: "本科预科", 2: "本科", 3: "硕士预科", 4: "硕士", 5: "博士", 6: "其他"}
			var enterTime int64
			if req.WorkflowId != 0 {
				respGetWorkInfo, _ := global.WorkflowClient.WorkflowDetail(ctx, &workflow.WorkflowDetailReq{
					Id: req.WorkflowId,
				})
				university = respGetWorkInfo.ApplicationInfo.SelectionUniversityZh
				major = respGetWorkInfo.ApplicationInfo.SelectionMajorZh
				levelInt := respGetWorkInfo.ApplicationInfo.SelectionCourseLevel
				value, ok := mapLevel[levelInt]
				if ok {
					level = value
				}
				enterTime = respGetWorkInfo.ApplicationInfo.SelectionExpectedEntry
			}
			_, err = global.FinancialClient.RefundUpdate(ctx, &order.FinancialRefundUpdateReq{
				FinancialRefundId:        refundInfo.Id,
				OrderNo:                  respSaveOrder.GetOrderNo(),
				OrderId:                  respSaveOrder.GetId(),
				CustomerId:               fundInfo.CustomerId,
				Currency:                 req.Currency,
				RealAmountOther:          realAmountOther,
				RealAmountRmb:            realAmountRmb,
				ShouldAmountOther:        respGetOrderInfo.OrderPay.AmountContract,
				ExchangeRate:             req.ExchangeRate,
				RefundType:               int32(refundType),
				UpdatedBy:                authInfo.Id,
				RefundReceiveAccountType: req.RefundReceiveAccountType,
				RefundReceiveAccount:     req.RefundReceiveAccount,
				RefundReason:             req.RefundReason,
				RefundAgreement:          refundAgreement,
				ApproveLog:               approveLog,
				ScholarshipAgreement:     scholarshipAgreement,
				Visa:                     visa,
				StudentCard:              studentCard,
				TuitionPaymentProof:      tuitionPaymentProof,
				WorkflowNo:               req.WorkflowNo,
				WorkflowId:               req.WorkflowId,
				WorkflowName:             req.WorkflowName,
				BrandName:                respGetOrderInfo.GetOrder().GetBrandName(),
				ServiceName:              respGetOrderInfo.GetOrder().GetServiceName(),
				BusinessName:             respGetOrderInfo.GetOrder().GetBusinessName(),
				GoodsName:                respGetOrderInfo.GetOrderGoods()[0].GoodsName,
				GoodsSpecsName:           respGetOrderInfo.GetOrderGoods()[0].GoodsSpec,
				Num:                      respGetOrderInfo.GetOrderGoods()[0].GoodsNum,
				GoodsId:                  respGetOrderInfo.GetOrderGoods()[0].GoodsId,
				ApproveStatus:            1,
				University:               university,
				Major:                    major,
				Level:                    level,
				EnterTime:                enterTime,
			})
			if err != nil {
				logger.CtxErrorf(ctx, "%s global.FinancialClient.RefundCreate rpc error: %v", funcName, err)
				httputil.ResponseInternalErr(c)
				return
			}
		}

	}

	httputil.ResponseSuccess(c, resp)
}

// DisbursementOrderDisbursement 撤销支款订单审核（支款订单）
// 订单状态：5%支款
// 审核状态：3%草稿待审核|4%驳回待审核|5%审核驳回 -> 2%草稿|5%审核驳回
// StatusOrder 订单状态#1%支付成功|2%定金|3%首款|4%尾款|5%支款|6%已关闭
// StatusReview 审核状态#1%审核通过|2%草稿|3%草稿待审核|4%驳回待审核|5%审核驳回
func DisbursementOrderDisbursement(ctx context.Context, c *app.RequestContext) {
	funcName := getFuncName(1)

	var err error
	var req admin_api.DisbursementOrderDisbursementReq
	err = c.BindAndValidate(&req)
	if err != nil {
		logger.CtxErrorf(ctx, "%s invalid request param: %v", funcName, err)
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	orderId := req.GetId()
	editStatus := order.StatusOrder_STATUS_ORDER_DISBURSEMENT

	// 获取订单信息
	respRpc, returnBreak := getOrderInfo(ctx, c, orderId, "", funcName)
	if returnBreak {
		return
	}

	status := respRpc.GetOrder().GetStatus()
	statusReview := respRpc.GetOrder().GetStatusReview()
	statusPrev := respRpc.GetOrder().GetStatusPrev()
	StatusReviewPrev := respRpc.GetOrder().GetStatusReviewPrev()

	// 仅支款订单状态，允许操作撤销支款
	if status != editStatus {
		httputil.ResponseErrorCodeWithLang(c, errno.Errno_OrderStatusNotPermission, i18n.OrderStatusNotPermission)

		return
	}

	// 仅草稿待审核，审核驳回待审核，审核驳回，允许操作撤销支款
	if statusReview != order.StatusOrderReview_STATUS_REVIEW_DRAFT_AUDIT &&
		statusReview != order.StatusOrderReview_STATUS_REVIEW_REJECT_AUDIT &&
		statusReview != order.StatusOrderReview_STATUS_REVIEW_REJECT {
		httputil.ResponseErrorCodeWithLang(c, errno.Errno_OrderStatusNotPermission, i18n.OrderStatusNotPermission)

		return
	}

	//if newStatusReview != order.StatusOrderReview_STATUS_REVIEW_DRAFT_AUDIT &&
	//	newStatusReview != order.StatusOrderReview_STATUS_REVIEW_REJECT_AUDIT {
	//	httputil.ResponseErrorCodeWithLang(c, errno.Errno_OrderStatusNotPermission, i18n.OrderStatusNotPermission)
	//	return
	//}

	//// 获取订单最新一条操作日志信息，用于回滚订单
	//respRpc, returnBreak := getOrderInfoForCurrentStatus(ctx, c, funcName, orderId, editStatus)
	//if returnBreak {
	//	return
	//}
	//
	//if len(respRpc.GetOrderOperationLog()) == 0 {
	//	httputil.ResponseErrorCodeWithLang(c, errno.Errno_OrderNotFound, i18n.OrderNotFound)
	//	return
	//}
	//
	//preOrderOperationLog := respRpc.GetOrderOperationLog()[0]
	//log := preOrderOperationLog.GetLog()
	//newStatus := log.GetNew().GetStatus()
	//newStatusReview := log.GetNew().GetStatusReview()
	//oldOrderStatus := log.GetOld().GetStatus()
	//oldStatusReview := log.GetOld().GetStatusReview()
	//logger.Infof("preOrderOperationLog: %v, newOrderStatus: %d, newStatusReview: %d, oldOrderStatus: %d, oldStatusReview: %d", log, newStatus, newStatusReview, oldOrderStatus, oldStatusReview)
	//
	//// 日志中的订单状态不是支款待审核，则无法操作撤销支款审核
	//// 订单状态：5%支款
	//if newStatus != order.StatusOrder_STATUS_ORDER_DISBURSEMENT {
	//	httputil.ResponseErrorCodeWithLang(c, errno.Errno_OrderStatusNotPermission, i18n.OrderStatusNotPermission)
	//	returnBreak = true
	//	return
	//}
	//
	//// 日志中的订单状态不是支款待审核，则无法操作撤销支款审核
	//// 审核状态：3%草稿待审核|4%驳回待审核
	//if newStatusReview != order.StatusOrderReview_STATUS_REVIEW_DRAFT_AUDIT &&
	//	newStatusReview != order.StatusOrderReview_STATUS_REVIEW_REJECT_AUDIT {
	//	httputil.ResponseErrorCodeWithLang(c, errno.Errno_OrderStatusNotPermission, i18n.OrderStatusNotPermission)
	//	return
	//}

	authInfo := httputil.MustGetAuthInfoFromContext(ctx, c)

	// 撤销支款审核
	// 订单状态恢复到发起支款审核前的状态
	// 审核状态恢复到发起支款审核前的状态
	// 待审核支款金额恢复到0
	query := order.UpdateOrderStatusReq{
		Id:                 orderId,
		Status:             statusPrev,
		StatusReview:       StatusReviewPrev,
		RevokeDisbursement: order.StatusYesNo_STATUS_YES,
		Base:               &base.BaseReq{AuthInfo: httputil.ConvertToAuthInfo(authInfo)},
	}

	// 如果撤销到支款列表，则撤销到支款完成（支款状态为已支款）
	if statusPrev == order.StatusOrder_STATUS_ORDER_DISBURSEMENT {
		query.StatusPayDisbursement = order.StatusOrderPay_STATUS_PAY_PAID
	}

	// 更新订单状态
	respUpdateRpc, err := global.OrderClient.UpdateOrderStatus(ctx, &query)
	if err != nil {
		logger.CtxErrorf(ctx, "%s global.OrderClient.UpdateOrderStatus rpc error: %v", funcName, err)
		httputil.ResponseInternalErr(c)
		returnBreak = true
		return
	}

	if respUpdateRpc.GetBase().GetCode() != errno.Errno_SUCCESS {
		logger.CtxErrorf(ctx, "%s global.OrderClient.UpdateOrderStatus rpc base error %v", funcName, respUpdateRpc.GetBase())
		httputil.ResponseErrorCodeWithBase(c, respUpdateRpc.GetBase())
		returnBreak = true
		return
	}

	refundInfo, _ := global.FinancialClient.RefundInfo(ctx, &order.FinancialRefundInfoReq{
		OrderId: orderId,
	})
	if refundInfo.Id != 0 {
		approveLog, _ := global.FinancialClient.RefundApproveList(ctx, &order.FinancialRefundApproveListReq{
			FinancialRefundId: refundInfo.Id,
			PageNum:           1,
			PageSize:          2,
		})
		if len(approveLog.FinancialRefundApproveInfo) > 0 {
			//有记录不删
			global.FinancialClient.RefundUpdate(ctx, &order.FinancialRefundUpdateReq{
				FinancialRefundId: refundInfo.Id,
				ApproveStatus:     4,
			})
		} else {
			//没记录删掉
			global.FinancialClient.RefundDel(ctx, &order.FinancialRefundDelReq{
				FinancialRefundId: refundInfo.Id,
			})
		}
	}

	resp := new(admin_api.DisbursementOrderDisbursementRsp)
	resp.Id = req.GetId()

	httputil.ResponseSuccess(c, resp)
}

// stringsToDecimal 字符串转 decimal，为空时，默认0
func stringsToDecimal(str string) decimal.Decimal {
	if strings.TrimSpace(str) == "" {
		return decimal.Zero
	}

	return decimal.RequireFromString(str)
}
