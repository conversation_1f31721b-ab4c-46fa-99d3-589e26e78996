package finanacial_service

import (
	"context"
	"encoding/json"
	"strconv"
	"uofferv2/kitex_gen/base"
	"uofferv2/kitex_gen/server/cmd/customer"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/kitex_gen/server/cmd/user"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/ctxmeta"
	"uofferv2/pkg/logger"
	"uofferv2/pkg/utils"
	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"
	"uofferv2/server/cmd/admin_api/internal/global"
	"uofferv2/server/cmd/admin_api/internal/services/httputil"

	"github.com/cloudwego/hertz/pkg/app"
)

type RelationFundRequest struct {
	OrderId  int64  `json:"order_id"`
	OrderNo  string `json:"order_no"`
	FundType int32  `json:"fund_type"`
}

// RefundList 支款列表.
// @router /financial/RefundList [POST]
func RefundList(ctx context.Context, c *app.RequestContext) {
	var req admin_api.FinancialRefundListReq
	if err := c.BindAndValidate(&req); err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	//数据权限处理
	//主管
	staffIds := make([]int64, 0, 0)
	isManager := ctxmeta.IsManger(ctx)
	isAdmin := ctxmeta.IsFinancePayableFull(ctx)
	isControl := req.IsControl
	if isAdmin || isControl == 0 {
		//超管
		staffIds = []int64{}
	} else if isManager {
		//主管
		staffIdList, err := global.UsersClient.ListChildEmployeeById(ctx, &user.ListChildEmployeeByIdReq{
			Id: ctxmeta.MustGetAuth(ctx).EmployeeId(),
		})
		if err != nil {
			logger.CtxErrorf(ctx, "ListChildEmployeeById rpc internal error: %v", err)
			httputil.ResponseInternalErr(c)
			return
		}
		staffIds = staffIdList.Id
		staffIds = append(staffIds, ctxmeta.MustGetAuth(ctx).EmployeeId())
	} else {
		//员工
		staffIds = []int64{ctxmeta.MustGetAuth(ctx).EmployeeId()}
	}

	// 转换 PaymentAccountTypes 类型
	paymentAccountTypes := utils.ConvertSlice(req.PaymentAccountTypes, func(t admin_api.PaymentAccountType) order.PaymentAccountType {
		return order.PaymentAccountType(t)
	})

	respRpc, err := global.FinancialClient.RefundList(ctx, &order.FinancialRefundListReq{
		ApproveStatus:       req.ApproveStatus,
		ApproveStatusList:   req.ApproveStatusList,
		PageNum:             req.PageNum,
		PageSize:            req.PageSize,
		OrderBy:             req.OrderBy,
		CreatedAtStart:      req.CreatedAtStart,
		CreatedAtEnd:        req.CreatedAtEnd,
		PassTimeStart:       req.PassTimeStart,
		PassTimeEnd:         req.PassTimeEnd,
		RejectTimeStart:     req.RejectTimeStart,
		RejectTimeEnd:       req.RejectTimeEnd,
		CompleteTimeStart:   req.CompleteTimeStart,
		CompleteTimeEnd:     req.CompleteTimeEnd,
		RefundNo:            req.RefundNo,
		RefundType:          req.RefundType,
		OrderNo:             req.OrderNo,
		CustomerId:          req.CustomerId,
		GoodsName:           req.GoodsName,
		ServiceName:         req.ServiceName,
		SubmitId:            req.SubmitId,
		BrandName:           req.BrandName,
		BusinessName:        req.BusinessName,
		PaymentAccountId:    req.PaymentAccountId,
		PaymentAccountIds:   req.PaymentAccountIds,
		Currency:            req.Currency,
		WorkflowId:          req.WorkflowId,
		WorkflowNo:          req.WorkflowNo,
		UserType:            req.UserType,
		StaffIds:            staffIds,
		PaymentAccountTypes: paymentAccountTypes,
		PaymentAccountType:  order.PaymentAccountType(req.PaymentAccountType),
		UserSource:          req.UserSource,
		UserSourceDepart:    req.UserSourceDepart,
		OrderSource:         req.OrderSource,
		OrderSourceDepart:   req.OrderSourceDepart,
		CustomerName:        req.CustomerName,
		SubmitDepart:        req.SubmitDepart,
		TransactionNo:       req.TransactionNo,
		ApproveBy:           req.ApproveBy,
		RefundDeadlineStart:      req.RefundDeadlineStart,
		RefundDeadlineEnd:        req.RefundDeadlineEnd,
	})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	if len(respRpc.FinancialRefundList) == 0 {
		httputil.ResponseSuccess(c, &admin_api.FinancialRefundListRsp{})
		return
	}
	items := make([]*admin_api.FinancialRefundInfo, 0, len(respRpc.FinancialRefundList))
	customerIds := make([]int64, 0, 0)
	employeeIds := make([]int64, 0, 0)

	for _, item := range respRpc.FinancialRefundList {
		var refundAgreement []*admin_api.ImageInfo
		var approveLog []*admin_api.ImageInfo
		var scholarshipAgreement []*admin_api.ImageInfo
		var visa []*admin_api.ImageInfo
		var studentCard []*admin_api.ImageInfo
		var tuitionPaymentProof []*admin_api.ImageInfo
		var transactionNo []*admin_api.ImageInfo
		if len(item.RefundAgreement) > 0 {
			_ = json.Unmarshal([]byte(item.RefundAgreement), &refundAgreement)
		}
		if len(item.ApproveLog) > 0 {
			_ = json.Unmarshal([]byte(item.ApproveLog), &approveLog)
		}
		if len(item.ScholarshipAgreement) > 0 {
			_ = json.Unmarshal([]byte(item.ScholarshipAgreement), &scholarshipAgreement)
		}
		if len(item.Visa) > 0 {
			_ = json.Unmarshal([]byte(item.Visa), &visa)
		}
		if len(item.StudentCard) > 0 {
			_ = json.Unmarshal([]byte(item.StudentCard), &studentCard)
		}
		if len(item.TuitionPaymentProof) > 0 {
			_ = json.Unmarshal([]byte(item.TuitionPaymentProof), &tuitionPaymentProof)
		}
		if len(item.TransactionNo) > 0 {
			_ = json.Unmarshal([]byte(item.TransactionNo), &transactionNo)
			for i, _ := range transactionNo {
				transactionNo[i].ThumbnailUrl = transactionNo[i].Url
			}
		}
		serviceName := ""
		if len(item.ServiceName) > 0 {
			serviceName = item.ServiceName[0]
		}
		var amock []*admin_api.AccountInfo
		for _, v := range item.AccountInfo {
			amock = append(amock, &admin_api.AccountInfo{
				Type: v.Type,
				Name: v.Name,
			})
		}
		items = append(items, &admin_api.FinancialRefundInfo{
			Id:       item.Id,
			RefundNo: item.RefundNo,
			OrderInfo: &admin_api.RelationOrderInfo{
				OrderId:     item.OrderId,
				OrderNo:     item.OrderNo,
				GoodsName:   item.GoodsName,
				GoodsSpec:   item.GoodsSpecsName,
				ServiceName: serviceName,
				GoodsNum:    item.GoodsNum,
			},
			CustomerId:               item.CustomerId,
			ShouldAmountOther:        item.ShouldAmountOther,
			RealAmountOther:          item.RealAmountOther,
			Currency:                 item.Currency,
			CreatedAt:                item.CreatedAt,
			SubmitId:                 item.SubmitId,
			RefundType:               item.RefundType,
			PaymentAccountId:         item.PaymentAccountId,
			TransactionNo:            transactionNo,
			AccountType:              item.AccountType,
			FundNo:                   item.FundNo,
			ContractAmount:           item.ContractAmount,
			BusinessName:             item.BusinessName,
			BrandName:                item.BrandName,
			ServiceName:              item.ServiceName,
			PassTime:                 item.PassTime,
			RejectTime:               item.RejectTime,
			CompleteTime:             item.CompleteTime,
			AccountName:              item.AccountName,
			ApproveStatus:            item.ApproveStatus,
			RealAmountRmb:            item.RealAmountRmb,
			RefundReason:             item.RefundReason,
			WorkflowNo:               item.WorkflowNo,
			WorkflowId:               item.WorkflowId,
			WorkflowName:             item.WorkflowName,
			RefundAgreement:          refundAgreement,
			ApproveLog:               approveLog,
			ScholarshipAgreement:     scholarshipAgreement,
			Visa:                     visa,
			StudentCard:              studentCard,
			TuitionPaymentProof:      tuitionPaymentProof,
			ExchangeRate:             item.ExchangeRate,
			RefundReceiveAccount:     item.RefundReceiveAccount,
			RefundReceiveAccountType: item.RefundReceiveAccountType,
			ApproveBy:                item.ApproveBy,
			ApproveComment:           item.ApproveComment,
			UserType:                 item.UserType,
			AccountInfo:              amock,
			OrderTransactionNo:       item.OrderTransactionNo,
			PaidAmount:               item.PaidAmount,
			RefundDeadline:           item.RefundDeadline,
		})
		customerIds = append(customerIds, item.CustomerId)
		employeeIds = append(employeeIds, item.SubmitId)
		employeeIds = append(employeeIds, item.ApproveBy)
		for _, i := range item.UserSource {
			employeeIds = append(employeeIds, i)
		}
		for _, i := range item.OrderSource {
			employeeIds = append(employeeIds, i)
		}
		for _, i := range item.SubmitSource {
			employeeIds = append(employeeIds, i)
		}
	}
	// 获取客户信息
	customerRpcResp, err := global.CustomerProfileClient.GetCustomersByIds(ctx, &customer.GetCustomersByIdsReq{
		CustomerIds: customerIds,
	})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	// 构建客户信息映射并关联到 items
	customerMap := make(map[int64]*admin_api.CustomerWithTags, len(customerRpcResp.GetCustomers()))
	for _, customerInfo := range customerRpcResp.GetCustomers() {
		customerMap[int64(customerInfo.GetId())] = &admin_api.CustomerWithTags{
			Id:        int64(customerInfo.GetId()),
			Name:      customerInfo.GetName(),
			Tags:      convertTags2(customerInfo.Tags),
			SmartTags: convertTags2(customerInfo.SmartTags),
		}
	}
	for i := range items {
		if customerInfo, ok := customerMap[items[i].CustomerId]; ok {
			items[i].CustomerInfo = customerInfo
		}
	}
	employeeResp := &user.GetEmployeeInfoByIdsRsp{}
	if len(employeeIds) > 0 {
		employeeResp, err = global.UsersClient.GetEmployeeInfoByIds(ctx, &user.GetEmployeeInfoByIdsReq{
			Id: employeeIds,
		})
		if err != nil {
			httputil.ResponseParamsErr(c, err.Error())
			return
		}
	}
	// 创建员工信息映射
	type employeeInfo struct {
		name     string
		deptName string
	}
	employeeMap := make(map[int64]employeeInfo)
	for _, emp := range employeeResp.List {
		employeeMap[emp.Id] = employeeInfo{
			name:     emp.Name,
			deptName: emp.DeptName,
		}
	}
	// 更新员工信息
	for i := range items {
		if info, ok := employeeMap[items[i].SubmitId]; ok {
			items[i].SubmitName = info.name
			items[i].SubmitDepartment = info.deptName
		}
		if info2, ok2 := employeeMap[items[i].ApproveBy]; ok2 {
			items[i].ApproveName = info2.name
			items[i].ApproveDepartment = info2.deptName
		}
	}

	for index, i := range respRpc.FinancialRefundList {
		//订单来源人
		var omock []*admin_api.IdNameDept
		for _, e := range i.OrderSource {
			if info, ok := employeeMap[e]; ok {
				omock = append(omock, &admin_api.IdNameDept{
					Id:     e,
					Name:   info.name,
					Depart: info.deptName,
				})
			}
		}
		items[index].OrderSource = omock

		//客户来源人
		var cmock []*admin_api.IdNameDept
		for _, c := range i.UserSource {
			if info, ok := employeeMap[c]; ok {
				cmock = append(cmock, &admin_api.IdNameDept{
					Id:     c,
					Name:   info.name,
					Depart: info.deptName,
				})
			}
		}
		items[index].UserSource = cmock

		//订单共同提交人
		var smock []*admin_api.IdNameDept
		smock = append(smock, &admin_api.IdNameDept{
			Id:     items[index].SubmitId,
			Name:   items[index].SubmitName,
			Depart: items[index].SubmitDepartment,
		})
		for _, s := range i.SubmitSource {
			if info, ok := employeeMap[s]; ok {
				smock = append(smock, &admin_api.IdNameDept{
					Id:     s,
					Name:   info.name,
					Depart: info.deptName,
				})
			}
		}
		items[index].SubmitSource = smock
	}

	httputil.ResponseSuccess(c, &admin_api.FinancialRefundListRsp{
		Total: respRpc.Total,
		Items: items,
	})
}

// RefundDetail 支款详情.
// @router /financial/RefundDetail [POST]
func RefundDetail(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.FinancialRefundDetailReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	resp := new(admin_api.FinancialRefundDetailRsp)
	refundRespRpc, _ := global.FinancialClient.RefundInfo(ctx, &order.FinancialRefundInfoReq{
		Id:            req.Id,
		RefundNo:      req.RefundNo,
		OrderId:       req.OrderId,
		RefundType:    req.RefundType,
		ApproveStatus: req.ApproveStatus,
	})
	if refundRespRpc.Id == 0 {
		httputil.ResponseSuccess(c, resp)
		return
	}
	customerRpcResp, err := global.CustomerProfileClient.GetCustomerDetail(ctx, &customer.GetCustomerDetailReq{
		Id: refundRespRpc.CustomerId,
	})
	customerInfo := customerRpcResp.GetCustomerInfo() //客户信息
	refundCustomer := &admin_api.CustomerWithTags{
		Id:        refundRespRpc.CustomerId,
		Name:      customerInfo.Name,
		Tags:      convertTags2(customerInfo.Tags),
		SmartTags: convertTags2(customerInfo.SmartTags),
	}
	var refundAgreement []*admin_api.ImageInfo
	var approveLog []*admin_api.ImageInfo
	var scholarshipAgreement []*admin_api.ImageInfo
	var visa []*admin_api.ImageInfo
	var studentCard []*admin_api.ImageInfo
	var tuitionPaymentProof []*admin_api.ImageInfo
	var transactionNo []*admin_api.ImageInfo
	if len(refundRespRpc.RefundAgreement) > 0 {
		_ = json.Unmarshal([]byte(refundRespRpc.RefundAgreement), &refundAgreement)
	}
	if len(refundRespRpc.ApproveLog) > 0 {
		_ = json.Unmarshal([]byte(refundRespRpc.ApproveLog), &approveLog)
	}
	if len(refundRespRpc.ScholarshipAgreement) > 0 {
		_ = json.Unmarshal([]byte(refundRespRpc.ScholarshipAgreement), &scholarshipAgreement)
	}
	if len(refundRespRpc.Visa) > 0 {
		_ = json.Unmarshal([]byte(refundRespRpc.Visa), &visa)
	}
	if len(refundRespRpc.StudentCard) > 0 {
		_ = json.Unmarshal([]byte(refundRespRpc.StudentCard), &studentCard)
	}
	if len(refundRespRpc.TuitionPaymentProof) > 0 {
		_ = json.Unmarshal([]byte(refundRespRpc.TuitionPaymentProof), &tuitionPaymentProof)
	}
	if len(refundRespRpc.TransactionNo) > 0 {
		_ = json.Unmarshal([]byte(refundRespRpc.TransactionNo), &transactionNo)
		for i, _ := range transactionNo {
			transactionNo[i].ThumbnailUrl = transactionNo[i].Url
		}
	}
	financialRefundInfo := &admin_api.FinancialRefundDetail{
		Id:                       refundRespRpc.Id,
		OrderId:                  refundRespRpc.OrderId,
		OrderNo:                  refundRespRpc.OrderNo,
		RefundNo:                 refundRespRpc.RefundNo,
		CustomerId:               refundRespRpc.CustomerId,
		RealAmountOther:          refundRespRpc.RealAmountOther,
		Currency:                 refundRespRpc.Currency,
		RefundType:               refundRespRpc.RefundType,
		ApproveStatus:            refundRespRpc.ApproveStatus,
		CreatedAt:                refundRespRpc.CreatedAt,
		RefundReceiveAccountType: refundRespRpc.RefundReceiveAccountType,
		RefundReceiveAccount:     refundRespRpc.RefundReceiveAccount,
		CompleteTime:             refundRespRpc.CompleteTime,
		RefundReason:             refundRespRpc.RefundReason,
		RefundAgreement:          refundAgreement,
		ApproveLog:               approveLog,
		AccountName:              refundRespRpc.AccountName,
		RealAmountRmb:            refundRespRpc.RealAmountRmb,
		ExchangeRate:             refundRespRpc.ExchangeRate,
		ScholarshipAgreement:     scholarshipAgreement,
		StudentCard:              studentCard,
		Visa:                     visa,
		TuitionPaymentProof:      tuitionPaymentProof,
		UserType:                 refundRespRpc.UserType,
		TransactionNo:            transactionNo,
		RefundDeadline:           refundRespRpc.RefundDeadline,
	}
	historyRpcResp, err := global.FinancialClient.RefundAssociateList(ctx, &order.FinancialRefundAssociateReq{
		Id:      refundRespRpc.Id,
		OrderId: refundRespRpc.OrderId,
	})
	fund1, err := RelationFundInfo(ctx, &RelationFundRequest{OrderNo: refundRespRpc.OrderNo, FundType: 1})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	fund2, err := RelationFundInfo(ctx, &RelationFundRequest{OrderNo: refundRespRpc.OrderNo, FundType: 2})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	fund3, err := RelationFundInfo(ctx, &RelationFundRequest{OrderNo: refundRespRpc.OrderNo, FundType: 3})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	// 创建动态长度的切片
	fundList := make([]*admin_api.FinancialFundDetail, 0)
	if fund1.Id != 0 {
		fundList = append(fundList, fund1)
	}
	if fund2.Id != 0 {
		fundList = append(fundList, fund2)
	}
	if fund3.Id != 0 {
		fundList = append(fundList, fund3)
	}

	// 转换历史支款信息
	var totalAmount float64
	refundList := make([]*admin_api.FinancialRefundDetail, 0, len(historyRpcResp.RefundList))
	for _, refund := range historyRpcResp.RefundList {
		amount, err := strconv.ParseFloat(refund.RealAmountRmb, 64)
		if err == nil {
			totalAmount += amount
		}
		var historyRefundAgreement []*admin_api.ImageInfo
		var historyApproveLog []*admin_api.ImageInfo
		if len(refund.RefundAgreement) > 0 {
			_ = json.Unmarshal([]byte(refund.RefundAgreement), &historyRefundAgreement)
		}
		if len(refund.ApproveLog) > 0 {
			_ = json.Unmarshal([]byte(refund.ApproveLog), &historyApproveLog)
		}
		refundList = append(refundList, &admin_api.FinancialRefundDetail{
			Id:                       refund.Id,
			RefundNo:                 refund.RefundNo,
			CustomerId:               refund.CustomerId,
			RealAmountOther:          refund.RealAmountOther,
			Currency:                 refund.Currency,
			CreatedAt:                refund.CreatedAt,
			RefundType:               refund.RefundType,
			CompleteTime:             refund.CompleteTime,
			RefundReceiveAccountType: refund.RefundReceiveAccountType,
			RefundReceiveAccount:     refund.RefundReceiveAccount,
			RefundReason:             refund.RefundReason,
			RefundAgreement:          historyRefundAgreement,
			ApproveLog:               historyApproveLog,
			RealAmountRmb:            refund.RealAmountRmb,
			ExchangeRate:             refund.ExchangeRate,
		})
	}

	//审核信息
	approveRespRpc, _ := global.FinancialClient.RefundApproveList(ctx, &order.FinancialRefundApproveListReq{
		FinancialRefundId: refundRespRpc.Id,
		PageNum:           1,
		PageSize:          100,
	})

	// 转换审核日志
	approveByIds := make([]int64, 0, len(approveRespRpc.FinancialRefundApproveInfo))
	approveList := make([]*admin_api.FinancialRefundApproveLog, 0, len(approveRespRpc.FinancialRefundApproveInfo))
	for _, approve := range approveRespRpc.FinancialRefundApproveInfo {
		approveList = append(approveList, &admin_api.FinancialRefundApproveLog{
			ApproveBy:      approve.ApproveBy,
			Status:         approve.Status,
			ApproveComment: approve.ApproveComment,
			CreatedAt:      approve.CreatedAt,
		})
		approveByIds = append(approveByIds, approve.ApproveBy)
	}
	employeeResp, err := global.UsersClient.GetEmployeeInfoByIds(ctx, &user.GetEmployeeInfoByIdsReq{
		Id: approveByIds,
	})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	// 创建员工信息映射
	type employeeInfo struct {
		name     string
		deptName string
	}
	employeeMap := make(map[int64]employeeInfo)
	for _, emp := range employeeResp.List {
		employeeMap[emp.Id] = employeeInfo{
			name:     emp.Name,
			deptName: emp.DeptName,
		}
	}
	// 更新审批日志中的员工信息
	for i := range approveList {
		if info, ok := employeeMap[approveList[i].ApproveBy]; ok {
			approveList[i].ApproveName = info.name
			approveList[i].ApproveDepartment = info.deptName
		}
	}

	resp.CustomerInfo = refundCustomer                                    //客户信息
	resp.RefundTotalAmount = strconv.FormatFloat(totalAmount, 'f', 2, 64) // 保留2位小数
	resp.FinancialRefundInfo = financialRefundInfo                        //本次支款信息
	resp.RelationFinancialInfo = fundList
	resp.FinancialRefundList = refundList
	resp.ApproveLog = approveList
	httputil.ResponseSuccess(c, resp)
}

// RefundApprove 支款审核.
// @router /financial/RefundApprove [POST]
func RefundApprove(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.FinancialRefundApproveReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	employeeId := ctxmeta.MustGetAuth(ctx).EmployeeId()
	resp := new(admin_api.FinancialRefundApproveRsp)
	//更新支款状态

	transactionNo, _ := json.Marshal(req.TransactionNo)
	query := &order.FinancialRefundUpdateReq{
		FinancialRefundId: req.FinancialRefundId,
		Status:            req.Status, //审批状态 1通过,2驳回,3回退,4完成打款
		AccountName:       req.AccountName,
		TransactionNo:     string(transactionNo),
		ApproveComment:    req.ApproveComment,
		UpdatedBy:         employeeId,
		PaymentAccountId:  req.PaymentAccountId,
	}
	if req.Status == 1 || req.Status == 2 {
		query.ApproveBy = employeeId
	}
	rpcResp, err := global.FinancialClient.RefundUpdate(ctx, query)
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil {
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	//新增审核记录
	_, err = global.FinancialClient.RefundApproveCreate(ctx, &order.FinancialRefundApproveCreateReq{
		FinancialRefundId: req.FinancialRefundId,
		Status:            req.Status, //审批状态 1通过,2驳回,3回退,4完成打款
		ApproveComment:    req.ApproveComment,
		ApproveBy:         employeeId,
		OrderId:           req.OrderId,
	})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	// 1、发起支款后，就是支款待审核状态
	// 2、财务操作审核通过，订单就是待打款状态
	// 3、财务操作完成支款，订单就是支款完成状态
	// 4、财务操作审核驳回，订单就是支款审核驳回状态
	// 5、财务操作回退呢，订单就回到支款待审核状态

	// 更新订单状态：审核驳回
	newStatusReview := order.StatusOrderReview_STATUS_REVIEW_REJECT
	// 更新支款支付状态：待支付
	newStatusPayDisbursement := order.StatusOrderPay_STATUS_PAY_PENDING
	// 1通过
	if req.GetStatus() == 1 {
		// 审核通过
		newStatusReview = order.StatusOrderReview_STATUS_REVIEW_PASS
		// 支款中
		newStatusPayDisbursement = order.StatusOrderPay_STATUS_PAY_PENDING
	}

	// 2驳回
	if req.GetStatus() == 2 {
		// 审核驳回
		newStatusReview = order.StatusOrderReview_STATUS_REVIEW_REJECT
		// 待支付
		newStatusPayDisbursement = order.StatusOrderPay_STATUS_PAY_PENDING
	}

	// 3回退
	if req.GetStatus() == 3 {
		// 草稿待审核
		newStatusReview = order.StatusOrderReview_STATUS_REVIEW_DRAFT_AUDIT
		// 待支付
		newStatusPayDisbursement = order.StatusOrderPay_STATUS_PAY_PENDING

		// 获取订单最新一条操作日志信息，用于回滚订单
		respRpc, _ := getOrderInfo(ctx, c, req.GetOrderId(), "", "RefundApprove")
		if respRpc != nil && len(respRpc.GetOrderOperationLog()) > 0 {
			preOrderOperationLog := respRpc.GetOrderOperationLog()[0]
			log := preOrderOperationLog.GetLog()
			logger.Infof("/financial/RefundApprove preOrderOperationLog: %v, oldOrderStatus: %d, oldStatusReview: %d", log, log.GetOld().GetStatus(), log.GetNew().GetStatusReview())
			newStatusReview = log.GetOld().GetStatusReview()
		}
	}

	// 4完成打款
	if req.GetStatus() == 4 {
		// 审核通过
		newStatusReview = order.StatusOrderReview_STATUS_REVIEW_PASS
		// 支款完成
		newStatusPayDisbursement = order.StatusOrderPay_STATUS_PAY_PAID
	}

	logger.Infof("newStatusReview: %d, newStatusPayDisbursement: %d", newStatusReview, newStatusPayDisbursement)

	// 获取财务支款信息
	refundRespRpc, _ := global.FinancialClient.RefundInfo(ctx, &order.FinancialRefundInfoReq{
		Id:      req.GetFinancialRefundId(),
		OrderId: req.GetOrderId(),
	})

	// 款项类型（1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费）
	// 支款类型#1%退定金|2%退服务费|3%奖学金|4%退差价|5%支付违约金
	// 仅支款类型为订单支持的类型时，才更新订单状态
	if refundRespRpc != nil && refundRespRpc.GetRefundType() < 6 {
		authInfo := httputil.MustGetAuthInfoFromContext(ctx, c)

		query := order.UpdateOrderStatusReq{
			Id:                    req.GetOrderId(),
			StatusReview:          newStatusReview,
			StatusPayDisbursement: newStatusPayDisbursement,
			ReviewerId:            employeeId,
			Base:                  &base.BaseReq{AuthInfo: httputil.ConvertToAuthInfo(authInfo)},
		}

		// 更新订单状态
		_, err = global.OrderClient.UpdateOrderStatus(ctx, &query)
		if err != nil {
			httputil.ResponseParamsErr(c, err.Error())

			return
		}
	}

	httputil.ResponseSuccess(c, resp)
}

func RelationFundInfo(ctx context.Context, req *RelationFundRequest) (*admin_api.FinancialFundDetail, error) {
	fundRespRpc, _ := global.FinancialClient.FundInfo(ctx, &order.FinancialFundInfoReq{
		OrderNo:  req.OrderNo,
		FundType: req.FundType,
	})
	if fundRespRpc == nil {
		return nil, nil
	}
	financialFundInfo := FundDetailConToApi(fundRespRpc)
	return financialFundInfo, nil
}
