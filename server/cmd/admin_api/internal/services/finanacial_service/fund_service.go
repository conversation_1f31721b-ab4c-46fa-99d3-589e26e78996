package finanacial_service

import (
	"context"
	"encoding/json"
	"strconv"
	"unicode"

	"uofferv2/kitex_gen/base"
	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/customer"
	"uofferv2/kitex_gen/server/cmd/message"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/kitex_gen/server/cmd/tools"
	"uofferv2/kitex_gen/server/cmd/user"
	"uofferv2/kitex_gen/ws_message"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/ctxmeta"
	"uofferv2/pkg/logger"
	"uofferv2/pkg/utils"
	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"
	"uofferv2/server/cmd/admin_api/internal/global"
	"uofferv2/server/cmd/admin_api/internal/services/httputil"

	"github.com/cloudwego/hertz/pkg/protocol/consts"
	"github.com/shopspring/decimal"

	"github.com/cloudwego/hertz/pkg/app"
)

// FundList 收款单列表.
// @router /financial/FundList [POST]
func FundList(ctx context.Context, c *app.RequestContext) {
	var req admin_api.FinancialFundListReq
	if err := c.BindAndValidate(&req); err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	//数据权限处理
	//主管
	staffIds := make([]int64, 0, 0)
	isManager := ctxmeta.IsManger(ctx)
	isAdmin := ctxmeta.IsFinanceReceivableFull(ctx)
	isControl := req.IsControl
	if isAdmin || isControl == 0 {
		//超管或者没有权限控制
		staffIds = []int64{}
	} else if isManager {
		//主管
		staffIdList, err := global.UsersClient.ListChildEmployeeById(ctx, &user.ListChildEmployeeByIdReq{
			Id: ctxmeta.MustGetAuth(ctx).EmployeeId(),
		})
		if err != nil {
			logger.CtxErrorf(ctx, "ListChildEmployeeById rpc internal error: %v", err)
			httputil.ResponseInternalErr(c)
			return
		}
		staffIds = staffIdList.Id
		staffIds = append(staffIds, ctxmeta.MustGetAuth(ctx).EmployeeId())
	} else {
		//员工
		staffIds = []int64{ctxmeta.MustGetAuth(ctx).EmployeeId()}
	}
	paymentAccountTypes := utils.ConvertSlice(req.PaymentAccountTypes, func(t admin_api.PaymentAccountType) order.PaymentAccountType {
		return order.PaymentAccountType(t)
	})

	respRpc, err := global.FinancialClient.FundList(ctx, &order.FinancialFundListReq{
		ApproveStatus:       req.ApproveStatus,
		ApproveStatusList:   req.ApproveStatusList,
		FundNo:              req.FundNo,
		FundType:            req.FundType,
		OrderNo:             req.OrderNo,
		CustomerId:          req.CustomerId,
		GoodsName:           req.GoodsName,
		ServiceName:         req.ServiceName,
		BrandName:           req.BrandName,
		BusinessName:        req.BusinessName,
		PayType:             req.PayType,
		PaymentAccountId:    req.PaymentAccountId, //暂时保留，兼容用
		PaymentAccountIds:   req.PaymentAccountIds,
		Currency:            req.Currency,
		ContractNo:          req.ContractNo,
		OrderBy:             req.OrderBy,
		SubmitId:            req.SubmitId,
		CreatedAtStart:      req.CreatedAtStart,
		CreatedAtEnd:        req.CreatedAtEnd,
		PassTimeStart:       req.PassTimeStart,
		PassTimeEnd:         req.PassTimeEnd,
		RejectTimeStart:     req.RejectTimeStart,
		RejectTimeEnd:       req.RejectTimeEnd,
		TransactionNo:       req.TransactionNo,
		UserType:            req.UserType,
		PageNum:             req.PageNum,
		PageSize:            req.PageSize,
		StaffIds:            staffIds,
		CustomerName:        req.CustomerName,
		PaidTimeStart:       req.PaidTimeStart,
		PaidTimeEnd:         req.PaidTimeEnd,
		UserSource:          req.UserSource,
		UserSourceDepart:    req.UserSourceDepart,
		OrderSource:         req.OrderSource,
		OrderSourceDepart:   req.OrderSourceDepart,
		OrderStatus:         req.OrderStatus,
		SubmitSourceDepart:  req.SubmitSourceDepart,
		PaymentAccountTypes: paymentAccountTypes,
	})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	if len(respRpc.FinancialFundList) == 0 {
		httputil.ResponseSuccess(c, &admin_api.FinancialFundListRsp{})
		return
	}
	items := make([]*admin_api.FinancialFundInfo, 0, len(respRpc.FinancialFundList))
	customerIds := make([]int64, 0, 0)
	employeeIds := make([]int64, 0, 0)
	for _, item := range respRpc.FinancialFundList {
		thirdAmountList := make([]*admin_api.AmountInfo, 0, 0)
		m := map[string]string{}
		tmp1 := decimal.Decimal{}
		tmp2 := decimal.Decimal{}
		tmp3 := decimal.Decimal{}
		paidList := make([]*admin_api.FinancialPaidInfo, 0, len(item.FinancialPaiInfo))
		for _, paid := range item.FinancialPaiInfo {
			var imagesPath []*admin_api.FundContractInfo
			if len(paid.ImagesPath) > 0 {
				_ = json.Unmarshal([]byte(paid.ImagesPath), &imagesPath)
				for i, _ := range imagesPath {
					imagesPath[i].ThumbnailUrl = imagesPath[i].Url
				}
			}
			accountName := ""
			tmp, ok := TypeNameMap[paid.PaidType]
			if ok {
				accountName = tmp
			}
			paidList = append(paidList, &admin_api.FinancialPaidInfo{
				PaymentAccountId:     paid.PaymentAccountId,
				Currency:             paid.Currency,
				PaidType:             admin_api.PaymentAccountType(paid.PaidType),
				FinancialAccountName: paid.AccountName,
				AmountOther:          paid.AmountOther,
				ImagesPath:           imagesPath,
				FinancialPaidId:      paid.Id,
				AmountCny:            paid.AmountCny,
				ExchangeRate:         paid.ExchangeRate,
				TransactionNo:        paid.TransactionNo,
				AccountTypeName:      accountName,
			})
			val, ok := m[paid.Currency]
			if ok {
				tmp1, _ = decimal.NewFromString(val)
				tmp2, _ = decimal.NewFromString(paid.AmountOther)
				tmp3 = tmp1.Add(tmp2)
				m[paid.Currency] = tmp3.String()
			} else {
				m[paid.Currency] = paid.AmountOther
			}
		}
		for k, v := range m {
			thirdAmountList = append(thirdAmountList, &admin_api.AmountInfo{
				Currency: k,
				Amount:   v,
			})
		}
		var contractInfo []*admin_api.FundContractInfo
		_ = json.Unmarshal([]byte(item.ContractUrl), &contractInfo)
		serviceName := ""
		if len(item.ServiceName) > 0 {
			serviceName = item.ServiceName[0]
		}
		items = append(items, &admin_api.FinancialFundInfo{
			Id:     item.Id,
			FundNo: item.FundNo,
			OrderInfo: &admin_api.FinancialOrderInfo{
				OrderId:     item.OrderId,
				OrderNo:     item.OrderNo,
				GoodsName:   item.GoodsName,
				GoodsSpec:   item.GoodsSpec,
				ServiceName: serviceName,
				GoodsNum:    item.GoodsNum,
			},
			CustomerId:        item.CustomerId,
			ShouldAmountOther: item.ShouldAmountOther,
			ShouldAmountRmb:   item.ShouldAmountRmb,
			RealAmountOther:   item.RealAmountOther,
			Currency:          item.Currency,
			PaidTime:          item.PaidTime,
			PayType:           item.PayType,
			FundType:          item.FundType,
			ServiceName:       item.ServiceName,
			BrandName:         item.BrandName,
			BusinessName:      item.BusinessName,
			ContractNo:        item.ContractNo,
			CreatedAt:         item.CreatedAt,
			SubmitId:          item.SubmitId,
			FinancialPaiInfo:  paidList,
			PassTime:          item.PassTime,
			RejectTime:        item.RejectTime,
			ApproveStatus:     item.ApproveStatus,
			RealAmountRmb:     item.RealAmountRmb,
			ExchangeRate:      item.ExchangeRate,
			ContractInfo:      contractInfo,
			WorkflowNo:        item.WorkflowNo,
			WorkflowId:        item.WorkflowId,
			UserType:          item.UserType,
			ThirdAmountList:   thirdAmountList,
			OrderStatus:       item.OrderStatus,
			Remark:            item.Remark,
		})
		for _, i := range item.UserSource {
			employeeIds = append(employeeIds, i)
		}
		for _, i := range item.OrderSource {
			employeeIds = append(employeeIds, i)
		}
		for _, i := range item.SubmitSource {
			employeeIds = append(employeeIds, i)
		}
		customerIds = append(customerIds, item.CustomerId)
		employeeIds = append(employeeIds, item.SubmitId)
	}
	employeeResp := &user.GetEmployeeInfoByIdsRsp{}
	if len(employeeIds) > 0 {
		employeeResp, err = global.UsersClient.GetEmployeeInfoByIds(ctx, &user.GetEmployeeInfoByIdsReq{
			Id: employeeIds,
		})
		if err != nil {
			httputil.ResponseParamsErr(c, err.Error())
			return
		}
	}
	// 获取客户信息
	customerRpcResp, err := global.CustomerProfileClient.GetCustomersByIds(ctx, &customer.GetCustomersByIdsReq{
		CustomerIds: customerIds,
	})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	// 构建客户信息映射并关联到 items
	customerMap := make(map[int64]*admin_api.CustomerWithTag, len(customerRpcResp.GetCustomers()))
	for _, customerInfo := range customerRpcResp.GetCustomers() {
		customerMap[int64(customerInfo.GetId())] = &admin_api.CustomerWithTag{
			Id:        int64(customerInfo.GetId()),
			Name:      customerInfo.GetName(),
			Tags:      convertTags(customerInfo.GetTags()),
			SmartTags: convertTags(customerInfo.GetSmartTags()),
		}
	}
	// 创建员工信息映射
	type employeeInfo struct {
		name     string
		deptName string
	}
	employeeMap := make(map[int64]employeeInfo)
	for _, emp := range employeeResp.List {
		employeeMap[emp.Id] = employeeInfo{
			name:     emp.Name,
			deptName: emp.DeptName,
		}
	}
	// 更新员工信息
	for i := range items {
		if info, ok := employeeMap[items[i].SubmitId]; ok {
			items[i].SubmitName = info.name
			items[i].SubmitDept = info.deptName
			var smock []*admin_api.IdNameDept
			smock = append(smock, &admin_api.IdNameDept{
				Id:     items[i].SubmitId,
				Name:   info.name,
				Depart: info.deptName,
			})
			items[i].UserSource = smock
			items[i].SubmitSource = smock
		}
	}

	for index, i := range respRpc.FinancialFundList {
		//订单来源人
		var omock []*admin_api.IdNameDept
		for _, e := range i.OrderSource {
			if info, ok := employeeMap[e]; ok {
				omock = append(omock, &admin_api.IdNameDept{
					Id:     e,
					Name:   info.name,
					Depart: info.deptName,
				})
			}
		}
		items[index].OrderSource = omock

		//客户来源人
		var cmock []*admin_api.IdNameDept
		for _, c := range i.UserSource {
			if info, ok := employeeMap[c]; ok {
				cmock = append(cmock, &admin_api.IdNameDept{
					Id:     c,
					Name:   info.name,
					Depart: info.deptName,
				})
			}
		}
		items[index].UserSource = cmock

		//订单共同提交人
		var smock []*admin_api.IdNameDept
		smock = append(smock, &admin_api.IdNameDept{
			Id:     items[index].SubmitId,
			Name:   items[index].SubmitName,
			Depart: items[index].SubmitDept,
		})
		for _, s := range i.SubmitSource {
			if info, ok := employeeMap[s]; ok {
				smock = append(smock, &admin_api.IdNameDept{
					Id:     s,
					Name:   info.name,
					Depart: info.deptName,
				})
			}
		}
		items[index].SubmitSource = smock

	}
	//更新客户信息
	for i := range items {
		if customer, ok := customerMap[items[i].CustomerId]; ok {
			items[i].CustomerInfo = customer
		}
	}
	//返回
	httputil.ResponseSuccess(c, &admin_api.FinancialFundListRsp{
		Total: respRpc.Total,
		Items: items,
	})
}

// FundDetail 收款单详情.
// @router /financial/FundListDetail [POST]
func FundDetail(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.FinancialFundDetailReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	resp := new(admin_api.FinancialFundDetailRsp)
	fundRespRpc, _ := global.FinancialClient.FundInfo(ctx, &order.FinancialFundInfoReq{
		Id:         req.Id,
		FundNo:     req.FundNo,
		OrderId:    req.OrderId,
		OrderNo:    req.OrderNo,
		FundType:   req.FundType,
		WorkflowId: req.WorkflowId,
	})
	if fundRespRpc.Id == 0 {
		httputil.ResponseSuccess(c, resp)
		return
	}
	customerRpcResp, err := global.CustomerProfileClient.GetCustomerDetail(ctx, &customer.GetCustomerDetailReq{
		Id: fundRespRpc.CustomerId,
	})
	customerInfo := customerRpcResp.GetCustomerInfo() //客户信息
	fundCustomer := &admin_api.CustomerWithTag{
		Id:        fundRespRpc.CustomerId,
		Name:      customerInfo.Name,
		Tags:      convertTags(customerInfo.Tags),
		SmartTags: convertTags(customerInfo.SmartTags),
	}

	// 汇总所有的员工id
	employeeIds := make([]int64, 0)
	// 提交人
	employeeIds = append(employeeIds, fundRespRpc.SubmitId)
	// 审核人
	employeeIds = append(employeeIds, fundRespRpc.ApproveBy)
	// 共同提交人
	for _, id := range fundRespRpc.OtherSubmitIds {
		employeeIds = append(employeeIds, id)
	}
	// 订单来源人
	for _, id := range fundRespRpc.OrderSourceIds {
		employeeIds = append(employeeIds, id)
	}
	employeeMap, err := GetEmployeeInfoByIds(ctx, employeeIds)
	financialFundInfo := FundDetailConToApi(fundRespRpc)
	if employee, ok := employeeMap[fundRespRpc.SubmitId]; !ok {
		logger.CtxErrorf(ctx, "GetEmployeeInfoById not found employee id: %v", fundRespRpc.SubmitId)
	} else {
		financialFundInfo.SubmitName = employee.Name
		financialFundInfo.SubmitDepartment = employee.DeptName
	}
	if employee, ok := employeeMap[fundRespRpc.ApproveBy]; !ok {
		logger.CtxErrorf(ctx, "GetEmployeeInfoById not found employee id: %v", fundRespRpc.ApproveBy)
	} else {
		financialFundInfo.ApproveName = employee.Name
		financialFundInfo.ApproveDepartment = employee.DeptName
	}

	paidRespRpc, _ := global.FinancialClient.FundPaidList(ctx, &order.FinancialPaidListReq{
		FinancialFundId: fundRespRpc.Id,
	})
	approveRespRpc, _ := global.FinancialClient.FundApproveList(ctx, &order.FinancialFundApproveListReq{
		FinancialFundId: fundRespRpc.Id,
		PageNum:         1,
		PageSize:        100,
	})
	items1 := PaidInfoConToApi(paidRespRpc)
	transactionNos := make([]string, 0, len(items1))
	for _, item := range items1 {
		if len(item.TransactionNo) > 0 {
			transactionNos = append(transactionNos, item.TransactionNo...)
		}
	}
	items2 := FundApproveConToApi(approveRespRpc)
	approveByIds := make([]int64, 0, 0)
	for _, item := range items2 {
		approveByIds = append(approveByIds, item.ApproveBy)
	}
	employeeMap2, err := GetEmployeeInfoByIds(ctx, approveByIds)
	if err != nil {
		logger.CtxErrorf(ctx, "GetEmployeeInfoByIds rpc error: %v, ids: %v", err.Error(), approveByIds)
	}

	// 更新审批日志中的员工信息
	for i := range items2 {
		if info, ok := employeeMap2[items2[i].ApproveBy]; ok {
			items2[i].ApproveName = info.Name
			items2[i].ApproveDepartment = info.DeptName
		}
		items2[i].TransactionNo = transactionNos
	}
	// 订单来源
	for _, id := range fundRespRpc.OrderSourceIds {
		if info, ok := employeeMap[id]; ok {
			financialFundInfo.OrderSource = append(financialFundInfo.OrderSource, &admin_api.IdNameDept{
				Id:     id,
				Name:   info.Name,
				Depart: info.DeptName,
			})
		}
	}
	// 共同提交人
	for _, id := range fundRespRpc.OtherSubmitIds {
		if info, ok := employeeMap[id]; ok {
			financialFundInfo.OtherSubmitInfo = append(financialFundInfo.OtherSubmitInfo, &admin_api.IdNameDept{
				Id:     id,
				Name:   info.Name,
				Depart: info.DeptName,
			})
		}
	}
	// 提交人信息
	if info, ok := employeeMap[fundRespRpc.SubmitId]; ok {
		financialFundInfo.SubmitInfo = &admin_api.IdNameDept{
			Id:     fundRespRpc.SubmitId,
			Name:   info.Name,
			Depart: info.DeptName,
		}
	}

	resp.CustomerInfo = fundCustomer
	resp.FinancialInfo = financialFundInfo
	resp.ApproveLog = items2
	resp.RelationFinancialInfo = GetRelationFund(ctx, fundRespRpc.FundType, fundRespRpc.OrderId)
	httputil.ResponseSuccess(c, resp)
}

// FundApprove 收款单审核.
// @router /financial/FundListApprove [POST]
func FundApprove(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.FinancialFundApproveReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	if !(req.Status == 2 || req.Status == 3) {
		httputil.ResponseParamsErr(c, "status is not supported")
		return
	}
	employeeId := ctxmeta.MustGetAuth(ctx).EmployeeId()
	transactionInfo := make([]*order.TransactionInfo, 0)
	if req.Status == 2 {
		//2是通过要验证交易单号
		for _, item := range req.TransactionInfo {
			transactionInfo = append(transactionInfo, &order.TransactionInfo{
				FinancialPaidId: item.FinancialPaidId,
				TransactionNo:   item.TransactionNo,
			})
		}
	}
	rpcResp, err := global.FinancialClient.FundApproveCreate(ctx, &order.FinancialFundApproveCreateReq{
		FinancialFundId: req.FinancialFundId,
		Status:          req.Status,
		ApproveComment:  req.ApproveComment,
		TransactionInfo: transactionInfo,
		ApproveBy:       employeeId,
		OrderId:         req.OrderId,
	})
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil {
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}

	resp := new(admin_api.FinancialFundApproveRsp)
	_, err = global.FinancialClient.FundStatusUpdate(ctx, &order.FinancialFundStatusUpdateReq{
		FinancialFundId: req.FinancialFundId,
		Status:          req.Status,
		UpdatedBy:       employeeId,
		ApproveComment:  req.ApproveComment,
	})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	//更新订单状态
	newStatusReview := order.StatusOrderReview_STATUS_REVIEW_REJECT
	if req.Status == 2 {
		reqGetOrderInfo := &order.GetOrderInfoReq{
			Id: req.OrderId,
		}
		// 根据订单ID，获取订单信息
		respGetOrderInfo, _ := global.OrderClient.GetOrderInfo(ctx, reqGetOrderInfo)
		// 订单首款审核通过后，发送客户订单支付成功邮件
		if respGetOrderInfo != nil && respGetOrderInfo.GetOrder().GetStatus() == order.StatusOrder_STATUS_ORDER_FIRST {
			//	发送订单支付成功邮件
			reqSendEmail := &sendOrderPaySuccessReq{
				CustomerId: respGetOrderInfo.GetOrder().GetCustomerId(),
				BrandId:    respGetOrderInfo.GetOrder().GetBrandId(),
				BrandName:  respGetOrderInfo.GetOrder().GetBrandName(),
			}

			err = sendOrderPaySuccessEmail(ctx, reqSendEmail)
			if err != nil {
				logger.CtxErrorf(ctx, "%s sendOrderPaySuccessEmail error: %v", "FundApprove", err)
			}
		}

		newStatusReview = order.StatusOrderReview_STATUS_REVIEW_PASS
		//更新留言板
		messageBoardReq := &message.UpdateMessageBoardReq{
			RelationId:     req.OrderId,
			RelationStatus: int32(order.StatusOrder_STATUS_ORDER_DISBURSEMENT),
			RelationType:   ws_message.MessageBoardRelationType_MESSAGE_BOARD_RELATION_TYPE_ORDER,
		}
		_, err = global.MessageClient.UpdateMessageBoard(ctx, messageBoardReq)
	}

	// 获取财务收款信息
	fundRespRpc, _ := global.FinancialClient.FundInfo(ctx, &order.FinancialFundInfoReq{
		Id:      req.GetFinancialFundId(),
		OrderId: req.GetOrderId(),
	})

	// 款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)
	// 订单状态#1%已下定金（定金）|2%支付待确认（首款）|3%尾款待支付（尾款）|4%支付成功|5%支款订单|6%交易关闭
	// 仅收款类型为订单支持的类型时，才更新订单状态
	if fundRespRpc != nil && fundRespRpc.GetFundType() < 4 {
		authInfo := httputil.MustGetAuthInfoFromContext(ctx, c)

		query := order.UpdateOrderStatusReq{
			Id:           req.GetOrderId(),
			StatusReview: newStatusReview,
			ReviewerId:   employeeId,
			Base:         &base.BaseReq{AuthInfo: httputil.ConvertToAuthInfo(authInfo)},
		}

		// 更新订单状态
		_, err = global.OrderClient.UpdateOrderStatus(ctx, &query)
		if err != nil {
			httputil.ResponseParamsErr(c, err.Error())

			return
		}
	}

	httputil.ResponseSuccess(c, resp)
}

// SaveFundDraft 保存草稿.
// @router /financial/SaveFundDraft [POST]
func SaveFundDraft(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.FinancialFundDraftReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	resp := new(admin_api.FinancialFundDraftRsp)
	content, _ := json.Marshal(req.FinancialPaidList)
	_, err = global.FinancialClient.FundDraftCreate(ctx, &order.FinancialFundDraftReq{
		OrderId:  req.OrderId,
		FundType: req.FundType,
		Content:  string(content),
	})
	httputil.ResponseSuccess(c, resp)
}

// GetFundDraft 获取草稿 .
// @router /financial/GetFundDraft [POST]
func GetFundDraft(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.GetFinancialFundDraftReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	resp := new(admin_api.GetFinancialFundDraftRsp)
	respRpc, _ := global.FinancialClient.GetFundDraft(ctx, &order.GetFinancialFundDraftReq{
		OrderId:  req.OrderId,
		FundType: 1,
	})

	resp.OrderId = respRpc.OrderId
	resp.FundType = respRpc.FundType
	resp.Remark = respRpc.Remark
	json.Unmarshal([]byte(respRpc.Content), &resp.FinancialPaidList)

	httputil.ResponseSuccess(c, resp)
}

// GetRelationFund 获取关联收款单
func GetRelationFund(ctx context.Context, fundType int32, orderId int64) []*admin_api.FinancialFundDetail {
	fund1RespRpc, _ := global.FinancialClient.FundInfo(ctx, &order.FinancialFundInfoReq{
		OrderId:  orderId,
		FundType: 1,
	})
	fund2RespRpc, _ := global.FinancialClient.FundInfo(ctx, &order.FinancialFundInfoReq{
		OrderId:  orderId,
		FundType: 2,
	})
	fund3RespRpc, _ := global.FinancialClient.FundInfo(ctx, &order.FinancialFundInfoReq{
		OrderId:  orderId,
		FundType: 3,
	})
	financialFundInfo1 := FundDetailConToApi(fund1RespRpc)
	financialFundInfo2 := FundDetailConToApi(fund2RespRpc)
	financialFundInfo3 := FundDetailConToApi(fund3RespRpc)
	relationFinancialInfo := make([]*admin_api.FinancialFundDetail, 0)
	if fundType == 1 {
		if financialFundInfo2.Id != 0 {
			relationFinancialInfo = append(relationFinancialInfo, financialFundInfo2)
		}
		if financialFundInfo3.Id != 0 {
			relationFinancialInfo = append(relationFinancialInfo, financialFundInfo3)
		}
	}
	if fundType == 2 {
		if financialFundInfo1.Id != 0 {
			relationFinancialInfo = append(relationFinancialInfo, financialFundInfo1)
		}
		if financialFundInfo3.Id != 0 {
			relationFinancialInfo = append(relationFinancialInfo, financialFundInfo3)
		}
	}
	if fundType == 3 {
		if financialFundInfo1.Id != 0 {
			relationFinancialInfo = append(relationFinancialInfo, financialFundInfo1)
		}
		if financialFundInfo2.Id != 0 {
			relationFinancialInfo = append(relationFinancialInfo, financialFundInfo2)
		}
	}
	if fundType == 4 {
		if financialFundInfo1.Id != 0 {
			relationFinancialInfo = append(relationFinancialInfo, financialFundInfo1)
		}
		if financialFundInfo2.Id != 0 {
			relationFinancialInfo = append(relationFinancialInfo, financialFundInfo2)
		}
		if financialFundInfo3.Id != 0 {
			relationFinancialInfo = append(relationFinancialInfo, financialFundInfo3)
		}
	}
	return relationFinancialInfo
}

// GetOperationLog 获取操作日志
func GetOperationLog(ctx context.Context, c *app.RequestContext) {
	var req admin_api.FinancialOperationLogListReq
	err := c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	resp := new(admin_api.FinancialOperationLogListRsp)
	respRpc, err := global.FinancialClient.FinancialOperationLogList(ctx, &order.FinancialOperationLogListReq{
		CustomerId: req.CustomerId,
		OperType:   req.OperType,
		OrderBy:    req.OrderBy,
		PageNum:    req.PageNum,
		PageSize:   req.PageSize,
	})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	items := make([]*admin_api.FinancialOperationLogInfo, 0, len(respRpc.FinancialOperationLogList))
	approveByIds := make([]int64, 0, len(respRpc.FinancialOperationLogList))
	for _, item := range respRpc.FinancialOperationLogList {
		approveByIds = append(approveByIds, item.OpUserId)
	}
	employeeResp, err := global.UsersClient.GetEmployeeInfoByIds(ctx, &user.GetEmployeeInfoByIdsReq{
		Id: approveByIds,
	})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	type employeeInfo struct {
		name     string
		deptName string
	}
	employeeMap := make(map[int64]employeeInfo)
	for _, emp := range employeeResp.List {
		employeeMap[emp.Id] = employeeInfo{
			name:     emp.Name,
			deptName: emp.DeptName,
		}
	}
	for _, item := range respRpc.FinancialOperationLogList {
		var opUserName, opUserDepartment string
		if empInfo, ok := employeeMap[item.OpUserId]; ok {
			opUserName = empInfo.name
			opUserDepartment = empInfo.deptName
		}
		items = append(items, &admin_api.FinancialOperationLogInfo{
			Id:               item.Id,
			CustomerId:       item.CustomerId,
			FinancialId:      item.FinancialId,
			OperType:         item.OperType,
			Content:          item.Content,
			OpUserId:         item.OpUserId,
			CreateAt:         item.CreateAt,
			OpUserName:       opUserName,
			OpUesrDepartment: opUserDepartment,
		})
	}
	resp.FinancialOperationLogList = items
	resp.Total = respRpc.Total
	httputil.ResponseSuccess(c, resp)
}

// ThirdFundCreate .
// @router /financial/ThirdFundCreate [POST]
func ThirdFundCreate(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.ThirdFundCreateReq
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}
	// 转换支付信息列表
	paidInfoList := make([]*order.FinancialPaidInfo, 0, len(req.FinancialPaidInfo))
	for _, item := range req.FinancialPaidInfo {
		imagesPath, _ := json.Marshal(item.ImagesPath)
		paidInfoList = append(paidInfoList, &order.FinancialPaidInfo{
			PaymentAccountId: item.PaymentAccountId,
			AmountOther:      item.AmountOther,
			ImagesPath:       string(imagesPath),
			AccountName:      item.FinancialAccountName,
			Currency:         item.Currency,
			AmountCny:        item.AmountCny,
			ExchangeRate:     item.ExchangeRate,
		})
	}
	rpcRsp, err := global.FinancialClient.ThirdFundCreate(ctx, &order.ThirdFundCreateReq{
		Currency:          req.Currency,
		WorkflowId:        req.WorkflowId,
		WorkflowName:      req.WorkflowName,
		WorkflowNo:        req.WorkflowNo,
		RealAmountRmb:     req.RealAmountRmb,
		ExchangeRate:      req.ExchangeRate,
		CustomerId:        req.CustomerId,
		RealAmountOther:   req.RealAmountOther,
		PaidTime:          req.PaidTime,
		FinancialPaidInfo: paidInfoList,
		Remark:            req.Remark,
	})
	resp := new(admin_api.ThirdFundCreateRsp)
	resp.Id = rpcRsp.Id
	httputil.ResponseSuccess(c, resp)
}

func GetRelationExchangeRate(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.RelationExchangeRateReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	rpcRsp, err := global.FinancialClient.GetRelationExchangeRate(ctx, &order.RelationExchangeRateReq{
		OrderId:    req.OrderId,
		Currency:   req.Currency,
		OrderNo:    req.OrderNo,
		WorkflowId: req.WorkflowId,
	})
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	resp := new(admin_api.RelationExchangeRateRsp)
	resp.ExchangeRate = rpcRsp.ExchangeRate
	httputil.ResponseSuccess(c, resp)
}

// AddExportFinancialFundList .
// @router /financial/AddExportFinancialFundList [POST]
func AddExportFinancialFundList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.AddExportFinancialFundListReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	var (
		createdAtResult          []string
		passTimeResult           []string
		rejectTimeResult         []string
		approveStatusResult      []string
		paidTimeResult           []string
		userSourceDepartResult   []string
		orderSourceDepartResult  []string
		submitSourceDepartResult []string
		orderStatusResult        []string
	)

	for _, createdAt := range req.CreatedAt {
		createdAtResult = append(createdAtResult, strconv.Itoa(int(createdAt)))
	}

	for _, passTime := range req.PassTime {
		passTimeResult = append(passTimeResult, strconv.Itoa(int(passTime)))
	}

	for _, rejectTime := range req.RejectTime {
		rejectTimeResult = append(rejectTimeResult, strconv.Itoa(int(rejectTime)))
	}

	for _, approveStatus := range req.ApproveStatusList {
		approveStatusResult = append(approveStatusResult, strconv.Itoa(int(approveStatus)))
	}

	for _, paidTime := range req.PaidTime {
		paidTimeResult = append(paidTimeResult, strconv.Itoa(int(paidTime)))
	}

	for _, userSourceDepart := range req.UserSourceDepart {
		userSourceDepartResult = append(userSourceDepartResult, strconv.Itoa(int(userSourceDepart)))
	}
	for _, orderSourceDepart := range req.OrderSourceDepart {
		orderSourceDepartResult = append(orderSourceDepartResult, strconv.Itoa(int(orderSourceDepart)))
	}
	for _, submitSourceDepart := range req.SubmitSourceDepart {
		submitSourceDepartResult = append(submitSourceDepartResult, strconv.Itoa(int(submitSourceDepart)))
	}
	for _, orderStatus := range req.OrderStatus {
		orderStatusResult = append(orderStatusResult, strconv.Itoa(int(orderStatus)))
	}

	DownloadConditionsArray := map[string]*tools.ValueUnion{
		"created_at": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: createdAtResult,
				},
			},
		},
		"pass_time": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: passTimeResult,
				},
			},
		},
		"reject_time": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: rejectTimeResult,
				},
			},
		},
		"approve_status": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: approveStatusResult,
				},
			},
		},
		"goods_name": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: req.GoodsName,
				},
			},
		},
		"service_name": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: req.ServiceName,
				},
			},
		},
		"pay_type": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: strconv.Itoa(int(req.PayType)),
				},
			},
		},
		"fund_type": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: strconv.Itoa(int(req.FundType)),
				},
			},
		},
		"user_type": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: strconv.Itoa(int(req.UserType)),
				},
			},
		},
		"brand_name": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: req.BrandName,
				},
			},
		},
		"business_name": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: req.BusinessName,
				},
			},
		},
		"paid_time": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: paidTimeResult,
				},
			},
		},
		"user_source_depart": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: userSourceDepartResult,
				},
			},
		},
		"order_source_depart": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: orderSourceDepartResult,
				},
			},
		},
		"submit_source_depart": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: submitSourceDepartResult,
				},
			},
		},
		"order_status": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: orderStatusResult,
				},
			},
		},
	}

	resp, err := global.ToolsClient.AddExportFileTask(ctx, &tools.AddExportFileTaskReq{
		TaskType:                tools.TaskType_FUND,
		DownloadFields:          req.DownloadFields,
		DownloadConditionsArray: DownloadConditionsArray,
		DownloadFieldsOrder:     req.DownloadFieldsOrder,
	})
	if err != nil {
		httputil.ResponseInternalErr(c)
		return
	}
	if resp.Base != nil && resp.Base.Code != errno.Errno_SUCCESS {
		httputil.ResponseErrorCodeWithBase(c, resp.Base)
		return
	}
	httputil.ResponseSuccess(c, resp)
}

// AddExportFinancialRefundList .
// @router /financial/AddExportFinancialRefundList [POST]
func AddExportFinancialRefundList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.AddExportFinancialRefundListReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	var (
		createdAtResult         []string
		passTimeResult          []string
		rejectTimeResult        []string
		completeTimeResult      []string
		approveStatusResult     []string
		userSourceDepartResult  []string
		orderSourceDepartResult []string
		submitDepartResult      []string
	)

	for _, createdAt := range req.CreatedAt {
		createdAtResult = append(createdAtResult, strconv.Itoa(int(createdAt)))
	}

	for _, passTime := range req.PassTime {
		passTimeResult = append(passTimeResult, strconv.Itoa(int(passTime)))
	}

	for _, rejectTime := range req.RejectTime {
		rejectTimeResult = append(rejectTimeResult, strconv.Itoa(int(rejectTime)))
	}

	for _, completeTime := range req.CompleteTime {
		completeTimeResult = append(completeTimeResult, strconv.Itoa(int(completeTime)))
	}

	for _, approveStatus := range req.ApproveStatusList {
		approveStatusResult = append(approveStatusResult, strconv.Itoa(int(approveStatus)))
	}

	for _, userSourceDepart := range req.UserSourceDepart {
		userSourceDepartResult = append(userSourceDepartResult, strconv.Itoa(int(userSourceDepart)))
	}
	for _, orderSourceDepart := range req.OrderSourceDepart {
		orderSourceDepartResult = append(orderSourceDepartResult, strconv.Itoa(int(orderSourceDepart)))
	}
	for _, submitDepart := range req.SubmitDepart {
		submitDepartResult = append(submitDepartResult, strconv.Itoa(int(submitDepart)))
	}

	DownloadConditionsArray := map[string]*tools.ValueUnion{
		"created_at": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: createdAtResult,
				},
			},
		},
		"pass_time": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: passTimeResult,
				},
			},
		},
		"reject_time": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: rejectTimeResult,
				},
			},
		},
		"complete_time": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: completeTimeResult,
				},
			},
		},
		"approve_status": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: approveStatusResult,
				},
			},
		},
		"goods_name": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: req.GoodsName,
				},
			},
		},
		"service_name": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: req.ServiceName,
				},
			},
		},
		"refund_receive_account_type": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: strconv.Itoa(int(req.RefundReceiveAccountType)),
				},
			},
		},
		"refund_type": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: strconv.Itoa(int(req.RefundType)),
				},
			},
		},
		"user_type": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: strconv.Itoa(int(req.UserType)),
				},
			},
		},
		"brand_name": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: req.BrandName,
				},
			},
		},
		"business_name": {
			Value: &tools.ValueUnion_StringVal{
				StringVal: &tools.StringMessage{
					Value: req.BusinessName,
				},
			},
		},
		"user_source_depart": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: userSourceDepartResult,
				},
			},
		},
		"order_source_depart": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: orderSourceDepartResult,
				},
			},
		},
		"submit_depart": {
			Value: &tools.ValueUnion_ArrayStringVal{
				ArrayStringVal: &tools.ArrayStringMessage{
					Value: submitDepartResult,
				},
			},
		},
	}

	resp, err := global.ToolsClient.AddExportFileTask(ctx, &tools.AddExportFileTaskReq{
		TaskType:                tools.TaskType_REFUND,
		DownloadFields:          req.DownloadFields,
		DownloadConditionsArray: DownloadConditionsArray,
		DownloadFieldsOrder:     req.DownloadFieldsOrder,
	})
	if err != nil {
		httputil.ResponseInternalErr(c)
		return
	}
	if resp.Base != nil && resp.Base.Code != errno.Errno_SUCCESS {
		httputil.ResponseErrorCodeWithBase(c, resp.Base)
		return
	}
	httputil.ResponseSuccess(c, resp)
}

// GetWorkflowFund .
// @router /financial/GetWorkflowFund [POST]
func GetWorkflowFund(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.WorkflowFundReq
	err = c.BindAndValidate(&req)
	if err != nil {
		httputil.ResponseParamsErr(c, err.Error())
		return
	}
	workflowName := ""
	workflowNo := ""
	if isNumeric(req.SearchKey) {
		workflowNo = req.SearchKey
	} else {
		workflowName = req.SearchKey
	}
	rpcRsp, err := global.FinancialClient.GetWorkflowFund(ctx, &order.WorkflowFundReq{
		WorkflowName:  workflowName,
		WorkflowNo:    workflowNo,
		CustomerId:    req.CustomerId,
		ApproveStatus: req.ApproveStatus,
		PageSize:      req.PageSize,
		PageNum:       req.PageNum,
	})
	items := make([]*admin_api.WorkflowFund, 0, len(rpcRsp.WorkflowFund))
	for _, item := range rpcRsp.WorkflowFund {
		items = append(items, &admin_api.WorkflowFund{
			Id:           item.Id,
			FundNo:       item.FundNo,
			WorkflowName: item.WorkflowName,
			WorkflowNo:   item.WorkflowNo,
		})
	}
	resp := new(admin_api.WorkflowFundRsp)
	resp.Items = items
	resp.Total = rpcRsp.Total
	httputil.ResponseSuccess(c, resp)
}

func isNumeric(s string) bool {
	for _, r := range s {
		if !unicode.IsDigit(r) {
			return false
		}
	}
	return true
}

// GetOperatorInfoByIds 批量获取用户信息，拿不到也会给空结构体
func GetOperatorInfoByIds(ctx context.Context, ids []int64) map[int64]*admin_api.OperatorInfo {
	resp, err := global.UsersClient.GetEmployeeInfoByIds(ctx, &user.GetEmployeeInfoByIdsReq{
		Id: ids,
	})
	errWrap := coderror.Warp(resp, err)
	if errWrap != nil {
		logger.CtxErrorf(ctx, "GetEmployeeInfoByIds rpc error: %v, ids: %v", errWrap.Error(), ids)
		res := make(map[int64]*admin_api.OperatorInfo)
		for _, id := range ids {
			res[id] = &admin_api.OperatorInfo{}
		}
		return res
	}

	if len(resp.List) == 0 {
		logger.CtxErrorf(ctx, "GetEmployeeInfoByIds not found employee ids: %v", ids)
		res := make(map[int64]*admin_api.OperatorInfo)
		for _, id := range ids {
			res[id] = &admin_api.OperatorInfo{}
		}
		return res
	}

	res := make(map[int64]*admin_api.OperatorInfo)
	for _, info := range resp.List {
		res[info.Id] = &admin_api.OperatorInfo{
			StaffId:   info.Id,
			StaffName: info.Name,
		}
	}

	for _, id := range ids {
		if _, ok := res[id]; !ok {
			res[id] = &admin_api.OperatorInfo{}
		}
	}

	return res
}

// EditApprovedFinancialFund 编辑已审核通过的收款信息.
// @router /financial/EditApprovedFinancialFund [POST]
func EditApprovedFinancialFund(ctx context.Context, c *app.RequestContext) {
	var err error
	var req admin_api.FinancialFundApprovedEditReq
	err = c.BindAndValidate(&req)
	if err != nil {
		logger.CtxErrorf(ctx, "EditApprovedFinancialFund bind and validate error: %v, raw body: %s", err, c.Request.Body())
		httputil.ResponseParamsErr(c, err.Error())
		return
	}

	// 获取当前操作用户ID
	employeeId := ctxmeta.MustGetAuth(ctx).EmployeeId()
	logger.CtxInfof(ctx, "EditApprovedFinancialFund request from employee: %d for fund_id: %d", employeeId, req.FinancialFundId)

	// 处理合同信息，转换为 JSON 字符串
	var contractInfoStr string
	if req.ContractInfo != nil {
		contractInfoBytes, err := json.Marshal(req.ContractInfo)
		if err != nil {
			logger.CtxErrorf(ctx, "EditApprovedFinancialFund marshal contract info error: %v for fund_id: %d", err, req.FinancialFundId)
			httputil.ResponseInternalErr(c)
			return
		}
		contractInfoStr = string(contractInfoBytes)
		logger.CtxInfof(ctx, "EditApprovedFinancialFund contract info marshaled successfully, length: %d for fund_id: %d",
			len(contractInfoStr), req.FinancialFundId)
	}

	// 调用RPC接口
	rpcResp, err := global.FinancialClient.EditApprovedFinancialFund(ctx, &order.FinancialFundApprovedEditReq{
		FinancialFundId: req.FinancialFundId,
		PaidTime:        req.PaidTime,
		UserType:        req.UserType,
		OrderSourceIds:  req.OrderSourceIds,
		ContractInfo:    contractInfoStr,
		OtherSubmitIds:  req.OtherSubmitIds,
		UpdatedBy:       employeeId,
		EditReason:      req.EditReason,
	})

	// 处理RPC响应
	errWrap := coderror.Warp(rpcResp, err)
	if errWrap != nil {
		httputil.ResponseWithErrorWrap(c, errWrap)
		return
	}
	//将新增的共同提交人同步为新增为客户的跟进人
	reqAddCustomerFollowEmployee := &customer.AddCustomerFollowEmployeeReq{
		CustomerId:  int32(rpcResp.CustomerId),
		EmployeeIds: rpcResp.ToAdd,
	}
	// 同步订单跟进员工（仅创建订单时）
	_, err = global.CustomerProfileClient.AddCustomerFollowEmployee(ctx, reqAddCustomerFollowEmployee)
	if err != nil {
		logger.CtxErrorf(ctx, "%s  global.CustomerProfileClient.AddCustomerFollowEmployee rpc error %v", "EditApprovedFinancialFund", err)
	}
	//将删除的共同提交人，从客户跟进人里面删除
	deleteCustomerSupportAgents := &customer.DeleteCustomerSupportAgentsReq{
		CustomerId:  rpcResp.CustomerId,
		EmployeeIds: rpcResp.ToDelete,
	}
	// 同步订单跟进员工（仅创建订单时）
	_, err = global.CustomerProfileClient.DeleteCustomerSupportAgents(ctx, deleteCustomerSupportAgents)
	if err != nil {
		logger.CtxErrorf(ctx, "%s  global.CustomerProfileClient.DeleteCustomerSupportAgents rpc error %v", "EditApprovedFinancialFund", err)
	}
	// 返回成功响应
	resp := &admin_api.FinancialFundApprovedEditRsp{
		FinancialFundId: rpcResp.FinancialFundId,
		UpdatedAt:       rpcResp.UpdatedAt,
	}

	httputil.ResponseSuccess(c, resp)
}
