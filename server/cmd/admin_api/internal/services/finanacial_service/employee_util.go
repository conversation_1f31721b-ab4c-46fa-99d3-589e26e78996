package finanacial_service

import (
	"context"
	"uofferv2/kitex_gen/server/cmd/user"
	"uofferv2/server/cmd/admin_api/internal/global"
)

type employeeInfo struct {
	Name     string
	DeptName string
}

func GetEmployeeInfoByIds(ctx context.Context, ids []int64) (map[int64]employeeInfo, error) {
	employeeResp, err := global.UsersClient.GetEmployeeInfoByIds(ctx, &user.GetEmployeeInfoByIdsReq{
		Id: ids,
	})
	if err != nil {
		return nil, err
	}
	employeeMap := make(map[int64]employeeInfo)
	for _, emp := range employeeResp.List {
		employeeMap[emp.Id] = employeeInfo{
			Name:     emp.Name,
			DeptName: emp.DeptName,
		}
	}
	return employeeMap, nil
}
