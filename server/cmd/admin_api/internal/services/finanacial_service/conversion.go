package finanacial_service

import (
	"encoding/json"
	"reflect"
	"uofferv2/kitex_gen/server/cmd/customer"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"

	"github.com/shopspring/decimal"
)

// ConvertSlice 是一个通用的切片转换函数
// From 是源类型，To 是目标类型
// converter 是一个将 From 类型转换为 To 类型的函数
func ConvertSlice[From, To any](items []From, converter func(From) To) []To {
	result := make([]To, len(items))
	for i, item := range items {
		result[i] = converter(item)
	}
	return result
}

func PaidInfoConToApi(srv *order.FinancialPaidListRsp) []*admin_api.FinancialPaidInfo {
	items := make([]*admin_api.FinancialPaidInfo, 0, len(srv.PaidList))
	for _, item := range srv.PaidList {
		var imagesPath []*admin_api.FundContractInfo
		if len(item.ImagesPath) > 0 {
			_ = json.Unmarshal([]byte(item.ImagesPath), &imagesPath)
			for i, _ := range imagesPath {
				imagesPath[i].ThumbnailUrl = imagesPath[i].Url
			}
		}
		items = append(items, &admin_api.FinancialPaidInfo{
			FinancialPaidId:      item.Id,
			PaymentAccountId:     item.PaymentAccountId,
			Currency:             item.Currency,
			PaidType:             admin_api.PaymentAccountType(item.PaidType),
			FinancialAccountName: item.AccountName,
			AmountOther:          item.AmountOther,
			ImagesPath:           imagesPath,
			TransactionNo:        item.TransactionNo,
			AmountCny:            item.AmountCny,
		})
	}
	return items
}

func FundApproveConToApi(srv *order.FinancialFundApproveListRsp) []*admin_api.FinancialFundApproveLog {
	items := make([]*admin_api.FinancialFundApproveLog, 0, len(srv.FinancialFundApproveInfo))
	for _, item := range srv.FinancialFundApproveInfo {
		items = append(items, &admin_api.FinancialFundApproveLog{
			Status:         item.Status,
			ApproveComment: item.ApproveComment,
			ApproveBy:      item.ApproveBy,
			CreatedAt:      item.CreatedAt,
		})
	}
	return items
}

func convertTags(tags []*customer.SimpleTag) []*admin_api.CustomerSimpleTag {
	return ConvertSlice(tags, func(tag *customer.SimpleTag) *admin_api.CustomerSimpleTag {
		result, _ := ConvertStructSameFields[*customer.SimpleTag, *admin_api.CustomerSimpleTag](tag)
		return result
	})
}

func convertTags2(tags []*customer.SimpleTag) []*admin_api.CustomerSimpleTags {
	return ConvertSlice(tags, func(tag *customer.SimpleTag) *admin_api.CustomerSimpleTags {
		result, _ := ConvertStructSameFields[*customer.SimpleTag, *admin_api.CustomerSimpleTags](tag)
		return result
	})
}

// ConvertStructSameFields 用于字段名称完全相同的结构体转换
func ConvertStructSameFields[From, To any](from From) (To, error) {
	var to To
	fromValue := reflect.ValueOf(from)
	toValue := reflect.ValueOf(&to)

	// 处理源值是指针的情况
	if fromValue.Kind() == reflect.Ptr {
		if fromValue.IsNil() {
			return to, nil
		}
		fromValue = fromValue.Elem()
	}

	// 确保 toValue 是结构体而不是指针
	if toValue.Kind() == reflect.Ptr {
		toValue = toValue.Elem()
	}

	// 创建新的目标实例
	if toValue.Kind() == reflect.Ptr {
		if toValue.IsNil() {
			toValue.Set(reflect.New(toValue.Type().Elem()))
		}
		toValue = toValue.Elem()
	}

	for i := 0; i < toValue.NumField(); i++ {
		toField := toValue.Field(i)
		fromField := fromValue.FieldByName(toValue.Type().Field(i).Name)

		if !fromField.IsValid() {
			continue
		}

		// 处理字段类型不完全匹配的情况
		if toField.Kind() == reflect.Ptr && fromField.Kind() == reflect.Ptr {
			// 如果源字段为 nil，跳过赋值
			if fromField.IsNil() {
				continue
			}

			// 如果目标字段是指针类型但还未初始化
			if toField.IsNil() {
				toField.Set(reflect.New(toField.Type().Elem()))
			}

			// 获取指针指向的实际值
			fromElem := fromField.Elem()
			toElem := toField.Elem()

			if toElem.Type() == fromElem.Type() && toField.CanSet() {
				toElem.Set(fromElem)
			}
		} else if toField.Type() == fromField.Type() && toField.CanSet() {
			toField.Set(fromField)
		}
	}

	return to, nil
}

func FundDetailConToApi(srv *order.FinancialFundInfoRsp) *admin_api.FinancialFundDetail {
	if srv == nil {
		return nil
	}
	var contractInfo []*admin_api.FundContractInfo
	_ = json.Unmarshal([]byte(srv.ContractUrl), &contractInfo)
	paidList := make([]*admin_api.FinancialPaidInfo, 0, len(srv.FinancialPaiInfo))
	thirdAmountList := make([]*admin_api.AmountInfo, 0, 0)
	m := map[string]string{}
	tmp1 := decimal.Decimal{}
	tmp2 := decimal.Decimal{}
	tmp3 := decimal.Decimal{}
	for _, paid := range srv.FinancialPaiInfo {
		var imagesPath []*admin_api.FundContractInfo
		if len(paid.ImagesPath) > 0 {
			_ = json.Unmarshal([]byte(paid.ImagesPath), &imagesPath)
			for i, _ := range imagesPath {
				imagesPath[i].ThumbnailUrl = imagesPath[i].Url
			}
		}
		paidList = append(paidList, &admin_api.FinancialPaidInfo{
			PaymentAccountId:     paid.PaymentAccountId,
			Currency:             paid.Currency,
			PaidType:             admin_api.PaymentAccountType(paid.PaidType),
			FinancialAccountName: paid.AccountName,
			AmountOther:          paid.AmountOther,
			ImagesPath:           imagesPath,
			FinancialPaidId:      paid.Id,
			AmountCny:            paid.AmountCny,
			ExchangeRate:         paid.ExchangeRate,
			TransactionNo:        paid.TransactionNo,
		})
		val, ok := m[paid.Currency]
		if ok {
			tmp1, _ = decimal.NewFromString(val)
			tmp2, _ = decimal.NewFromString(paid.AmountOther)
			tmp3 = tmp1.Add(tmp2)
			m[paid.Currency] = tmp3.String()
		} else {
			m[paid.Currency] = paid.AmountOther
		}
	}
	for k, v := range m {
		thirdAmountList = append(thirdAmountList, &admin_api.AmountInfo{
			Currency: k,
			Amount:   v,
		})
	}
	financialFundInfo := &admin_api.FinancialFundDetail{
		Id:                srv.Id,
		OrderId:           srv.OrderId,
		OrderNo:           srv.OrderNo,
		FundNo:            srv.FundNo,
		CustomerId:        srv.CustomerId,
		ExchangeRate:      srv.ExchangeRate,
		ShouldAmountOther: srv.ShouldAmountOther,
		RealAmountOther:   srv.RealAmountOther,
		Currency:          srv.Currency,
		PayType:           srv.PayType,
		FundType:          srv.FundType,
		ApproveStatus:     srv.ApproveStatus,
		Discount:          srv.Discount,
		DiscountRate:      srv.DiscountRate,
		UrgentSpeed:       srv.UrgentSpeed,
		ContractNo:        srv.ContractNo,
		CreatedAt:         srv.CreatedAt,
		SubmitId:          srv.SubmitId,
		PaidTime:          srv.PaidTime,
		ContractInfo:      contractInfo,
		FinancialPaidList: paidList,
		RealAmountRmb:     srv.RealAmountRmb,
		ThirdAmountList:   thirdAmountList,
		ApproveBy:         srv.ApproveBy,
		ApproveComment:    srv.ApproveComment,
		UserType:          admin_api.FinancialCustomerType(srv.UserType),
		Remark:            srv.Remark,
		ShouldAmountRmb:   srv.ShouldAmountRmb,
	}
	return financialFundInfo
}
