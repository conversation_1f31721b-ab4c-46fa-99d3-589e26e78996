// Code generated by hertz generator.

package admin_api

import (
	"context"
	"uofferv2/server/cmd/admin_api/biz/model/server/cmd/admin_api"

	"uofferv2/server/cmd/admin_api/internal/services/finanacial_service"
	"uofferv2/server/cmd/admin_api/internal/services/order_service"

	"github.com/cloudwego/hertz/pkg/app"
)

var _ = admin_api.HealthCheckReq{} // 使代码自动编译通过

// CreateAccount .
// @router /financial/CreateAccount [POST]
func CreateAccount(ctx context.Context, c *app.RequestContext) {
	finanacial_service.CreateAccount(ctx, c)
}

// AccountInfo .
// @router /financial/AccountInfo [POST]
func AccountInfo(ctx context.Context, c *app.RequestContext) {
	finanacial_service.AccountInfo(ctx, c)
}

// AccountList .
// @router /financial/AccountList [POST]
func AccountList(ctx context.Context, c *app.RequestContext) {
	finanacial_service.AccountList(ctx, c)
}

// AccountDelete .
// @router /financial/AccountDelete [POST]
func AccountDelete(ctx context.Context, c *app.RequestContext) {
	finanacial_service.AccountDelete(ctx, c)
}

// AccountUpdate .
// @router /financial/AccountUpdate [POST]
func AccountUpdate(ctx context.Context, c *app.RequestContext) {
	finanacial_service.AccountUpdate(ctx, c)
}

// GetOrderInfo .
// @router /order/GetOrderInfo [POST]
func GetOrderInfo(ctx context.Context, c *app.RequestContext) {
	order_service.GetOrderInfo(ctx, c)
}

// GetOrderDepositList .
// @router /order/GetOrderDepositList [POST]
func GetOrderDepositList(ctx context.Context, c *app.RequestContext) {
	order_service.GetOrderDepositList(ctx, c)
}

// SaveOrderDeposit .
// @router /order/SaveOrderDeposit [POST]
func SaveOrderDeposit(ctx context.Context, c *app.RequestContext) {
	order_service.SaveOrderDeposit(ctx, c)
}

// DisbursementOrderDeposit .
// @router /order/DisbursementOrderDeposit [POST]
func DisbursementOrderDeposit(ctx context.Context, c *app.RequestContext) {
	order_service.DisbursementOrderDeposit(ctx, c)
}

// CloseOrderDeposit .
// @router /order/CloseOrderDeposit [POST]
func CloseOrderDeposit(ctx context.Context, c *app.RequestContext) {
	order_service.CloseOrderDeposit(ctx, c)
}

// OrderDepositRefund .
// @router /order/OrderDepositRefund [POST]
func OrderDepositRefund(ctx context.Context, c *app.RequestContext) {
	order_service.OrderDepositRefund(ctx, c)
}

// GetOrderFirstList .
// @router /order/GetOrderFirstList [POST]
func GetOrderFirstList(ctx context.Context, c *app.RequestContext) {
	order_service.GetOrderFirstList(ctx, c)
}

// SaveOrderFirst .
// @router /order/SaveOrderFirst [POST]
func SaveOrderFirst(ctx context.Context, c *app.RequestContext) {
	order_service.SaveOrderFirst(ctx, c)
}

// DisbursementOrderFirst .
// @router /order/DisbursementOrderFirst [POST]
func DisbursementOrderFirst(ctx context.Context, c *app.RequestContext) {
	order_service.DisbursementOrderFirst(ctx, c)
}

// CloseOrderFirst .
// @router /order/CloseOrderFirst [POST]
func CloseOrderFirst(ctx context.Context, c *app.RequestContext) {
	order_service.CloseOrderFirst(ctx, c)
}

// GetOrderFinalList .
// @router /order/GetOrderFinalList [POST]
func GetOrderFinalList(ctx context.Context, c *app.RequestContext) {
	order_service.GetOrderFinalList(ctx, c)
}

// UpdateOrderFinal .
// @router /order/UpdateOrderFinal [POST]
func UpdateOrderFinal(ctx context.Context, c *app.RequestContext) {
	order_service.UpdateOrderFinal(ctx, c)
}

// DisbursementOrderFinal .
// @router /order/DisbursementOrderFinal [POST]
func DisbursementOrderFinal(ctx context.Context, c *app.RequestContext) {
	order_service.DisbursementOrderFinal(ctx, c)
}

// GetOrderDisbursementList .
// @router /order/GetOrderDisbursementList [POST]
func GetOrderDisbursementList(ctx context.Context, c *app.RequestContext) {
	order_service.GetOrderDisbursementList(ctx, c)
}

// UpdateOrderDisbursement .
// @router /order/UpdateOrderDisbursement [POST]
func UpdateOrderDisbursement(ctx context.Context, c *app.RequestContext) {
	order_service.UpdateOrderDisbursement(ctx, c)
}

// DisbursementOrderDisbursement .
// @router /order/DisbursementOrderDisbursement [POST]
func DisbursementOrderDisbursement(ctx context.Context, c *app.RequestContext) {
	order_service.DisbursementOrderDisbursement(ctx, c)
}

// GetOrderSuccessList .
// @router /order/GetOrderSuccessList [POST]
func GetOrderSuccessList(ctx context.Context, c *app.RequestContext) {
	order_service.GetOrderSuccessList(ctx, c)
}

// GetOrderCloseList .
// @router /order/GetOrderCloseList [POST]
func GetOrderCloseList(ctx context.Context, c *app.RequestContext) {
	order_service.GetOrderCloseList(ctx, c)
}

// FundList .
// @router /financial/FundList [POST]
func FundList(ctx context.Context, c *app.RequestContext) {
	finanacial_service.FundList(ctx, c)
}

// FundDetail .
// @router /financial/FundListDetail [POST]
func FundDetail(ctx context.Context, c *app.RequestContext) {
	finanacial_service.FundDetail(ctx, c)
}

// FundApprove .
// @router /financial/FundListApprove [POST]
func FundApprove(ctx context.Context, c *app.RequestContext) {
	finanacial_service.FundApprove(ctx, c)
}

// RefundList .
// @router /financial/RefundList [POST]
func RefundList(ctx context.Context, c *app.RequestContext) {
	finanacial_service.RefundList(ctx, c)
}

// RefundDetail .
// @router /financial/FundListDetail [POST]
func RefundDetail(ctx context.Context, c *app.RequestContext) {
	finanacial_service.RefundDetail(ctx, c)
}

// RefundApprove .
// @router /financial/RefundApprove [POST]
func RefundApprove(ctx context.Context, c *app.RequestContext) {
	finanacial_service.RefundApprove(ctx, c)
}

// GetOrderInfoByIds .
// @router /order/GetOrderInfoByIds [POST]
func GetOrderInfoByIds(ctx context.Context, c *app.RequestContext) {
	order_service.GetOrderInfoByIds(ctx, c)
}

// GetOrderInfoByOrderNo .
// @router /order/GetOrderInfoByOrderNo [POST]
func GetOrderInfoByOrderNo(ctx context.Context, c *app.RequestContext) {
	order_service.GetOrderInfoByOrderNo(ctx, c)
}

// AccountType .
// @router /financial/AccountType [POST]
func AccountType(ctx context.Context, c *app.RequestContext) {
	finanacial_service.AccountType(ctx, c)
}

// SaveFundDraft .
// @router /financial/SaveFundDraft [POST]
func SaveFundDraft(ctx context.Context, c *app.RequestContext) {
	finanacial_service.SaveFundDraft(ctx, c)
}

// GetFundDraft .
// @router /financial/GetFundDraft [POST]
func GetFundDraft(ctx context.Context, c *app.RequestContext) {
	finanacial_service.GetFundDraft(ctx, c)
}

// GetCurrencyList .
// @router /order/GetCurrencyList [POST]
func GetCurrencyList(ctx context.Context, c *app.RequestContext) {
	order_service.GetCurrencyList(ctx, c)
}

// GetOrderList .
// @router /order/GetOrderList [POST]
func GetOrderList(ctx context.Context, c *app.RequestContext) {
	order_service.GetOrderList(ctx, c)
}

// AddExportOrderList .
// @router /order/AddExportOrderList [POST]
func AddExportOrderList(ctx context.Context, c *app.RequestContext) {
	order_service.AddExportOrderList(ctx, c)
}

// AddExportFinancialFundList .
// @router /financial/AddExportFinancialFundList [POST]
func AddExportFinancialFundList(ctx context.Context, c *app.RequestContext) {
	finanacial_service.AddExportFinancialFundList(ctx, c)
}

// GetOperationLog .
// @router /financial/GetOperationLog [POST]
func GetOperationLog(ctx context.Context, c *app.RequestContext) {
	finanacial_service.GetOperationLog(ctx, c)
}

// PaymentLinkCreate .
// @router /financial/PaymentLinkCreate [POST]
func PaymentLinkCreate(ctx context.Context, c *app.RequestContext) {
	finanacial_service.PaymentLinkCreate(ctx, c)
}

// PaymentOrderCreate .
// @router /financial/PaymentOrderCreate [POST]
func PaymentOrderCreate(ctx context.Context, c *app.RequestContext) {
	finanacial_service.PaymentOrderCreate(ctx, c)
}

// AlipayWebhook .
// @router /financial/AlipayWebhook [POST]
func AlipayWebhook(ctx context.Context, c *app.RequestContext) {
	finanacial_service.AlipayWebhook(ctx, c)
}

// WechatWebhook .
// @router /financial/WechatWebhook [POST]
func WechatWebhook(ctx context.Context, c *app.RequestContext) {
	finanacial_service.WechatWebhook(ctx, c)
}

// ThirdFundCreate .
// @router /financial/ThirdFundCreate [POST]
func ThirdFundCreate(ctx context.Context, c *app.RequestContext) {
	finanacial_service.ThirdFundCreate(ctx, c)
}

// BatchUpdateWorkflowStatus .
// @router /order/BatchUpdateWorkflowStatus [POST]
func BatchUpdateWorkflowStatus(ctx context.Context, c *app.RequestContext) {
	order_service.BatchUpdateWorkflowStatus(ctx, c)
}

// GetRefundHighRiskCustomers .
// @router /order/GetRefundHighRiskCustomers [POST]
func GetRefundHighRiskCustomers(ctx context.Context, c *app.RequestContext) {
	order_service.GetRefundHighRiskCustomers(ctx, c)
}

// GetWorkflowCompleteOrders .
// @router /order/GetWorkflowCompleteOrders [POST]
func GetWorkflowCompleteOrders(ctx context.Context, c *app.RequestContext) {
	order_service.GetWorkflowCompleteOrders(ctx, c)
}

// GetLatestOrderOperationLogByOrderId .
// @router /order/GetLatestOrderOperationLogByOrderId [POST]
func GetLatestOrderOperationLogByOrderId(ctx context.Context, c *app.RequestContext) {
	order_service.GetLatestOrderOperationLogByOrderId(ctx, c)
}

// GetRelationExchangeRate .
// @router /financial/GetRelationExchangeRate [POST]
func GetRelationExchangeRate(ctx context.Context, c *app.RequestContext) {
	finanacial_service.GetRelationExchangeRate(ctx, c)
}

// AddExportFinancialRefundList .
// @router /financial/AddExportFinancialRefundList [POST]
func AddExportFinancialRefundList(ctx context.Context, c *app.RequestContext) {
	finanacial_service.AddExportFinancialRefundList(ctx, c)
}

// GetWorkflowFund .
// @router /financial/GetWorkflowFund [POST]
func GetWorkflowFund(ctx context.Context, c *app.RequestContext) {
	finanacial_service.GetWorkflowFund(ctx, c)
}

// GetLatestOrderInfoByCustomerIds .
// @router /order/GetLatestOrderInfoByCustomerIds [POST]
func GetLatestOrderInfoByCustomerIds(ctx context.Context, c *app.RequestContext) {
	order_service.GetLatestOrderInfoByCustomerIds(ctx, c)
}

// PaymentOrderInfo .
// @router /financial/PaymentOrderInfo [POST]
func PaymentOrderInfo(ctx context.Context, c *app.RequestContext) {
	finanacial_service.PaymentOrderInfo(ctx, c)
}

// GetRedLineRiskCustomers .
// @router /order/GetRedLineRiskCustomers [POST]
func GetRedLineRiskCustomers(ctx context.Context, c *app.RequestContext) {
	order_service.GetRedLineRiskCustomers(ctx, c)
}

// GetOldNewCustomers .
// @router /order/GetOldNewCustomers [POST]
func GetOldNewCustomers(ctx context.Context, c *app.RequestContext) {
	order_service.GetOldNewCustomers(ctx, c)
}

// GetOrderCountByCustomerId .
// @router /order/GetOrderCountByCustomerId [POST]
func GetOrderCountByCustomerId(ctx context.Context, c *app.RequestContext) {
	order_service.GetOrderCountByCustomerId(ctx, c)
}

// GetOrderCountByCustomerIds .
// @router /order/GetOrderCountByCustomerIds [POST]
func GetOrderCountByCustomerIds(ctx context.Context, c *app.RequestContext) {
	order_service.GetOrderCountByCustomerIds(ctx, c)
}

// GetOrderOperationLogList .
// @router /order/GetOrderOperationLogList [POST]
func GetOrderOperationLogList(ctx context.Context, c *app.RequestContext) {
	order_service.GetOrderOperationLogList(ctx, c)
}

// BatchUpdateUpdaterId .
// @router /order/BatchUpdateUpdaterId [POST]
func BatchUpdateUpdaterId(ctx context.Context, c *app.RequestContext) {
	order_service.BatchUpdateUpdaterId(ctx, c)
}

// BatchUpdateOrderOwnId .
// @router /order/BatchUpdateOrderOwnId [POST]
func BatchUpdateOrderOwnId(ctx context.Context, c *app.RequestContext) {
	order_service.BatchUpdateOrderOwnId(ctx, c)
}

// EditApprovedFinancialFund .
// @router /financial/EditApprovedFinancialFund [POST]
func EditApprovedFinancialFund(ctx context.Context, c *app.RequestContext) {
	finanacial_service.EditApprovedFinancialFund(ctx, c)
}
