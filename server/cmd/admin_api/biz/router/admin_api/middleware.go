// Code generated by hertz generator.

package admin_api

import (
	"github.com/cloudwego/hertz/pkg/app"
)

func rootMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _authMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _authinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _authloginMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _authlogoutMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _brandMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _createbrandMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _existbrandnamebydomainMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listallbrandindomaintreeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listbrandbydomainMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listbrandinfobysearchMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _customerMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _checkemailregisteredMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _checkphoneregisteredMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _createcustomerMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _customerfinanciallistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _customergroupchatlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _customeroperationloglistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _customerorderlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _customerworkorderlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _customerfinancialexpenselistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _customerfinancialreceiptlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getallcountrycodesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getawardexperienceMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getcustomerdetailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getcustomersbyidsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _geteducationexperienceMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _geteducationinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getemailplaintextMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getemailverifycodeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getemployeefollowcustomerlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getextendedinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getphoneplaintextMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getphoneverifycodeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getreferrerinformationMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getworkinternshipexperienceMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listcustomeroptionsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listcustomersMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _tagallMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _tagbatchgetMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _tagcreateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _tagdeleteMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _taglistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _tagupdateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _tagupdatestatusMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _transferassetMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateawardexperienceMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updatecustomerMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updatecustomerfollowemployeeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateeducationexperienceMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateinternaloutputdocumentMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updatelanguageachievementMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updatepasswordMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updatereferrerinformationMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateresultdocumentMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updatestudentappealMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updatestudentinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkinternshipexperienceMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _verifyemailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _verifyphoneMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deptMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _createdeptMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deletedeptMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listdeptandbrandMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listdeptbybrandidMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updatedeptMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _domainMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _createdomainMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listdomainMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _employeeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cancelpositionMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _createemployeeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _editemployeeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _existsemailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _existsnameMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _existsphoneMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getemployeeinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getemployeeinfobyidsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listemployeeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _setemployeemenuMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _setemploymentstatusMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _setpositionMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _financialMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountdeleteMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountupdateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _createaccountMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _fundapproveMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _funddetailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _fundlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _refundapproveMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _refunddetailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _refundlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _healthMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _healthcheckMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _menuMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _createmenuMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deletemenuMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _existmenupermMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getmenubymenuidMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listmenubymenuparentidMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listmenutreeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updatemenuMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _messageMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _addmessageboardmessageMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deletemessageboardmessageMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _dismissgroupchatMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getmessageboardlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getmessageboardmessageMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getmessageboardorderinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getmessageboardstaffMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getmessageboardworkflowinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _groupchatdetailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listgroupchatsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listgroupmessageMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _modifygroupchatmembernicknameMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _modifygroupchatnameMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _modifygroupchatnoticeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _quitgroupchatMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _savefiletocustomerMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _savefiletoworkflowMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _topmessageboardmessageMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _transfergroupchatMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _orderMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _closeorderdepositMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _closeorderfirstMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _disbursementorderdepositMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _disbursementorderdisbursementMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _disbursementorderfinalMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _disbursementorderfirstMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getordercloselistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getorderdepositlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getorderdisbursementlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getorderfinallistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getorderfirstlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getorderinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getorderinfobyidsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getorderinfobyordernoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getordersuccesslistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _orderdepositrefundMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _saveorderdepositMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _saveorderfirstMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateorderdisbursementMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateorderfinalMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _productMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _changeproductstatusMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _createmajorcategoryMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deletebusinessMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deletecontractMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deletemajorMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deletemajorcategoryMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deleteproductMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deleteserviceitemMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deleteuniversityMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getbrandbusinesslistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getbusinessdetailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getbusinesslistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getcontractdetailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getcontractlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listcourselevelsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getlocationslistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getmajorMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getmajorcategoryMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getmajorcategoryhierarchyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getproductdetailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getproductlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getserviceitemdetailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getserviceitemlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getuniversityMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listmajorcategoriesfornewsubMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listmajorcategorygroupsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listmajorsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listsubcategoriesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listuniversitiesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listuniversitymajorMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _savebusinessMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _savecontractMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _savemajorMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _saveproductMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _saveserviceitemMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _saveuniversityMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _roleMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _createroleMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deleteroleMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getroleempcountMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listroleMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateroleMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _toolsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _addemailblacklistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deleteemailblacklistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getststokenMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _searchemailblacklistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _workflowMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _createworkflowMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getworkflowdetailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listworkflowMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listworkflowtaskviewMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getimsettingMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getdeptdetailbyidMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listdeptbyemployeeidMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _sortdeptMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updatebrandMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deletebrandMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _topmessageboardMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _markmessageboardreadMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getmessageboardunreadcountMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getworkflowtemplateandbusinessMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _gettopmessageboardlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listemployeebysearchkeyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listworkflowdispatcherMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getmessageboardfollowstafflistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accounttypeMw() []app.HandlerFunc {
	return nil
}

func _listworkflowprocessorMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listworkflownodedispatcherMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getmenulistbyidMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listchildemployeebyidMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getfunddraftMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getworkflowordergoodspaymentMw() []app.HandlerFunc {

	// your code...
	return nil
}

func _savefunddraftMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _creategroupchatMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _rejectworkflowdispatchMw() []app.HandlerFunc {

	// your code...
	return nil
}

func _addgroupmembersMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _bindworkflownotogroupchatMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _removegroupmembersMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getgroupchatbycustomeridMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _acceptworkflowdispatchMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getapplyconfigMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getcurrencylistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _imcallbackMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _createworkflowbyorderMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getworkflowfollowMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflownodetaskstatusMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getimusertypeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflownodeprocessorMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _finishworkflownodeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _terminateworkflownodeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _pauseworkflownodeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _restartworkflownodeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _transferworkflownodeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _userobjectqryMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _userobjectupsertMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _receiveworkflownodeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getworkflownodeschoolinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _applyworkflownodefileMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _applyworkflownodematerialpackageMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _applyworkflownodeinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hasgroupchatpermissionMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _editselfemployeeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getemployeeorginfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getemployeemangertreedeptMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _applyworkflownodeofferMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _applyworkflownodevisaMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _applyworkflownodeconfirmMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _applyworkflownoderejectMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getnotifysettingsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updatenotifysettingsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getbindcustomersMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getproductspecdetaillistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getimchattitleMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getbatchnamecardMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getnamecardMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getstaffinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getmessageboardlistbyordernoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getmessageboardlistbyworkflownoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getmessageboardlistbyorderidMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getmessageboardlistbyworkflowidMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getcustomermessageboardlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getmessageboardbyrelationidMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getoperationlogMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _workflowservicedoneMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _applyworkflownodesinglefileMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _applyworkflownodesinglerequirementMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getworkflowguidanceMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowguidancedemandinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowguidancefinalfileMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowguidancefirstdraftfileMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowguidancelessonbyguideMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowguidancelessoninfobyexplainMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowguidancelessoninfobyfirstMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowguidancelessoninfobytopicMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowguidancemanuscriptinfobyfinalMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowguidancemanuscriptinfobyfirstMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowguidancemanuscriptinfobysecondMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowguidancemanuscriptinfobytopicMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowguidancematerialfileMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowguidanceofferfileMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowguidancepublishurlMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowguidanceteacherinfoMw() []app.HandlerFunc {
	return nil
}

func _getcustomertagMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updatecustomerfileMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowguidancelessoninfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowguidancemanuscriptinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowguidanceresultinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getorderlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _applyworkflownodechooseguideMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _applyworkflownodeclosenessletterMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _paymentlinkcreateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflownameMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _relationshipworkflowgroupchatMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _workflowcreategroupchatMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _workflowserviceabnormaldoneMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listemployeebyroleidsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _paymentordercreateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _saveworkflowfileMw() []app.HandlerFunc {
	// your code...
	return nil
}
func _alipaywebhookMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getcustomerfollowemployeelistMw() []app.HandlerFunc {
	// your code...
	return nil
}
func _wechatwebhookMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listmenubypermMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _wechatwebhook0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _alipaywebhook0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listemployeebyroleidsornameMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _exportemployeedeptlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _exportorderlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _exportorderlist0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _exportworkflowlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _addexportemployeedeptlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _addexportorderlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getexportfiletasklistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _addexportfinancialfundlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _addexportcustomerslistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _thirdfundcreateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getcustomerworkflowlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _addimportemployeedeptlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getimportfiletasklistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getimportfiletaskstatusMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _workflowtemplatedetailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflownodeschoolinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateapplyworkflownodechooseguideMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateapplyworkflownodeclosenessletterMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateapplyworkflownodeconfirmMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateapplyworkflownodefileMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateapplyworkflownodeinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateapplyworkflownodematerialpackageMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateapplyworkflownodeofferMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateapplyworkflownoderejectMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateapplyworkflownodevisaMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getapplyworkflownodeconfirmMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getapplyworkflownodechooseguideMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getapplyworkflownodeclosenessletterMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getapplyworkflownodefileMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getapplyworkflownodeinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getapplyworkflownodematerialpackageMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getapplyworkflownodeofferMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getapplyworkflownoderejectMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getapplyworkflownodevisaMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getapplyworkflownodesinglefileMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getapplyworkflownodesinglerequirementMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateapplyworkflownodesinglefileMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateapplyworkflownodesinglerequirementMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _batchupdateworkflowstatusMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getrefundhighriskcustomersMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getworkflowcompleteordersMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _workflowconfirmpushMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _addexportworkflowtasklistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _addexportworkflowworkorderlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _checkcustomernewpasswordandoldpasswordissameMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getlatestorderoperationlogbyorderidMw() []app.HandlerFunc {
	return nil

}
func _getworkflowguidancedemandinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getworkflowguidancelessoninfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getworkflowguidancemanuscriptinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getworkflowguidanceresultinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getworkflowguidanceteacherinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _workflowoperationlogMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _unbindworkflowgroupchatMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listemployeebyemailMw() []app.HandlerFunc {
	// your code...
	return nil
}
func _getrelationexchangerateMw() []app.HandlerFunc {
	// your code...
	return nil
}
func _addfundwithworkflowapplyMw() []app.HandlerFunc {

	// your code...
	return nil
}

func _addexportfinancialrefundlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _bindfundwithworkflowapplyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cancelfundwithworkflowapplyMw() []app.HandlerFunc {

	return nil
}

func _getworkflowfundMw() []app.HandlerFunc {
	// your code...
	return nil
}
func _getlatestorderinfobycustomeridsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _isingroupchatMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getapplyworkflowfundinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updatesingleworkflowdispatchMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getsingleworkflowdispatchMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getworkflowlistbyorderidMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _paymentorderinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getredlineriskcustomersMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowstatusMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowinnerstatusMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getoldnewcustomersMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _suspendworkflownodeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getordercountbycustomeridMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getemployeeadminMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _addworkflowapplyfundcurrencyinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowapplyfundcurrencyinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getattachmentsbyorderidMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getworkflowdetailsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getcustomeriminfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getuseriminfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getguidancedetailbyapplyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listuniversityMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateworkflowfollowMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getordercountbycustomeridsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _skipworkflownodeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _batchtransferworkflownodeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _addfinancialrefundwithworkflowapplyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getgroupchatnoticeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updatefinancialrefundwithworkflowapplyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deletefinancialrefundwithworkflowapplyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getorderoperationlogbyorderidMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getorderoperationloglistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _workflowdetailsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getbrandinfobynameMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listemployeebysearchkeynoauthMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listmanageridbyidMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _udataMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _addemployeeudataprioritybyemailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _addemployeeudataprioritybyroleMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getgroupsalesdataMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getgroupworkflowdataMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getpersonalroleMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getsalesdataMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getworkflowdataMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _batchupdateupdateridMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deladminversionMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getadminversionMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getlastadminversionMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listadminversionMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updateadminversionMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _addadminversionMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listworkflowwithoutctxMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getemployeeudatapriorityMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _setemployeeasudatamanagerMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _readadminversionMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _addhelpconfigMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _delhelpconfigMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _gethelpconfigMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listhelpconfigMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updatehelpconfigMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getorderrefundinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getordersalesinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getoverviewdataMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getcustomerpagedataMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listemployeebydeptMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getallowrolelistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _customerupdatestatusMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _createtransfertaskMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _gettransfertaskdetailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _addmessageboardfollowstaffMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deletemessageboardfollowstaffMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _createcustomerprovisionedemailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deletecustomerprovisionedemailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _getcustomerprovisionedemailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _listcustomerprovisionedemailsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _updatecustomerprovisionedemailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _savecustomerremarkMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _batchupdatecustomerprovisionedemailsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _batchupdateorderownidMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _editapprovedfinancialfundMw() []app.HandlerFunc {
	// your code...
	return nil
}
