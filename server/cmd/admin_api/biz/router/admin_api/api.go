// Code generated by hertz generator. DO NOT EDIT.

package admin_api

import (
	"github.com/cloudwego/hertz/pkg/app/server"
	admin_api "uofferv2/server/cmd/admin_api/biz/handler/admin_api"
)

/*
 This file will register all the routes of the services in the master idl.
 And it will update automatically when you use the "update" command for the idl.
 So don't modify the contents of the file, or your code will be deleted when it is updated.
*/

// Register register routes based on the IDL 'api.${HTTP Method}' annotation.
func Register(r *server.Hertz) {

	root := r.Group("/", rootMw()...)
	{
		_auth := root.Group("/auth", _authMw()...)
		_auth.POST("/AuthInfo", append(_authinfoMw(), admin_api.AuthInfo)...)
		_auth.POST("/AuthLogin", append(_authloginMw(), admin_api.AuthLogin)...)
		_auth.POST("/AuthLogout", append(_authlogoutMw(), admin_api.AuthLogout)...)
	}
	{
		_brand := root.Group("/brand", _brandMw()...)
		_brand.POST("/CreateBrand", append(_createbrandMw(), admin_api.CreateBrand)...)
		_brand.POST("/DeleteBrand", append(_deletebrandMw(), admin_api.DeleteBrand)...)
		_brand.POST("/ExistBrandNameByDomain", append(_existbrandnamebydomainMw(), admin_api.ExistBrandNameByDomain)...)
		_brand.POST("/GetBrandInfoByName", append(_getbrandinfobynameMw(), admin_api.GetBrandInfoByName)...)
		_brand.POST("/ListAllBrandInDomainTree", append(_listallbrandindomaintreeMw(), admin_api.ListAllBrandInDomainTree)...)
		_brand.POST("/ListBrandByDomain", append(_listbrandbydomainMw(), admin_api.ListBrandByDomain)...)
		_brand.POST("/ListBrandInfoBySearch", append(_listbrandinfobysearchMw(), admin_api.ListBrandInfoBySearch)...)
		_brand.POST("/UpdateBrand", append(_updatebrandMw(), admin_api.UpdateBrand)...)
	}
	{
		_customer := root.Group("/customer", _customerMw()...)
		_customer.POST("/AddExportCustomersList", append(_addexportcustomerslistMw(), admin_api.AddExportCustomersList)...)
		_customer.POST("/BatchUpdateCustomerProvisionedEmails", append(_batchupdatecustomerprovisionedemailsMw(), admin_api.BatchUpdateCustomerProvisionedEmails)...)
		_customer.POST("/CheckCustomerNewPasswordAndOldPasswordIsSame", append(_checkcustomernewpasswordandoldpasswordissameMw(), admin_api.CheckCustomerNewPasswordAndOldPasswordIsSame)...)
		_customer.POST("/CheckEmailRegistered", append(_checkemailregisteredMw(), admin_api.CheckEmailRegistered)...)
		_customer.POST("/CheckPhoneRegistered", append(_checkphoneregisteredMw(), admin_api.CheckPhoneRegistered)...)
		_customer.POST("/CreateCustomer", append(_createcustomerMw(), admin_api.CreateCustomer)...)
		_customer.POST("/CreateCustomerProvisionedEmail", append(_createcustomerprovisionedemailMw(), admin_api.CreateCustomerProvisionedEmail)...)
		_customer.POST("/CustomerFinancialList", append(_customerfinanciallistMw(), admin_api.CustomerFinancialList)...)
		_customer.POST("/CustomerGroupChatList", append(_customergroupchatlistMw(), admin_api.CustomerGroupChatList)...)
		_customer.POST("/CustomerOperationLogList", append(_customeroperationloglistMw(), admin_api.CustomerOperationLogList)...)
		_customer.POST("/CustomerOrderList", append(_customerorderlistMw(), admin_api.CustomerOrderList)...)
		_customer.POST("/CustomerUpdateStatus", append(_customerupdatestatusMw(), admin_api.CustomerUpdateStatus)...)
		_customer.POST("/CustomerWorkOrderList", append(_customerworkorderlistMw(), admin_api.CustomerWorkOrderList)...)
		_customer.POST("/DeleteCustomerProvisionedEmail", append(_deletecustomerprovisionedemailMw(), admin_api.DeleteCustomerProvisionedEmail)...)
		_customer.POST("/FinancialExpenseList", append(_customerfinancialexpenselistMw(), admin_api.CustomerFinancialExpenseList)...)
		_customer.POST("/FinancialReceiptList", append(_customerfinancialreceiptlistMw(), admin_api.CustomerFinancialReceiptList)...)
		_customer.POST("/GetAllCountryCodes", append(_getallcountrycodesMw(), admin_api.GetAllCountryCodes)...)
		_customer.POST("/GetAwardExperience", append(_getawardexperienceMw(), admin_api.GetAwardExperience)...)
		_customer.POST("/GetBindCustomers", append(_getbindcustomersMw(), admin_api.GetBindCustomers)...)
		_customer.POST("/GetCustomerDetail", append(_getcustomerdetailMw(), admin_api.GetCustomerDetail)...)
		_customer.POST("/GetCustomerFollowEmployeeList", append(_getcustomerfollowemployeelistMw(), admin_api.GetCustomerFollowEmployeeList)...)
		_customer.POST("/GetCustomerTag", append(_getcustomertagMw(), admin_api.GetCustomerTag)...)
		_customer.POST("/GetCustomersByIds", append(_getcustomersbyidsMw(), admin_api.GetCustomersByIds)...)
		_customer.POST("/GetEducationExperience", append(_geteducationexperienceMw(), admin_api.GetEducationExperience)...)
		_customer.POST("/GetEducationInfo", append(_geteducationinfoMw(), admin_api.GetEducationInfo)...)
		_customer.POST("/GetEmailPlainText", append(_getemailplaintextMw(), admin_api.GetEmailPlainText)...)
		_customer.POST("/GetEmailVerifyCode", append(_getemailverifycodeMw(), admin_api.GetEmailVerifyCode)...)
		_customer.POST("/GetEmployeeFollowCustomerList", append(_getemployeefollowcustomerlistMw(), admin_api.GetEmployeeFollowCustomerList)...)
		_customer.POST("/GetExtendedInfo", append(_getextendedinfoMw(), admin_api.GetExtendedInfo)...)
		_customer.POST("/GetPhonePlainText", append(_getphoneplaintextMw(), admin_api.GetPhonePlainText)...)
		_customer.POST("/GetPhoneVerifyCode", append(_getphoneverifycodeMw(), admin_api.GetPhoneVerifyCode)...)
		_customer.POST("/GetReferrerInformation", append(_getreferrerinformationMw(), admin_api.GetReferrerInformation)...)
		_customer.POST("/GetWorkInternshipExperience", append(_getworkinternshipexperienceMw(), admin_api.GetWorkInternshipExperience)...)
		_customer.POST("/ListCustomerOptions", append(_listcustomeroptionsMw(), admin_api.ListCustomerOptions)...)
		_customer.POST("/ListCustomerProvisionedEmails", append(_listcustomerprovisionedemailsMw(), admin_api.ListCustomerProvisionedEmails)...)
		_customer.POST("/ListCustomers", append(_listcustomersMw(), admin_api.ListCustomers)...)
		_customer.POST("/ListUniversity", append(_listuniversityMw(), admin_api.ListUniversity)...)
		_customer.POST("/SaveCustomerRemark", append(_savecustomerremarkMw(), admin_api.SaveCustomerRemark)...)
		_customer.POST("/TagAll", append(_tagallMw(), admin_api.TagAll)...)
		_customer.POST("/TagBatchGet", append(_tagbatchgetMw(), admin_api.TagBatchGet)...)
		_customer.POST("/TagCreate", append(_tagcreateMw(), admin_api.TagCreate)...)
		_customer.POST("/TagDelete", append(_tagdeleteMw(), admin_api.TagDelete)...)
		_customer.POST("/TagList", append(_taglistMw(), admin_api.TagList)...)
		_customer.POST("/TagUpdate", append(_tagupdateMw(), admin_api.TagUpdate)...)
		_customer.POST("/TagUpdateStatus", append(_tagupdatestatusMw(), admin_api.TagUpdateStatus)...)
		_customer.POST("/TransferAsset", append(_transferassetMw(), admin_api.TransferAsset)...)
		_customer.POST("/UpdateAwardExperience", append(_updateawardexperienceMw(), admin_api.UpdateAwardExperience)...)
		_customer.POST("/UpdateCustomer", append(_updatecustomerMw(), admin_api.UpdateCustomer)...)
		_customer.POST("/UpdateCustomerFile", append(_updatecustomerfileMw(), admin_api.UpdateCustomerFile)...)
		_customer.POST("/UpdateCustomerFollowEmployee", append(_updatecustomerfollowemployeeMw(), admin_api.UpdateCustomerFollowEmployee)...)
		_customer.POST("/UpdateCustomerProvisionedEmail", append(_updatecustomerprovisionedemailMw(), admin_api.UpdateCustomerProvisionedEmail)...)
		_customer.POST("/UpdateEducationExperience", append(_updateeducationexperienceMw(), admin_api.UpdateEducationExperience)...)
		_customer.POST("/UpdateInternalOutputDocument", append(_updateinternaloutputdocumentMw(), admin_api.UpdateInternalOutputDocument)...)
		_customer.POST("/UpdateLanguageAchievement", append(_updatelanguageachievementMw(), admin_api.UpdateLanguageAchievement)...)
		_customer.POST("/UpdatePassword", append(_updatepasswordMw(), admin_api.UpdatePassword)...)
		_customer.POST("/UpdateReferrerInformation", append(_updatereferrerinformationMw(), admin_api.UpdateReferrerInformation)...)
		_customer.POST("/UpdateResultDocument", append(_updateresultdocumentMw(), admin_api.UpdateResultDocument)...)
		_customer.POST("/UpdateStudentAppeal", append(_updatestudentappealMw(), admin_api.UpdateStudentAppeal)...)
		_customer.POST("/UpdateStudentInfo", append(_updatestudentinfoMw(), admin_api.UpdateStudentInfo)...)
		_customer.POST("/UpdateWorkInternshipExperience", append(_updateworkinternshipexperienceMw(), admin_api.UpdateWorkInternshipExperience)...)
		_customer.POST("/VerifyEmail", append(_verifyemailMw(), admin_api.VerifyEmail)...)
		_customer.POST("/VerifyPhone", append(_verifyphoneMw(), admin_api.VerifyPhone)...)
	}
	{
		_dept := root.Group("/dept", _deptMw()...)
		_dept.POST("/CreateDept", append(_createdeptMw(), admin_api.CreateDept)...)
		_dept.POST("/DeleteDeptByBrandId", append(_deletedeptMw(), admin_api.DeleteDept)...)
		_dept.POST("/GetDeptDetailById", append(_getdeptdetailbyidMw(), admin_api.GetDeptDetailById)...)
		_dept.POST("/ListDeptAndBrand", append(_listdeptandbrandMw(), admin_api.ListDeptAndBrand)...)
		_dept.POST("/ListDeptByBrandId", append(_listdeptbybrandidMw(), admin_api.ListDeptByBrandId)...)
		_dept.POST("/ListDeptByEmployeeId", append(_listdeptbyemployeeidMw(), admin_api.ListDeptByEmployeeId)...)
		_dept.POST("/SortDept", append(_sortdeptMw(), admin_api.SortDept)...)
		_dept.POST("/UpdateDept", append(_updatedeptMw(), admin_api.UpdateDept)...)
	}
	{
		_domain := root.Group("/domain", _domainMw()...)
		_domain.POST("/CreateDomain", append(_createdomainMw(), admin_api.CreateDomain)...)
		_domain.POST("/ListDomain", append(_listdomainMw(), admin_api.ListDomain)...)
	}
	{
		_employee := root.Group("/employee", _employeeMw()...)
		_employee.POST("/AddExportEmployeeDeptList", append(_addexportemployeedeptlistMw(), admin_api.AddExportEmployeeDeptList)...)
		_employee.POST("/AddImportEmployeeDeptList", append(_addimportemployeedeptlistMw(), admin_api.AddImportEmployeeDeptList)...)
		_employee.POST("/CancelPosition", append(_cancelpositionMw(), admin_api.CancelPosition)...)
		_employee.POST("/CreateEmployee", append(_createemployeeMw(), admin_api.CreateEmployee)...)
		_employee.POST("/CreateTransferTask", append(_createtransfertaskMw(), admin_api.CreateTransferTask)...)
		_employee.POST("/EditEmployee", append(_editemployeeMw(), admin_api.EditEmployee)...)
		_employee.POST("/EditSelfEmployee", append(_editselfemployeeMw(), admin_api.EditSelfEmployee)...)
		_employee.POST("/ExistsEmail", append(_existsemailMw(), admin_api.ExistsEmail)...)
		_employee.POST("/ExistsName", append(_existsnameMw(), admin_api.ExistsName)...)
		_employee.POST("/ExistsPhone", append(_existsphoneMw(), admin_api.ExistsPhone)...)
		_employee.POST("/GetEmployeeAdmin", append(_getemployeeadminMw(), admin_api.GetEmployeeAdmin)...)
		_employee.POST("/GetEmployeeInfo", append(_getemployeeinfoMw(), admin_api.GetEmployeeInfo)...)
		_employee.POST("/GetEmployeeInfoByIds", append(_getemployeeinfobyidsMw(), admin_api.GetEmployeeInfoByIds)...)
		_employee.POST("/GetEmployeeMangerTreeDept", append(_getemployeemangertreedeptMw(), admin_api.GetEmployeeMangerTreeDept)...)
		_employee.POST("/GetEmployeeOrgInfoReq", append(_getemployeeorginfoMw(), admin_api.GetEmployeeOrgInfo)...)
		_employee.POST("/GetMenuListById", append(_getmenulistbyidMw(), admin_api.GetMenuListById)...)
		_employee.POST("/GetTransferTaskDetail", append(_gettransfertaskdetailMw(), admin_api.GetTransferTaskDetail)...)
		_employee.POST("/ListChildEmployeeById", append(_listchildemployeebyidMw(), admin_api.ListChildEmployeeById)...)
		_employee.POST("/ListEmployee", append(_listemployeeMw(), admin_api.ListEmployee)...)
		_employee.POST("/ListEmployeeByDept", append(_listemployeebydeptMw(), admin_api.ListEmployeeByDept)...)
		_employee.POST("/ListEmployeeByEmail", append(_listemployeebyemailMw(), admin_api.ListEmployeeByEmail)...)
		_employee.POST("/ListEmployeeByRoleIdsOrName", append(_listemployeebyroleidsornameMw(), admin_api.ListEmployeeByRoleIdsOrName)...)
		_employee.POST("/ListEmployeeBySearchKey", append(_listemployeebysearchkeyMw(), admin_api.ListEmployeeBySearchKey)...)
		_employee.POST("/ListEmployeeBySearchKeyNoAuth", append(_listemployeebysearchkeynoauthMw(), admin_api.ListEmployeeBySearchKeyNoAuth)...)
		_employee.POST("/ListManagerIdById", append(_listmanageridbyidMw(), admin_api.ListManagerIdById)...)
		_employee.POST("/SetEmployeeMenu", append(_setemployeemenuMw(), admin_api.SetEmployeeMenu)...)
		_employee.POST("/SetEmploymentStatus", append(_setemploymentstatusMw(), admin_api.SetEmploymentStatus)...)
		_employee.POST("/SetPosition", append(_setpositionMw(), admin_api.SetPosition)...)
		_employee.POST("/UserObjectQry", append(_userobjectqryMw(), admin_api.UserObjectQry)...)
		_employee.POST("/UserObjectUpsert", append(_userobjectupsertMw(), admin_api.UserObjectUpsert)...)
	}
	{
		_financial := root.Group("/financial", _financialMw()...)
		_financial.POST("/AccountDelete", append(_accountdeleteMw(), admin_api.AccountDelete)...)
		_financial.POST("/AccountInfo", append(_accountinfoMw(), admin_api.AccountInfo)...)
		_financial.POST("/AccountList", append(_accountlistMw(), admin_api.AccountList)...)
		_financial.POST("/AccountType", append(_accounttypeMw(), admin_api.AccountType)...)
		_financial.POST("/AccountUpdate", append(_accountupdateMw(), admin_api.AccountUpdate)...)
		_financial.POST("/AddExportFinancialFundList", append(_addexportfinancialfundlistMw(), admin_api.AddExportFinancialFundList)...)
		_financial.POST("/AddExportFinancialRefundList", append(_addexportfinancialrefundlistMw(), admin_api.AddExportFinancialRefundList)...)
		_financial.POST("/CreateAccount", append(_createaccountMw(), admin_api.CreateAccount)...)
		_financial.POST("/EditApprovedFinancialFund", append(_editapprovedfinancialfundMw(), admin_api.EditApprovedFinancialFund)...)
		_financial.POST("/FundApprove", append(_fundapproveMw(), admin_api.FundApprove)...)
		_financial.POST("/FundDetail", append(_funddetailMw(), admin_api.FundDetail)...)
		_financial.POST("/FundList", append(_fundlistMw(), admin_api.FundList)...)
		_financial.POST("/GetFundDraft", append(_getfunddraftMw(), admin_api.GetFundDraft)...)
		_financial.POST("/GetOperationLog", append(_getoperationlogMw(), admin_api.GetOperationLog)...)
		_financial.POST("/GetRelationExchangeRate", append(_getrelationexchangerateMw(), admin_api.GetRelationExchangeRate)...)
		_financial.POST("/GetWorkflowFund", append(_getworkflowfundMw(), admin_api.GetWorkflowFund)...)
		_financial.POST("/PaymentLinkCreate", append(_paymentlinkcreateMw(), admin_api.PaymentLinkCreate)...)
		_financial.POST("/PaymentOrderCreate", append(_paymentordercreateMw(), admin_api.PaymentOrderCreate)...)
		_financial.POST("/PaymentOrderInfo", append(_paymentorderinfoMw(), admin_api.PaymentOrderInfo)...)
		_financial.POST("/RefundApprove", append(_refundapproveMw(), admin_api.RefundApprove)...)
		_financial.POST("/RefundDetail", append(_refunddetailMw(), admin_api.RefundDetail)...)
		_financial.POST("/RefundList", append(_refundlistMw(), admin_api.RefundList)...)
		_financial.POST("/SaveFundDraft", append(_savefunddraftMw(), admin_api.SaveFundDraft)...)
		_financial.POST("/ThirdFundCreate", append(_thirdfundcreateMw(), admin_api.ThirdFundCreate)...)
		{
			_alipaywebhook := _financial.Group("/AlipayWebhook", _alipaywebhookMw()...)
			_alipaywebhook.POST("/:id", append(_alipaywebhook0Mw(), admin_api.AlipayWebhook)...)
		}
		{
			_wechatwebhook := _financial.Group("/WechatWebhook", _wechatwebhookMw()...)
			_wechatwebhook.POST("/:id", append(_wechatwebhook0Mw(), admin_api.WechatWebhook)...)
		}
	}
	{
		_health := root.Group("/health", _healthMw()...)
		_health.POST("/Check", append(_healthcheckMw(), admin_api.HealthCheck)...)
	}
	{
		_menu := root.Group("/menu", _menuMw()...)
		_menu.POST("/CreateMenu", append(_createmenuMw(), admin_api.CreateMenu)...)
		_menu.POST("/DeleteMenu", append(_deletemenuMw(), admin_api.DeleteMenu)...)
		_menu.POST("/ExistMenuPerm", append(_existmenupermMw(), admin_api.ExistMenuPerm)...)
		_menu.POST("/GetMenuByMenuId", append(_getmenubymenuidMw(), admin_api.GetMenuByMenuId)...)
		_menu.POST("/ListMenuByMenuParentId", append(_listmenubymenuparentidMw(), admin_api.ListMenuByMenuParentId)...)
		_menu.POST("/ListMenuByPerm", append(_listmenubypermMw(), admin_api.ListMenuByPerm)...)
		_menu.POST("/ListMenuTree", append(_listmenutreeMw(), admin_api.ListMenuTree)...)
		_menu.POST("/UpdateMenu", append(_updatemenuMw(), admin_api.UpdateMenu)...)
	}
	{
		_message := root.Group("/message", _messageMw()...)
		_message.POST("/AddGroupChatMembers", append(_addgroupmembersMw(), admin_api.AddGroupMembers)...)
		_message.POST("/AddMessageBoardFollowStaff", append(_addmessageboardfollowstaffMw(), admin_api.AddMessageBoardFollowStaff)...)
		_message.POST("/AddMessageBoardMessage", append(_addmessageboardmessageMw(), admin_api.AddMessageBoardMessage)...)
		_message.POST("/BindWorkflowNoToGroupChat", append(_bindworkflownotogroupchatMw(), admin_api.BindWorkflowNoToGroupChat)...)
		_message.POST("/CreateGroupChat", append(_creategroupchatMw(), admin_api.CreateGroupChat)...)
		_message.POST("/DeleteGroupChatMembers", append(_removegroupmembersMw(), admin_api.RemoveGroupMembers)...)
		_message.POST("/DeleteMessageBoardFollowStaff", append(_deletemessageboardfollowstaffMw(), admin_api.DeleteMessageBoardFollowStaff)...)
		_message.POST("/DeleteMessageBoardMessage", append(_deletemessageboardmessageMw(), admin_api.DeleteMessageBoardMessage)...)
		_message.POST("/DismissGroupChat", append(_dismissgroupchatMw(), admin_api.DismissGroupChat)...)
		_message.POST("/GetCustomerMessageBoardList", append(_getcustomermessageboardlistMw(), admin_api.GetCustomerMessageBoardList)...)
		_message.POST("/GetGroupChatByCustomerId", append(_getgroupchatbycustomeridMw(), admin_api.GetGroupChatByCustomerId)...)
		_message.POST("/GetGroupChatNotice", append(_getgroupchatnoticeMw(), admin_api.GetGroupChatNotice)...)
		_message.POST("/GetImChatTitle", append(_getimchattitleMw(), admin_api.GetImChatTitle)...)
		_message.POST("/GetImSetting", append(_getimsettingMw(), admin_api.GetImSetting)...)
		_message.POST("/GetImUserType", append(_getimusertypeMw(), admin_api.GetImUserType)...)
		_message.POST("/GetMessageBoardByRelationId", append(_getmessageboardbyrelationidMw(), admin_api.GetMessageBoardByRelationId)...)
		_message.POST("/GetMessageBoardFollowStaffList", append(_getmessageboardfollowstafflistMw(), admin_api.GetMessageBoardFollowStaffList)...)
		_message.POST("/GetMessageBoardList", append(_getmessageboardlistMw(), admin_api.GetMessageBoardList)...)
		_message.POST("/GetMessageBoardMessage", append(_getmessageboardmessageMw(), admin_api.GetMessageBoardMessage)...)
		_message.POST("/GetMessageBoardOrderInfo", append(_getmessageboardorderinfoMw(), admin_api.GetMessageBoardOrderInfo)...)
		_message.POST("/GetMessageBoardUnreadCount", append(_getmessageboardunreadcountMw(), admin_api.GetMessageBoardUnreadCount)...)
		_message.POST("/GetMessageBoardWorkflowInfo", append(_getmessageboardworkflowinfoMw(), admin_api.GetMessageBoardWorkflowInfo)...)
		_message.POST("/GetNotifySettings", append(_getnotifysettingsMw(), admin_api.GetNotifySettings)...)
		_message.POST("/GetTopMessageBoardList", append(_gettopmessageboardlistMw(), admin_api.GetTopMessageBoardList)...)
		_message.POST("/GetUserImInfo", append(_getuseriminfoMw(), admin_api.GetUserImInfo)...)
		_message.POST("/GetWorkFlowDetails", append(_getworkflowdetailsMw(), admin_api.GetWorkFlowDetails)...)
		_message.POST("/GroupChatDetail", append(_groupchatdetailMw(), admin_api.GroupChatDetail)...)
		_message.POST("/HasGroupChatPermission", append(_hasgroupchatpermissionMw(), admin_api.HasGroupChatPermission)...)
		_message.POST("/ImCallBack", append(_imcallbackMw(), admin_api.ImCallBack)...)
		_message.POST("/IsInGroupChat", append(_isingroupchatMw(), admin_api.IsInGroupChat)...)
		_message.POST("/ListGroupChats", append(_listgroupchatsMw(), admin_api.ListGroupChats)...)
		_message.POST("/ListGroupMessage", append(_listgroupmessageMw(), admin_api.ListGroupMessage)...)
		_message.POST("/MarkMessageBoardRead", append(_markmessageboardreadMw(), admin_api.MarkMessageBoardRead)...)
		_message.POST("/ModifyGroupChatMemberNickname", append(_modifygroupchatmembernicknameMw(), admin_api.ModifyGroupChatMemberNickname)...)
		_message.POST("/ModifyGroupChatName", append(_modifygroupchatnameMw(), admin_api.ModifyGroupChatName)...)
		_message.POST("/ModifyGroupChatNotice", append(_modifygroupchatnoticeMw(), admin_api.ModifyGroupChatNotice)...)
		_message.POST("/QuitGroupChat", append(_quitgroupchatMw(), admin_api.QuitGroupChat)...)
		_message.POST("/SaveFileToCustomer", append(_savefiletocustomerMw(), admin_api.SaveFileToCustomer)...)
		_message.POST("/SaveFileToWorkflow", append(_savefiletoworkflowMw(), admin_api.SaveFileToWorkflow)...)
		_message.POST("/TopMessageBoard", append(_topmessageboardMw(), admin_api.TopMessageBoard)...)
		_message.POST("/TransferGroupChat", append(_transfergroupchatMw(), admin_api.TransferGroupChat)...)
		_message.POST("/UpdateNotifySettings", append(_updatenotifysettingsMw(), admin_api.UpdateNotifySettings)...)
	}
	{
		_order := root.Group("/order", _orderMw()...)
		_order.POST("/AddExportOrderList", append(_addexportorderlistMw(), admin_api.AddExportOrderList)...)
		_order.POST("/BatchUpdateOrderOwnId", append(_batchupdateorderownidMw(), admin_api.BatchUpdateOrderOwnId)...)
		_order.POST("/BatchUpdateUpdaterId", append(_batchupdateupdateridMw(), admin_api.BatchUpdateUpdaterId)...)
		_order.POST("/BatchUpdateWorkflowStatus", append(_batchupdateworkflowstatusMw(), admin_api.BatchUpdateWorkflowStatus)...)
		_order.POST("/CloseOrderDeposit", append(_closeorderdepositMw(), admin_api.CloseOrderDeposit)...)
		_order.POST("/CloseOrderFirst", append(_closeorderfirstMw(), admin_api.CloseOrderFirst)...)
		_order.POST("/DisbursementOrderDeposit", append(_disbursementorderdepositMw(), admin_api.DisbursementOrderDeposit)...)
		_order.POST("/DisbursementOrderDisbursement", append(_disbursementorderdisbursementMw(), admin_api.DisbursementOrderDisbursement)...)
		_order.POST("/DisbursementOrderFinal", append(_disbursementorderfinalMw(), admin_api.DisbursementOrderFinal)...)
		_order.POST("/DisbursementOrderFirst", append(_disbursementorderfirstMw(), admin_api.DisbursementOrderFirst)...)
		_order.POST("/GetCurrencyList", append(_getcurrencylistMw(), admin_api.GetCurrencyList)...)
		_order.POST("/GetLatestOrderInfoByCustomerIds", append(_getlatestorderinfobycustomeridsMw(), admin_api.GetLatestOrderInfoByCustomerIds)...)
		_order.POST("/GetLatestOrderOperationLogByOrderId", append(_getlatestorderoperationlogbyorderidMw(), admin_api.GetLatestOrderOperationLogByOrderId)...)
		_order.POST("/GetOldNewCustomers", append(_getoldnewcustomersMw(), admin_api.GetOldNewCustomers)...)
		_order.POST("/GetOrderCloseList", append(_getordercloselistMw(), admin_api.GetOrderCloseList)...)
		_order.POST("/GetOrderCountByCustomerId", append(_getordercountbycustomeridMw(), admin_api.GetOrderCountByCustomerId)...)
		_order.POST("/GetOrderCountByCustomerIds", append(_getordercountbycustomeridsMw(), admin_api.GetOrderCountByCustomerIds)...)
		_order.POST("/GetOrderDepositList", append(_getorderdepositlistMw(), admin_api.GetOrderDepositList)...)
		_order.POST("/GetOrderDisbursementList", append(_getorderdisbursementlistMw(), admin_api.GetOrderDisbursementList)...)
		_order.POST("/GetOrderFinalList", append(_getorderfinallistMw(), admin_api.GetOrderFinalList)...)
		_order.POST("/GetOrderFirstList", append(_getorderfirstlistMw(), admin_api.GetOrderFirstList)...)
		_order.POST("/GetOrderInfo", append(_getorderinfoMw(), admin_api.GetOrderInfo)...)
		_order.POST("/GetOrderInfoByIds", append(_getorderinfobyidsMw(), admin_api.GetOrderInfoByIds)...)
		_order.POST("/GetOrderInfoByOrderNo", append(_getorderinfobyordernoMw(), admin_api.GetOrderInfoByOrderNo)...)
		_order.POST("/GetOrderList", append(_getorderlistMw(), admin_api.GetOrderList)...)
		_order.POST("/GetOrderOperationLogList", append(_getorderoperationloglistMw(), admin_api.GetOrderOperationLogList)...)
		_order.POST("/GetOrderSuccessList", append(_getordersuccesslistMw(), admin_api.GetOrderSuccessList)...)
		_order.POST("/GetRedLineRiskCustomers", append(_getredlineriskcustomersMw(), admin_api.GetRedLineRiskCustomers)...)
		_order.POST("/GetRefundHighRiskCustomers", append(_getrefundhighriskcustomersMw(), admin_api.GetRefundHighRiskCustomers)...)
		_order.POST("/GetWorkflowCompleteOrders", append(_getworkflowcompleteordersMw(), admin_api.GetWorkflowCompleteOrders)...)
		_order.POST("/OrderDepositRefund", append(_orderdepositrefundMw(), admin_api.OrderDepositRefund)...)
		_order.POST("/SaveOrderDeposit", append(_saveorderdepositMw(), admin_api.SaveOrderDeposit)...)
		_order.POST("/SaveOrderFirst", append(_saveorderfirstMw(), admin_api.SaveOrderFirst)...)
		_order.POST("/UpdateOrderDisbursement", append(_updateorderdisbursementMw(), admin_api.UpdateOrderDisbursement)...)
		_order.POST("/UpdateOrderFinal", append(_updateorderfinalMw(), admin_api.UpdateOrderFinal)...)
	}
	{
		_product := root.Group("/product", _productMw()...)
		_product.POST("/ChangeProductStatus", append(_changeproductstatusMw(), admin_api.ChangeProductStatus)...)
		_product.POST("/CreateMajorCategory", append(_createmajorcategoryMw(), admin_api.CreateMajorCategory)...)
		_product.POST("/DeleteBusiness", append(_deletebusinessMw(), admin_api.DeleteBusiness)...)
		_product.POST("/DeleteContract", append(_deletecontractMw(), admin_api.DeleteContract)...)
		_product.POST("/DeleteMajor", append(_deletemajorMw(), admin_api.DeleteMajor)...)
		_product.POST("/DeleteMajorCategory", append(_deletemajorcategoryMw(), admin_api.DeleteMajorCategory)...)
		_product.POST("/DeleteProduct", append(_deleteproductMw(), admin_api.DeleteProduct)...)
		_product.POST("/DeleteServiceItem", append(_deleteserviceitemMw(), admin_api.DeleteServiceItem)...)
		_product.POST("/DeleteUniversity", append(_deleteuniversityMw(), admin_api.DeleteUniversity)...)
		_product.POST("/GetBrandBusinessList", append(_getbrandbusinesslistMw(), admin_api.GetBrandBusinessList)...)
		_product.POST("/GetBusinessDetail", append(_getbusinessdetailMw(), admin_api.GetBusinessDetail)...)
		_product.POST("/GetBusinessList", append(_getbusinesslistMw(), admin_api.GetBusinessList)...)
		_product.POST("/GetContractDetail", append(_getcontractdetailMw(), admin_api.GetContractDetail)...)
		_product.POST("/GetContractList", append(_getcontractlistMw(), admin_api.GetContractList)...)
		_product.POST("/GetCourseLevelList", append(_listcourselevelsMw(), admin_api.ListCourseLevels)...)
		_product.POST("/GetLocationsList", append(_getlocationslistMw(), admin_api.GetLocationsList)...)
		_product.POST("/GetMajor", append(_getmajorMw(), admin_api.GetMajor)...)
		_product.POST("/GetMajorCategory", append(_getmajorcategoryMw(), admin_api.GetMajorCategory)...)
		_product.POST("/GetMajorCategoryHierarchy", append(_getmajorcategoryhierarchyMw(), admin_api.GetMajorCategoryHierarchy)...)
		_product.POST("/GetProductDetail", append(_getproductdetailMw(), admin_api.GetProductDetail)...)
		_product.POST("/GetProductList", append(_getproductlistMw(), admin_api.GetProductList)...)
		_product.POST("/GetProductSpecDetailList", append(_getproductspecdetaillistMw(), admin_api.GetProductSpecDetailList)...)
		_product.POST("/GetServiceItemDetail", append(_getserviceitemdetailMw(), admin_api.GetServiceItemDetail)...)
		_product.POST("/GetServiceItemList", append(_getserviceitemlistMw(), admin_api.GetServiceItemList)...)
		_product.POST("/GetUniversity", append(_getuniversityMw(), admin_api.GetUniversity)...)
		_product.POST("/ListMajorCategoriesForNewSub", append(_listmajorcategoriesfornewsubMw(), admin_api.ListMajorCategoriesForNewSub)...)
		_product.POST("/ListMajorCategoryGroups", append(_listmajorcategorygroupsMw(), admin_api.ListMajorCategoryGroups)...)
		_product.POST("/ListMajors", append(_listmajorsMw(), admin_api.ListMajors)...)
		_product.POST("/ListSubCategories", append(_listsubcategoriesMw(), admin_api.ListSubCategories)...)
		_product.POST("/ListUniversities", append(_listuniversitiesMw(), admin_api.ListUniversities)...)
		_product.POST("/ListUniversityMajor", append(_listuniversitymajorMw(), admin_api.ListUniversityMajor)...)
		_product.POST("/SaveBusiness", append(_savebusinessMw(), admin_api.SaveBusiness)...)
		_product.POST("/SaveContract", append(_savecontractMw(), admin_api.SaveContract)...)
		_product.POST("/SaveMajor", append(_savemajorMw(), admin_api.SaveMajor)...)
		_product.POST("/SaveProduct", append(_saveproductMw(), admin_api.SaveProduct)...)
		_product.POST("/SaveServiceItem", append(_saveserviceitemMw(), admin_api.SaveServiceItem)...)
		_product.POST("/SaveUniversity", append(_saveuniversityMw(), admin_api.SaveUniversity)...)
	}
	{
		_role := root.Group("/role", _roleMw()...)
		_role.POST("/CreateRole", append(_createroleMw(), admin_api.CreateRole)...)
		_role.POST("/DeleteRole", append(_deleteroleMw(), admin_api.DeleteRole)...)
		_role.POST("/GetRoleEmpCount", append(_getroleempcountMw(), admin_api.GetRoleEmpCount)...)
		_role.POST("/ListRole", append(_listroleMw(), admin_api.ListRole)...)
		_role.POST("/UpdateRole", append(_updateroleMw(), admin_api.UpdateRole)...)
	}
	{
		_tools := root.Group("/tools", _toolsMw()...)
		_tools.POST("/AddAdminVersion", append(_addadminversionMw(), admin_api.AddAdminVersion)...)
		_tools.POST("/AddEmailBlacklist", append(_addemailblacklistMw(), admin_api.AddEmailBlacklist)...)
		_tools.POST("/AddHelpConfig", append(_addhelpconfigMw(), admin_api.AddHelpConfig)...)
		_tools.POST("/DelAdminVersion", append(_deladminversionMw(), admin_api.DelAdminVersion)...)
		_tools.POST("/DelHelpConfig", append(_delhelpconfigMw(), admin_api.DelHelpConfig)...)
		_tools.POST("/DeleteEmailBlacklist", append(_deleteemailblacklistMw(), admin_api.DeleteEmailBlacklist)...)
		_tools.POST("/GetAdminVersion", append(_getadminversionMw(), admin_api.GetAdminVersion)...)
		_tools.POST("/GetApplyConfig", append(_getapplyconfigMw(), admin_api.GetApplyConfig)...)
		_tools.POST("/GetExportFileTaskList", append(_getexportfiletasklistMw(), admin_api.GetExportFileTaskList)...)
		_tools.POST("/GetHelpConfig", append(_gethelpconfigMw(), admin_api.GetHelpConfig)...)
		_tools.POST("/GetImportFileTaskList", append(_getimportfiletasklistMw(), admin_api.GetImportFileTaskList)...)
		_tools.POST("/GetImportFileTaskStatus", append(_getimportfiletaskstatusMw(), admin_api.GetImportFileTaskStatus)...)
		_tools.POST("/GetLastAdminVersion", append(_getlastadminversionMw(), admin_api.GetLastAdminVersion)...)
		_tools.POST("/GetStsToken", append(_getststokenMw(), admin_api.GetStsToken)...)
		_tools.POST("/ListAdminVersion", append(_listadminversionMw(), admin_api.ListAdminVersion)...)
		_tools.POST("/ListHelpConfig", append(_listhelpconfigMw(), admin_api.ListHelpConfig)...)
		_tools.POST("/SearchEmailBlacklist", append(_searchemailblacklistMw(), admin_api.SearchEmailBlacklist)...)
		_tools.POST("/UpdateAdminVersion", append(_updateadminversionMw(), admin_api.UpdateAdminVersion)...)
		_tools.POST("/UpdateHelpConfig", append(_updatehelpconfigMw(), admin_api.UpdateHelpConfig)...)
	}
	{
		_udata := root.Group("/udata", _udataMw()...)
		_udata.POST("/AddEmployeeUdataPriorityByEmail", append(_addemployeeudataprioritybyemailMw(), admin_api.AddEmployeeUdataPriorityByEmail)...)
		_udata.POST("/AddEmployeeUdataPriorityByRole", append(_addemployeeudataprioritybyroleMw(), admin_api.AddEmployeeUdataPriorityByRole)...)
		_udata.POST("/GetCustomerPageData", append(_getcustomerpagedataMw(), admin_api.GetCustomerPageData)...)
		_udata.POST("/GetEmployeeUdataPriority", append(_getemployeeudatapriorityMw(), admin_api.GetEmployeeUdataPriority)...)
		_udata.POST("/GetGroupSalesData", append(_getgroupsalesdataMw(), admin_api.GetGroupSalesData)...)
		_udata.POST("/GetGroupWorkflowData", append(_getgroupworkflowdataMw(), admin_api.GetGroupWorkflowData)...)
		_udata.POST("/GetOrderRefundInfo", append(_getorderrefundinfoMw(), admin_api.GetOrderRefundInfo)...)
		_udata.POST("/GetOrderSalesInfo", append(_getordersalesinfoMw(), admin_api.GetOrderSalesInfo)...)
		_udata.POST("/GetOverviewData", append(_getoverviewdataMw(), admin_api.GetOverviewData)...)
		_udata.POST("/GetSalesData", append(_getsalesdataMw(), admin_api.GetSalesData)...)
		_udata.POST("/GetWorkflowData", append(_getworkflowdataMw(), admin_api.GetWorkflowData)...)
		_udata.POST("/SetEmployeeAsUdataManager", append(_setemployeeasudatamanagerMw(), admin_api.SetEmployeeAsUdataManager)...)
	}
	{
		_workflow := root.Group("/workflow", _workflowMw()...)
		_workflow.POST("/AcceptWorkflowDispatch", append(_acceptworkflowdispatchMw(), admin_api.AcceptWorkflowDispatch)...)
		_workflow.POST("/AddExportWorkflowTaskList", append(_addexportworkflowtasklistMw(), admin_api.AddExportWorkflowTaskList)...)
		_workflow.POST("/AddExportWorkflowWorkOrderList", append(_addexportworkflowworkorderlistMw(), admin_api.AddExportWorkflowWorkOrderList)...)
		_workflow.POST("/AddFinancialRefundWithWorkflowApply", append(_addfinancialrefundwithworkflowapplyMw(), admin_api.AddFinancialRefundWithWorkflowApply)...)
		_workflow.POST("/AddFundWithWorkflowApply", append(_addfundwithworkflowapplyMw(), admin_api.AddFundWithWorkflowApply)...)
		_workflow.POST("/BatchTransferWorkflowNode", append(_batchtransferworkflownodeMw(), admin_api.BatchTransferWorkflowNode)...)
		_workflow.POST("/BindFundWithWorkflowApply", append(_bindfundwithworkflowapplyMw(), admin_api.BindFundWithWorkflowApply)...)
		_workflow.POST("/CancelFundWithWorkflowApply", append(_cancelfundwithworkflowapplyMw(), admin_api.CancelFundWithWorkflowApply)...)
		_workflow.POST("/CreateWorkflowByOrder", append(_createworkflowbyorderMw(), admin_api.CreateWorkflowByOrder)...)
		_workflow.POST("/DeleteFinancialRefundWithWorkflowApply", append(_deletefinancialrefundwithworkflowapplyMw(), admin_api.DeleteFinancialRefundWithWorkflowApply)...)
		_workflow.POST("/FinishWorkflowNode", append(_finishworkflownodeMw(), admin_api.FinishWorkflowNode)...)
		_workflow.POST("/GetAllowRoleList", append(_getallowrolelistMw(), admin_api.GetAllowRoleList)...)
		_workflow.POST("/GetApplyWorkflowFundInfo", append(_getapplyworkflowfundinfoMw(), admin_api.GetApplyWorkflowFundInfo)...)
		_workflow.POST("/GetApplyWorkflowNodeChooseGuide", append(_getapplyworkflownodechooseguideMw(), admin_api.GetApplyWorkflowNodeChooseGuide)...)
		_workflow.POST("/GetApplyWorkflowNodeClosenessLetter", append(_getapplyworkflownodeclosenessletterMw(), admin_api.GetApplyWorkflowNodeClosenessLetter)...)
		_workflow.POST("/GetApplyWorkflowNodeConfirm", append(_getapplyworkflownodeconfirmMw(), admin_api.GetApplyWorkflowNodeConfirm)...)
		_workflow.POST("/GetApplyWorkflowNodeFile", append(_getapplyworkflownodefileMw(), admin_api.GetApplyWorkflowNodeFile)...)
		_workflow.POST("/GetApplyWorkflowNodeInfo", append(_getapplyworkflownodeinfoMw(), admin_api.GetApplyWorkflowNodeInfo)...)
		_workflow.POST("/GetApplyWorkflowNodeMaterialPackage", append(_getapplyworkflownodematerialpackageMw(), admin_api.GetApplyWorkflowNodeMaterialPackage)...)
		_workflow.POST("/GetApplyWorkflowNodeOffer", append(_getapplyworkflownodeofferMw(), admin_api.GetApplyWorkflowNodeOffer)...)
		_workflow.POST("/GetApplyWorkflowNodeReject", append(_getapplyworkflownoderejectMw(), admin_api.GetApplyWorkflowNodeReject)...)
		_workflow.POST("/GetApplyWorkflowNodeSingleFile", append(_getapplyworkflownodesinglefileMw(), admin_api.GetApplyWorkflowNodeSingleFile)...)
		_workflow.POST("/GetApplyWorkflowNodeSingleRequirement", append(_getapplyworkflownodesinglerequirementMw(), admin_api.GetApplyWorkflowNodeSingleRequirement)...)
		_workflow.POST("/GetApplyWorkflowNodeVisa", append(_getapplyworkflownodevisaMw(), admin_api.GetApplyWorkflowNodeVisa)...)
		_workflow.POST("/GetAttachmentsByOrderId", append(_getattachmentsbyorderidMw(), admin_api.GetAttachmentsByOrderId)...)
		_workflow.POST("/GetCustomerWorkflowList", append(_getcustomerworkflowlistMw(), admin_api.GetCustomerWorkflowList)...)
		_workflow.POST("/GetGuidanceDemandInfo", append(_getworkflowguidancedemandinfoMw(), admin_api.GetWorkflowGuidanceDemandInfo)...)
		_workflow.POST("/GetGuidanceDetailByApply", append(_getguidancedetailbyapplyMw(), admin_api.GetGuidanceDetailByApply)...)
		_workflow.POST("/GetGuidanceLessonInfo", append(_getworkflowguidancelessoninfoMw(), admin_api.GetWorkflowGuidanceLessonInfo)...)
		_workflow.POST("/GetGuidanceManuscriptInfo", append(_getworkflowguidancemanuscriptinfoMw(), admin_api.GetWorkflowGuidanceManuscriptInfo)...)
		_workflow.POST("/GetGuidanceResultInfo", append(_getworkflowguidanceresultinfoMw(), admin_api.GetWorkflowGuidanceResultInfo)...)
		_workflow.POST("/GetGuidanceTeacherInfo", append(_getworkflowguidanceteacherinfoMw(), admin_api.GetWorkflowGuidanceTeacherInfo)...)
		_workflow.POST("/GetSingleWorkflowDispatch", append(_getsingleworkflowdispatchMw(), admin_api.GetSingleWorkflowDispatch)...)
		_workflow.POST("/GetWorkflowDetail", append(_getworkflowdetailMw(), admin_api.GetWorkflowDetail)...)
		_workflow.POST("/GetWorkflowFollow", append(_getworkflowfollowMw(), admin_api.GetWorkflowFollow)...)
		_workflow.POST("/GetWorkflowListByOrderId", append(_getworkflowlistbyorderidMw(), admin_api.GetWorkflowListByOrderId)...)
		_workflow.POST("/GetWorkflowNodeSchoolInfo", append(_getworkflownodeschoolinfoMw(), admin_api.GetWorkflowNodeSchoolInfo)...)
		_workflow.POST("/GetWorkflowOrderGoodsPayment", append(_getworkflowordergoodspaymentMw(), admin_api.GetWorkflowOrderGoodsPayment)...)
		_workflow.POST("/GetWorkflowTemplateAndBusiness", append(_getworkflowtemplateandbusinessMw(), admin_api.GetWorkflowTemplateAndBusiness)...)
		_workflow.POST("/ListWorkflow", append(_listworkflowMw(), admin_api.ListWorkflow)...)
		_workflow.POST("/ListWorkflowDispatcher", append(_listworkflowdispatcherMw(), admin_api.ListWorkflowDispatcher)...)
		_workflow.POST("/ListWorkflowNodeDispatcher", append(_listworkflownodedispatcherMw(), admin_api.ListWorkflowNodeDispatcher)...)
		_workflow.POST("/ListWorkflowProcessor", append(_listworkflowprocessorMw(), admin_api.ListWorkflowProcessor)...)
		_workflow.POST("/ListWorkflowTaskView", append(_listworkflowtaskviewMw(), admin_api.ListWorkflowTaskView)...)
		_workflow.POST("/ListWorkflowWithoutCtx", append(_listworkflowwithoutctxMw(), admin_api.ListWorkflowWithoutCtx)...)
		_workflow.POST("/PauseWorkflowNode", append(_pauseworkflownodeMw(), admin_api.PauseWorkflowNode)...)
		_workflow.POST("/ReceiveWorkflowNode", append(_receiveworkflownodeMw(), admin_api.ReceiveWorkflowNode)...)
		_workflow.POST("/RejectWorkflowDispatch", append(_rejectworkflowdispatchMw(), admin_api.RejectWorkflowDispatch)...)
		_workflow.POST("/RelationshipWorkflowGroupChat", append(_relationshipworkflowgroupchatMw(), admin_api.RelationshipWorkflowGroupChat)...)
		_workflow.POST("/RestartWorkflowNode", append(_restartworkflownodeMw(), admin_api.RestartWorkflowNode)...)
		_workflow.POST("/SaveWorkflowFile", append(_saveworkflowfileMw(), admin_api.SaveWorkflowFile)...)
		_workflow.POST("/SkipWorkflowNode", append(_skipworkflownodeMw(), admin_api.SkipWorkflowNode)...)
		_workflow.POST("/SuspendWorkflowNode", append(_suspendworkflownodeMw(), admin_api.SuspendWorkflowNode)...)
		_workflow.POST("/TerminateWorkflowNode", append(_terminateworkflownodeMw(), admin_api.TerminateWorkflowNode)...)
		_workflow.POST("/TransferWorkflowNode", append(_transferworkflownodeMw(), admin_api.TransferWorkflowNode)...)
		_workflow.POST("/UnbindWorkflowGroupChat", append(_unbindworkflowgroupchatMw(), admin_api.UnbindWorkflowGroupChat)...)
		_workflow.POST("/UpdateApplyWorkflowNodeChooseGuide", append(_updateapplyworkflownodechooseguideMw(), admin_api.UpdateApplyWorkflowNodeChooseGuide)...)
		_workflow.POST("/UpdateApplyWorkflowNodeClosenessLetter", append(_updateapplyworkflownodeclosenessletterMw(), admin_api.UpdateApplyWorkflowNodeClosenessLetter)...)
		_workflow.POST("/UpdateApplyWorkflowNodeConfirm", append(_updateapplyworkflownodeconfirmMw(), admin_api.UpdateApplyWorkflowNodeConfirm)...)
		_workflow.POST("/UpdateApplyWorkflowNodeFile", append(_updateapplyworkflownodefileMw(), admin_api.UpdateApplyWorkflowNodeFile)...)
		_workflow.POST("/UpdateApplyWorkflowNodeInfo", append(_updateapplyworkflownodeinfoMw(), admin_api.UpdateApplyWorkflowNodeInfo)...)
		_workflow.POST("/UpdateApplyWorkflowNodeMaterialPackage", append(_updateapplyworkflownodematerialpackageMw(), admin_api.UpdateApplyWorkflowNodeMaterialPackage)...)
		_workflow.POST("/UpdateApplyWorkflowNodeOffer", append(_updateapplyworkflownodeofferMw(), admin_api.UpdateApplyWorkflowNodeOffer)...)
		_workflow.POST("/UpdateApplyWorkflowNodeReject", append(_updateapplyworkflownoderejectMw(), admin_api.UpdateApplyWorkflowNodeReject)...)
		_workflow.POST("/UpdateApplyWorkflowNodeSingleFile", append(_updateapplyworkflownodesinglefileMw(), admin_api.UpdateApplyWorkflowNodeSingleFile)...)
		_workflow.POST("/UpdateApplyWorkflowNodeSingleRequirement", append(_updateapplyworkflownodesinglerequirementMw(), admin_api.UpdateApplyWorkflowNodeSingleRequirement)...)
		_workflow.POST("/UpdateApplyWorkflowNodeVisa", append(_updateapplyworkflownodevisaMw(), admin_api.UpdateApplyWorkflowNodeVisa)...)
		_workflow.POST("/UpdateFinancialRefundWithWorkflowApply", append(_updatefinancialrefundwithworkflowapplyMw(), admin_api.UpdateFinancialRefundWithWorkflowApply)...)
		_workflow.POST("/UpdateGuidanceDemandInfo", append(_updateworkflowguidancedemandinfoMw(), admin_api.UpdateWorkflowGuidanceDemandInfo)...)
		_workflow.POST("/UpdateGuidanceLessonInfo", append(_updateworkflowguidancelessoninfoMw(), admin_api.UpdateWorkflowGuidanceLessonInfo)...)
		_workflow.POST("/UpdateGuidanceManuscriptInfo", append(_updateworkflowguidancemanuscriptinfoMw(), admin_api.UpdateWorkflowGuidanceManuscriptInfo)...)
		_workflow.POST("/UpdateGuidanceResultInfo", append(_updateworkflowguidanceresultinfoMw(), admin_api.UpdateWorkflowGuidanceResultInfo)...)
		_workflow.POST("/UpdateGuidanceTeacherInfo", append(_updateworkflowguidanceteacherinfoMw(), admin_api.UpdateWorkflowGuidanceTeacherInfo)...)
		_workflow.POST("/UpdateSingleWorkflowDispatch", append(_updatesingleworkflowdispatchMw(), admin_api.UpdateSingleWorkflowDispatch)...)
		_workflow.POST("/UpdateWorkflowApplyFundCurrencyInfo", append(_updateworkflowapplyfundcurrencyinfoMw(), admin_api.UpdateWorkflowApplyFundCurrencyInfo)...)
		_workflow.POST("/UpdateWorkflowFollow", append(_updateworkflowfollowMw(), admin_api.UpdateWorkflowFollow)...)
		_workflow.POST("/UpdateWorkflowInnerStatus", append(_updateworkflowinnerstatusMw(), admin_api.UpdateWorkflowInnerStatus)...)
		_workflow.POST("/UpdateWorkflowName", append(_updateworkflownameMw(), admin_api.UpdateWorkflowName)...)
		_workflow.POST("/UpdateWorkflowNodeProcessor", append(_updateworkflownodeprocessorMw(), admin_api.UpdateWorkflowNodeProcessor)...)
		_workflow.POST("/UpdateWorkflowNodeSchoolInfo", append(_updateworkflownodeschoolinfoMw(), admin_api.UpdateWorkflowNodeSchoolInfo)...)
		_workflow.POST("/UpdateWorkflowNodeTaskStatus", append(_updateworkflownodetaskstatusMw(), admin_api.UpdateWorkflowNodeTaskStatus)...)
		_workflow.POST("/WorkflowConfirmPush", append(_workflowconfirmpushMw(), admin_api.WorkflowConfirmPush)...)
		_workflow.POST("/WorkflowCreateGroupChat", append(_workflowcreategroupchatMw(), admin_api.WorkflowCreateGroupChat)...)
		_workflow.POST("/WorkflowDetails", append(_workflowdetailsMw(), admin_api.WorkflowDetails)...)
		_workflow.POST("/WorkflowOperationLog", append(_workflowoperationlogMw(), admin_api.WorkflowOperationLog)...)
		_workflow.POST("/WorkflowServiceAbnormalDone", append(_workflowserviceabnormaldoneMw(), admin_api.WorkflowServiceAbnormalDone)...)
		_workflow.POST("/WorkflowServiceDone", append(_workflowservicedoneMw(), admin_api.WorkflowServiceDone)...)
		_workflow.POST("/WorkflowTemplateDetail", append(_workflowtemplatedetailMw(), admin_api.WorkflowTemplateDetail)...)
	}
}
