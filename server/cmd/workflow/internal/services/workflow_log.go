package services

import (
	"context"

	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/workflow"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/ctxmeta"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/workflow/internal/cross_models"
)

func GetWorkflowOperatorLog(ctx context.Context, req *workflow.GetWorkflowLogRequest) (resp *workflow.GetWorkflowLogResponse, err error) {
	resp = &workflow.GetWorkflowLogResponse{}
	newAuth := ctxmeta.MustGetAuth(ctx)
	if !newAuth.IsEmployee() {
		logger.CtxErrorf(ctx, "客户无法访问此接口")
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_AuthForbidden)
		return resp, nil
	}

	operatorLogList, total, err := cross_models.GetWorkflowOperatorLogList(ctx, req.GetWorkflowId(), req.GetOrderBy(), req.GetPageNum(), req.GetPageSize())
	if err != nil {
		logger.CtxErrorf(ctx, "获取工作流操作日志列表失败: %+v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	resp.Total = total
	respOperatorList := make([]*workflow.WorkflowOperatorLog, 0, len(operatorLogList))
	for _, operatorLog := range operatorLogList {
		respOperatorList = append(respOperatorList, &workflow.WorkflowOperatorLog{
			OperationAt:     operatorLog.OpAt.UnixMilli(),
			OperatorLogType: workflow.WorkflowOperatorLogType(operatorLog.OperType),
			OperatorInfo:    operatorLog.OpUserInfo,
			OperatorContent: operatorLog.Content,
			WorkflowNo:      operatorLog.WorkflowNo,
		})
	}
	resp.WorkflowOperatorLog = respOperatorList
	return resp, nil
}
