package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/workflow"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/workflow/internal/models"
)

type WorkflowNodeTaskService struct {
	workflowNodeTaskRepo *models.WorkflowNodeTaskDao
}

func NewWorkflowNodeTaskService(
	workflowNodeTaskRepo *models.WorkflowNodeTaskDao,
) *WorkflowNodeTaskService {
	return &WorkflowNodeTaskService{
		workflowNodeTaskRepo: workflowNodeTaskRepo,
	}
}

func GetWorkflowSameStatusNodeTask(ctx context.Context, req *workflow.GetWorkflowSameStatusNodeTaskReq) (resp *workflow.GetWorkflowSameStatusNodeTaskRsp, err error) {
	resp = &workflow.GetWorkflowSameStatusNodeTaskRsp{}

	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to get workflow service: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, err
	}

	return workflowService.getWorkflowSameStatusNodeTask(ctx, req)
}

// CreateWorkflowNodeTask 创建节点任务
func (s *WorkflowNodeTaskService) CreateWorkflowNodeTask(ctx context.Context, nodeID int64, task TaskTemplate) error {
	nodeTask := &model.WorkflowNodeTask{
		WorkflowNodeID: nodeID,
		Name:           task.Name,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	if err := s.workflowNodeTaskRepo.Create(ctx, nodeTask); err != nil {
		logger.CtxErrorf(ctx, "Failed to create workflow node task: %v", err)
		return fmt.Errorf("failed to create workflow node task: %w", err)
	}

	return nil
}

// BatchCreateWorkflowNodeTasks 批量创建节点任务
func (s *WorkflowNodeTaskService) BatchCreateWorkflowNodeTasks(ctx context.Context, node *model.WorkflowNode, tasks []TaskTemplate) error {
	nodeTasks := make([]*model.WorkflowNodeTask, 0, len(tasks))
	now := time.Now()
	for _, task := range tasks {
		nodeTask := &model.WorkflowNodeTask{
			WorkflowNodeID: node.ID,
			Name:           task.Name,
			CreatedAt:      now,
			UpdatedAt:      now,
		}
		if node.StartedAt != nil && !node.StartedAt.IsZero() {
			nodeTask.StartedAt = node.StartedAt
		}
		nodeTasks = append(nodeTasks, nodeTask)
	}

	if err := s.workflowNodeTaskRepo.BatchCreate(ctx, nodeTasks); err != nil {
		logger.CtxErrorf(ctx, "Failed to batch create workflow node tasks: %v", err)
		return fmt.Errorf("failed to batch create workflow node tasks: %w", err)
	}

	return nil
}

// UpdateTaskStatus 更新任务状态
func (s *WorkflowNodeTaskService) UpdateTaskStatus(ctx context.Context, taskID int64, status int32) error {
	task, err := s.workflowNodeTaskRepo.GetByID(ctx, taskID)
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to get workflow node task: %v", err)
		return fmt.Errorf("failed to get workflow node task: %w", err)
	}

	task.Status = status
	task.UpdatedAt = time.Now()

	if err := s.workflowNodeTaskRepo.Update(ctx, task); err != nil {
		logger.CtxErrorf(ctx, "Failed to update workflow node task status: %v", err)
		return fmt.Errorf("failed to update workflow node task status: %w", err)
	}

	return nil
}

// BatchUpdateTaskStatus 批量更新任务状态
func (s *WorkflowNodeTaskService) BatchUpdateTaskStatus(ctx context.Context, tasks []*models.UpdateTaskStatusData) (err error) {
	if len(tasks) == 0 {
		return nil
	}

	if err = s.workflowNodeTaskRepo.BatchUpdate(ctx, tasks); err != nil {
		logger.CtxErrorf(ctx, "Failed to batch update workflow node task status: %v", err)
		return fmt.Errorf("failed to batch update workflow node task status: %w", err)
	}

	return nil
}

// GetNodeTasks 批量获取节点的所有任务
func (s *WorkflowNodeTaskService) GetNodeTasks(ctx context.Context, nodeIDs []int64) (map[int64][]*model.WorkflowNodeTask, error) {
	nodeTaskMap, err := s.workflowNodeTaskRepo.GetByNodeIDs(ctx, nodeIDs)
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to get workflow node tasks: %v", err)
		return nil, fmt.Errorf("failed to get workflow node tasks: %w", err)
	}

	return nodeTaskMap, nil
}

// 检查节点的任务是否都完成
func (s *WorkflowNodeTaskService) checkNodeTasksFinished(ctx context.Context, nodeID int64) bool {
	tasks, err := s.workflowNodeTaskRepo.GetByNodeID(ctx, nodeID)
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to get workflow node tasks: %v", err)
		return false
	}
	for _, task := range tasks {
		if task.Status != int32(workflow.WorkflowNodeTaskStatus_WORKFLOW_NODE_TASK_STATUS_COMPLETED) {
			logger.CtxWarnf(ctx, "Node task %d is not completed", task.ID)
			return false
		}
	}
	return true
}

func (w *WorkflowService) getWorkflowSameStatusNodeTask(ctx context.Context, req *workflow.GetWorkflowSameStatusNodeTaskReq) (resp *workflow.GetWorkflowSameStatusNodeTaskRsp, err error) {
	resp = &workflow.GetWorkflowSameStatusNodeTaskRsp{}

	workflowId := req.GetWorkflowId()
	workflowNodeId := req.GetWorkflowNodeId()
	tasks := req.GetTasks()

	if workflowId <= 0 || workflowNodeId <= 0 {
		logger.CtxWarnf(ctx, "invalid request: workflow ID, node ID must be positive")
		return resp, nil
	}

	var wg sync.WaitGroup
	var wf *model.Workflow
	var wn *model.WorkflowNode
	var wfErr, nodeErr error

	wg.Add(2)
	go func() {
		defer wg.Done()
		wf, wfErr = w.workflowRepo.GetByID(ctx, workflowId)
	}()
	go func() {
		defer wg.Done()
		wn, nodeErr = w.nodeService.workflowNodeRepo.GetByWorkflowIDAndNodeId(ctx, workflowId, workflowNodeId)
	}()
	wg.Wait()

	if wfErr != nil {
		logger.CtxErrorf(ctx, "Failed to get workflow by ID: %v", wfErr)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, fmt.Errorf("failed to get workflow: %w", wfErr)
	}
	if nodeErr != nil {
		logger.CtxErrorf(ctx, "Failed to get workflow node: %v", nodeErr)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, fmt.Errorf("failed to get workflow node: %w", nodeErr)
	}

	filter := models.SameWorkflowFilter{
		WorkflowStatus: workflow.WorkflowStatus(wf.Status),
		CustomerId:     wf.CustomerID,
		BusinessType:   workflow.WorkflowBusinessType(wf.BusinessType),
		TemplateId:     wf.WorkflowTemplateID,
		OrderId:        wf.OrderID,
		CreateBy:       wf.CreatedBy,
		GoodsType:      wf.GoodsType,
		GoodsId:        wf.ProductID,
		SpecId:         wf.SpecID,
	}

	workflowList, err := w.workflowRepo.ListSameWorkflow(ctx, &filter)
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to list similar workflows: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, fmt.Errorf("failed to list similar workflows: %w", err)
	}

	workflowIds := make([]int64, 0, len(workflowList))
	for _, wfl := range workflowList {
		if wfl.ID == wf.ID {
			continue
		}
		workflowIds = append(workflowIds, wfl.ID)
	}

	if len(workflowIds) == 0 {
		logger.CtxWarnf(ctx, "No workflow found with same status")
		return resp, nil
	}

	nodes, err := w.nodeService.workflowNodeRepo.BatchGetByWorkflowIDsAndNodeName(ctx, workflowIds, wn.Name, workflow.WorkflowNodeStatus(wn.Status), wn.ProcessedBy)
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to batch get workflow nodes: %v", err)
		return resp, nil
	}

	nodeIds := make([]int64, 0, len(nodes))
	for _, node := range nodes {
		if node.ID == wn.ID {
			continue
		}
		nodeIds = append(nodeIds, node.ID)
	}

	if len(nodeIds) == 0 {
		logger.CtxWarnf(ctx, "No workflow nodes found with name: %s", wn.Name)
		return resp, nil
	}

	// 更新节点下任务状态
	for _, task := range tasks {
		err = w.taskService.workflowNodeTaskRepo.UpdateTaskStatusByName(ctx, nodeIds, task.Name, int32(task.Status))
		if err != nil {
			logger.CtxErrorf(ctx, "Failed to update task status: %v", err)
			continue
		}
	}

	return resp, nil
}
