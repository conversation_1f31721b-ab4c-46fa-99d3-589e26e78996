package services

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"uofferv2/kitex_gen/constants"
	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/workflow"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/ctxmeta"
	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/workflow/internal/cross_models"
	"uofferv2/server/cmd/workflow/internal/models"
	"uofferv2/server/cmd/workflow/internal/services/rpcs"
)

func ListWorkflow(ctx context.Context, req *workflow.ListWorkflowReq) (resp *workflow.ListWorkflowRsp, err error) {
	resp = &workflow.ListWorkflowRsp{}

	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "获取工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}

	return workflowService.listWorkflow(ctx, req)
}

func (w *WorkflowService) listWorkflow(ctx context.Context, req *workflow.ListWorkflowReq) (resp *workflow.ListWorkflowRsp, err error) {
	resp = new(workflow.ListWorkflowRsp)
	var employeeId int64
	var employeeIds []int64
	var customerId int64
	auth := ctxmeta.MustGetAuth(ctx)
	isWorkflow := ctxmeta.IsWorkflowFull(ctx)
	if auth.IsEmployee() {
		employeeId = auth.EmployeeId()
		// 1. 获取员工ID列表
		employeeIds, err = rpcs.GetEmployeeIDs(ctx, employeeId)
		if err != nil {
			logger.CtxErrorf(ctx, "获取员工ID列表失败: %v", err)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_EmployeeNotExists)
			return resp, nil
		}
	}

	if auth.IsCustomer() {
		customerId = auth.CustomerId()
		req.CustomerId = customerId
	}

	if isWorkflow {
		employeeIds = []int64{}
	}

	// 2. 获取工单列表数据
	wfs, total, err := fetchWorkflowList(ctx, w.workflowRepo, employeeIds, req)
	if err != nil {
		logger.CtxErrorf(ctx, "获取工单列表失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	// 3. 处理空结果情况
	if len(wfs) == 0 {
		resp.Items = make([]*workflow.ListWorkflowRsp_ListWorkflowViewItem, 0)
		resp.Total = 0
		return resp, nil
	}

	// 4. 收集所需的ID信息
	workflowIDs, employeeIds, customerIDs := collectRequiredIDs(wfs, employeeIds)

	// 5. 并发获取关联数据
	relatedData, err := fetchRelatedData(ctx, w.nodeService, wfs, workflowIDs, employeeIds, customerIDs)
	if err != nil {
		logger.CtxErrorf(ctx, "获取关联数据失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_ThirdPartyError)
		return resp, nil
	}
	// 6. 构建响应
	resp.Items = buildWorkflowListResponse(ctx, wfs, relatedData)
	resp.Total = int32(total)

	return resp, nil
}

func ListWorkflowWithoutCtx(ctx context.Context, req *workflow.ListWorkflowWithoutCtxReq) (resp *workflow.ListWorkflowWithoutCtxRsp, err error) {
	resp = &workflow.ListWorkflowWithoutCtxRsp{}

	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "获取工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}

	return workflowService.ListWorkflowWithoutCtx(ctx, req)
}

func (w *WorkflowService) ListWorkflowWithoutCtx(ctx context.Context, req *workflow.ListWorkflowWithoutCtxReq) (resp *workflow.ListWorkflowWithoutCtxRsp, err error) {
	resp = new(workflow.ListWorkflowWithoutCtxRsp)

	wf, total, err := w.workflowRepo.WorkflowListByOrderIdWithoutCtxV1(ctx, &models.WorkflowListFilter{
		WorkflowStatus:   req.WorkflowStatus,
		CustomerId:       req.CustomerId,
		BusinessType:     req.BusinessType,
		OrderId:          req.GetOrderId(),
		WorkflowNameOrNo: req.GetWorkflowNoOrName(),
		WorkflowIds:      req.GetWorkflowIds(),
		PageNum:          req.GetPageNum(),
		PageSize:         req.GetPageSize(),
	})
	if err != nil {
		logger.CtxErrorf(ctx, "获取工单列表失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	// 3. 处理空结果情况
	if len(wf) == 0 {
		resp.Items = make([]*workflow.WorkflowInfoEntity, 0)
		resp.Total = 0
		logger.CtxWarnf(ctx, "未找到工单")
		return resp, nil
	}

	resp.Items = modelList2WorkflowEntityList(wf)
	resp.Total = int32(total)
	return resp, nil
}

// collectRequiredIDs 收集所需的ID信息
func collectRequiredIDs(workflows []*models.WorkflowAllInOne, employeeIDs []int64) ([]int64, []int64, []int64) {
	workflowIDs := make([]int64, 0, len(workflows))
	customerIDs := make([]int64, 0, len(workflows))

	for _, wf := range workflows {
		workflowIDs = append(workflowIDs, wf.Workflow.ID)
		customerIDs = append(customerIDs, wf.Workflow.CustomerID)
		employeeIDs = append(employeeIDs, wf.Workflow.CreatedBy, wf.Workflow.UpdatedBy)
	}
	workflowIDs = RemoveDuplicates(workflowIDs, true)
	customerIDs = RemoveDuplicates(customerIDs, true)
	employeeIDs = RemoveDuplicates(employeeIDs, true)
	return workflowIDs, employeeIDs, customerIDs
}

// fetchWorkflowList 获取工单列表数据
func fetchWorkflowList(ctx context.Context, workflowRepo *models.WorkflowDao, employeeIDs []int64, req *workflow.ListWorkflowReq) ([]*models.WorkflowAllInOne, int64, error) {
	// 1. 构建过滤条件
	workflowFilter := &models.WorkflowListFilter{
		BusinessType:     req.GetBusinessType(),
		TemplateIds:      req.GetTemplateIds(),
		CurrentNodeName:  req.GetCurrentNodeName(),
		BusinessInfo:     req.GetBusinessInfo(),
		WorkflowStatus:   req.GetWorkflowStatus(),
		InnerStatus:      req.GetInnerStatus(),
		WorkflowNameOrNo: req.GetWorkflowNoOrName(),
		ServiceName:      req.GetServiceName(),
		OrderNo:          req.GetOrderNo(),
		CustomerId:       req.GetCustomerId(),
		CreatorId:        req.GetCreatorId(),
		GoodsIdOrName:    req.GetGoodsIdOrName(),
		DataStr:          req.GetDateString(),
		DataStart:        req.GetDateStart(),
		DataEnd:          req.GetDateEnd(),
		OrderBy:          req.GetOrderBy(),
		PageNum:          req.GetPageNum(),
		PageSize:         req.GetPageSize(),
		ProcessedBy:      req.GetAssigneeId(),
		OrderId:          req.GetOrderId(),
		WorkflowIds:      req.GetWorkflowIds(),
	}

	applicationFilter := &models.WorkflowApplicationFilter{
		SchoolId:           req.GetSchoolId(),
		MajorId:            req.GetMajorId(),
		CourseLevels:       req.GetCourseLevel(),
		Channels:           req.GetChannel(),
		Locations:          req.GetLocations(),
		ExpectedEntryStart: req.GetExpectedEntryStart(),
		ExpectedEntryEnd:   req.GetExpectedEntryEnd(),
	}

	guideFilter := &models.WorkflowInfoGuidanceFilter{
		DemandTypeList: req.GetDemandType(),
		GuideTeacher:   req.GetGuideTeacher(),
	}
	// 2. 获取工单列表
	workflowAllInfoList, total, err := workflowRepo.ListWorkflowViewV1(ctx, employeeIDs, workflowFilter, applicationFilter, guideFilter)
	if err != nil {
		logger.CtxErrorf(ctx, "获取工单列表失败: %v", err)
		return nil, 0, fmt.Errorf("获取工单列表失败: %w", err)
	}

	return workflowAllInfoList, total, nil
}

// RelatedData 关联数据结构
type RelatedData struct {
	NodeMap       map[int64][]*models.WorkflowNodeWithTasks
	EmployeeMap   map[int64]*workflow.EmployeeData
	CustomerMap   map[int64]*workflow.CustomerData
	orderMap      map[int64]*model.Order
	orderGoodsMap map[int64]*model.OrderGood
}

// fetchRelatedData 并发获取关联数据
func fetchRelatedData(ctx context.Context, nodeService *WorkflowNodeService, wf []*models.WorkflowAllInOne, workflowIDs []int64, employeeIDs []int64, customerIDs []int64) (*RelatedData, error) {
	var (
		wg          sync.WaitGroup
		mu          sync.Mutex
		fetchErr    error
		relatedData = &RelatedData{}
	)

	wg.Add(3)
	// 获取订单数据
	go func() {
		defer wg.Done()
		orderMap, orderGoodsMap, err := cross_models.GetByOrderIdsAndGoodsIds(ctx, wf)
		mu.Lock()
		if err != nil {
			fetchErr = err
		} else {
			relatedData.orderMap = orderMap
			relatedData.orderGoodsMap = orderGoodsMap
		}
		mu.Unlock()
	}()

	// 获取节点数据
	go func() {
		defer wg.Done()
		nodeMap, err := nodeService.workflowNodeRepo.GetByWorkflowIDsAndWorkflowStatus(ctx, workflowIDs)
		mu.Lock()
		if err != nil {
			fetchErr = err
		} else {
			relatedData.NodeMap = nodeMap
			for _, nl := range nodeMap {
				for _, n := range nl {
					if n.DispatchedBy != 0 {
						employeeIDs = append(employeeIDs, n.DispatchedBy)
					}
					if n.ProcessedBy != 0 {
						employeeIDs = append(employeeIDs, n.ProcessedBy)
					}
				}
			}
		}
		mu.Unlock()
	}()

	// 获取客户数据
	go func() {
		defer wg.Done()
		customerMap, err := rpcs.GetCustomerInfoByIds(ctx, RemoveDuplicates(customerIDs, true))
		mu.Lock()
		if err != nil {
			fetchErr = err
		} else {
			relatedData.CustomerMap = customerMap
		}
		mu.Unlock()
	}()

	wg.Wait()

	if fetchErr != nil {
		return nil, fetchErr
	}

	// 获取员工数据
	employeeMap, err := rpcs.GetEmployeeInfoByIds(ctx, RemoveDuplicates(employeeIDs, true))
	if err != nil {
		logger.CtxErrorf(ctx, "获取员工信息失败: %v", err)
		relatedData.EmployeeMap = nil
		return relatedData, nil
	}
	relatedData.EmployeeMap = employeeMap

	return relatedData, nil
}

// buildWorkflowListResponse 构建工单列表响应
func buildWorkflowListResponse(ctx context.Context, workflows []*models.WorkflowAllInOne, data *RelatedData) []*workflow.ListWorkflowRsp_ListWorkflowViewItem {
	workflowEntities := make([]*workflow.ListWorkflowRsp_ListWorkflowViewItem, len(workflows))
	for i, wf := range workflows {
		var creator *workflow.EmployeeData
		var updater *workflow.EmployeeData
		if data.EmployeeMap == nil {
			creator = &workflow.EmployeeData{
				Id: wf.Workflow.CreatedBy,
			}
			updater = &workflow.EmployeeData{
				Id: wf.Workflow.UpdatedBy,
			}
		} else {
			creator = data.EmployeeMap[wf.Workflow.CreatedBy]
			updater = data.EmployeeMap[wf.Workflow.UpdatedBy]
		}

		if wf.Workflow.CreatedRoleSnapshot != "" {
			creator.RoleNameSnapshot = wf.Workflow.CreatedRoleSnapshot
		}
		if wf.Workflow.UpdatedRoleSnapshot != "" {
			updater.RoleNameSnapshot = wf.Workflow.UpdatedRoleSnapshot
		}
		if wf.Workflow.CreatedDeptSnapshot != "" {
			creator.DeptNameSnapshot = wf.Workflow.CreatedDeptSnapshot
		}
		if wf.Workflow.UpdatedDeptSnapshot != "" {
			updater.DeptNameSnapshot = wf.Workflow.UpdatedDeptSnapshot
		}

		var customer *workflow.CustomerData
		if data.CustomerMap != nil {
			customer = data.CustomerMap[wf.Workflow.CustomerID]
		}

		var steps []*workflow.ListWorkflowRsp_StepInfo
		if len(data.NodeMap) != 0 {
			if nodes, ok := data.NodeMap[wf.Workflow.ID]; ok {
				steps = buildStepInfoList(ctx, nodes, data.EmployeeMap)
			}
		}

		// 构建基本工单信息
		workflowEntity := &workflow.ListWorkflowRsp_ListWorkflowViewItem{
			Id:                   wf.Workflow.ID,
			WorkflowNo:           wf.Workflow.WorkflowNo,
			WorkflowName:         wf.Workflow.WorkflowName,
			WorkflowTemplateId:   wf.Workflow.WorkflowTemplateID,
			WorkflowTemplateName: wf.Workflow.WorkflowTemplateName,
			Status:               workflow.WorkflowStatus(wf.Workflow.Status),
			BusinessType:         workflow.WorkflowBusinessType(wf.Workflow.BusinessType),
			BusinessName:         wf.Workflow.BusinessName,
			OrderId:              wf.Workflow.OrderID,
			Creator:              creator,
			Updater:              updater,
			Customer:             customer,
			CreatedAt:            getUnixTime(wf.Workflow.CreatedAt),
			UpdatedAt:            getUnixTime(wf.Workflow.UpdatedAt),
			DeletedAt:            getUnixTime(wf.Workflow.DeletedAt),
			DispatchedAt:         getUnixTime(wf.Workflow.DispatchedAt),
			ReceivedAt:           getUnixTime(wf.Workflow.ReceivedAt),
			PausedAt:             getUnixTime(wf.Workflow.PausedAt),
			TerminatedAt:         getUnixTime(wf.Workflow.TerminatedAt),
			CompletedAt:          getUnixTime(wf.Workflow.CompletedAt),
			DueAt:                getUnixTime(wf.Workflow.DueAt),
			MessageBoardId:       wf.Workflow.MessageBoardID,
			QqImGroupId:          wf.Workflow.QqImGroupID,
			Remark:               wf.Workflow.Remark,
			Steps:                steps,
			Version:              wf.Workflow.Version,
			InnerStatus:          workflow.InnerStatus(wf.Workflow.InnerStatus),
			OrderNo:              wf.Workflow.OrderNo,
			ServiceName:          wf.Workflow.ServiceName,
			GoodsName:            wf.Workflow.ProductName,
		}
		if wf.Guidance != nil {
			workflowEntity.DemandType = workflow.WorkflowDemandType(wf.Guidance.DemandType)
		}
		// 设置应用相关信息
		if wf.Application != nil {
			workflowEntity.SchoolNameZh = wf.Application.SelectionUniversityZh
			workflowEntity.SchoolNameEn = wf.Application.SelectionUniversityEn
			workflowEntity.MajorNameZh = wf.Application.SelectionMajorZh
			workflowEntity.MajorNameEn = wf.Application.SelectionMajorEn
			workflowEntity.CourseLevel = workflow.WorkflowCourseLevel(wf.Application.SelectionCourseLevel)
			workflowEntity.Channel = workflow.Channel(wf.Application.SelectionChannel)
			workflowEntity.ExpectedEntryAt = getUnixTime(wf.Application.SelectionExpectedEntry)
		}

		// 设置订单商品相关信息
		if data.orderGoodsMap != nil {
			if orderGoods, exists := data.orderGoodsMap[wf.Workflow.ID]; exists {
				workflowEntity.GoodsNum = orderGoods.GoodsNum
				workflowEntity.GoodsSpec = orderGoods.GoodsSpec
			}
		}

		workflowEntities[i] = workflowEntity
	}

	return workflowEntities
}

// buildStepInfoList 构建步骤信息列表
func buildStepInfoList(ctx context.Context, nodes []*models.WorkflowNodeWithTasks, employeeMap map[int64]*workflow.EmployeeData) []*workflow.ListWorkflowRsp_StepInfo {
	steps := make([]*workflow.ListWorkflowRsp_StepInfo, 0, len(nodes))
	for _, node := range nodes {
		if node == nil {
			logger.CtxWarnf(ctx, "节点不存在,请检查数据")
			continue
		}
		// 获取处理人和派发人信息
		var dispatcher, processor *workflow.EmployeeData
		if employeeMap == nil {
			logger.CtxWarnf(ctx, "员工不存在,请检查数据")
			dispatcher = &workflow.EmployeeData{
				Id: node.DispatchedBy,
			}
			processor = &workflow.EmployeeData{
				Id: node.ProcessedBy,
			}
		} else {
			if node.DispatchedBy != 0 {
				if d, exists := employeeMap[node.DispatchedBy]; exists {
					dispatcher = d
					dispatcher.DeptNameSnapshot = node.DispatchedByDeptSnapshot
					dispatcher.RoleNameSnapshot = node.DispatchedByRoleSnapshot
				}
			}
			if node.ProcessedBy != 0 {
				if p, exists := employeeMap[node.ProcessedBy]; exists {
					processor = p
					processor.DeptNameSnapshot = node.ProcessedByDeptSnapshot
					processor.RoleNameSnapshot = node.ProcessedByRoleSnapshot
				}
			}
		}

		step := &workflow.ListWorkflowRsp_StepInfo{
			Id:             node.ID,
			Name:           node.Name,
			ChineseName:    node.ChineseName,
			Status:         workflow.WorkflowNodeStatus(node.Status),
			Type:           workflow.WorkflowNodeType(node.Type),
			Dispatcher:     dispatcher,
			Processor:      processor,
			AbnormalReason: node.AbnormalReason,
			Attachments:    node.Attachments,
			SyncNode:       constants.YesNo(node.SyncNode),
			CreatedAt:      getUnixTime(node.CreatedAt),
			UpdatedAt:      getUnixTime(node.UpdatedAt),
			DispatchedAt:   getUnixTime(node.DispatchedAt),
			StartedAt:      getUnixTime(node.StartedAt),
			PausedAt:       getUnixTime(node.PausedAt),
			TerminatedAt:   getUnixTime(node.TerminatedAt),
			CompletedAt:    getUnixTime(node.CompletedAt),
			Branch:         node.Branch,
			AllowRoleList:  strings.Split(node.AllowRoleList, ","),
		}
		steps = append(steps, step)
	}

	logger.CtxInfof(ctx, "构建步骤信息列表成功")
	return steps
}

func ListWorkflowTaskView(ctx context.Context, req *workflow.ListWorkflowTaskViewReq) (resp *workflow.ListWorkflowTaskViewRsp, err error) {
	resp = &workflow.ListWorkflowTaskViewRsp{}

	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "获取工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}

	return workflowService.listWorkflowTaskView(ctx, req)
}

// listWorkflowTaskView 获取工单任务视图列表
func (w *WorkflowService) listWorkflowTaskView(ctx context.Context, req *workflow.ListWorkflowTaskViewReq) (resp *workflow.ListWorkflowTaskViewRsp, err error) {
	resp = new(workflow.ListWorkflowTaskViewRsp)
	var employeeId int64
	var employeeIDs []int64
	var customerId int64
	isWorkflow := ctxmeta.IsWorkflowFull(ctx)
	auth := ctxmeta.MustGetAuth(ctx)
	if auth.IsEmployee() {
		employeeId = auth.EmployeeId()
		// 1. 获取员工ID列表
		employeeIDs, err = rpcs.GetEmployeeIDs(ctx, employeeId)
		if err != nil {
			logger.CtxErrorf(ctx, "获取员工ID列表失败: %v", err)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_EmployeeNotExists)
			return resp, nil
		}
	}

	if auth.IsCustomer() {
		customerId = auth.CustomerId()
		req.CustomerId = customerId
	}

	if isWorkflow {
		employeeIDs = []int64{}
	}
	// 2. 获取工单列表
	wfs, total, err := fetchWorkflowTaskViewList(ctx, w.workflowRepo, employeeIDs, req)
	if err != nil {
		logger.CtxErrorf(ctx, "获取工单列表失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	// 3. 处理空结果情况
	if len(wfs) == 0 {
		resp.Items = make([]*workflow.WorkflowTaskView, 0)
		resp.Total = 0
		return resp, nil
	}

	// 6. 构建响应
	resp.Items = buildTaskViewListResponse(wfs)
	resp.Total = int32(total)

	return resp, nil
}

func buildTaskViewListResponse(wfs []*models.WorkflowTaskAllInOne) []*workflow.WorkflowTaskView {
	workflowTaskViews := make([]*workflow.WorkflowTaskView, 0, len(wfs))
	for _, wf := range wfs {
		workflowTaskViews = append(workflowTaskViews, buildWorkflowTaskView(wf))
	}
	return workflowTaskViews
}

func buildWorkflowTaskView(wf *models.WorkflowTaskAllInOne) *workflow.WorkflowTaskView {
	return &workflow.WorkflowTaskView{
		WorkflowInfo:    model2NewWorkflowEntity(wf.Info),
		ApplicationInfo: model2WorkflowApplicationEntity(wf.Application),
		GuideInfo:       model2WorkflowGuidanceEntity(wf.Guidance),
		SingleInfo:      model2WorkflowSingleItemEntity(wf.Single),
		StepInfo:        model2WorkflowNodeEntity(wf.Node),
	}
}

// fetchWorkflowTaskViewList 获取工单任务视图列表数据(永远排除未接收的工单)
func fetchWorkflowTaskViewList(ctx context.Context, workflowRepo *models.WorkflowDao, employeeIDs []int64, req *workflow.ListWorkflowTaskViewReq) ([]*models.WorkflowTaskAllInOne, int64, error) {
	// 1. 构建过滤条件
	filter := &models.WorkflowTaskViewListFilter{
		WorkflowNoOrName:       req.GetWorkflowNoOrName(),
		OrderNo:                req.GetOrderNo(),
		CustomerId:             req.GetCustomerId(),
		ServiceName:            req.GetServiceName(),
		DispatcherId:           req.GetDispatcherId(),
		DataStr:                req.GetDateString(),
		DataStart:              req.GetDateStart(),
		DataEnd:                req.GetDateEnd(),
		OrderBy:                req.GetOrderBy(),
		PageNum:                req.GetPageNum(),
		PageSize:               req.GetPageSize(),
		WorkflowStatus:         req.GetWorkflowStatus(),
		WorkflowTaskViewStatus: req.GetWorkflowTaskViewStatus(),
		BusinessType:           req.GetBusinessType(),
		TemplateIds:            req.GetTemplateIds(),
		CurrentNodeName:        req.GetCurrentNodeName(),
		BusinessInfo:           req.GetBusinessInfo(),
		AssigneeId:             req.GetAssigneeId(),
		InnerStatus:            req.GetInnerStatus(),
	}

	applicationFilter := &models.WorkflowApplicationFilter{
		SchoolId:           req.GetSchoolId(),
		MajorId:            req.GetMajorId(),
		CourseLevels:       req.GetCourseLevel(),
		Channels:           req.GetChannel(),
		Locations:          req.GetLocations(),
		ExpectedEntryStart: req.GetExpectedEntryStart(),
		ExpectedEntryEnd:   req.GetExpectedEntryEnd(),
	}

	guideFilter := &models.WorkflowInfoGuidanceFilter{
		DemandTypeList: req.GetDemandType(),
		GuideTeacher:   req.GetGuideTeacher(),
	}

	// 2. 获取工单列表
	workflows, total, err := workflowRepo.ListWorkflowTaskViewV2(ctx, employeeIDs, filter, applicationFilter, guideFilter)
	if err != nil {
		logger.CtxErrorf(ctx, "获取工单任务视图列表失败: %v", err)
		return nil, 0, fmt.Errorf("获取工单任务视图列表失败: %w", err)
	}
	logger.CtxDebugf(ctx, "获取工单任务视图列表成功, 总数: %d", total)
	return workflows, total, nil
}

func GetCustomerWorkflowList(ctx context.Context, req *workflow.GetCustomerWorkflowListReq) (resp *workflow.GetCustomerWorkflowListRsp, err error) {
	resp = &workflow.GetCustomerWorkflowListRsp{}

	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "获取工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}

	return workflowService.getCustomerWorkflowList(ctx, req)
}

func (w *WorkflowService) getCustomerWorkflowList(ctx context.Context, req *workflow.GetCustomerWorkflowListReq) (resp *workflow.GetCustomerWorkflowListRsp, err error) {
	resp = new(workflow.GetCustomerWorkflowListRsp)
	customerId := req.GetCustomerId()
	status := req.GetStatus()
	newAuth := ctxmeta.MustGetAuth(ctx)
	if newAuth.IsCustomer() {
		customerId = newAuth.CustomerId()
	}

	workflowList, err := w.workflowRepo.GetCustomerWorkflowList(ctx, customerId, status)
	if err != nil {
		logger.CtxErrorf(ctx, "获取客户工单列表失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowNotFound)
		return resp, nil
	}

	return buildGetCustomerWorkflowListRsp(ctx, workflowList, w), nil
}

func buildGetCustomerWorkflowListRsp(ctx context.Context, workflowList []*model.Workflow, w *WorkflowService) (resp *workflow.GetCustomerWorkflowListRsp) {
	resp = new(workflow.GetCustomerWorkflowListRsp)
	if len(workflowList) == 0 {
		resp.CustomerWorkflowList = make([]*workflow.CustomerWorkflowList, 0)
		resp.Total = 0
		return resp
	}

	// 过滤有效的工作流（模板ID不为0）
	validWorkflows := make([]*model.Workflow, 0, len(workflowList))
	workflowIds := make([]int64, 0, len(workflowList))
	for _, wf := range workflowList {
		if wf.WorkflowTemplateID != 0 {
			validWorkflows = append(validWorkflows, wf)
			workflowIds = append(workflowIds, wf.ID)
		}
	}

	// 使用BatchGet方法批量获取所有节点数据
	allNodesMap, err := w.workflowRepo.BatchGetNodesWithTasks(ctx, workflowIds)
	if err != nil {
		logger.CtxErrorf(ctx, "获取节点数据失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp
	}

	// 使用BatchGetLatestNodes方法批量获取最新节点数据
	latestNodesMap, err := w.workflowRepo.BatchGetLatestNodes(ctx, workflowIds)
	if err != nil {
		logger.CtxErrorf(ctx, "获取最新节点数据失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp
	}

	// 构建响应列表
	customerWorkflowList := make([]*workflow.CustomerWorkflowList, 0, len(validWorkflows))
	for _, wf := range validWorkflows {
		customerWorkflow := &workflow.CustomerWorkflowList{
			WorkflowTemplateId: wf.WorkflowTemplateID,
			WorkflowId:         wf.ID,
			WorkflowName:       wf.WorkflowName,
		}

		// 处理所有节点信息
		if allNodes, ok := allNodesMap[wf.ID]; ok {
			customerWorkflow.AllNodes = make([]*workflow.CustomerWorkflowList_NodeInfo, 0, len(allNodes))
			for _, node := range allNodes {
				customerWorkflow.AllNodes = append(customerWorkflow.AllNodes, buildCustomerNode(node.WorkflowNode))
			}
		} else {
			customerWorkflow.AllNodes = make([]*workflow.CustomerWorkflowList_NodeInfo, 0)
		}

		// 处理当前最新节点信息
		if latestNodes, ok := latestNodesMap[wf.ID]; ok {
			customerWorkflow.CurrentNodes = make([]*workflow.CustomerWorkflowList_NodeNameInfo, 0, len(latestNodes))
			for _, node := range latestNodes {
				nodeInfo := &workflow.CustomerWorkflowList_NodeNameInfo{
					NodeNameZh: node.WorkflowNode.ChineseName,
					NodeNameEn: node.WorkflowNode.Name,
					NodeStatus: workflow.WorkflowNodeStatus(node.WorkflowNode.Status),
				}
				customerWorkflow.CurrentNodes = append(customerWorkflow.CurrentNodes, nodeInfo)
			}
		} else {
			customerWorkflow.CurrentNodes = make([]*workflow.CustomerWorkflowList_NodeNameInfo, 0)
		}

		customerWorkflowList = append(customerWorkflowList, customerWorkflow)
	}

	resp.CustomerWorkflowList = customerWorkflowList
	resp.Total = int64(len(customerWorkflowList))
	return resp
}

func buildCustomerNode(modelNode *model.WorkflowNode) (node *workflow.CustomerWorkflowList_NodeInfo) {
	node = &workflow.CustomerWorkflowList_NodeInfo{}
	node.Node = &workflow.CustomerWorkflowList_NodeNameInfo{
		NodeNameZh: modelNode.ChineseName,
		NodeNameEn: modelNode.Name,
		NodeStatus: workflow.WorkflowNodeStatus(modelNode.Status),
	}
	previewNodesStr := modelNode.Preview
	node.PreviousNodes = str2Slice(previewNodesStr, ",")

	nextNodesStr := modelNode.Next
	node.NextNodes = str2Slice(nextNodesStr, ",")
	return node
}

func GetCustomerWorkflowListNewest(ctx context.Context, req *workflow.GetCustomerWorkflowListNewestReq) (resp *workflow.GetCustomerWorkflowListNewestRsp, err error) {
	resp = &workflow.GetCustomerWorkflowListNewestRsp{}

	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "获取工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}

	return workflowService.getCustomerWorkflowListNewest(ctx, req)
}

func (w *WorkflowService) getCustomerWorkflowListNewest(ctx context.Context, req *workflow.GetCustomerWorkflowListNewestReq) (resp *workflow.GetCustomerWorkflowListNewestRsp, err error) {
	resp = new(workflow.GetCustomerWorkflowListNewestRsp)
	customerIds := RemoveDuplicates(req.GetCustomerIds(), true)
	if len(customerIds) == 0 {
		logger.CtxErrorf(ctx, "请求参数错误: customer IDs必须为正整数")
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}

	// 使用批量查询替代并发查询
	customerWorkflows, err := w.workflowRepo.BatchGetCustomerNewestWorkflow(ctx, customerIds)
	if err != nil {
		logger.CtxErrorf(ctx, "获取客户最新工单列表失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	resp.CustomerNewestWorkflowInfo = customerWorkflows
	return resp, nil
}
