package services

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"uofferv2/kitex_gen/constants"
	"uofferv2/kitex_gen/server/cmd/workflow"
	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/workflow/internal/models"
)

const (
	timestampFormat = "20060102150405"
	suffixLength    = 6
	maxCounter      = 999999
)

var (
	mutex      sync.Mutex
	lastSecond int64
	counter    int
	rnd        *rand.Rand
	once       sync.Once
)

func initRandom() {
	once.Do(func() {
		rnd = rand.New(rand.NewSource(time.Now().UnixNano()))
	})
}

// 生成工单号，规则年月日时分秒+6位随机不重复数字
func generateWorkflowNo() string {
	initRandom()

	now := time.Now()
	currentSecond := now.Unix()
	timestamp := now.Format(timestampFormat)

	mutex.Lock()
	defer mutex.Unlock()

	if lastSecond != currentSecond {
		lastSecond = currentSecond
		counter = rnd.Intn(maxCounter)
	}

	suffix := fmt.Sprintf("%0*d", suffixLength, counter)
	counter = (counter + 1) % maxCounter

	return timestamp + suffix
}

// generateWorkflowName 生成工单名称 {规格}-{商品}-{服务项目}
func generateWorkflowName(serviceName, goodsName, goodsSpec string) string {
	return fmt.Sprintf("%s-%s-%s", goodsSpec, goodsName, serviceName)
}

// getUnixTime 辅助函数：将时间指针转换为 Unix 时间戳(毫秒)
func getUnixTime(t any) int64 {
	switch v := t.(type) {
	case time.Time:
		return v.UnixMilli()
	case *time.Time:
		if v != nil {
			return v.UnixMilli()
		}
	}
	return 0
}

// str2Slice 方法将 node Next/Previous 字段转换为 []string
func str2Slice(nodeStr, sep string) []string {
	if nodeStr == "" {
		return []string{}
	}
	slices := strings.Split(nodeStr, sep)
	for i, s := range slices {
		slices[i] = strings.TrimSpace(s)
	}
	return slices
}

// RemoveDuplicates 数组切片去重，并根据参数决定是否去掉0
func RemoveDuplicates(arr []int64, removeZero bool) []int64 {
	m := make(map[int64]bool)
	result := make([]int64, 0, len(arr))
	for _, v := range arr {
		if removeZero && v == 0 {
			continue
		}
		if _, ok := m[v]; !ok {
			m[v] = true
			result = append(result, v)
		}
	}
	return result
}

// intersection 求两个切片的交集
func intersection(slice1, slice2 []int64) []int64 {
	// 创建一个 map 来存储第一个切片的元素
	elemMap := make(map[int64]struct{})
	for _, v := range slice1 {
		elemMap[v] = struct{}{}
	}

	// 遍历第二个切片，检查哪些元素存在于 map 中
	var intersect []int64
	for _, v := range slice2 {
		if _, exists := elemMap[v]; exists {
			intersect = append(intersect, v)
		}
	}

	return RemoveDuplicates(intersect, true)
}

// CheckStringLength 获取字符串长度，支持中英文混合
func CheckStringLength(str string, maxLength int) bool {
	if str == "" {
		return true
	}

	// 使用 utf8.RuneCountInString 来计算字符串的实际字符数
	// 这会正确处理中文、英文等 Unicode 字符
	length := utf8.RuneCountInString(str)
	return length <= maxLength
}

func CheckBool(s constants.YesNo) bool {
	switch s {
	case constants.YesNo_Yes:
		return true
	case constants.YesNo_No:
		return false
	default:
		return false
	}
}

func CheckYesNo(s bool) constants.YesNo {
	if s {
		return constants.YesNo_Yes
	}
	return constants.YesNo_No
}

func coverEntity2Json(ctx context.Context, v any) string {
	// 将结构体转换为 JSON 字符串
	jsonData, err := json.Marshal(v)
	if err != nil {
		logger.CtxErrorf(ctx, "failed to marshal UrlEntity to JSON: %v", err)
		return "[]"
	}
	if len(jsonData) == 0 {
		return "[]"
	}
	return string(jsonData)
}

// parseUrlEntity 解析 UrlEntity
// func parseUrlEntity(src string) *workflow.UrlEntity {
// 	urlEntity := &workflow.UrlEntity{
// 		Url:            "",
// 		Name:           "",
// 		SubmissionTime: 0,
// 		ThumbnailUrl:   "",
// 	}
// 	if src == "" {
// 		return urlEntity
// 	}
// 	if err := json.Unmarshal([]byte(src), &urlEntity); err != nil {
// 		logger.Errorf("failed to unmarshal UrlEntity from JSON: %v", err)
// 		return nil
// 	}
// 	return urlEntity
// }

func parseGuideGroup(src string) []*workflow.GuideInfo {
	result := make([]*workflow.GuideInfo, 10)
	if src == "" {
		return result
	}
	if err := json.Unmarshal([]byte(src), &result); err != nil {
		logger.Errorf("failed to unmarshal UrlEntity from JSON: %v", err)
		return nil
	}
	return result
}

func parseLanguageScore(src string) *workflow.WorkflowLanguageScore {
	result := &workflow.WorkflowLanguageScore{
		Total:     "",
		Listening: "",
		Speaking:  "",
		Reading:   "",
		Writing:   "",
	}
	if src == "" {
		return result
	}
	if err := json.Unmarshal([]byte(src), &result); err != nil {
		logger.Errorf("failed to unmarshal UrlEntity from JSON: %v", err)
		return nil
	}
	return result
}

func parseUrlEntities(src string) []*workflow.UrlEntity {
	urlEntities := make([]*workflow.UrlEntity, 0)
	if src == "" {
		return urlEntities
	}

	if err := json.Unmarshal([]byte(src), &urlEntities); err != nil {
		logger.Errorf("failed to unmarshal UrlEntity from JSON: %v", err)
		return nil
	}
	for _, urlEntity := range urlEntities {
		if urlEntity.Url == "" {
			logger.Errorf("UrlEntity.Url is empty")
			return nil
		}
	}
	return urlEntities
}

func parseGuideInfo(src string) []*workflow.GuideInfo {
	result := make([]*workflow.GuideInfo, 0)
	if src == "" {
		return result
	}
	if err := json.Unmarshal([]byte(src), &result); err != nil {
		logger.Errorf("failed to unmarshal UrlEntity from JSON: %v", err)
		return nil
	}
	return result
}

func model2NewWorkflowEntity(src *model.Workflow) *workflow.NewWorkflowEntity {
	if src == nil {
		return nil
	}
	return &workflow.NewWorkflowEntity{
		Id:                   src.ID,
		WorkflowNo:           src.WorkflowNo,
		WorkflowName:         src.WorkflowName,
		WorkflowTemplateId:   src.WorkflowTemplateID,
		WorkflowTemplateName: src.WorkflowTemplateName,
		Status:               src.Status,
		InnerStatus:          src.InnerStatus,
		BusinessType:         src.BusinessType,
		BusinessName:         src.BusinessName,
		OrderId:              src.OrderID,
		ProductId:            src.ProductID,
		CreatedBy:            src.CreatedBy,
		CreatedRoleSnapshot:  src.CreatedRoleSnapshot,
		CreatedDeptSnapshot:  src.CreatedDeptSnapshot,
		UpdatedBy:            src.UpdatedBy,
		UpdatedRoleSnapshot:  src.UpdatedRoleSnapshot,
		UpdatedDeptSnapshot:  src.UpdatedDeptSnapshot,
		Processors:           src.Processors,
		CustomerId:           src.CustomerID,
		CustomerProfile:      src.CustomerProfile,
		CreatedAt:            getUnixTime(src.CreatedAt),
		UpdatedAt:            getUnixTime(src.UpdatedAt),
		DeletedAt:            getUnixTime(src.DeletedAt),
		DispatchedAt:         getUnixTime(src.DispatchedAt),
		ReceivedAt:           getUnixTime(src.ReceivedAt),
		PausedAt:             getUnixTime(src.PausedAt),
		TerminatedAt:         getUnixTime(src.TerminatedAt),
		CompletedAt:          getUnixTime(src.CompletedAt),
		DueAt:                getUnixTime(src.DueAt),
		MessageBoardId:       src.MessageBoardID,
		QqImGroupId:          src.QqImGroupID,
		Remark:               src.Remark,
		Version:              src.Version,
		OrderNo:              src.OrderNo,
		ProductName:          src.ProductName,
		ServiceName:          src.ServiceName,
		GoodsType:            src.GoodsType,
		GoodsSpecIndex:       src.GoodsSpecIndex,
		SpecId:               src.SpecID,
	}
}

func model2WorkflowApplicationEntity(src *model.WorkflowApplication) *workflow.NewWorkflowApplicationEntity {
	if src == nil {
		return nil
	}
	return &workflow.NewWorkflowApplicationEntity{
		Id:                           src.ID,
		WorkflowId:                   src.WorkflowID,
		SelectionUniversityId:        src.SelectionUniversityID,
		SelectionUniversityZh:        src.SelectionUniversityZh,
		SelectionUniversityEn:        src.SelectionUniversityEn,
		SelectionMajorId:             src.SelectionMajorID,
		SelectionMajorZh:             src.SelectionMajorZh,
		SelectionMajorEn:             src.SelectionMajorEn,
		SelectionCourseLevel:         src.SelectionCourseLevel,
		SelectionExpectedEntry:       getUnixTime(src.SelectionExpectedEntry),
		FundBindType:                 src.FundBindType,
		FundId:                       src.FundID,
		FundFreeReason:               src.FundFreeReason,
		SelectionChannel:             src.SelectionChannel,
		SelectionMajorLink:           src.SelectionMajorLink,
		SelectionOfferAgreementLink:  src.SelectionOfferAgreementLink,
		DocumentsCvLinks:             src.DocumentsCvLinks,
		DocumentsRlLinks:             src.DocumentsRlLinks,
		DocumentsPsLinks:             src.DocumentsPsLinks,
		MaterialsPackageLinks:        src.MaterialsPackageLinks,
		ApplicationNo:                src.ApplicationNo,
		ApplicationReceiptLink:       src.ApplicationReceiptLink,
		ApplicationInfo:              src.ApplicationInfo,
		ApplicationTrackingInfo:      src.ApplicationTrackingInfo,
		ApplicationSecurityRecord:    src.ApplicationSecurityRecord,
		OfferFileLinks:               src.OfferFileLinks,
		OfferConfirmationDeadline:    getUnixTime(src.OfferConfirmationDeadline),
		OfferDepositRequired:         src.OfferDepositRequired,
		OfferDepositAmount:           src.OfferDepositAmount,
		OfferDepositCurrency:         src.OfferDepositCurrency,
		OfferDepositDeadline:         getUnixTime(src.OfferDepositDeadline),
		OfferCasRequired:             src.OfferCasRequired,
		OfferCasDepositAmount:        src.OfferCasDepositAmount,
		OfferCasDepositCurrency:      src.OfferCasDepositCurrency,
		OfferCasDeadline:             getUnixTime(src.OfferCasDeadline),
		OfferDepositLink:             src.OfferDepositLink,
		OfferAccommodationLink:       src.OfferAccommodationLink,
		OfferAcademicRequirement:     src.OfferAcademicRequirement,
		OfferAcademicScore:           src.OfferAcademicScore,
		OfferLanguageTestType:        src.OfferLanguageTestType,
		OfferLanguageTestScore:       src.OfferLanguageTestScore,
		OfferReferenceRequirement:    src.OfferReferenceRequirement,
		OfferRemark:                  src.OfferRemark,
		RejectionFileLinks:           src.RejectionFileLinks,
		VisaRequired:                 src.VisaRequired,
		VisaFileLinks:                src.VisaFileLinks,
		AdmissionConfirmationLink:    src.AdmissionConfirmationLink,
		CreatedAt:                    getUnixTime(src.CreatedAt),
		UpdatedAt:                    getUnixTime(src.UpdatedAt),
		DeletedAt:                    getUnixTime(src.DeletedAt),
		ClosenessMaterialLinks:       src.ClosenessMaterialLinks,
		ClosenessLetterLinks:         src.ClosenessLetterLinks,
		ClosenessPolishedLetterLinks: src.ClosenessPolishedLetterLinks,
		GuideInfoLinks:               src.GuideInfoLinks,
		SelectionLocationId:          src.SelectionLocationID,
		SelectionLocationZh:          src.SelectionLocationZh,
		SelectionLocationEn:          src.SelectionLocationEn,
		FundCurrencyCode:             src.FundCurrencyCode,
		FundCurrencyValue:            src.FundCurrencyValue,
		RefundIdsJson:                src.RefundIdsJSON,
	}
}

// model2WorkflowAttachmentEntity 将 model.WorkflowAttachment 转换为 workflow.NewWorkflowAttachmentEntity
// func model2WorkflowAttachmentEntity(src *model.WorkflowAttachment) *workflow.NewWorkflowAttachmentEntity {
// 	if src == nil {
// 		return nil
// 	}
// 	return &workflow.NewWorkflowAttachmentEntity{
// 		Id:                                src.ID,
// 		OrderId:                           src.OrderID,
// 		CustomerId:                        src.CustomerID,
// 		BusinessType:                      src.BusinessType,
// 		UofferAppInfo:                     src.UofferAppInfo,
// 		PastExperience:                    src.PastExperience,
// 		SpecialApplication:                src.SpecialApplication,
// 		IsDesignatedInstitution:           src.IsDesignatedInstitution,
// 		InstitutionName:                   src.InstitutionName,
// 		IsCommitmentSuccessRate:           src.IsCommitmentSuccessRate,
// 		CommitmentFileLinks:               src.CommitmentFileLinks,
// 		IsGuaranteedCollegeMajor:          src.IsGuaranteedCollegeMajor,
// 		IsNativeDocument:                  src.IsNativeDocument,
// 		IsNativeCommunication:             src.IsNativeCommunication,
// 		IsDocumentBackground:              src.IsDocumentBackground,
// 		IsCommissionerBackground:          src.IsCommissionerBackground,
// 		CvFileLinks:                       src.CvFileLinks,
// 		UgApplicationLevel:                src.UgApplicationLevel,
// 		UgHomeReturnPermit:                src.UgHomeReturnPermit,
// 		UgExtraFreeService:                src.UgExtraFreeService,
// 		UgCommissionerInfo:                src.UgCommissionerInfo,
// 		UgThirdPartyInfo:                  src.UgThirdPartyInfo,
// 		UgGuaranteedCollegeMajorFileLinks: src.UgGuaranteedCollegeMajorFileLinks,
// 		UgNativeDocumentFileLinks:         src.UgNativeDocumentFileLinks,
// 		UgNativeCommunicationFileLinks:    src.UgNativeCommunicationFileLinks,
// 		UgDocumentBackgroundFileLinks:     src.UgDocumentBackgroundFileLinks,
// 		UgCommissionerBackgroundFileLinks: src.UgCommissionerBackgroundFileLinks,
// 		UgIsNoLanguage:                    src.UgIsNoLanguage,
// 		UgNoLanguageFileLinks:             src.UgNoLanguageFileLinks,
// 		PmApplicationLevel:                src.PmApplicationLevel,
// 		PmClosenessLetter:                 src.PmClosenessLetter,
// 		PmGuaranteedCollegeName:           src.PmGuaranteedCollegeName,
// 		PmDocumentBackground:              src.PmDocumentBackground,
// 		PmCommissionerBackground:          src.PmCommissionerBackground,
// 		PmIsIntroduceClosenessLetter:      src.PmIsIntroduceClosenessLetter,
// 		PmIsPrTutorship:                   src.PmIsPrTutorship,
// 		PmIsSelectTeacher:                 src.PmIsSelectTeacher,
// 		PmTutoringStartedAt:               getUnixTime(src.PmTutoringStartedAt),
// 		CoPrSubmit:                        src.CoPrSubmit,
// 		CoPrWord:                          src.CoPrWord,
// 		CoRefStyle:                        src.CoRefStyle,
// 		CoIsSelectGuide:                   src.CoIsSelectGuide,
// 		SupplementsInfo:                   src.SupplementsInfo,
// 		SupplementsFileLinks:              src.SupplementsFileLinks,
// 		CreatedAt:                         getUnixTime(src.CreatedAt),
// 		UpdatedAt:                         getUnixTime(src.UpdatedAt),
// 		DeletedAt:                         getUnixTime(src.DeletedAt),
// 		ProcessorId:                       src.ProcessorID,
// 	}
// }

func model2WorkflowGuidanceEntity(src *model.WorkflowInfoGuidance) *workflow.NewWorkflowGuideEntity {
	if src == nil {
		return nil
	}
	return &workflow.NewWorkflowGuideEntity{
		Id:                       src.ID,
		WorkflowId:               src.WorkflowID,
		DemandType:               src.DemandType,
		ProfessionalType:         src.ProfessionalType,
		Notes:                    src.Notes,
		Teacher:                  src.Teacher,
		UserId:                   src.UserID,
		MatchUserName:            src.MatchUserName,
		TeacherPriceVal:          src.TeacherPriceVal,
		TeacherPriceUnit:         src.TeacherPriceUnit,
		TeacherCost:              src.TeacherCost,
		InitLessonDate:           src.InitLessonDate,
		InitLessonTime:           src.InitLessonTime,
		LessonPlanFileUrl:        src.LessonPlanFileURL,
		TopicLessonDate:          src.TopicLessonDate,
		TopicLessonTime:          src.TopicLessonTime,
		TopicReadyListFileUrl:    src.TopicReadyListFileURL,
		ExplainLessonDate:        src.ExplainLessonDate,
		ExplainLessonTime:        src.ExplainLessonTime,
		LessonInfoJson:           src.LessonInfoJSON,
		ManuscriptOutlineFileUrl: src.ManuscriptOutlineFileURL,
		FirstDraftLessonDate:     src.FirstDraftLessonDate,
		FirstDraftLessonTime:     src.FirstDraftLessonTime,
		FirstDraftFileUrl:        src.FirstDraftFileURL,
		SecondDraftLessonDate:    src.SecondDraftLessonDate,
		SecondDraftLessonTime:    src.SecondDraftLessonTime,
		SecondDraftFileUrl:       src.SecondDraftFileURL,
		RefinementDraftFileUrl:   src.RefinementDraftFileURL,
		FinalDraftFileUrl:        src.FinalDraftFileURL,
		ResultMaterialFileUrl:    src.ResultMaterialFileURL,
		ResultFirstDraftFileUrl:  src.ResultFirstDraftFileURL,
		ResultFinalDraftFileUrl:  src.ResultFinalDraftFileURL,
		ResultOfferNoticeFileUrl: src.ResultOfferNoticeFileURL,
		ResultPublishUrl:         src.ResultPublishURL,
		CreatedAt:                getUnixTime(src.CreatedAt),
		UpdatedAt:                getUnixTime(src.UpdatedAt),
		DeletedAt:                getUnixTime(src.DeletedAt),
	}
}

func model2WorkflowSingleItemEntity(src *model.WorkflowSingleItem) *workflow.NewWorkflowSingleEntity {
	if src == nil {
		return nil
	}
	return &workflow.NewWorkflowSingleEntity{
		Id:                    src.ID,
		WorkflowId:            src.WorkflowID,
		SingleRequirementInfo: src.SingleRequirementInfo,
		SingleFileLinks:       src.SingleFileLinks,
		CreatedAt:             getUnixTime(src.CreatedAt),
		UpdatedAt:             getUnixTime(src.UpdatedAt),
		DeletedAt:             getUnixTime(src.DeletedAt),
		DispatchRequired:      src.DispatchRequired,
	}
}

func modelList2WorkflowNodeEntityList(src []*models.WorkflowNodeWithTasks) []*workflow.NewWorkflowNodeEntity {
	if src == nil {
		return nil
	}
	var result []*workflow.NewWorkflowNodeEntity
	for _, item := range src {
		result = append(result, model2WorkflowNodeWithTaskEntity(item))
	}
	return result
}

func model2WorkflowNodeEntity(src *model.WorkflowNode) *workflow.NewWorkflowNodeEntity {
	if src == nil {
		return nil
	}
	return &workflow.NewWorkflowNodeEntity{
		Id:                       src.ID,
		Name:                     src.Name,
		ChineseName:              src.ChineseName,
		Status:                   src.Status,
		Type:                     src.Type,
		DispatchedBy:             src.DispatchedBy,
		DispatchedByRoleSnapshot: src.DispatchedByRoleSnapshot,
		DispatchedByDeptSnapshot: src.DispatchedByDeptSnapshot,
		ProcessedBy:              src.ProcessedBy,
		ProcessedByRoleSnapshot:  src.ProcessedByRoleSnapshot,
		ProcessedByDeptSnapshot:  src.ProcessedByDeptSnapshot,
		CreatedAt:                getUnixTime(src.CreatedAt),
		UpdatedAt:                getUnixTime(src.UpdatedAt),
		DispatchedAt:             getUnixTime(src.DispatchedAt),
		StartedAt:                getUnixTime(src.StartedAt),
		PausedAt:                 getUnixTime(src.PausedAt),
		DeletedAt:                getUnixTime(src.DeletedAt),
		CompletedAt:              getUnixTime(src.CompletedAt),
		TerminatedAt:             getUnixTime(src.TerminatedAt),
		DueAt:                    getUnixTime(src.DueAt),
		AbnormalReason:           src.AbnormalReason,
		Attachments:              src.Attachments,
		WorkflowId:               src.WorkflowID,
		Next:                     src.Next,
		Preview:                  src.Preview,
		SyncNode:                 src.SyncNode,
		AllowRoleList:            src.AllowRoleList,
	}
}

func model2WorkflowNodeWithTaskEntity(src *models.WorkflowNodeWithTasks) *workflow.NewWorkflowNodeEntity {
	if src == nil {
		return nil
	}
	return &workflow.NewWorkflowNodeEntity{
		Id:                       src.ID,
		Name:                     src.Name,
		ChineseName:              src.ChineseName,
		Status:                   src.Status,
		Type:                     src.Type,
		DispatchedBy:             src.DispatchedBy,
		DispatchedByRoleSnapshot: src.DispatchedByRoleSnapshot,
		DispatchedByDeptSnapshot: src.DispatchedByDeptSnapshot,
		ProcessedBy:              src.ProcessedBy,
		ProcessedByRoleSnapshot:  src.ProcessedByRoleSnapshot,
		ProcessedByDeptSnapshot:  src.ProcessedByDeptSnapshot,
		CreatedAt:                getUnixTime(src.CreatedAt),
		UpdatedAt:                getUnixTime(src.UpdatedAt),
		DispatchedAt:             getUnixTime(src.DispatchedAt),
		StartedAt:                getUnixTime(src.StartedAt),
		PausedAt:                 getUnixTime(src.PausedAt),
		DeletedAt:                getUnixTime(src.DeletedAt),
		CompletedAt:              getUnixTime(src.CompletedAt),
		TerminatedAt:             getUnixTime(src.TerminatedAt),
		DueAt:                    getUnixTime(src.DueAt),
		AbnormalReason:           src.AbnormalReason,
		Attachments:              src.Attachments,
		WorkflowId:               src.WorkflowID,
		Next:                     src.Next,
		Preview:                  src.Preview,
		SyncNode:                 src.SyncNode,
		AllowRoleList:            src.AllowRoleList,
		Tasks:                    modelList2WorkflowNodeTaskEntity(src.Tasks),
		Branch:                   src.Branch,
		Level:                    src.Level,
	}
}

func model2WorkflowNodeTaskEntity(src *model.WorkflowNodeTask) *workflow.NewWorkflowTaskEntity {
	if src == nil {
		return nil
	}
	return &workflow.NewWorkflowTaskEntity{
		Id:             src.ID,
		Name:           src.Name,
		Status:         src.Status,
		CreatedAt:      getUnixTime(src.CreatedAt),
		StartedAt:      getUnixTime(src.StartedAt),
		UpdatedAt:      getUnixTime(src.UpdatedAt),
		DeletedAt:      getUnixTime(src.DeletedAt),
		CompletedAt:    getUnixTime(src.CompletedAt),
		DueAt:          getUnixTime(src.DueAt),
		WorkflowNodeId: src.WorkflowNodeID,
	}
}

func modelList2WorkflowNodeTaskEntity(src []*model.WorkflowNodeTask) []*workflow.NewWorkflowTaskEntity {
	if src == nil {
		return nil
	}
	var result []*workflow.NewWorkflowTaskEntity
	for _, v := range src {
		result = append(result, model2WorkflowNodeTaskEntity(v))
	}
	return result
}

func modelList2WorkflowEntityList(workflowList []*models.WorkflowAllInOne) []*workflow.WorkflowInfoEntity {
	workflowInfoEntity := make([]*workflow.WorkflowInfoEntity, len(workflowList))
	for i, wf := range workflowList {
		workflowInfoEntity[i] = &workflow.WorkflowInfoEntity{}
		workflowInfoEntity[i].WorkflowInfo = model2NewWorkflowEntity(wf.Workflow)
		workflowInfoEntity[i].ApplicationInfo = model2WorkflowApplicationEntity(wf.Application)
		workflowInfoEntity[i].GuideInfo = model2WorkflowGuidanceEntity(wf.Guidance)
		workflowInfoEntity[i].SingleInfo = model2WorkflowSingleItemEntity(wf.SingleItem)
		workflowInfoEntity[i].NodesInfo = modelList2WorkflowNodeEntityList(wf.Nodes)
		workflowInfoEntity[i].StepsInfo = modelList2WorkflowNodeEntityList(wf.Steps)
	}
	return workflowInfoEntity
}

func buildWorkflowInfo(wa *models.WorkflowAllInOne, steps []*models.WorkflowNodeWithTasks) *workflow.WorkflowDetailRsp {
	resp := &workflow.WorkflowDetailRsp{}
	resp.WorkflowInfo = model2NewWorkflowEntity(wa.Workflow)
	resp.ApplicationInfo = model2WorkflowApplicationEntity(wa.Application)
	resp.GuideInfo = model2WorkflowGuidanceEntity(wa.Guidance)
	resp.SingleInfo = model2WorkflowSingleItemEntity(wa.SingleItem)
	resp.NodesInfo = modelList2WorkflowNodeEntityList(wa.Nodes)
	resp.StepsInfo = modelList2WorkflowNodeEntityList(steps)
	logger.Infof("buildWorkflowInfo success: %+v", resp)
	return resp
}
