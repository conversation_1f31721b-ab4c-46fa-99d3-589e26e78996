package services

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"strings"
	"sync"
	"time"

	"uofferv2/kitex_gen/constants"
	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/kitex_gen/server/cmd/workflow"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/ctxmeta"
	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/workflow/internal/global"
	"uofferv2/server/cmd/workflow/internal/models"
	"uofferv2/server/cmd/workflow/internal/services/rpcs"
)

func GetWorkflowOrderGoodsPayment(ctx context.Context, req *workflow.GetWorkflowOrderGoodsPaymentReq) (resp *workflow.GetWorkflowOrderGoodsPaymentRsp, err error) {
	resp = &workflow.GetWorkflowOrderGoodsPaymentRsp{}
	if err = validateRequest(ctx, req); err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}
	// 2. 获取服务实例
	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to get workflow service: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}

	return workflowService.getWorkflowOrderGoodsPayment(ctx, req)
}

func RejectWorkflowDispatch(ctx context.Context, req *workflow.RejectWorkflowDispatchReq) (resp *workflow.RejectWorkflowDispatchRsp, err error) {
	resp = &workflow.RejectWorkflowDispatchRsp{}
	// 1. 验证请求
	if err = validateRequest(ctx, req); err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}

	tx := global.DB.Begin()
	if tx.Error != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logger.CtxErrorf(ctx, "create workflow panic occurred: %v", r)
		} else if tx.Error != nil || (resp != nil && resp.GetBase() != nil && resp.GetBase().GetCode() != 0) {
			tx.Rollback()
			logger.CtxErrorf(ctx, "Failed to commit create workflow transaction: %v, err: %v", tx.Error, err)
		} else {
			tx.Commit()
			logger.Debugf("commit create workflow transaction successfully")
		}
	}()

	// 使用事务创建工作流服务实例
	workflowServiceWithTx, err := InitializeWorkflowServiceWithTx(tx)
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to initialize workflow service with tx: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}
	resp, err = workflowServiceWithTx.rejectWorkflowDispatch(ctx, req)
	return resp, err
}

func AcceptWorkflowDispatch(ctx context.Context, req *workflow.AcceptWorkflowDispatchReq) (resp *workflow.AcceptWorkflowDispatchRsp, err error) {
	// 1. 验证请求
	if err = validateRequest(ctx, req); err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}

	tx := global.DB.Begin()
	if tx.Error != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logger.CtxErrorf(ctx, "create workflow panic occurred: %v", r)
		} else if tx.Error != nil || (resp != nil && resp.GetBase() != nil && resp.GetBase().GetCode() != 0) {
			tx.Rollback()
			logger.CtxErrorf(ctx, "Failed to commit create workflow transaction: %v, err: %v", tx.Error, err)
		} else {
			tx.Commit()
			logger.Debugf("commit create workflow transaction successfully")
		}
	}()

	// 使用事务创建工作流服务实例
	workflowServiceWithTx, err := InitializeWorkflowServiceWithTx(tx)
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to initialize workflow service with tx: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}
	resp, err = workflowServiceWithTx.acceptWorkflowDispatch(ctx, req)
	return resp, err
}

func GetWorkflowFollow(ctx context.Context, req *workflow.GetWorkflowFollowReq) (resp *workflow.GetWorkflowFollowRsp, err error) {
	resp = &workflow.GetWorkflowFollowRsp{}
	if err = validateRequest(ctx, req); err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}
	// 2. 获取服务实例
	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to get workflow service: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}

	return workflowService.getWorkflowFollow(ctx, req)
}

func UpdateWorkflowFollow(ctx context.Context, req *workflow.UpdateWorkflowFollowReq) (resp *workflow.UpdateWorkflowFollowRsp, err error) {
	resp = &workflow.UpdateWorkflowFollowRsp{}
	if err = validateRequest(ctx, req); err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}
	// 2. 获取服务实例
	tx := global.DB.Begin()
	if tx.Error != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logger.CtxErrorf(ctx, "UpdateWorkflowFollow panic occurred: %v", r)
		} else if tx.Error != nil || (resp != nil && resp.GetBase() != nil && resp.GetBase().GetCode() != 0) {
			tx.Rollback()
			logger.CtxErrorf(ctx, "Failed to commit UpdateWorkflowFollow workflow transaction: %v, err: %v", tx.Error, err)
		} else {
			tx.Commit()
			logger.Debugf("commit create workflow transaction successfully")
		}
	}()

	// 使用事务创建工作流服务实例
	workflowServiceWithTx, err := InitializeWorkflowServiceWithTx(tx)
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to initialize workflow service with tx: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}
	return workflowServiceWithTx.updateWorkflowFollow(ctx, req)
}

func UpdateWorkflowNodeTaskStatus(ctx context.Context, req *workflow.UpdateWorkflowNodeTaskStatusReq) (resp *workflow.UpdateWorkflowNodeTaskStatusRsp, err error) {
	resp = &workflow.UpdateWorkflowNodeTaskStatusRsp{}
	if err = validateRequest(ctx, req); err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}
	// 2. 获取服务实例
	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to get workflow service: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}
	return workflowService.updateWorkflowNodeTaskStatus(ctx, req)
}

func UpdateWorkflowNodeProcessor(ctx context.Context, req *workflow.UpdateWorkflowNodeProcessorReq) (resp *workflow.UpdateWorkflowNodeProcessorRsp, err error) {
	resp = &workflow.UpdateWorkflowNodeProcessorRsp{}
	if err = validateRequest(ctx, req); err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}
	// 2. 获取服务实例
	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to get workflow service: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}
	return workflowService.updateWorkflowNodeProcessor(ctx, req)
}

func FinishWorkflowNode(ctx context.Context, req *workflow.FinishWorkflowNodeReq) (resp *workflow.FinishWorkflowNodeRsp, err error) {
	resp = &workflow.FinishWorkflowNodeRsp{}
	// 1. 验证请求
	if err = validateRequest(ctx, req); err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}

	tx := global.DB.Begin()
	if tx.Error != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logger.CtxErrorf(ctx, "create workflow panic occurred: %v", r)
		} else if tx.Error != nil || (resp != nil && resp.GetBase() != nil && resp.GetBase().GetCode() != 0) {
			tx.Rollback()
			logger.CtxErrorf(ctx, "Failed to commit create workflow transaction: %v", tx.Error)
		} else {
			tx.Commit()
			logger.Debugf("commit create workflow transaction successfully")
		}
	}()

	// 使用事务创建工作流服务实例
	workflowServiceWithTx, err := InitializeWorkflowServiceWithTx(tx)
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to initialize workflow service with tx: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}

	resp, err = workflowServiceWithTx.finishWorkflowNode(ctx, req)
	return resp, err
}

func TerminateWorkflowNode(ctx context.Context, req *workflow.TerminateWorkflowNodeReq) (resp *workflow.TerminateWorkflowNodeRsp, err error) {
	resp = &workflow.TerminateWorkflowNodeRsp{}
	if err = validateRequest(ctx, req); err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}

	tx := global.DB.Begin()
	if tx.Error != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logger.CtxErrorf(ctx, "create workflow panic occurred: %v", r)
		} else if tx.Error != nil {
			tx.Rollback()
			logger.CtxErrorf(ctx, "Failed to commit create workflow transaction: %v", tx.Error)
		} else {
			tx.Commit()
			logger.Debugf("commit create workflow transaction successfully")
		}
	}()

	// 使用事务创建工作流服务实例
	workflowServiceWithTx, err := InitializeWorkflowServiceWithTx(tx)
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to initialize workflow service with tx: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}
	resp, err = workflowServiceWithTx.terminateWorkflowNode(ctx, req)
	return resp, err
}

func RestartWorkflowNode(ctx context.Context, req *workflow.RestartWorkflowNodeReq) (resp *workflow.RestartWorkflowNodeRsp, err error) {
	resp = &workflow.RestartWorkflowNodeRsp{}
	if err = validateRequest(ctx, req); err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}

	tx := global.DB.Begin()
	if tx.Error != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logger.CtxErrorf(ctx, "restart workflow panic occurred: %v", r)
		} else if tx.Error != nil || (resp != nil && resp.GetBase() != nil && resp.GetBase().GetCode() != 0) {
			tx.Rollback()
			logger.CtxErrorf(ctx, "Failed to commit restart workflow transaction: %v, err: %v", tx.Error, err)
		} else {
			tx.Commit()
			logger.Debugf("commit restart workflow transaction successfully")
		}
	}()

	// 使用事务创建工作流服务实例
	workflowServiceWithTx, err := InitializeWorkflowServiceWithTx(tx)
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to initialize workflow service with tx: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}
	resp, err = workflowServiceWithTx.restartWorkflowNode(ctx, req)
	return resp, err
}

func PauseWorkflowNode(ctx context.Context, req *workflow.PauseWorkflowNodeReq) (resp *workflow.PauseWorkflowNodeRsp, err error) {
	resp = &workflow.PauseWorkflowNodeRsp{}
	if err = validateRequest(ctx, req); err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}

	tx := global.DB.Begin()
	if tx.Error != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logger.CtxErrorf(ctx, "pause workflow panic occurred: %v", r)
		} else if tx.Error != nil || (resp != nil && resp.GetBase() != nil && resp.GetBase().GetCode() != 0) {
			tx.Rollback()
			logger.CtxErrorf(ctx, "Failed to commit pause workflow transaction: %v, err: %v", tx.Error, err)
		} else {
			tx.Commit()
			logger.Debugf("commit pause workflow transaction successfully")
		}
	}()

	// 使用事务创建工作流服务实例
	workflowServiceWithTx, err := InitializeWorkflowServiceWithTx(tx)
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to initialize workflow service with tx: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}
	resp, err = workflowServiceWithTx.pauseWorkflowNode(ctx, req)
	return resp, err
}

func TransferWorkflowNode(ctx context.Context, req *workflow.TransferWorkflowNodeReq) (resp *workflow.TransferWorkflowNodeRsp, err error) {
	resp = &workflow.TransferWorkflowNodeRsp{}
	if err = validateRequest(ctx, req); err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}
	// 2. 获取服务实例
	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to get workflow service: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}
	return workflowService.transferWorkflowNode(ctx, req)
}

func ReceiveWorkflowNode(ctx context.Context, req *workflow.ReceiveWorkflowNodeReq) (resp *workflow.ReceiveWorkflowNodeRsp, err error) {
	resp = &workflow.ReceiveWorkflowNodeRsp{}
	if err = validateRequest(ctx, req); err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}
	// 2. 获取服务实例
	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to get workflow service: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}
	return workflowService.receiveWorkflowNode(ctx, req)
}

func SetWorkflowNodeProcessorAndStatus(ctx context.Context, req *workflow.SetWorkflowNodeReq) (resp *workflow.SetWorkflowNodeRsp, err error) {
	resp = &workflow.SetWorkflowNodeRsp{}
	if err = validateRequest(ctx, req); err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}
	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to get workflow service: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}
	return workflowService.setWorkflowNodeProcessorAndStatus(ctx, req)
}

func UnbindWorkflowGroupChat(ctx context.Context, req *workflow.UnbindWorkflowGroupChatReq) (resp *workflow.UnbindWorkflowGroupChatRsp, err error) {
	resp = &workflow.UnbindWorkflowGroupChatRsp{}
	if err = validateRequest(ctx, req); err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}
	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to get workflow service: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}
	return workflowService.unbindWorkflowGroupChat(ctx, req)
}

// ==========================================创建工单==========================================

// LoadAndValidateTemplate 加载并验证工单模板
func (w *WorkflowService) LoadAndValidateTemplate(ctx context.Context, templateID int64) (*WorkflowTemplate, error) {
	template, err := w.templateService.LoadWorkflowTemplate(templateID)
	if err != nil {
		logger.CtxErrorf(ctx, "failed to load workflow template: %v", err)
		return nil, fmt.Errorf("failed to load workflow template: %w", err)
	}
	return template, nil
}

// ==========================================审核工单：检查合同与收款==========================================

func (w *WorkflowService) getWorkflowOrderGoodsPayment(ctx context.Context, req *workflow.GetWorkflowOrderGoodsPaymentReq) (resp *workflow.GetWorkflowOrderGoodsPaymentRsp, err error) {
	resp = new(workflow.GetWorkflowOrderGoodsPaymentRsp)
	wfId := req.GetWorkflowId()
	// 通过工单ID获取订单ID(order_id)和商品ID(productId)
	wf, err := w.workflowRepo.GetByID(ctx, wfId)
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to get workflow by id: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowNotFound)
		return resp, nil
	}

	o, og, op, err := rpcs.GetOrderDetailsByOrderIdAndProductId(ctx, wf.OrderID, wf.ProductID)
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to get order details by id: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_OrderNotFound)
		return resp, nil
	}
	resp, err = buildGetWorkflowOrderGoodsPaymentResponse(ctx, o.GetId(), o.GetOrderNo(), o, og, op)
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to build get workflow order goods payment response: %v", err)
		resp = new(workflow.GetWorkflowOrderGoodsPaymentRsp)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_ThirdPartyError)
		return resp, nil
	}
	return resp, nil
}

// buildGetWorkflowOrderGoodsPaymentResponse 构建工作流订单商品支付响应
func buildGetWorkflowOrderGoodsPaymentResponse(ctx context.Context, orderId int64, orderNo string, o *order.OrderEntity, og *order.OrderGoodsEntity, op *order.OrderPayEntity) (*workflow.GetWorkflowOrderGoodsPaymentRsp, error) {
	// 参数验证
	if err := validateBuildParams(o, og, op); err != nil {
		logger.CtxErrorf(ctx, "Failed to validate build params: %v", err)
		return nil, fmt.Errorf("invalid parameters: %w", err)
	}

	resp := new(workflow.GetWorkflowOrderGoodsPaymentRsp)

	// 构建基础订单信息
	resp.Order = buildOrderEntity(ctx, o)
	resp.OrderGoods = buildOrderGoodsEntity(ctx, og)
	resp.OrderPay = buildOrderPayEntity(ctx, op)

	// 获取并构建支付相关信息
	payment, err := buildPaymentEntity(ctx, orderId, orderNo)
	if err != nil {
		return nil, fmt.Errorf("failed to build payment entity: %w", err)
	}
	resp.Payment = payment

	return resp, nil
}

// validateBuildParams 验证构建参数
func validateBuildParams(o *order.OrderEntity, og *order.OrderGoodsEntity, op *order.OrderPayEntity) error {
	if o == nil {
		return fmt.Errorf("order entity is nil")
	}
	if og == nil {
		return fmt.Errorf("order goods entity is nil")
	}
	if op == nil {
		return fmt.Errorf("order pay entity is nil")
	}
	return nil
}

// buildOrderEntity 构建订单实体
func buildOrderEntity(ctx context.Context, o *order.OrderEntity) *workflow.GetWorkflowOrderGoodsPaymentRsp_OrderEntity {
	logger.CtxInfof(ctx, "buildOrderEntity success")
	return &workflow.GetWorkflowOrderGoodsPaymentRsp_OrderEntity{
		Id:                    o.GetId(),
		OrderNo:               o.GetOrderNo(),
		CustomerId:            o.GetCustomerId(),
		ReviewerId:            o.GetReviewerId(),
		ExecutorId:            o.GetExecutorId(),
		BrandId:               o.GetBrandId(),
		BusinessId:            o.GetBusinessId(),
		ServiceId:             o.GetServiceId(),
		BrandName:             o.GetBrandName(),
		BusinessName:          o.GetBusinessName(),
		ServiceName:           o.GetServiceName(),
		InstallmentType:       workflow.GetWorkflowOrderGoodsPaymentRsp_OrderInstallmentType(o.GetInstallmentType()),
		DisbursementType:      workflow.GetWorkflowOrderGoodsPaymentRsp_OrderDisbursementType(o.GetDisbursementType()),
		Status:                workflow.GetWorkflowOrderGoodsPaymentRsp_StatusOrder(o.GetStatus()),
		StatusReview:          workflow.GetWorkflowOrderGoodsPaymentRsp_StatusOrderReview(o.GetStatusReview()),
		StatusPayDeposit:      workflow.GetWorkflowOrderGoodsPaymentRsp_StatusOrderPay(o.GetStatusPayDeposit()),
		StatusPayFirst:        workflow.GetWorkflowOrderGoodsPaymentRsp_StatusOrderPay(o.GetStatusPayFirst()),
		StatusPayFinal:        workflow.GetWorkflowOrderGoodsPaymentRsp_StatusOrderPay(o.GetStatusPayFinal()),
		StatusPayDisbursement: workflow.GetWorkflowOrderGoodsPaymentRsp_StatusOrderPay(o.GetStatusPayDisbursement()),
		PayDepositAt:          o.GetPayDepositAt(),
		PayFirstAt:            o.GetPayFirstAt(),
		PayFinalAt:            o.GetPayFinalAt(),
		PayDisbursementAt:     o.GetPayDisbursementAt(),
		ApplyDepositAt:        o.GetApplyDepositAt(),
		ApplyFirstAt:          o.GetApplyFirstAt(),
		ApplyFinalAt:          o.GetApplyFinalAt(),
		ApplyDisbursementAt:   o.GetApplyDisbursementAt(),
		PassDepositAt:         o.GetPassDepositAt(),
		PassFirstAt:           o.GetPassFirstAt(),
		PassFinalAt:           o.GetPassFinalAt(),
		PassDisbursementAt:    o.GetPassDisbursementAt(),
		RejectAt:              o.GetRejectAt(),
		CreatedBy:             o.GetCreatedBy(),
		UpdatedBy:             o.GetUpdatedBy(),
		CreatedAt:             o.GetCreatedAt(),
		UpdatedAt:             o.GetUpdatedAt(),
	}
}

// buildOrderGoodsEntity 构建订单商品实体
func buildOrderGoodsEntity(ctx context.Context, og *order.OrderGoodsEntity) *workflow.GetWorkflowOrderGoodsPaymentRsp_OrderGoodsEntity {
	logger.CtxInfof(ctx, "buildOrderGoodsEntity success")
	return &workflow.GetWorkflowOrderGoodsPaymentRsp_OrderGoodsEntity{
		Id:                 og.GetOrderId(),
		CustomerId:         og.GetCustomerId(),
		OrderId:            og.GetOrderId(),
		GoodsId:            og.GetGoodsId(),
		SpecId:             og.GetSpecId(),
		GoodsType:          workflow.GetWorkflowOrderGoodsPaymentRsp_OrderGoodsType(og.GetGoodsType()),
		GoodsNo:            og.GetGoodsNo(),
		GoodsName:          og.GetGoodsName(),
		GoodsSpec:          og.GetGoodsSpec(),
		GoodsPrice:         og.GetGoodsPrice(),
		GoodsFirstPrice:    og.GetGoodsFirstPrice(),
		GoodsFinalPrice:    og.GetGoodsFinalPrice(),
		GoodsCurrency:      og.GetGoodsCurrency(),
		GoodsFirstCurrency: og.GetGoodsFirstCurrency(),
		GoodsFinalCurrency: og.GetGoodsFinalCurrency(),
		GoodsRedundancy:    og.GetGoodsRedundancy(),
		GoodsNum:           og.GetGoodsNum(),
		AmountFirst:        og.GetAmountFirst(),
		AmountFinal:        og.GetAmountFinal(),
		AmountTotal:        og.GetAmountTotal(),
		CurrencyFirst:      og.GetCurrencyFirst(),
		CurrencyFinal:      og.GetCurrencyFinal(),
		CurrencyTotal:      og.GetCurrencyTotal(),
		PurchaseNumType:    workflow.GetWorkflowOrderGoodsPaymentRsp_StatusYesNo(og.GetPurchaseNumType()),
	}
}

// buildOrderPayEntity 构建订单支付实体
func buildOrderPayEntity(ctx context.Context, op *order.OrderPayEntity) *workflow.GetWorkflowOrderGoodsPaymentRsp_OrderPayEntity {
	logger.CtxInfof(ctx, "buildOrderPayEntity success")
	return &workflow.GetWorkflowOrderGoodsPaymentRsp_OrderPayEntity{
		Id:                         op.GetId(),
		OrderId:                    op.GetOrderId(),
		UrgentTimes:                op.GetUrgentTimes(),
		DiscountRate:               op.GetDiscountRate(),
		AmountDeposit:              op.GetAmountDeposit(),
		AmountTotalGoods:           op.GetAmountTotalGoods(),
		AmountTotal:                op.GetAmountTotal(),
		AmountContract:             op.GetAmountContract(),
		AmountFirstEstimated:       op.GetAmountFirstEstimated(),
		AmountFinalEstimated:       op.GetAmountFinalEstimated(),
		AmountFirstReceivable:      op.GetAmountFirstReceivable(),
		AmountFinalReceivable:      op.GetAmountFinalReceivable(),
		AmountFirst:                op.GetAmountFirst(),
		AmountFinal:                op.GetAmountFinal(),
		AmountDisbursement:         op.GetAmountDisbursement(),
		AmountDisbursementReview:   op.GetAmountDisbursementReview(),
		AmountScholarship:          op.GetAmountScholarship(),
		AmountUrgent:               op.GetAmountUrgent(),
		CurrencyDeposit:            op.GetCurrencyDeposit(),
		CurrencyTotalGoods:         op.GetCurrencyTotalGoods(),
		CurrencyTotal:              op.GetCurrencyTotal(),
		CurrencyContract:           op.GetCurrencyContract(),
		CurrencyFirstEstimated:     op.GetCurrencyFirstEstimated(),
		CurrencyFinalEstimated:     op.GetCurrencyFinalEstimated(),
		CurrencyFirstReceivable:    op.GetAmountFirstReceivable(),
		CurrencyFinalReceivable:    op.GetCurrencyFinalReceivable(),
		CurrencyFirst:              op.GetCurrencyFirst(),
		CurrencyFinal:              op.GetCurrencyFinal(),
		CurrencyDisbursement:       op.GetCurrencyDisbursement(),
		CurrencyDisbursementReview: op.GetCurrencyDisbursementReview(),
		CurrencyScholarship:        op.GetCurrencyScholarship(),
		CurrencyUrgent:             op.GetCurrencyUrgent(),
		ExemptFinal:                workflow.GetWorkflowOrderGoodsPaymentRsp_StatusYesNo(op.GetExemptFinal()),
		UrgentService:              workflow.GetWorkflowOrderGoodsPaymentRsp_StatusYesNo(op.GetUrgentService()),
		HasScholarship:             workflow.GetWorkflowOrderGoodsPaymentRsp_StatusYesNo(op.GetHasScholarship()),
		AutoSchedule:               workflow.GetWorkflowOrderGoodsPaymentRsp_StatusYesNo(op.GetAutoSchedule()),
		FinalPaymentType:           workflow.GetWorkflowOrderGoodsPaymentRsp_OrderFinalPaymentType(op.GetFinalPaymentType()),
		ExemptFinalReason:          op.GetExemptFinalReason(),
	}
}

type ContractURL struct {
	URL          string `json:"url"`
	Name         string `json:"name"`
	ThumbnailURL string `json:"thumbnail_url"`
	CreatedAt    int64  `json:"created_at"`
}

func parseContractURLs(urlString string) ([]ContractURL, error) {
	var urls []ContractURL
	if err := json.Unmarshal([]byte(urlString), &urls); err != nil {
		return nil, fmt.Errorf("failed to unmarshal contract URLs: %v", err)
	}

	return urls, nil
}

// buildPaymentEntity 构建支付实体
func buildPaymentEntity(ctx context.Context, orderId int64, orderNo string) (*workflow.GetWorkflowOrderGoodsPaymentRsp_PaymentEntity, error) {
	// 获取资金信息
	fundInfo, err := rpcs.GetFinancialFundInfo(ctx, orderId, orderNo)
	if err != nil {
		logger.CtxErrorf(ctx, "GetFinancialFundInfo rpc error: %v", err)
		return nil, err
	}

	// contractNoList := strings.Split(fundInfo.GetContractNo(), ",")
	// contractUrlList, err := parseContractURLs(fundInfo.GetContractUrl())
	// if err != nil {
	// 	logger.CtxErrorf(ctx, "parseContractURLs error: %v", err)
	// 	return nil, err
	// }

	// contractList := make([]*workflow.GetWorkflowOrderGoodsPaymentRsp_ContractEntity, 0, len(contractNoList))
	// if len(contractUrlList) != 0 {
	// 	// 构建合同列表
	// 	for i := 0; i < len(contractNoList); i++ {
	// 		contractList = append(contractList, &workflow.GetWorkflowOrderGoodsPaymentRsp_ContractEntity{
	// 			ContractNo:   strings.TrimSpace(contractNoList[i]),
	// 			ContractUrl:  contractUrlList[i].URL,
	// 			ContractName: contractUrlList[i].Name,
	// 			ThumbnailUrl: contractUrlList[i].ThumbnailURL,
	// 			CreatedAt:    contractUrlList[i].CreatedAt,
	// 		})
	// 	}
	// }

	var (
		wg           sync.WaitGroup
		mu           sync.Mutex
		fundPaidList []*workflow.GetWorkflowOrderGoodsPaymentRsp_FundPaidEntity
		submit       *workflow.EmployeeData
		fetchErr     error
	)

	// 并发获取支付列表和提交人信息
	wg.Add(2)

	// 获取支付列表
	go func() {
		defer wg.Done()
		paidList, err := buildFundPaidList(ctx, fundInfo.GetId())
		mu.Lock()
		if err != nil {
			fetchErr = err
		} else {
			fundPaidList = paidList
		}
		mu.Unlock()
	}()

	// 获取提交人信息
	go func() {
		defer wg.Done()
		employeeInfo, err := rpcs.GetEmployeeInfo(ctx, fundInfo.GetSubmitId())
		mu.Lock()
		if err != nil {
			fetchErr = err
		} else {
			submit = employeeInfo
		}
		mu.Unlock()
	}()

	wg.Wait()

	if fetchErr != nil {
		return nil, fetchErr
	}

	return &workflow.GetWorkflowOrderGoodsPaymentRsp_PaymentEntity{
		FundNo:          fundInfo.GetFundNo(),
		FundType:        fundInfo.GetFundType(),
		RealAmountRmb:   fundInfo.GetRealAmountRmb(),
		RealAmountOther: fundInfo.GetRealAmountOther(),
		// ShouldAmountRmb:   fundInfo.GetShouldAmountRmb(),  // TODO: @ChenMingwei 待接口补充字段
		ShouldAmountOther: fundInfo.GetShouldAmountOther(),
		ApproveStatus:     fundInfo.GetApproveStatus(),
		PayType:           fundInfo.GetPayType(),
		Currency:          fundInfo.GetCurrency(),
		PaidTime:          fundInfo.GetPaidTime(),
		// ContractList:      contractList,
		FundPaidList:      fundPaidList,
		SubmittedBy:       submit,
		SubmitTime:        fundInfo.GetCreatedAt(),
	}, nil
}

// buildFundPaidList 构建资金支付列表
func buildFundPaidList(ctx context.Context, fundId int64) ([]*workflow.GetWorkflowOrderGoodsPaymentRsp_FundPaidEntity, error) {
	paidRespRpc, err := rpcs.GetFinancialPaidList(ctx, fundId)
	if err != nil {
		logger.CtxErrorf(ctx, "GetFinancialPaidList rpc error: %v", err)
		return nil, err
	}

	fundPaidList := make([]*workflow.GetWorkflowOrderGoodsPaymentRsp_FundPaidEntity, 0, len(paidRespRpc.GetPaidList()))
	for _, item := range paidRespRpc.GetPaidList() {
		fundPaidList = append(fundPaidList, &workflow.GetWorkflowOrderGoodsPaymentRsp_FundPaidEntity{
			AccountName: item.GetAccountName(),
			AmountCny:   item.GetAmountCny(),
			AmountOther: item.GetAmountOther(),
			Currency:    item.GetCurrency(),
			ImagesPath:  item.GetImagesPath(),
		})
	}
	return fundPaidList, nil
}

// ==========================================审核工单：拒绝==========================================

func (w *WorkflowService) rejectWorkflowDispatch(ctx context.Context, req *workflow.RejectWorkflowDispatchReq) (resp *workflow.RejectWorkflowDispatchRsp, err error) {
	resp = new(workflow.RejectWorkflowDispatchRsp)
	workflowId := req.GetWorkflowId()
	abnormalReason := req.GetAbnormalReason()
	newAuth := ctxmeta.MustGetAuth(ctx)
	updateId := newAuth.EmployeeId()

	if err = w.workflowRepo.RejectWorkflowDispatch(ctx, workflowId, updateId); err != nil {
		logger.CtxErrorf(ctx, "RejectWorkflowDispatch error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	if err = w.nodeService.workflowNodeRepo.RejectWorkflowNodeDispatch(ctx, workflowId, abnormalReason); err != nil {
		logger.CtxErrorf(ctx, "RejectWorkflowNodeDispatch error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	resp.WorkflowId = workflowId
	return resp, nil
}

// ==========================================审核工单：接收==========================================
func (w *WorkflowService) acceptWorkflowDispatch(ctx context.Context, req *workflow.AcceptWorkflowDispatchReq) (resp *workflow.AcceptWorkflowDispatchRsp, err error) {
	resp = new(workflow.AcceptWorkflowDispatchRsp)
	workflowId := req.GetWorkflowId()
	workflowTemplateId := req.GetWorkflowTemplateId()
	isAcceptAll := req.GetIsAcceptAll()
	businessType := req.GetWorkflowBusinessType()
	newAuth := ctxmeta.MustGetAuth(ctx)
	updateId := newAuth.EmployeeId()

	if isAcceptAll == constants.YesNo_Unknown {
		logger.CtxErrorf(ctx, "invalid isAcceptAll: %v", isAcceptAll)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}

	var workflowList []*model.Workflow
	var employeeIds []int64
	var workflowIdList []int64

	// 1. 加载并验证工单模板
	template, err := w.LoadAndValidateTemplate(ctx, workflowTemplateId)
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to load workflow template: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowTemplateNotFound)
		return resp, nil
	}

	startNode := template.Workflow.Nodes["start"]

	updateInfo, err := rpcs.GetEmployeeInfo(ctx, updateId)
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to get employee info: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_ThirdPartyError)
		return resp, nil
	}

	if !slices.Contains(startNode.Role, updateInfo.GetRoleName()) {
		logger.CtxErrorf(ctx, "The current user does not have permission to approve this workflow")
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowAcceptError)
		return resp, nil
	}

	initialWorkflow, err := w.workflowRepo.GetByID(ctx, workflowId)
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to get workflow: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowNotFound)
		return resp, nil
	}

	if initialWorkflow.Status != int32(workflow.WorkflowStatus_WORKFLOW_STATUS_PENDING) {
		logger.CtxErrorf(ctx, "Workflow is not in pending status")
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowAlreadyAccepted)
		return resp, nil
	}

	if workflowList, employeeIds, err = w.workflowRepo.AcceptWorkflowDispatch(ctx, initialWorkflow, updateId, businessType, isAcceptAll); err != nil {
		logger.CtxErrorf(ctx, "AcceptWorkflowDispatch error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	employeeMap, err := rpcs.GetEmployeeInfoByIds(ctx, RemoveDuplicates(employeeIds, true))
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to get employee info by IDs: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_ThirdPartyError)
		return resp, nil
	}

	for _, wf := range workflowList {
		workflowIdList = append(workflowIdList, wf.ID)
		if err = w.nodeService.BatchCreateNodesFromTemplate(ctx, wf.ID, employeeMap[wf.CreatedBy], employeeMap[updateId], template.Workflow.Nodes); err != nil {
			logger.CtxErrorf(ctx, "Failed to create workflow nodes: %v", err)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowNodeCreateFailed)
			return resp, nil
		}

		// 检查每个工单的类型
		switch workflow.WorkflowBusinessType(wf.BusinessType) {
		case workflow.WorkflowBusinessType_WORKFLOW_BUSINESS_TYPE_APPLICATION:
			logger.CtxInfof(ctx, "application type")
			if err = w.workflowApplicationRepo.Create(ctx, wf.ID); err != nil {
				logger.CtxErrorf(ctx, "Failed to create workflow application: %v", err)
				resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
				return resp, nil
			}
		case workflow.WorkflowBusinessType_WORKFLOW_BUSINESS_TYPE_APPEAL:
			logger.CtxInfof(ctx, "appeal type")
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowTypeInvalid)
			return resp, nil
		case workflow.WorkflowBusinessType_WORKFLOW_BUSINESS_TYPE_TUTORING:
			logger.CtxInfof(ctx, "tutoring type")
			if err = w.workflowGuidanceRepo.Create(ctx, wf.ID); err != nil {
				logger.CtxErrorf(ctx, "Failed to create workflow guidance: %v", err)
				resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
				return resp, nil
			}
		case workflow.WorkflowBusinessType_WORKFLOW_BUSINESS_TYPE_PHD:
			logger.CtxInfof(ctx, "phd single type")
			if workflowTemplateId == 412 || workflowTemplateId == 413 {
				logger.CtxInfof(ctx, "special single type: %v", workflowTemplateId)
				if err = w.workflowGuidanceRepo.Create(ctx, wf.ID); err != nil {
					logger.CtxErrorf(ctx, "Failed to create workflow guidance: %v", err)
					resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
					return resp, nil
				}
			} else {
				if err = w.workflowSingleRepo.Create(ctx, wf.ID); err != nil {
					logger.CtxErrorf(ctx, "Failed to create workflow single: %v", err)
					resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
					return resp, nil
				}
			}
		case workflow.WorkflowBusinessType_WORKFLOW_BUSINESS_TYPE_OTHER:
			logger.CtxInfof(ctx, "other single type")
			if err = w.workflowSingleRepo.Create(ctx, wf.ID); err != nil {
				logger.CtxErrorf(ctx, "Failed to create workflow single: %v", err)
				resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
				return resp, nil
			}
		default:
			logger.CtxErrorf(ctx, "Workflow %d is not application type, skip create application information.businessType is %v", wf.ID, wf.BusinessType)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowTypeInvalid)
			return resp, nil
		}
	}

	templateInfo := models.TemplateInfo{
		TemplateName: template.WorkflowTemplateName,
		TemplateId:   workflowTemplateId,
		BusinessName: template.BusinessName,
		BusinessType: template.BusinessType,
	}
	if err = w.workflowRepo.BatchUpdateTemplate(ctx, workflowIdList, templateInfo); err != nil {
		logger.CtxErrorf(ctx, "Failed to update workflow template: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	resp.WorkflowIds = workflowIdList
	return resp, nil
}

// ==========================================工单节点：更新跟进情况=========================================

func (w *WorkflowService) updateWorkflowFollow(ctx context.Context, req *workflow.UpdateWorkflowFollowReq) (resp *workflow.UpdateWorkflowFollowRsp, err error) {
	resp = new(workflow.UpdateWorkflowFollowRsp)
	workflowId := req.GetWorkflowId()
	customerProfile := req.GetCustomerProfile()
	remark := req.GetRemark()
	newAuth := ctxmeta.MustGetAuth(ctx)
	updateId := newAuth.EmployeeId()

	const maxLength = 2000 // 最大字符长度

	if !CheckStringLength(customerProfile, maxLength) || !CheckStringLength(remark, maxLength) {
		logger.CtxErrorf(ctx, "customerProfile or remark length is too long")
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}

	if err = w.workflowRepo.UpdateWorkflowFollow(ctx, workflowId, customerProfile, remark, updateId); err != nil {
		logger.CtxErrorf(ctx, "UpdateWorkflowFollow error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	return resp, nil
}

// ==========================================工单节点：获取跟进情况==========================================
func (w *WorkflowService) getWorkflowFollow(ctx context.Context, req *workflow.GetWorkflowFollowReq) (resp *workflow.GetWorkflowFollowRsp, err error) {
	resp = new(workflow.GetWorkflowFollowRsp)
	workflowId := req.GetWorkflowId()

	wf, err := w.workflowRepo.GetByID(ctx, workflowId)
	if err != nil {
		logger.CtxErrorf(ctx, "GetWorkflowFollow error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowNotFound)
		return resp, nil
	}

	resp = buildGetWorkflowFollowRsp(ctx, wf)
	return resp, nil
}

func buildGetWorkflowFollowRsp(ctx context.Context, wf *model.Workflow) *workflow.GetWorkflowFollowRsp {
	logger.CtxInfof(ctx, "buildGetWorkflowFollowRsp success")
	return &workflow.GetWorkflowFollowRsp{
		WorkflowId:      wf.ID,
		CustomerProfile: wf.CustomerProfile,
		Remark:          wf.Remark,
	}
}

// ==========================================工单节点：任务状态更新==========================================

func (w *WorkflowService) updateWorkflowNodeTaskStatus(ctx context.Context, req *workflow.UpdateWorkflowNodeTaskStatusReq) (resp *workflow.UpdateWorkflowNodeTaskStatusRsp, err error) {
	resp = new(workflow.UpdateWorkflowNodeTaskStatusRsp)
	updateId := ctxmeta.MustGetAuth(ctx).EmployeeId()
	workflowId := req.GetWorkflowId()
	workflowNodeId := req.GetWorkflowNodeId()
	taskList := req.GetTaskList()

	node, err := w.nodeService.workflowNodeRepo.GetByWorkflowIDAndNodeId(ctx, workflowId, workflowNodeId)
	if err != nil {
		logger.CtxErrorf(ctx, "GetByWorkflowIDAndNodeId error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	if len(taskList) != 0 {
		processor := node.ProcessedBy
		if processor != updateId {
			logger.CtxErrorf(ctx, "AuthForbidden update workflow node task status error: %v", err)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowNodeRoleInvalid)
			return resp, nil
		}
		processedData := &workflow.EmployeeData{
			Id:               updateId,
			RoleNameSnapshot: node.ProcessedByRoleSnapshot,
			DeptNameSnapshot: node.ProcessedByDeptSnapshot,
		}

		// 更新workflow信息
		if err = w.workflowRepo.UpdateWorkflowNodeTaskStatus(ctx, workflowId, processedData); err != nil {
			logger.CtxErrorf(ctx, "UpdateWorkflowNodeTaskStatus error: %v", err)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
			return resp, nil
		}

		// 更新node信息
		if err = w.nodeService.workflowNodeRepo.Update(ctx, node); err != nil {
			logger.CtxErrorf(ctx, "UpdateWorkflowNodeTaskStatus error: %v", err)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
			return resp, nil
		}

		// 更新task信息
		var taskDataList []*models.UpdateTaskStatusData
		for _, task := range taskList {
			taskDataList = append(taskDataList, buildWorkflowNodeTaskModel(ctx, task))
		}

		if err = w.taskService.BatchUpdateTaskStatus(ctx, taskDataList); err != nil {
			logger.CtxErrorf(ctx, "UpdateTaskStatus error: %v", err)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
			return resp, nil
		}
	}

	var updateTaskModelList []*model.WorkflowNodeTask
	updateTaskModelList, err = w.taskService.workflowNodeTaskRepo.GetByNodeID(ctx, node.ID)
	if err != nil {
		logger.CtxErrorf(ctx, "GetByNodeID error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	// 构建返回值
	resp = buildUpdateWorkflowNodeTaskStatusRsp(ctx, workflowId, workflowNodeId, updateTaskModelList)
	return resp, nil
}

func buildWorkflowNodeTaskModel(ctx context.Context, task *workflow.SimpleWorkflowNodeTaskEntity) *models.UpdateTaskStatusData {
	taskModel := &models.UpdateTaskStatusData{
		Id:     task.GetId(),
		Status: int32(task.GetStatus()),
	}
	logger.CtxInfof(ctx, "buildWorkflowNodeTaskModel success")
	return taskModel
}

func buildWorkflowNodeTaskEntity(ctx context.Context, task *model.WorkflowNodeTask) (resp *workflow.WorkflowNodeTaskEntity) {
	resp = &workflow.WorkflowNodeTaskEntity{
		Id:             task.ID,
		Name:           task.Name,
		Status:         workflow.WorkflowNodeTaskStatus(task.Status),
		CreatedAt:      task.CreatedAt.UnixMilli(),
		UpdatedAt:      task.UpdatedAt.UnixMilli(),
		WorkflowNodeId: task.WorkflowNodeID,
	}

	if task.CompletedAt != nil && !task.CompletedAt.IsZero() {
		resp.CompletedAt = task.CompletedAt.UnixMilli()
	} else {
		resp.CompletedAt = 0
	}
	if task.DueAt != nil && !task.DueAt.IsZero() {
		resp.DueAt = task.DueAt.UnixMilli()
	} else {
		resp.DueAt = 0
	}
	if task.StartedAt != nil && !task.StartedAt.IsZero() {
		resp.StartedAt = task.StartedAt.UnixMilli()
	} else {
		resp.StartedAt = 0
	}
	logger.CtxInfof(ctx, "buildWorkflowNodeTaskEntity success")
	return resp
}

func buildUpdateWorkflowNodeTaskStatusRsp(ctx context.Context, workflowId, nodeId int64, taskList []*model.WorkflowNodeTask) (resp *workflow.UpdateWorkflowNodeTaskStatusRsp) {
	var taskStatus []*workflow.WorkflowNodeTaskEntity
	for _, task := range taskList {
		taskStatus = append(taskStatus, buildWorkflowNodeTaskEntity(ctx, task))
	}

	result := &workflow.UpdateWorkflowNodeTaskStatusRsp{
		WorkflowId:     workflowId,
		WorkflowNodeId: nodeId,
		TaskList:       taskStatus,
	}
	return result
}

// ==========================================工单节点：指定节点处理人==========================================

// updateWorkflowNodeProcessor
func (w *WorkflowService) updateWorkflowNodeProcessor(ctx context.Context, req *workflow.UpdateWorkflowNodeProcessorReq) (resp *workflow.UpdateWorkflowNodeProcessorRsp, err error) {
	resp = new(workflow.UpdateWorkflowNodeProcessorRsp)
	updateId := ctxmeta.MustGetAuth(ctx).EmployeeId()
	workflowId := req.GetWorkflowId()
	workflowNodeId := req.GetWorkflowNodeId()
	nodeProcessList := req.GetNodeProcessors()

	wf, err := w.workflowRepo.GetByID(ctx, workflowId)
	if err != nil {
		logger.CtxErrorf(ctx, "GetByID error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowNotFound)
		return resp, nil
	}

	template, err := w.LoadAndValidateTemplate(ctx, wf.WorkflowTemplateID)
	if err != nil {
		logger.CtxErrorf(ctx, "LoadAndValidateTemplate error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowTemplateNotFound)
		return resp, nil
	}

	if len(nodeProcessList) == 0 {
		// 获取当前节点的下个节点信息
		currentNode, errNode := w.nodeService.workflowNodeRepo.GetByWorkflowIDAndNodeId(ctx, workflowId, workflowNodeId)
		if errNode != nil {
			logger.CtxErrorf(ctx, "GetByWorkflowIDAndNodeId error: %v", errNode)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowNodeNotFound)
			return resp, nil
		}

		// 产品经理确认：动态节点不会发起转案
		if currentNode.Type == int32(workflow.WorkflowNodeType_WORKFLOW_NODE_TYPE_DYNAMIC) {
			resp.WorkflowNodeId = workflowNodeId
			resp.NodeProcessors = nil
			resp.WorkflowId = wf.ID
			resp.QqImGroupId = wf.QqImGroupID
			return resp, nil
		}

		nextNodeList := str2Slice(currentNode.Next, ",")
		// 检查动态节点信息
		dynamicNodeName, errDynamic := checkDynamicNode(ctx, currentNode.Name, template.Workflow.Nodes)
		if errDynamic != nil {
			logger.CtxErrorf(ctx, "checkDynamicNode error: %v", errDynamic)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
			return resp, nil
		}
		if dynamicNodeName != "" {
			nextNodeList = append(nextNodeList, dynamicNodeName)
		}

		var nl []*workflow.UpdateWorkflowNodeProcessorEntity
		for _, n := range nextNodeList {
			nn := updateWorkflowNodeProcessorConvertNode(ctx, workflowId, n, template.Workflow.Nodes, w.nodeService, currentNode)
			if nn == nil {
				continue
			}
			nl = append(nl, nn)
		}
		resp.WorkflowNodeId = workflowNodeId
		resp.NodeProcessors = nl
		resp.WorkflowId = wf.ID
		resp.QqImGroupId = wf.QqImGroupID
		return resp, nil
	}

	var employeeIds []int64
	for _, nd := range nodeProcessList {
		employeeIds = append(employeeIds, nd.ProcessorId)
	}
	employeeIds = append(employeeIds, updateId)
	employeeMap, err := rpcs.GetEmployeeInfoByIds(ctx, RemoveDuplicates(employeeIds, true))
	if err != nil {
		logger.CtxErrorf(ctx, "GetEmployeeInfoByIds error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_EmployeeNotExists)
		return resp, nil
	}

	var workflowProcessors []int64
	for _, nodeProcessor := range nodeProcessList {
		if nodeProcessor.ProcessorId == 0 {
			logger.CtxErrorf(ctx, "processor_id is empty")
			continue
		}
		workflowProcessors = append(workflowProcessors, nodeProcessor.ProcessorId)
		// 更新workflow节点信息
		err = w.nodeService.workflowNodeRepo.UpdateWorkflowNodeProcessor(ctx, workflowId, nodeProcessor.NodeName, employeeMap[nodeProcessor.ProcessorId], employeeMap[updateId])
		if err != nil {
			logger.CtxErrorf(ctx, "GetByWorkflowIDAndNodeName error: %v", err)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
			return resp, nil
		}
		nodeProcessor.ProcessorName = employeeMap[nodeProcessor.ProcessorId].Name
	}

	// 更新workflow的processor字段
	if err = w.workflowRepo.UpdateWorkflowProcessors(ctx, workflowId, workflowProcessors); err != nil {
		logger.CtxErrorf(ctx, "UpdateWorkflowProcessors error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	resp.WorkflowId = workflowId
	resp.WorkflowNodeId = workflowNodeId
	resp.NodeProcessors = nodeProcessList
	return resp, nil
}

func checkDynamicNode(ctx context.Context, currentNodeName string, nodeTemplate map[string]WorkflowNodeTemplate) (dynamicNodeName string, err error) {
	if len(nodeTemplate) == 0 {
		logger.CtxErrorf(ctx, "node template is empty")
		return "", fmt.Errorf("node template is empty")
	}

	for k, nt := range nodeTemplate {
		if nt.Type == int32(workflow.WorkflowNodeType_WORKFLOW_NODE_TYPE_DYNAMIC) {
			if len(nt.PreviousNodes) == 0 {
				logger.CtxErrorf(ctx, "Invalid dynamic node: %s", k)
				return "", fmt.Errorf("invalid dynamic node: %s", k)
			}
			if slices.Contains(nt.PreviousNodes, currentNodeName) {
				return k, nil
			}
		}
	}
	return "", nil
}

func updateWorkflowNodeProcessorConvertNode(ctx context.Context, workflowId int64, key string, nodeTemplate map[string]WorkflowNodeTemplate, ns *WorkflowNodeService, currentNode *model.WorkflowNode) *workflow.UpdateWorkflowNodeProcessorEntity {
	var result *workflow.UpdateWorkflowNodeProcessorEntity
	if nt, ok := nodeTemplate[key]; ok {
		if slices.Contains(nt.Role, currentNode.ProcessedByRoleSnapshot) {
			return nil
		}

		n, err := ns.workflowNodeRepo.GetByWorkflowIDAndNodeName(ctx, workflowId, key)
		if err != nil {
			logger.CtxErrorf(ctx, "GetByWorkflowIDAndNodeName error: %v", err)
			return nil
		}

		result = &workflow.UpdateWorkflowNodeProcessorEntity{
			NodeName:        key,
			NodeChineseName: nt.Name,
			NodeRole:        strings.Join(nt.Role, ","),
			ProcessorId:     0,
			ProcessorName:   "",
		}

		if n.ProcessedBy != 0 {
			employeeData, err := rpcs.GetEmployeeInfo(ctx, n.ProcessedBy)
			if err != nil {
				logger.CtxErrorf(ctx, "GetEmployeeInfo error: %v", err)
				return nil
			}
			result.ProcessorId = n.ProcessedBy
			result.ProcessorName = employeeData.Name
		}
	}
	return result
}

// ==========================================工单节点：完成节点==========================================
func (w *WorkflowService) finishWorkflowNode(ctx context.Context, req *workflow.FinishWorkflowNodeReq) (resp *workflow.FinishWorkflowNodeRsp, err error) {
	resp = new(workflow.FinishWorkflowNodeRsp)
	workflowId := req.GetWorkflowId()
	workflowNodeId := req.GetWorkflowNodeId()
	nextNodeId := req.GetNextWorkflowNodeId()
	nodeProcessList := req.GetNodeProcessors()
	now := time.Now()

	// 1. 检查节点的任务是否全部完成
	s := w.taskService.checkNodeTasksFinished(ctx, req.GetWorkflowNodeId())
	if !s {
		logger.CtxErrorf(ctx, "node tasks not finished")
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowNodeTaskNotCompleted)
		return resp, nil
	}

	var employeeIds []int64
	for _, nd := range nodeProcessList {
		if nd.ProcessorId != 0 {
			employeeIds = append(employeeIds, nd.ProcessorId)
		}
	}

	var employeeMap map[int64]*workflow.EmployeeData

	newAuthInfo := ctxmeta.MustGetAuth(ctx)
	updateId := newAuthInfo.EmployeeId()

	wf, err := w.workflowRepo.GetByID(ctx, workflowId)
	if err != nil {
		logger.CtxErrorf(ctx, "GetByID error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowNotFound)
		return resp, nil
	}

	processorList, _ := models.SplitToInt64(ctx, wf.Processors)
	employeeIds = append(employeeIds, updateId)
	employeeIds = append(employeeIds, processorList...)

	employeeMap, err = rpcs.GetEmployeeInfoByIds(ctx, RemoveDuplicates(employeeIds, true))
	if err != nil {
		logger.CtxErrorf(ctx, "GetEmployeeInfoByIds error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_EmployeeNotExists)
		return resp, nil
	}

	t, err := w.LoadAndValidateTemplate(ctx, wf.WorkflowTemplateID)
	if err != nil {
		logger.CtxErrorf(ctx, "LoadAndValidateTemplate error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowTemplateNotFound)
		return resp, nil
	}
	// 2. 更新节点状态为已完成，更新节点完成时间
	var currentNode *model.WorkflowNode
	if currentNode, err = w.nodeService.workflowNodeRepo.FinishWorkflowNodeV1(ctx, workflowNodeId, employeeMap[updateId]); err != nil {
		logger.CtxErrorf(ctx, "FinishWorkflowNode error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	// 3. 检查当前节点类型，如果是决策节点，必须指定下一个节点ID
	if currentNode.Type == int32(workflow.WorkflowNodeType_WORKFLOW_NODE_TYPE_DECISION) {
		if nextNodeId == 0 {
			logger.CtxErrorf(ctx, "decision node must specify next_workflow_node_id")
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
			return resp, nil
		}
	}

	// 4. 更新nextNode字段
	nextNode, err := w.nodeService.updateNextNodeV1(ctx, currentNode, t, employeeMap, now, nextNodeId)
	if err != nil {
		logger.CtxErrorf(ctx, "updateNextNode error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	// 5. 节点完成成功后再更新下一个节点的processors字段
	if len(employeeIds) != 0 {
		for _, nodeProcessor := range nodeProcessList {
			if nodeProcessor.ProcessorId == 0 {
				logger.CtxErrorf(ctx, "processor_id is empty")
				continue
			}
			// 更新workflow节点信息
			err = w.nodeService.workflowNodeRepo.UpdateWorkflowNodeProcessor(ctx, workflowId, nodeProcessor.NodeName, employeeMap[nodeProcessor.ProcessorId], employeeMap[updateId])
			if err != nil {
				logger.CtxErrorf(ctx, "UpdateWorkflowNodeProcessor error: %v", err)
				resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
				return resp, nil
			}
		}
	}

	// 6. 更新工单的version、processors等字段
	updateWorkflow, err := w.workflowRepo.UpdateWorkflowByNodeOperate(ctx, workflowId, workflow.WorkflowStatus_WORKFLOW_STATUS_IN_SERVICE, currentNode)
	if err != nil {
		logger.CtxErrorf(ctx, "FinishWorkflowNode error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	// 7. 更新完成节点人到processors字段
	if err = w.workflowRepo.UpdateWorkflowProcessors(ctx, workflowId, []int64{currentNode.ProcessedBy}); err != nil {
		logger.CtxErrorf(ctx, "UpdateWorkflowProcessors error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	resp.WorkflowId = updateWorkflow.ID
	resp.WorkflowNodeId = workflowNodeId
	if nextNode == nil {
		resp.NextWorkflowNodeId = 0
	} else {
		resp.NextWorkflowNodeId = nextNode.ID
	}
	return resp, nil
}

// ==========================================工单节点：终止节点==========================================
func (w *WorkflowService) terminateWorkflowNode(ctx context.Context, req *workflow.TerminateWorkflowNodeReq) (resp *workflow.TerminateWorkflowNodeRsp, err error) {
	resp = new(workflow.TerminateWorkflowNodeRsp)
	workflowId := req.GetWorkflowId()
	workflowNodeId := req.GetWorkflowNodeId()
	abnormalReason := req.GetAbnormalReason()

	// 1. 更新节点
	var currentNode *model.WorkflowNode
	if currentNode, err = w.nodeService.workflowNodeRepo.TerminateWorkflow(ctx, workflowId, workflowNodeId, abnormalReason); err != nil {
		logger.CtxErrorf(ctx, "TerminateWorkflowNode error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	// 2. 更新工单
	if _, err = w.workflowRepo.UpdateWorkflowByNodeOperate(ctx, workflowId, workflow.WorkflowStatus_WORKFLOW_STATUS_TERMINATED, currentNode); err != nil {
		logger.CtxErrorf(ctx, "TerminateWorkflowNode error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	// 3. 构建返回值
	resp.WorkflowId = currentNode.WorkflowID
	resp.WorkflowNodeId = currentNode.ID
	resp.TerminatedAt = currentNode.TerminatedAt.UnixMilli()
	return resp, nil
}

// ==========================================工单节点：暂停==========================================
func (w *WorkflowService) pauseWorkflowNode(ctx context.Context, req *workflow.PauseWorkflowNodeReq) (resp *workflow.PauseWorkflowNodeRsp, err error) {
	resp = new(workflow.PauseWorkflowNodeRsp)
	workflowId := req.GetWorkflowId()
	workflowNodeId := req.GetWorkflowNodeId()
	pauseReason := req.GetPauseReason()
	// 1. 更新节点状态为暂停
	var currentNode *model.WorkflowNode
	if currentNode, err = w.nodeService.workflowNodeRepo.PauseWorkflowNodeV1(ctx, workflowId, workflowNodeId, pauseReason); err != nil {
		logger.CtxErrorf(ctx, "PauseWorkflowNode error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	// 2. 更新工单
	_, err = w.workflowRepo.UpdateWorkflowByNodeOperate(ctx, workflowId, workflow.WorkflowStatus_WORKFLOW_STATUS_PAUSED, currentNode)
	if err != nil {
		logger.CtxErrorf(ctx, "PauseWorkflowNode error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	// 留言板通过cdc自行处理
	//required := w.workflowRepo.CheckWorkflowStatus(ctx, wf.QqImGroupID, int32(workflow.WorkflowStatus_WORKFLOW_STATUS_PAUSED))
	//if required {
	//	logger.CtxInfof(ctx, "workflow status is paused, set group %s status to paused", wf.QqImGroupID)
	//	if err := rpcs.SetGroupStatus(ctx, wf.QqImGroupID, wf.CustomerID, workflow.WorkflowStatus_WORKFLOW_STATUS_PAUSED); err != nil {
	//		logger.CtxErrorf(ctx, "SetGroupStatus error: %v", err)
	//		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
	//		return resp, err
	//	}
	//}
	resp.WorkflowId = currentNode.WorkflowID
	resp.WorkflowNodeId = currentNode.ID
	resp.PausedAt = currentNode.PausedAt.UnixMilli()
	return resp, nil
}

// ==========================================工单：重启==========================================
func (w *WorkflowService) restartWorkflowNode(ctx context.Context, req *workflow.RestartWorkflowNodeReq) (resp *workflow.RestartWorkflowNodeRsp, err error) {
	resp = new(workflow.RestartWorkflowNodeRsp)
	workflowId := req.GetWorkflowId()
	workflowNodeId := req.GetWorkflowNodeId()
	newAuth := ctxmeta.MustGetAuth(ctx)
	if newAuth.IsCustomer() {
		logger.CtxErrorf(ctx, "customer can not restart workflow")
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_AuthForbidden)
		return resp, nil
	}
	//
	//employeeInfo, err := rpcs.GetEmployeeInfo(ctx, newAuth.EmployeeId())
	//if err != nil {
	//	logger.CtxErrorf(ctx, "GetEmployeeInfo error: %v", err)
	//	resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_ThirdPartyError)
	//	return resp, err
	//}
	var updateNode *model.WorkflowNode
	if updateNode, err = w.nodeService.workflowNodeRepo.RestartWorkflowNodesV1(ctx, workflowId, workflowNodeId); err != nil {
		logger.CtxErrorf(ctx, "RestartWorkflowNodes error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	// 搁置的恢复不用刷新工单信息
	if workflowNodeId <= 0 {
		if _, err = w.workflowRepo.UpdateWorkflowByNodeOperate(ctx, workflowId, workflow.WorkflowStatus_WORKFLOW_STATUS_IN_SERVICE, updateNode); err != nil {
			logger.CtxErrorf(ctx, "UpdateWorkflowByNodeOperate error: %v", err)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
			return resp, nil
		}
	}

	resp.WorkflowId = workflowId
	resp.WorkflowNodeId = workflowNodeId
	resp.RestartedAt = time.Now().UnixMilli()
	return resp, nil
}

// ==========================================工单节点：转单==========================================
func (w *WorkflowService) transferWorkflowNode(ctx context.Context, req *workflow.TransferWorkflowNodeReq) (resp *workflow.TransferWorkflowNodeRsp, err error) {
	resp = new(workflow.TransferWorkflowNodeRsp)
	workflowId := req.GetWorkflowId()
	workflowNodeId := req.GetWorkflowNodeId()
	transferId := req.GetTransferId()
	newAuth := ctxmeta.MustGetAuth(ctx)
	if newAuth.IsCustomer() {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_AuthForbidden)
		return resp, nil
	}

	updateId := newAuth.EmployeeId()

	employeeMap, err := rpcs.GetEmployeeInfoByIds(ctx, []int64{transferId, updateId})
	if err != nil {
		logger.CtxErrorf(ctx, "global.UserServiceClient.GetEmployeeInfoByIds error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_EmployeeNotExists)
		return resp, nil
	}

	updateNode, err := w.nodeService.workflowNodeRepo.TransferWorkflowNodeV1(ctx, workflowId, workflowNodeId, updateId, transferId, employeeMap)
	if err != nil {
		logger.CtxErrorf(ctx, "TransferWorkflowNode error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	resp.WorkflowId = updateNode.WorkflowID
	resp.WorkflowNodeId = updateNode.ID
	resp.TransferredAt = updateNode.DispatchedAt.UnixMilli()
	return resp, nil
}

// ==========================================工单节点：接收==========================================
// 当报错时，注意节点的处理人是否为当前登陆的用户，否则会报错，后续处理报错提醒权限不足
func (w *WorkflowService) receiveWorkflowNode(ctx context.Context, req *workflow.ReceiveWorkflowNodeReq) (resp *workflow.ReceiveWorkflowNodeRsp, err error) {
	resp = new(workflow.ReceiveWorkflowNodeRsp)
	workflowNodeId := req.GetWorkflowNodeId()
	currentUserId := ctxmeta.MustGetAuth(ctx).EmployeeId()

	// 3. 更新节点状态
	updatedNode, err := w.nodeService.workflowNodeRepo.ReceiveWorkflowNodeV1(ctx, workflowNodeId, currentUserId)
	if err != nil {
		logger.CtxErrorf(ctx, "Failed to receive workflow node: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	// 4. 构建返回值
	resp.WorkflowId = updatedNode.WorkflowID
	resp.WorkflowNodeId = updatedNode.ID
	resp.StartedAt = updatedNode.StartedAt.UnixMilli()
	return resp, nil
}

// ======================================= SetWorkflowNodeProcessorAndStatus设置节点处理人和状态 ========================================
func (w *WorkflowService) setWorkflowNodeProcessorAndStatus(ctx context.Context, req *workflow.SetWorkflowNodeReq) (resp *workflow.SetWorkflowNodeRsp, err error) {
	resp = new(workflow.SetWorkflowNodeRsp)

	var employeeId int64
	var employeeIds []int64
	var employeeMap map[int64]*workflow.EmployeeData

	workflowId := req.GetWorkflowId()
	workflowNodeId := req.GetWorkflowNodeId()
	nodeProcessList := req.GetNodeProcessors()
	newAuth := ctxmeta.MustGetAuth(ctx)
	if newAuth.IsEmployee() {
		employeeId = newAuth.EmployeeId()
		employeeIds = append(employeeIds, employeeId)
	}

	if newAuth.IsCustomer() {
		logger.CtxErrorf(ctx, "Customer cannot access this interface")
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_AuthForbidden)
		return resp, nil
	}

	for _, nd := range nodeProcessList {
		if nd.ProcessorId != 0 {
			employeeIds = append(employeeIds, nd.ProcessorId)
		}
	}

	employeeMap, err = rpcs.GetEmployeeInfoByIds(ctx, RemoveDuplicates(employeeIds, true))
	if err != nil {
		logger.CtxErrorf(ctx, "GetEmployeeInfoByIds error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_EmployeeNotExists)
		return resp, nil
	}

	// 发起推送时，完成节点下的所有任务
	err = w.taskService.workflowNodeTaskRepo.FinishCustomerConfirmationNodeTasks(ctx, workflowNodeId)
	if err != nil {
		logger.CtxErrorf(ctx, "FinishCustomerConfirmationNodeTasks error: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	for _, nodeProcessor := range nodeProcessList {
		if nodeProcessor.ProcessorId == 0 {
			logger.CtxErrorf(ctx, "processor_id is empty")
			continue
		}
		// 更新workflow节点信息
		err = w.nodeService.workflowNodeRepo.UpdateWorkflowNodeProcessorAndStatus(ctx, workflowId, nodeProcessor.NodeName, nodeProcessor.NodeStatus, employeeMap[nodeProcessor.ProcessorId], employeeMap[employeeId])
		if err != nil {
			logger.CtxErrorf(ctx, "UpdateWorkflowNodeProcessor error: %v", err)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
			return resp, nil
		}
	}
	resp.TaskStatus = workflow.WorkflowNodeTaskStatus_WORKFLOW_NODE_TASK_STATUS_COMPLETED
	return resp, nil
}

// =======================================解绑工单群聊========================================
func (w *WorkflowService) unbindWorkflowGroupChat(ctx context.Context, req *workflow.UnbindWorkflowGroupChatReq) (resp *workflow.UnbindWorkflowGroupChatRsp, err error) {
	resp = new(workflow.UnbindWorkflowGroupChatRsp)

	workflowId := req.GetWorkflowId()
	if workflowId <= 0 {
		logger.CtxErrorf(ctx, "invalid request: workflow ID must be positive")
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, fmt.Errorf("invalid request: workflow ID must be positive")
	}
	if err = w.workflowRepo.UnbindGroupId(ctx, workflowId); err != nil {
		logger.CtxErrorf(ctx, "Failed to unbind workflow group chat: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, fmt.Errorf("failed to unbind workflow group chat: %w", err)
	}
	return resp, nil
}
