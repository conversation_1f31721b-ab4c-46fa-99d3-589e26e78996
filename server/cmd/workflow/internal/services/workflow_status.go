package services

import (
	"context"

	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/workflow"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/ctxmeta"
	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/workflow/internal/models"
)

func ListWorkflowStatusCount(ctx context.Context, req *workflow.ListWorkflowStatusCountReq) (resp *workflow.ListWorkflowStatusCountRsp, err error) {
	resp = &workflow.ListWorkflowStatusCountRsp{}

	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "获取工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}

	return workflowService.listWorkflowStatusCountByCustomer(ctx, req)
}

func (w *WorkflowService) listWorkflowStatusCountByCustomer(ctx context.Context, _ *workflow.ListWorkflowStatusCountReq) (resp *workflow.ListWorkflowStatusCountRsp, err error) {
	resp = &workflow.ListWorkflowStatusCountRsp{}

	newAuthInfo := ctxmeta.MustGetAuth(ctx)
	if !newAuthInfo.IsCustomer() {
		logger.CtxErrorf(ctx, "仅客户可访问此接口")
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_AuthForbidden)
		return resp, nil
	}
	customerId := newAuthInfo.CustomerId()

	countResult, versionResult, err := w.workflowRepo.ListWorkflowStatusCountByCustomerId(ctx, customerId)
	if err != nil {
		logger.CtxErrorf(ctx, "获取工作流状态计数失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	resp.Items = make([]*workflow.ListWorkflowStatusCountRsp_Item, 0)

	for status := workflow.WorkflowStatus_WORKFLOW_STATUS_PENDING; status <= workflow.WorkflowStatus_WORKFLOW_STATUS_TERMINATED; status++ {
		resp.Items = append(resp.Items, &workflow.ListWorkflowStatusCountRsp_Item{
			WorkflowStatus: status,
			Count:          countResult[int32(status)],
			Version:        versionResult[int32(status)],
		})
	}

	var total int64
	for _, c := range countResult {
		total += c
	}
	resp.Total = total
	return resp, nil
}

func GetWorkflowSameStatus(ctx context.Context, req *workflow.GetWorkflowSameStatusReq) (resp *workflow.GetWorkflowSameStatusRsp, err error) {
	resp = &workflow.GetWorkflowSameStatusRsp{}

	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "获取工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}

	return workflowService.getWorkflowSameStatus(ctx, req)
}

func (w *WorkflowService) getWorkflowSameStatus(ctx context.Context, req *workflow.GetWorkflowSameStatusReq) (resp *workflow.GetWorkflowSameStatusRsp, err error) {
	resp = &workflow.GetWorkflowSameStatusRsp{}

	var wf *model.Workflow
	workflowId := req.GetWorkflowId()
	if workflowId <= 0 {
		logger.CtxWarnf(ctx, "请求参数错误: workflow ID必须大于0")
		return resp, nil
	}

	wf, err = w.workflowRepo.GetByID(ctx, workflowId)
	if err != nil {
		logger.CtxErrorf(ctx, "通过ID: %d 获取工作流失败: %v", workflowId, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	filter := &models.SameWorkflowFilter{
		WorkflowStatus: workflow.WorkflowStatus(wf.Status),
		CustomerId:     wf.CustomerID,
		BusinessType:   workflow.WorkflowBusinessType(wf.BusinessType),
		TemplateId:     wf.WorkflowTemplateID,
		OrderId:        wf.OrderID,
		CreateBy:       wf.CreatedBy,
		GoodsType:      wf.GoodsType,
		GoodsId:        wf.ProductID,
		SpecId:         wf.SpecID,
	}

	workflowList, err := w.workflowRepo.ListSameWorkflow(ctx, filter)
	if err != nil {
		logger.CtxErrorf(ctx, "通过filter: %v 获取相同状态的工作流失败: %v", filter, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	resp.WorkflowList = make([]int64, 0, len(workflowList))
	for _, o := range workflowList {
		resp.WorkflowList = append(resp.WorkflowList, o.ID)
	}

	return resp, nil
}

func GetWorkflowSameStatusNode(ctx context.Context, req *workflow.GetWorkflowSameStatusNodeRequest) (resp *workflow.GetWorkflowSameStatusNodeResponse, err error) {
	resp = &workflow.GetWorkflowSameStatusNodeResponse{}

	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "获取工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}
	return workflowService.getWorkflowSameStatusNode(ctx, req)
}

func (w *WorkflowService) getWorkflowSameStatusNode(ctx context.Context, req *workflow.GetWorkflowSameStatusNodeRequest) (resp *workflow.GetWorkflowSameStatusNodeResponse, err error) {
	resp = &workflow.GetWorkflowSameStatusNodeResponse{
		WorkflowInfoList: make([]*workflow.SameWorkflowInfo, 0),
	}

	workflowId := req.GetWorkflowId()
	workflowNodeId := req.GetWorkflowNodeId()
	nodeStatus := req.GetStatus()
	if workflowId <= 0 || workflowNodeId <= 0 {
		logger.CtxWarnf(ctx, "请求参数错误: workflow ID和节点ID必须大于0")
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}

	var wf *model.Workflow
	var wn *model.WorkflowNode

	wf, err = w.workflowRepo.GetByID(ctx, workflowId)
	if err != nil {
		logger.CtxErrorf(ctx, "通过ID: %d 获取工作流失败: %v", workflowId, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	wn, err = w.nodeService.workflowNodeRepo.GetByWorkflowIDAndNodeId(ctx, workflowId, workflowNodeId)
	if err != nil {
		logger.CtxErrorf(ctx, "通过ID: %d 获取工作流节点失败: %v", workflowNodeId, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	filter := models.SameWorkflowFilter{
		WorkflowStatus: workflow.WorkflowStatus(wf.Status),
		CustomerId:     wf.CustomerID,
		BusinessType:   workflow.WorkflowBusinessType(wf.BusinessType),
		TemplateId:     wf.WorkflowTemplateID,
		OrderId:        wf.OrderID,
		CreateBy:       wf.CreatedBy,
		GoodsType:      wf.GoodsType,
		GoodsId:        wf.ProductID,
		SpecId:         wf.SpecID,
	}

	wfList, err := w.workflowRepo.ListSameWorkflow(ctx, &filter)
	if err != nil {
		logger.CtxErrorf(ctx, "通过filter: %v 获取相同状态的工作流失败: %v", filter, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	wfIds := make([]int64, 0, len(wfList))
	for _, wfl := range wfList {
		wfIds = append(wfIds, wfl.ID)
	}
	// 不能使用wn的status，因为当前wn是操作后的状态
	nodes, err := w.nodeService.workflowNodeRepo.BatchGetByWorkflowIDsAndNodeName(ctx, wfIds, wn.Name, nodeStatus, wn.ProcessedBy)
	if err != nil {
		logger.CtxErrorf(ctx, "通过ID: %v 获取工作流节点失败: %v", wfIds, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	for _, node := range nodes {
		if node.ID == workflowNodeId {
			continue
		}
		resp.WorkflowInfoList = append(resp.WorkflowInfoList, &workflow.SameWorkflowInfo{
			WorkflowId:     node.WorkflowID,
			WorkflowNodeId: node.ID,
		})
	}

	return resp, nil
}
