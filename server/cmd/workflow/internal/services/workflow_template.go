package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/workflow"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/workflow/internal/templates"
)

type WorkflowTemplate struct {
	WorkflowTemplateName string    `json:"workflow_template_name"`
	BusinessType         int32     `json:"business_type"`
	BusinessName         string    `json:"business_name"`
	CreatedAt            time.Time `json:"created_at"`
	Workflow             struct {
		Nodes map[string]WorkflowNodeTemplate `json:"nodes"`
	} `json:"workflow"`
}

type WorkflowNodeTemplate struct {
	Name          string         `json:"name"`
	Role          []string       `json:"role"`
	Type          int32          `json:"type"`
	No            string         `json:"no"`
	SyncNode      int32          `json:"sync_node"`
	NextNodes     []string       `json:"next_nodes"`
	PreviousNodes []string       `json:"previous_nodes"`
	Tasks         []TaskTemplate `json:"tasks"`
}

type TaskTemplate struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

type Template interface {
	Load(templateId int64) (*WorkflowTemplate, error)
}

type JsonFileTemplate struct{}

func (t *JsonFileTemplate) Load(templateID int64) (*WorkflowTemplate, error) {
	var wt map[string]WorkflowTemplate
	if err := json.Unmarshal(templates.WorkflowTemplatesJSON, &wt); err != nil {
		return nil, fmt.Errorf("failed to parse template JSON: %w", err)
	}

	template, exists := wt[strconv.FormatInt(templateID, 10)]
	if !exists {
		return nil, fmt.Errorf("template with ID %d not found", templateID)
	}

	return &template, nil
}

// WorkflowTemplateService 加载模板
type WorkflowTemplateService struct {
	template Template
}

func NewWorkflowTemplateService(template Template) *WorkflowTemplateService {
	return &WorkflowTemplateService{template: template}
}

func (s *WorkflowTemplateService) LoadWorkflowTemplate(templateID int64) (*WorkflowTemplate, error) {
	return s.template.Load(templateID)
}

var (
	workflowBusinessTypeOnce sync.Once
	workflowBusinessTypeData map[string]struct {
		CN           string `json:"cn"`
		BusinessType int32  `json:"businessType"`
		Switch       int64  `json:"switch"`
		Items        map[string]struct {
			TemplateID int32  `json:"templateId"`
			CN         string `json:"cn"`
		} `json:"items"`
	}
	workflowBusinessTypeError error
)

func initWorkflowBusinessType() {
	if err := json.Unmarshal(templates.BusinessTypeJSON, &workflowBusinessTypeData); err != nil {
		workflowBusinessTypeError = fmt.Errorf("unmarshal business_type.json failed: %v", err)
		return
	}
}

func GetWorkflowTemplateAndBusiness(ctx context.Context, req *workflow.GetWorkflowTemplateAndBusinessReq) (*workflow.GetWorkflowTemplateAndBusinessRsp, error) {
	resp := &workflow.GetWorkflowTemplateAndBusinessRsp{
		BusinessTypes: make(map[string]*workflow.GetWorkflowTemplateAndBusinessRsp_Category),
	}

	workflowBusinessTypeOnce.Do(func() {
		initWorkflowBusinessType()
	})

	if workflowBusinessTypeError != nil {
		logger.CtxErrorf(ctx, "Failed to initialize workflow business type data：%+v", workflowBusinessTypeError)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, workflowBusinessTypeError
	}

	for categoryKey, category := range workflowBusinessTypeData {
		if category.Switch == 0 {
			continue
		}
		items := make(map[string]*workflow.GetWorkflowTemplateAndBusinessRsp_Item)
		for itemKey, item := range category.Items {
			items[itemKey] = &workflow.GetWorkflowTemplateAndBusinessRsp_Item{
				TemplateId: item.TemplateID,
				Cn:         item.CN,
			}
		}
		resp.BusinessTypes[categoryKey] = &workflow.GetWorkflowTemplateAndBusinessRsp_Category{
			Cn:           category.CN,
			BusinessType: workflow.WorkflowBusinessType(category.BusinessType),
			Items:        items,
		}
	}

	return resp, nil
}

func WorkflowTemplateDetail(ctx context.Context, req *workflow.WorkflowTemplateDetailReq) (*workflow.WorkflowTemplateDetailRsp, error) {
	resp := &workflow.WorkflowTemplateDetailRsp{
		Templates: make([]*workflow.WorkflowTemplateInfo, 0),
	}

	var wt map[string]WorkflowTemplate
	if err := json.Unmarshal(templates.WorkflowTemplatesJSON, &wt); err != nil {
		logger.CtxErrorf(ctx, "Failed to parse template JSON: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	bt := req.GetBusinessType()
	templateIds := req.GetTemplateIds()
	// 创建templateIds的map以提高查找效率
	templateIdMap := make(map[int32]bool, len(templateIds))
	for _, id := range templateIds {
		templateIdMap[id] = true
	}

	for i, v := range wt {
		idx, err := strconv.ParseInt(i, 10, 64)
		if err != nil {
			logger.CtxErrorf(ctx, "Failed to parse template ID: %v", err)
			continue
		}

		templateID := int32(idx)
		// 检查业务类型是否匹配
		if bt > 0 && int32(bt) != v.BusinessType {
			logger.CtxWarnf(ctx, "business type %d does not match with template ID: %d", bt, idx)
			continue
		}

		// 检查模板ID是否在请求的ID列表中
		if len(templateIds) > 0 && !templateIdMap[templateID] {
			logger.CtxWarnf(ctx, "template ID: %d is not in templateIds", idx)
			continue
		}

		resp.Templates = append(resp.Templates, Workflow2Proto(templateID, &v))
	}
	return resp, nil
}

func Workflow2Proto(id int32, src *WorkflowTemplate) *workflow.WorkflowTemplateInfo {
	result := &workflow.WorkflowTemplateInfo{
		TemplateId:          id,
		TemplateName:        src.WorkflowTemplateName,
		BusinessType:        src.BusinessType,
		BusinessName:        src.BusinessName,
		CreatedAt:           src.CreatedAt.UnixMilli(),
		WorkflowTemplateMap: &workflow.WorkflowTemplateMap{Nodes: make(map[string]*workflow.WorkflowNodeTemplate)},
	}

	// 预先分配足够容量的map
	result.WorkflowTemplateMap.Nodes = make(map[string]*workflow.WorkflowNodeTemplate, len(src.Workflow.Nodes))

	for nodeID, node := range src.Workflow.Nodes {
		// 预先分配任务数组
		tasks := make([]*workflow.TaskTemplate, 0, len(node.Tasks))
		for _, task := range node.Tasks {
			tasks = append(tasks, &workflow.TaskTemplate{
				Id:   int32(task.ID),
				Name: task.Name,
			})
		}

		result.WorkflowTemplateMap.Nodes[nodeID] = &workflow.WorkflowNodeTemplate{
			Name:          node.Name,
			Role:          node.Role,
			Type:          node.Type,
			NextNodes:     node.NextNodes,
			PreviousNodes: node.PreviousNodes,
			Tasks:         tasks,
		}
	}
	return result
}
