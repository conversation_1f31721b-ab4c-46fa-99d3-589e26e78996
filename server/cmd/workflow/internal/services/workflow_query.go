package services

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"uofferv2/kitex_gen/constants"
	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/kitex_gen/server/cmd/workflow"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/workflow/internal/models"
	"uofferv2/server/cmd/workflow/internal/services/rpcs"
)

func GetWorkflowDetail(ctx context.Context, req *workflow.GetWorkflowDetailReq) (resp *workflow.GetWorkflowDetailRsp, err error) {
	resp = &workflow.GetWorkflowDetailRsp{}

	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "获取工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}

	return workflowService.getWorkflowDetail(ctx, req)
}

// getWorkflowDetail 获取工单详情
func (w *WorkflowService) getWorkflowDetail(ctx context.Context, req *workflow.GetWorkflowDetailReq) (resp *workflow.GetWorkflowDetailRsp, err error) {
	resp = &workflow.GetWorkflowDetailRsp{}

	// 1. 验证请求并获取工单基本信息
	wf, err := getWorkflowInfo(ctx, w.workflowRepo, req.GetWorkflowId(), req.GetWorkflowNo())
	if err != nil {
		logger.CtxErrorf(ctx, "通过ID: %d 或工单编号: %s 获取工单基本信息失败: %v", req.GetWorkflowId(), req.GetWorkflowNo(), err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowNotFound)
		return resp, nil
	}

	// 2. 并发获取关联数据
	detailData, err := fetchWorkflowDetailData(ctx, w.nodeService, w.taskService, wf)
	if err != nil {
		logger.CtxErrorf(ctx, "获取工单详情关联数据失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_OrderNotFound)
		return resp, nil
	}

	// 3. 获取员工信息
	employeeMap, err := buildWorkflowEmployeeInfo(ctx, detailData.Nodes)
	if err != nil {
		logger.CtxErrorf(ctx, "构建工单相关人员信息失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_EmployeeNotExists)
		return resp, nil
	}
	detailData.EmployeeMap = employeeMap

	// 获取order的留言板ID
	orderMessageBoardId, err := rpcs.GetOrderMessageBoardId(ctx, wf.CreatedBy, wf.OrderID)
	if err != nil {
		logger.CtxErrorf(ctx, "获取订单留言板ID失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_MessageBoardNotFound)
		return resp, nil
	}

	detailData.OrderMessageBoardId = orderMessageBoardId
	// 4. 获取当前工作流节点列表
	steps, err := w.nodeService.workflowNodeRepo.GetCurrentLatestNodes(ctx, wf.ID)
	if err != nil {
		logger.CtxErrorf(ctx, "获取当前工作流节点列表失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	// 根据过滤条件筛选节点
	var result []*workflow.SimpleCurrentNodeEntity
	nodeTask := detailData.NodeTasks
	for _, s := range steps {
		result = append(result, models.NodeModel2Entity(s.WorkflowNode, detailData.EmployeeMap[s.DispatchedBy], detailData.EmployeeMap[s.ProcessedBy], models.TaskModelList2EntityList(nodeTask[s.ID])))
	}

	// 5. 构建响应
	resp, err = buildWorkflowDetailResponse(ctx, w.templateService, wf.WorkflowTemplateID, detailData, result)
	if err != nil {
		logger.CtxErrorf(ctx, "构建工单详情响应失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	return resp, nil
}

// WorkflowDetailData 工单详情关联数据结构
type WorkflowDetailData struct {
	Workflow  *model.Workflow
	Nodes     []*model.WorkflowNode
	NodeTasks map[int64][]*model.WorkflowNodeTask

	EmployeeMap         map[int64]*workflow.EmployeeData
	Customer            *workflow.CustomerData
	OrderInfo           *order.OrderEntity
	OrderGoodsInfo      *order.OrderGoodsEntity
	OrderMessageBoardId int64
}

func fetchWorkflowDetailData(ctx context.Context, nodeService *WorkflowNodeService, taskService *WorkflowNodeTaskService, wf *model.Workflow) (*WorkflowDetailData, error) {
	if wf == nil {
		return nil, fmt.Errorf("workflow is nil")
	}

	data := &WorkflowDetailData{
		Workflow:       wf,
		Nodes:          make([]*model.WorkflowNode, 0),
		NodeTasks:      make(map[int64][]*model.WorkflowNodeTask),
		EmployeeMap:    make(map[int64]*workflow.EmployeeData),
		Customer:       &workflow.CustomerData{},
		OrderInfo:      &order.OrderEntity{},
		OrderGoodsInfo: &order.OrderGoodsEntity{},
	}

	var (
		wg       sync.WaitGroup
		mu       sync.Mutex
		fetchErr error
	)

	wg.Add(3)

	// 获取工单节点信息
	go func() {
		defer wg.Done()
		nodes, err := nodeService.GetNodesByWorkflow(ctx, wf.ID)
		mu.Lock()
		if err != nil {
			fetchErr = fmt.Errorf("获取工单节点信息失败: %w", err)
		} else {
			data.Nodes = nodes
			// 获取节点任务信息
			if len(nodes) > 0 {
				nodeIDs := make([]int64, 0, len(nodes))
				for _, node := range nodes {
					nodeIDs = append(nodeIDs, node.ID)
				}
				nodeTasks, err := taskService.GetNodeTasks(ctx, nodeIDs)
				if err != nil {
					fetchErr = fmt.Errorf("获取节点任务信息失败: %w", err)
				} else {
					data.NodeTasks = nodeTasks
				}
			}
		}
		mu.Unlock()
	}()

	// 获取客户信息
	go func() {
		defer wg.Done()
		customer, err := rpcs.GetCustomerInfo(ctx, wf.CustomerID)
		mu.Lock()
		if err != nil {
			fetchErr = err
		} else {
			data.Customer = customer
		}
		mu.Unlock()
	}()

	// 获取order信息
	go func() {
		defer wg.Done()
		orderInfo, orderGoodsInfo, _, err := rpcs.GetOrderDetailsByOrderIdAndProductId(ctx, wf.OrderID, wf.ProductID)
		mu.Lock()
		if err != nil {
			fetchErr = err
		} else {
			data.OrderInfo = orderInfo
			data.OrderGoodsInfo = orderGoodsInfo
		}
		mu.Unlock()
	}()

	wg.Wait()

	if fetchErr != nil {
		return nil, fetchErr
	}

	return data, nil
}

// getWorkflowInfo 获取工单基本信息
func getWorkflowInfo(ctx context.Context, workflowRepo *models.WorkflowDao, workflowID int64, workflowNo string) (*model.Workflow, error) {
	if workflowID != 0 {
		wf, err := workflowRepo.GetByID(ctx, workflowID)
		if err != nil {
			logger.CtxErrorf(ctx, "通过ID: %d 获取工单失败: %v", workflowID, err)
			return nil, fmt.Errorf("通过ID: %d 获取工单失败: %w", workflowID, err)
		}
		if wf == nil {
			logger.CtxErrorf(ctx, "工单不存在: ID: %d", workflowID)
			return nil, fmt.Errorf("工单不存在: ID: %d", workflowID)
		}
		return wf, nil
	}

	if workflowNo != "" {
		wf, err := workflowRepo.GetByWorkflowNo(ctx, workflowNo)
		if err != nil {
			logger.CtxErrorf(ctx, "通过工单编号: %s 获取工单失败: %v", workflowNo, err)
			return nil, fmt.Errorf("通过工单编号: %s 获取工单失败: %w", workflowNo, err)
		}
		if wf == nil {
			logger.CtxErrorf(ctx, "工单不存在: 编号: %s", workflowNo)
			return nil, fmt.Errorf("工单不存在: 编号: %s", workflowNo)
		}
		return wf, nil
	}

	return nil, fmt.Errorf("必须提供工单ID或工单编号")
}

// getWorkflowEmployeeInfo 获取工单相关人员信息
func buildWorkflowEmployeeInfo(ctx context.Context, nodes []*model.WorkflowNode) (map[int64]*workflow.EmployeeData, error) {
	var employeeIds []int64
	for _, node := range nodes {
		employeeIds = append(employeeIds, node.ProcessedBy, node.DispatchedBy)
	}
	employeeMap, err := rpcs.GetEmployeeInfoByIds(ctx, RemoveDuplicates(employeeIds, true))
	if err != nil {
		logger.CtxErrorf(ctx, "获取员工信息失败: %v", err)
		return nil, fmt.Errorf("获取员工信息失败: %w", err)
	}

	return employeeMap, nil
}

// buildWorkflowDetailResponse 构建工单详情响应
func buildWorkflowDetailResponse(ctx context.Context, templateService *WorkflowTemplateService, templateId int64, detailData *WorkflowDetailData, steps []*workflow.SimpleCurrentNodeEntity) (*workflow.GetWorkflowDetailRsp, error) {
	resp := &workflow.GetWorkflowDetailRsp{}

	// 参数校验
	if detailData == nil || detailData.Workflow == nil {
		logger.CtxErrorf(ctx, "无效的工单详情数据: detailData=%v", detailData)
		return nil, fmt.Errorf("无效的工单详情数据: detailData=%v", detailData)
	}

	// 构建工作流实体
	workflowEntity := &workflow.WorkflowEntity{
		Id:                   detailData.Workflow.ID,
		WorkflowNo:           detailData.Workflow.WorkflowNo,
		WorkflowName:         detailData.Workflow.WorkflowName,
		WorkflowTemplateId:   detailData.Workflow.WorkflowTemplateID,
		WorkflowTemplateName: detailData.Workflow.WorkflowTemplateName,
		Status:               workflow.WorkflowStatus(detailData.Workflow.Status),
		InnerStatus:          workflow.InnerStatus(detailData.Workflow.InnerStatus),
		BusinessType:         workflow.WorkflowBusinessType(detailData.Workflow.BusinessType),
		BusinessName:         detailData.Workflow.BusinessName,
		OrderId:              detailData.Workflow.OrderID,
		OrderNo:              detailData.Workflow.OrderNo,
		GoodsId:              detailData.Workflow.ProductID,
		ServiceName:          detailData.Workflow.ServiceName,
		CreatedAt:            getUnixTime(detailData.Workflow.CreatedAt),
		UpdatedAt:            getUnixTime(detailData.Workflow.UpdatedAt),
		DeletedAt:            getUnixTime(detailData.Workflow.DeletedAt),
		DispatchedAt:         getUnixTime(detailData.Workflow.DispatchedAt),
		ReceivedAt:           getUnixTime(detailData.Workflow.ReceivedAt),
		PausedAt:             getUnixTime(detailData.Workflow.PausedAt),
		TerminatedAt:         getUnixTime(detailData.Workflow.TerminatedAt),
		CompletedAt:          getUnixTime(detailData.Workflow.CompletedAt),
		DueAt:                getUnixTime(detailData.Workflow.DueAt),
		MessageBoardId:       detailData.Workflow.MessageBoardID,
		OrderMessageBoardId:  detailData.OrderMessageBoardId,
		QqImGroupId:          detailData.Workflow.QqImGroupID,
		Remark:               detailData.Workflow.Remark,
		Nodes:                nil,
		CustomerProfile:      detailData.Workflow.CustomerProfile,
		Version:              detailData.Workflow.Version,
	}

	if detailData.OrderGoodsInfo != nil {
		workflowEntity.GoodsName = detailData.OrderGoodsInfo.GetGoodsName()
		workflowEntity.GoodsNum = detailData.OrderGoodsInfo.GetGoodsNum()
		workflowEntity.GoodsSpec = detailData.OrderGoodsInfo.GetGoodsSpec()
	}

	if detailData.Customer != nil {
		workflowEntity.Customer = detailData.Customer
		workflowEntity.CustomerProfile = detailData.Customer.GetProfile()
	}

	// 设置员工信息
	if detailData.EmployeeMap != nil {
		if creator, ok := detailData.EmployeeMap[detailData.Workflow.CreatedBy]; ok {
			workflowEntity.Creator = creator
		}
		if updater, ok := detailData.EmployeeMap[detailData.Workflow.UpdatedBy]; ok {
			workflowEntity.Updater = updater
		}
	}

	// 设置节点信息
	workflowEntity.Nodes = make([]*workflow.WorkflowNodeEntity, 0)
	if templateService != nil && detailData.Nodes != nil {
		workflowEntity.Nodes = processNodes(ctx, templateService, templateId, detailData.Nodes, detailData.NodeTasks, detailData.EmployeeMap)
	}

	// 设置响应
	resp.Workflow = workflowEntity
	resp.Steps = steps

	return resp, nil
}

// processNodes 处理节点信息
func processNodes(ctx context.Context, w *WorkflowTemplateService, templateId int64, nodes []*model.WorkflowNode, nodeTasks map[int64][]*model.WorkflowNodeTask, employeeMap map[int64]*workflow.EmployeeData) []*workflow.WorkflowNodeEntity {
	if len(nodes) == 0 {
		return make([]*workflow.WorkflowNodeEntity, 0)
	}
	if len(nodeTasks) == 0 {
		nodeTasks = make(map[int64][]*model.WorkflowNodeTask)
	}

	nodeEntities := make([]*workflow.WorkflowNodeEntity, 0, len(nodes))
	for _, node := range nodes {
		nextList := str2Slice(node.Next, ",")
		previewList := str2Slice(node.Preview, ",")

		var simpleNextList []*workflow.SimpleWorkflowNodeEntity
		var simplePreviewList []*workflow.SimpleWorkflowNodeEntity
		nodeTemplate, err := w.LoadWorkflowTemplate(templateId)
		if err != nil {
			logger.CtxWarnf(ctx, "加载工作流模板失败: %v", err)
		} else {
			for _, next := range nextList {
				simpleNextList = append(simpleNextList, getWorkflowDetailConvertNode(next, nodeTemplate.Workflow.Nodes))
			}
			for _, preview := range previewList {
				simplePreviewList = append(simplePreviewList, getWorkflowDetailConvertNode(preview, nodeTemplate.Workflow.Nodes))
			}
		}

		// 获取处理人和派发人信息
		var dispatcher, processor *workflow.EmployeeData
		if node.DispatchedBy != 0 {
			if d, exists := employeeMap[node.DispatchedBy]; exists {
				dispatcher = d
				dispatcher.DeptNameSnapshot = node.DispatchedByDeptSnapshot
				dispatcher.RoleNameSnapshot = node.DispatchedByRoleSnapshot
			}
		}
		if node.ProcessedBy != 0 {
			if p, exists := employeeMap[node.ProcessedBy]; exists {
				processor = p
				processor.DeptNameSnapshot = node.ProcessedByDeptSnapshot
				processor.RoleNameSnapshot = node.ProcessedByRoleSnapshot
			}
		}

		nodeEntity := &workflow.WorkflowNodeEntity{
			Id:            node.ID,
			Name:          node.Name,
			ChineseName:   node.ChineseName,
			Status:        workflow.WorkflowNodeStatus(node.Status),
			Type:          workflow.WorkflowNodeType(node.Type),
			Tasks:         models.TaskModelList2EntityList(nodeTasks[node.ID]),
			Next:          simpleNextList,
			Preview:       simplePreviewList,
			Dispatcher:    dispatcher,
			Processor:     processor,
			SyncNode:      constants.YesNo(node.SyncNode),
			AllowRoleList: node.AllowRoleList,
			Branch:        node.Branch,
			// 添加时间相关字段
			CreatedAt:    getUnixTime(node.CreatedAt),
			StartedAt:    getUnixTime(node.StartedAt),
			UpdatedAt:    getUnixTime(node.UpdatedAt),
			DispatchedAt: getUnixTime(node.DispatchedAt),
			PausedAt:     getUnixTime(node.PausedAt),
			DeletedAt:    getUnixTime(node.DeletedAt),
			CompletedAt:  getUnixTime(node.CompletedAt),
			TerminatedAt: getUnixTime(node.TerminatedAt),
			DueAt:        getUnixTime(node.DueAt),
		}
		nodeEntities = append(nodeEntities, nodeEntity)
	}
	return nodeEntities
}

// getWorkflowDetailConvertNode 获取工作流节点模板信息
func getWorkflowDetailConvertNode(key string, nodeTemplate map[string]WorkflowNodeTemplate) *workflow.SimpleWorkflowNodeEntity {
	if nt, ok := nodeTemplate[key]; ok {
		return &workflow.SimpleWorkflowNodeEntity{
			Name:        key,
			ChineseName: nt.Name,
			RoleName:    strings.Join(nt.Role, ","),
		}
	}
	return nil
}

func WorkflowDetail(ctx context.Context, req *workflow.WorkflowDetailReq) (resp *workflow.WorkflowDetailRsp, err error) {
	resp = &workflow.WorkflowDetailRsp{}

	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "获取工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}

	return workflowService.workflowDetail(ctx, req)
}

func (w *WorkflowService) workflowDetail(ctx context.Context, req *workflow.WorkflowDetailReq) (resp *workflow.WorkflowDetailRsp, err error) {
	resp = &workflow.WorkflowDetailRsp{}

	wa, err := w.workflowRepo.WorkflowDetail(ctx, req.Id, req.No)
	if err != nil {
		logger.CtxErrorf(ctx, "获取工单详情失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	if wa == nil {
		logger.CtxErrorf(ctx, "工单不存在: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowNotFound)
		return resp, nil
	}

	steps, err := w.nodeService.workflowNodeRepo.GetCurrentLatestNodes(ctx, wa.Workflow.ID)
	if err != nil {
		logger.CtxErrorf(ctx, "获取当前最新节点失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowNodeNotFound)
		return resp, nil
	}

	return buildWorkflowInfo(wa, steps), nil
}

func GetWorkflowInfoByOrderId(ctx context.Context, req *workflow.GetWorkflowInfoByOrderIdReq) (resp *workflow.GetWorkflowInfoByOrderIdRsp, err error) {
	resp = &workflow.GetWorkflowInfoByOrderIdRsp{}

	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "获取工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}

	return workflowService.getWorkflowInfoByOrderId(ctx, req)
}

// ======================================= 根据订单ID获取工单的简单信息========================================
func (w *WorkflowService) getWorkflowInfoByOrderId(ctx context.Context, req *workflow.GetWorkflowInfoByOrderIdReq) (resp *workflow.GetWorkflowInfoByOrderIdRsp, err error) {
	resp = &workflow.GetWorkflowInfoByOrderIdRsp{}
	workflowList, total, err := w.workflowRepo.WorkflowListByOrderIdWithoutCtxV1(ctx, &models.WorkflowListFilter{
		OrderId: req.GetOrderId(),
	})
	if err != nil {
		logger.CtxErrorf(ctx, "通过订单ID获取工单信息失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	if len(workflowList) == 0 {
		resp.WorkflowInfoList = make([]*workflow.WorkflowInfoEntity, 0)
		resp.Total = 0
		logger.CtxWarnf(ctx, "没有找到工单")
		return resp, nil
	}

	resp.WorkflowInfoList = modelList2WorkflowEntityList(workflowList)
	resp.Total = total

	return resp, nil
}
