package services

import (
	"context"
	"fmt"
	"strconv"
	"sync"
	"time"

	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/kitex_gen/server/cmd/workflow"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/ctxmeta"
	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/workflow/internal/global"
	"uofferv2/server/cmd/workflow/internal/services/rpcs"
)

func CreateWorkflowByOrder(ctx context.Context, req *workflow.CreateWorkflowByOrderReq) (resp *workflow.CreateWorkflowByOrderRsp, err error) {
	resp = &workflow.CreateWorkflowByOrderRsp{}

	tx := global.DB.Begin()
	if tx.Error != nil {
		logger.CtxErrorf(ctx, "创建事务失败: %v", tx.Error)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logger.CtxErrorf(ctx, "创建工单时发生panic: %v", r)
		} else if tx.Error != nil || (resp != nil && resp.GetBase() != nil && resp.GetBase().GetCode() != 0) {
			tx.Rollback()
			logger.CtxErrorf(ctx, "创建工单时提交事务失败: %v, err: %v", tx.Error, err)
		} else {
			tx.Commit()
			logger.CtxDebugf(ctx, "创建工单时提交事务成功")
		}
	}()

	// 使用事务创建工作流服务实例
	workflowServiceWithTx, err := InitializeWorkflowServiceWithTx(tx)
	if err != nil {
		logger.CtxErrorf(ctx, "初始化工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}

	// 调用服务方法
	resp, err = workflowServiceWithTx.createWorkflowByOrder(ctx, req)
	return resp, err
}

// createWorkflowByOrder 根据订单创建工单实例(不包括节点)
func (w *WorkflowService) createWorkflowByOrder(ctx context.Context, req *workflow.CreateWorkflowByOrderReq) (resp *workflow.CreateWorkflowByOrderRsp, err error) {
	resp = new(workflow.CreateWorkflowByOrderRsp)
	newAuthInfo := ctxmeta.MustGetAuth(ctx)
	creatorId := newAuthInfo.EmployeeId()
	processorId := req.GetProcessorId()
	customerId := req.GetCustomerId()
	goodsInfoList := req.GetGoodsInfo()

	// 1. 并行获取所需信息
	var (
		orderInfo                          *order.OrderEntity
		orderGoodsListInfo                 []*order.OrderGoodsEntity
		employeeMap                        map[int64]*workflow.EmployeeData
		customerInfo                       *workflow.CustomerData
		wg                                 sync.WaitGroup
		orderErr, employeeErr, customerErr error
		mu                                 sync.Mutex
	)

	wg.Add(3)
	// 获取订单信息
	go func() {
		defer wg.Done()
		var tempOrderInfo *order.OrderEntity
		var tempOrderGoodsListInfo []*order.OrderGoodsEntity
		var tempOrderErr error
		tempOrderInfo, tempOrderGoodsListInfo, _, tempOrderErr = rpcs.GetOrderDetailsByOrderId(ctx, req.GetOrderId())
		mu.Lock()
		orderInfo, orderGoodsListInfo, orderErr = tempOrderInfo, tempOrderGoodsListInfo, tempOrderErr
		mu.Unlock()
	}()

	// 获取员工信息
	go func() {
		defer wg.Done()
		var tempEmployeeMap map[int64]*workflow.EmployeeData
		var tempErr error
		tempEmployeeMap, tempErr = rpcs.GetEmployeeInfoByIds(ctx, []int64{creatorId, processorId})
		mu.Lock()
		employeeMap, employeeErr = tempEmployeeMap, tempErr
		mu.Unlock()
	}()

	// 获取客户信息
	go func() {
		defer wg.Done()
		var tempCustomerInfo *workflow.CustomerData
		var tempErr error
		tempCustomerInfo, tempErr = rpcs.GetCustomerInfo(ctx, customerId)
		mu.Lock()
		customerInfo, customerErr = tempCustomerInfo, tempErr
		mu.Unlock()
	}()

	wg.Wait()

	// 检查错误
	if orderErr != nil {
		logger.CtxErrorf(ctx, "获取订单信息失败: %v", orderErr)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_OrderNotFound)
		return resp, nil
	}

	if employeeErr != nil {
		logger.CtxErrorf(ctx, "获取员工信息失败: %v", employeeErr)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_EmployeeNotExists)
		return resp, nil
	}

	if customerErr != nil {
		logger.CtxErrorf(ctx, "获取客户信息失败: %v", customerErr)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_CustomerNotFound)
		return resp, nil
	}

	// 创建工单附加信息
	wa := buildWorkflowAttachments(ctx, req)
	if err = w.workflowAttachmentsRepo.CreateOrUpdateAttachments(ctx, wa); err != nil {
		logger.CtxErrorf(ctx, "创建工单附加信息失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowAttachmentsCreateFailed)
		return resp, nil
	}

	// 检查员工信息是否存在
	creatorInfo, ok := employeeMap[creatorId]
	if !ok || creatorInfo == nil {
		logger.CtxErrorf(ctx, "creator员工信息不存在: %v", creatorId)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_EmployeeNotExists)
		return resp, nil
	}
	processorInfo, ok := employeeMap[processorId]
	if !ok || processorInfo == nil {
		logger.CtxErrorf(ctx, "processor员工信息不存在: %v", processorId)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_EmployeeNotExists)
		return resp, nil
	}
	// 构建工单模型列表
	wfList := buildWorkflowModelList(ctx, processorId, customerId, creatorInfo,
		processorInfo, orderInfo, orderGoodsListInfo, goodsInfoList)

	// 批量创建工单
	if err = w.batchCreateWorkflowWithStartNode(ctx, wfList, creatorId, processorId, employeeMap); err != nil {
		logger.CtxErrorf(ctx, "创建工单失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowCreateFailed)
		return resp, nil
	}

	// 为每个工单创建留言板
	if err = w.createMessageBoardsForWorkflows(ctx, wfList, customerInfo, processorId); err != nil {
		logger.CtxErrorf(ctx, "创建工单留言板失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowMessageBoardCreateFailed)
		return resp, nil
	}

	resp = buildCreateWorkflowByOrderRsp(ctx, wfList)
	return resp, nil
}

func (w *WorkflowService) batchCreateWorkflowWithStartNode(ctx context.Context, wfList []*model.Workflow, dispatchId, processorId int64, employeeMap map[int64]*workflow.EmployeeData) error {
	// 批量创建工单
	if err := w.workflowRepo.BatchCreate(ctx, wfList); err != nil {
		logger.CtxErrorf(ctx, "创建工单失败: %v", err)
		return err
	}

	// 批量创建start节点
	if err := w.nodeService.BatchCreateStartNodes(ctx, wfList, dispatchId, processorId, employeeMap); err != nil {
		logger.CtxErrorf(ctx, "创建start节点失败: %v", err)
		return err
	}
	return nil
}

// buildWorkflowModelList 构建工单模型列表
func buildWorkflowModelList(ctx context.Context, processorId, customerId int64,
	creatorInfo, processorInfo *workflow.EmployeeData,
	orderInfo *order.OrderEntity, orderGoodsListInfo []*order.OrderGoodsEntity,
	goodsInfoList []*workflow.WorkflowGoodsInfo,
) []*model.Workflow {
	// 预先分配足够的容量以避免多次扩容
	wfList := make([]*model.Workflow, 0, len(goodsInfoList))

	// 使用函数创建键以确保一致性
	createKeyForOrderGoods := func(orderGoods *order.OrderGoodsEntity) string {
		return fmt.Sprintf("%d-%d-%d", orderGoods.GoodsId, orderGoods.SpecId, int(orderGoods.GoodsType))
	}

	createKeyForGoodsInfo := func(goodsInfo *workflow.WorkflowGoodsInfo) string {
		return fmt.Sprintf("%d-%d-%d", goodsInfo.GoodsId, goodsInfo.SpecId, goodsInfo.GoodsType)
	}

	// 构建订单商品映射，提高查找效率
	orderGoodsMap := make(map[string]*order.OrderGoodsEntity, len(orderGoodsListInfo))
	for _, orderGoods := range orderGoodsListInfo {
		key := createKeyForOrderGoods(orderGoods)
		orderGoodsMap[key] = orderGoods
	}

	// 遍历商品信息列表，构建工单模型
	for _, goodsInfo := range goodsInfoList {
		key := createKeyForGoodsInfo(goodsInfo)
		if orderGoods, exists := orderGoodsMap[key]; exists {
			wf := buildWorkflowModel(ctx, processorId, customerId, creatorInfo, processorInfo, orderInfo, orderGoods, goodsInfo)
			wfList = append(wfList, wf)
		}
	}

	return wfList
}

// createMessageBoardsForWorkflows 为工单创建留言板
func (w *WorkflowService) createMessageBoardsForWorkflows(ctx context.Context, wfList []*model.Workflow,
	customerInfo *workflow.CustomerData, processorId int64,
) error {
	for _, wf := range wfList {
		// 为每个工单创建留言板
		messageRsp, err := rpcs.CreateMessageBoard(ctx, wf.WorkflowName, customerInfo.Id,
			customerInfo.GetName(), []int64{wf.CreatedBy, processorId}, wf.ID, wf.Status)
		errWrap := coderror.Warp(messageRsp, err)
		if errWrap != nil {
			logger.CtxErrorf(ctx, "global.MessageClient.CreateMessageBoard rpc error: %v", errWrap.Error())
			return errWrap
		}
		wf.MessageBoardID = messageRsp.GetMessageBoardId()
		if err = w.workflowRepo.UpdateMessageBoardId(ctx, wf.ID, messageRsp.GetMessageBoardId()); err != nil {
			logger.CtxErrorf(ctx, "更新留言板id失败: %v", err)
			return err
		}
	}

	return nil
}

// buildWorkflowAttachments 构建工单附件模型
func buildWorkflowAttachments(ctx context.Context, req *workflow.CreateWorkflowByOrderReq) *model.WorkflowAttachment {
	now := time.Now()
	wa := &model.WorkflowAttachment{
		OrderID:                           req.GetOrderId(),
		CustomerID:                        req.GetCustomerId(),
		BusinessType:                      int32(req.GetDispatchBusinessType()),
		ProcessorID:                       req.GetProcessorId(),
		UofferAppInfo:                     req.GetUofferAppInfo(),
		PastExperience:                    req.GetPastExperience(),
		SpecialApplication:                req.GetSpecialApplication(),
		IsDesignatedInstitution:           int32(req.GetIsDesignatedInstitution()),
		InstitutionName:                   req.GetInstitutionName(),
		IsCommitmentSuccessRate:           int32(req.GetIsCommitmentSuccessRate()),
		CommitmentFileLinks:               coverEntity2Json(ctx, req.GetCommitmentFileLinks()),
		IsGuaranteedCollegeMajor:          int32(req.GetIsGuaranteedCollegeMajor()),
		IsNativeDocument:                  int32(req.GetIsNativeDocument()),
		IsNativeCommunication:             int32(req.GetIsNativeCommunication()),
		IsDocumentBackground:              int32(req.GetIsDocumentBackground()),
		IsCommissionerBackground:          int32(req.GetIsCommissionerBackground()),
		CvFileLinks:                       coverEntity2Json(ctx, req.GetCvFileLinks()),
		UgApplicationLevel:                int32(req.GetUgApplicationLevel()),
		UgHomeReturnPermit:                int32(req.GetUgHomeReturnPermit()),
		UgExtraFreeService:                req.GetUgExtraFreeService(),
		UgCommissionerInfo:                req.GetUgCommissionerInfo(),
		UgThirdPartyInfo:                  req.GetUgThirdPartyInfo(),
		UgGuaranteedCollegeMajorFileLinks: coverEntity2Json(ctx, req.GetUgGuaranteedCollegeMajorFileLinks()),
		UgNativeDocumentFileLinks:         coverEntity2Json(ctx, req.GetUgNativeDocumentFileLinks()),
		UgNativeCommunicationFileLinks:    coverEntity2Json(ctx, req.GetUgNativeCommunicationFileLinks()),
		UgDocumentBackgroundFileLinks:     coverEntity2Json(ctx, req.GetUgDocumentBackgroundFileLinks()),
		UgCommissionerBackgroundFileLinks: coverEntity2Json(ctx, req.GetUgCommissionerBackgroundFileLinks()),
		UgIsNoLanguage:                    int32(req.GetUgIsNoLanguage()),
		UgNoLanguageFileLinks:             coverEntity2Json(ctx, req.GetUgNoLanguageFileLinks()),
		PmApplicationLevel:                int32(req.GetPmApplicationLevel()),
		PmClosenessLetter:                 int32(req.GetPmClosenessLetter()),
		PmGuaranteedCollegeName:           req.GetPmGuaranteedCollegeName(),
		PmDocumentBackground:              req.GetPmDocumentBackground(),
		PmCommissionerBackground:          req.GetPmCommissionerBackground(),
		PmIsIntroduceClosenessLetter:      int32(req.GetPmIsIntroduceClosenessLetter()),
		PmIsPrTutorship:                   int32(req.GetPmIsPrTutorship()),
		PmIsSelectTeacher:                 int32(req.GetPmIsSelectTeacher()),
		CoPrSubmit:                        req.GetCoPrSubmit(),
		CoPrWord:                          req.GetCoPrWord(),
		CoRefStyle:                        req.GetCoRefStyle(),
		CoIsSelectGuide:                   int32(req.GetCoIsSelectGuide()),
		SupplementsInfo:                   req.GetSupplementsInfo(),
		SupplementsFileLinks:              coverEntity2Json(ctx, req.GetSupplementsFileLinks()),
		CreatedAt:                         now,
		UpdatedAt:                         now,
	}
	if req.GetPmTutoringStartedAt() != 0 {
		tutoringStartedAt := time.UnixMilli(req.GetPmTutoringStartedAt())
		wa.PmTutoringStartedAt = &tutoringStartedAt
	}
	if req.GetSelectionExpectedEntry() != 0 {
		selectionExpectedEntry := time.UnixMilli(req.GetSelectionExpectedEntry())
		wa.SelectionExpectedEntry = &selectionExpectedEntry
	}
	logger.CtxDebugf(ctx, "构建工单附加信息成功")
	return wa
}

// buildWorkflowModel 构建工单模型
func buildWorkflowModel(ctx context.Context, processorId, customerId int64, creatorInfo, processorInfo *workflow.EmployeeData, orderInfo *order.OrderEntity, orderGoodsInfo *order.OrderGoodsEntity, goods_info *workflow.WorkflowGoodsInfo) *model.Workflow {
	workflowName := generateWorkflowName(orderInfo.ServiceName, orderGoodsInfo.GetGoodsName(), orderGoodsInfo.GetGoodsSpec())
	// 1. 构建工单模型
	wf := &model.Workflow{
		WorkflowNo:          generateWorkflowNo(),
		WorkflowName:        workflowName,
		OrderID:             orderInfo.GetId(),
		OrderNo:             orderInfo.GetOrderNo(),
		ProductID:           orderGoodsInfo.GetGoodsId(),
		ProductName:         orderGoodsInfo.GetGoodsName(),
		ServiceName:         orderInfo.GetServiceName(),
		CreatedBy:           creatorInfo.GetId(),
		CreatedRoleSnapshot: creatorInfo.GetRoleName(),
		CreatedDeptSnapshot: creatorInfo.GetDeptName(),
		OwnerBy:             creatorInfo.GetId(),
		UpdatedBy:           processorInfo.GetId(),
		UpdatedRoleSnapshot: processorInfo.GetRoleName(),
		UpdatedDeptSnapshot: processorInfo.GetDeptName(),
		Processors:          strconv.FormatInt(processorId, 10),
		CustomerID:          customerId,
		CustomerProfile:     "",
		SpecID:              goods_info.GetSpecId(),
		GoodsType:           goods_info.GetGoodsType(),
		GoodsSpecIndex:      goods_info.GetGoodsIndex(),
	}
	logger.CtxDebugf(ctx, "构建工单模型成功")
	return wf
}

// buildCreateWorkflowByOrderRsp 构建创建工单响应
func buildCreateWorkflowByOrderRsp(ctx context.Context, workflowList []*model.Workflow) *workflow.CreateWorkflowByOrderRsp {
	resp := &workflow.CreateWorkflowByOrderRsp{}
	wfList := make([]*workflow.SimpleWorkflowEntity, 0, len(workflowList))
	for _, wf := range workflowList {
		wfList = append(wfList, &workflow.SimpleWorkflowEntity{
			WorkflowId:   wf.ID,
			WorkflowNo:   wf.WorkflowNo,
			WorkflowName: wf.WorkflowName,
		})
	}
	resp.WorkflowList = wfList
	logger.CtxDebugf(ctx, "构建创建工单响应成功")
	return resp
}

func WorkflowServiceDone(ctx context.Context, req *workflow.WorkflowServiceDoneReq) (resp *workflow.WorkflowServiceDoneRsp, err error) {
	resp = &workflow.WorkflowServiceDoneRsp{}

	tx := global.DB.Begin()
	if tx.Error != nil {
		logger.CtxErrorf(ctx, "创建事务失败: %v", tx.Error)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logger.CtxErrorf(ctx, "工单服务完成时发生panic: %v", r)
		} else if tx.Error != nil || (resp != nil && resp.GetBase() != nil && resp.GetBase().GetCode() != 0) {
			tx.Rollback()
			logger.CtxErrorf(ctx, "工单服务完成时提交事务失败: %v, err: %v", tx.Error, err)
		} else {
			tx.Commit()
			logger.CtxDebugf(ctx, "工单服务完成时提交事务成功")
		}
	}()

	// 使用事务创建工作流服务实例
	workflowServiceWithTx, err := InitializeWorkflowServiceWithTx(tx)
	if err != nil {
		logger.CtxErrorf(ctx, "初始化工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}
	resp, err = workflowServiceWithTx.workflowServiceDone(ctx, req)
	return resp, err
}

func (w *WorkflowService) workflowServiceDone(ctx context.Context, req *workflow.WorkflowServiceDoneReq) (resp *workflow.WorkflowServiceDoneRsp, err error) {
	resp = new(workflow.WorkflowServiceDoneRsp)
	workflowId := req.GetWorkflowId()
	workflowNodeId := req.GetWorkflowNodeId()
	var updateId int64
	newAuth := ctxmeta.MustGetAuth(ctx)
	if newAuth.IsCustomer() {
		logger.CtxErrorf(ctx, "customer不能完成工单")
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_AuthForbidden)
		return resp, nil
	}

	if newAuth.IsEmployee() {
		updateId = newAuth.EmployeeId()
	}

	processInfo, err := rpcs.GetEmployeeInfo(ctx, updateId)
	if err != nil {
		logger.CtxErrorf(ctx, "global.UserServiceClient.GetEmployeeInfo错误: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	node, err := w.nodeService.workflowNodeRepo.GetByWorkflowIDAndNodeId(ctx, workflowId, workflowNodeId)
	if err != nil {
		logger.CtxErrorf(ctx, "获取工单节点失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	allPreviewNodesCompleted, incompleteNodeNames, err := w.nodeService.workflowNodeRepo.CheckServiceDonePreviewComplete(ctx, node)
	if err != nil {
		logger.CtxErrorf(ctx, "检查服务完成预览失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowNodePreviewNotCompleted)
		return resp, nil
	}

	if allPreviewNodesCompleted {
		finishNode, err := w.nodeService.workflowNodeRepo.FinishWorkflowNodeV1(ctx, workflowNodeId, processInfo)
		if err != nil {
			logger.CtxErrorf(ctx, "完成工单节点失败: %v", err)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
			return resp, nil
		}

		wf, err := w.workflowRepo.WorkflowServiceDone(ctx, workflowId, finishNode)
		if err != nil {
			logger.CtxErrorf(ctx, "更新工单状态和版本失败: %v", err)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
			return resp, nil
		}

		// 留言板通过cdc已自行处理
		//required := w.workflowRepo.CheckWorkflowStatus(ctx, wf.QqImGroupID, int32(workflow.WorkflowStatus_WORKFLOW_STATUS_COMPLETED))
		//if required {
		//	logger.CtxInfof(ctx, "workflow status is paused, set group %s status to paused", wf.QqImGroupID)
		//	if err := rpcs.SetGroupStatus(ctx, wf.QqImGroupID, wf.CustomerID, workflow.WorkflowStatus_WORKFLOW_STATUS_PAUSED); err != nil {
		//		logger.CtxErrorf(ctx, "SetGroupStatus error: %v", err)
		//		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		//		return resp, err
		//	}
		//}
		resp.WorkflowId = wf.ID
	} else {
		resp.IncompleteNode = incompleteNodeNames
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowNodePreviewNotCompleted)
		logger.CtxErrorf(ctx, "有未完成的工单节点：%v", incompleteNodeNames)
		return resp, nil
	}

	return resp, nil
}

func WorkflowServiceAbnormalDone(ctx context.Context, req *workflow.WorkflowServiceAbnormalDoneReq) (resp *workflow.WorkflowServiceAbnormalDoneRsp, err error) {
	resp = &workflow.WorkflowServiceAbnormalDoneRsp{}

	tx := global.DB.Begin()
	if tx.Error != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logger.CtxErrorf(ctx, "工单异常完成时发生panic: %v", r)
		} else if tx.Error != nil || (resp != nil && resp.GetBase() != nil && resp.GetBase().GetCode() != 0) {
			tx.Rollback()
			logger.CtxErrorf(ctx, "工单异常完成时提交事务失败: %v, err: %v", tx.Error, err)
		} else {
			tx.Commit()
			logger.CtxDebugf(ctx, "工单异常完成时提交事务成功")
		}
	}()

	// 使用事务创建工作流服务实例
	workflowServiceWithTx, err := InitializeWorkflowServiceWithTx(tx)
	if err != nil {
		logger.CtxErrorf(ctx, "初始化工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}

	resp, err = workflowServiceWithTx.workflowServiceAbnormalDone(ctx, req)
	return resp, err
}

func (w *WorkflowService) workflowServiceAbnormalDone(ctx context.Context, req *workflow.WorkflowServiceAbnormalDoneReq) (resp *workflow.WorkflowServiceAbnormalDoneRsp, err error) {
	resp = new(workflow.WorkflowServiceAbnormalDoneRsp)
	workflowId := req.GetWorkflowId()
	workflowNodeId := req.GetWorkflowNodeId()
	abnormalReason := req.GetAbnormalReason()
	abnormalNameCn := req.GetAbnormalNameCh()

	node, err := w.nodeService.workflowNodeRepo.TerminateWorkflowNode(ctx, workflowNodeId, abnormalNameCn, abnormalReason)
	if err != nil {
		logger.CtxErrorf(ctx, "终止工单节点失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	wf, err := w.workflowRepo.WorkflowServiceDone(ctx, workflowId, node)
	if err != nil {
		logger.CtxErrorf(ctx, "更新工单状态和版本失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	// 留言板已通过cdc自行处理
	//required := w.workflowRepo.CheckWorkflowStatus(ctx, wf.QqImGroupID, int32(workflow.WorkflowStatus_WORKFLOW_STATUS_COMPLETED))
	//if required {
	//	logger.CtxInfof(ctx, "workflow status is paused, set group %s status to paused", wf.QqImGroupID)
	//	if err := rpcs.SetGroupStatus(ctx, wf.QqImGroupID, wf.CustomerID, workflow.WorkflowStatus_WORKFLOW_STATUS_PAUSED); err != nil {
	//		logger.CtxErrorf(ctx, "SetGroupStatus error: %v", err)
	//		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
	//		return resp, err
	//	}
	//}

	resp.WorkflowId = wf.ID
	return resp, nil
}

func UpdateWorkflow(ctx context.Context, req *workflow.UpdateWorkflowReq) (resp *workflow.UpdateWorkflowRsp, err error) {
	resp = &workflow.UpdateWorkflowRsp{}

	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "获取工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}
	return workflowService.updateWorkflow(ctx, req)
}

func (w *WorkflowService) updateWorkflow(ctx context.Context, req *workflow.UpdateWorkflowReq) (resp *workflow.UpdateWorkflowRsp, err error) {
	resp = new(workflow.UpdateWorkflowRsp)
	newAuth := ctxmeta.MustGetAuth(ctx)
	if newAuth.IsCustomer() {
		logger.CtxErrorf(ctx, "Customer不能访问此接口")
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_AuthForbidden)
		return resp, nil
	}
	updateInfo, err := rpcs.GetEmployeeInfo(ctx, newAuth.EmployeeId())
	if err != nil {
		logger.CtxErrorf(ctx, "获取员工信息失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_EmployeeNotExists)
		return resp, nil
	}
	var updateWorkflow *model.Workflow
	data := &model.Workflow{
		WorkflowName:        req.GetWorkflowName(),
		Status:              int32(req.GetStatus()),
		InnerStatus:         int32(req.GetInnerStatus()),
		UpdatedBy:           newAuth.EmployeeId(),
		UpdatedRoleSnapshot: updateInfo.GetRoleName(),
		UpdatedDeptSnapshot: updateInfo.GetDeptName(),
		CustomerProfile:     req.GetCustomerProfile(),
		QqImGroupID:         req.GetQqImGroupId(),
		Remark:              req.GetRemark(),
	}
	updateWorkflow, err = w.workflowRepo.UpdateWorkflowInfo(ctx, req.GetId(), req.GetWorkflowNo(), data)
	if err != nil {
		logger.CtxErrorf(ctx, "更新工单信息失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	resp.Id = updateWorkflow.ID
	resp.WorkflowNo = updateWorkflow.WorkflowNo
	resp.WorkflowName = updateWorkflow.WorkflowName
	resp.Status = workflow.WorkflowStatus(updateWorkflow.Status)
	resp.InnerStatus = workflow.InnerStatus(updateWorkflow.InnerStatus)
	resp.Remark = updateWorkflow.Remark
	resp.QqImGroupId = updateWorkflow.QqImGroupID
	return resp, nil
}

func ContinueWorkflow(ctx context.Context, req *workflow.ContinueWorkflowReq) (resp *workflow.ContinueWorkflowRsp, err error) {
	resp = &workflow.ContinueWorkflowRsp{}
	if err = validateRequest(ctx, req); err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}
	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "获取工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}
	return workflowService.continueWorkflow(ctx, req)
}

func (w *WorkflowService) continueWorkflow(ctx context.Context, req *workflow.ContinueWorkflowReq) (resp *workflow.ContinueWorkflowRsp, err error) {
	resp = new(workflow.ContinueWorkflowRsp)
	workflowId := req.GetWorkflowId()
	workflowNodeId := req.GetWorkflowNodeId()

	var currentNode *model.WorkflowNode

	now := time.Now()
	// 1. 更新节点状态为已完成，更新节点完成时间
	wf, err := w.workflowRepo.GetByID(ctx, workflowId)
	if err != nil {
		logger.CtxErrorf(ctx, "获取工单失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowNotFound)
		return resp, nil
	}
	if currentNode, err = w.nodeService.workflowNodeRepo.FinishWorkflowNodeV1(ctx, workflowNodeId, nil); err != nil {
		logger.CtxErrorf(ctx, "完成工单节点失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	var employeeMap map[int64]*workflow.EmployeeData

	newAuthInfo := ctxmeta.MustGetAuth(ctx)
	updateId := newAuthInfo.EmployeeId()
	employeeIds := []int64{updateId, currentNode.ProcessedBy, currentNode.DispatchedBy, wf.CreatedBy, wf.UpdatedBy}

	employeeMap, err = rpcs.GetEmployeeInfoByIds(ctx, RemoveDuplicates(employeeIds, true))
	if err != nil {
		logger.CtxErrorf(ctx, "获取员工信息失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_EmployeeNotExists)
		return resp, nil
	}

	t, err := w.LoadAndValidateTemplate(ctx, wf.WorkflowTemplateID)
	if err != nil {
		logger.CtxErrorf(ctx, "加载和验证模板失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowTemplateNotFound)
		return resp, nil
	}
	if err = w.workflowRepo.FinishWorkflowNode(ctx, workflowId, currentNode, now); err != nil {
		logger.CtxErrorf(ctx, "完成工单节点失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	// 5. 更新下一级node的状态
	err = w.nodeService.updateNextNodes(ctx, currentNode, t, employeeMap, now)
	if err != nil {
		logger.CtxErrorf(ctx, "更新下一级节点状态失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	return resp, nil
}
