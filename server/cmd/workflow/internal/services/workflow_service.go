package services

import (
	"context"
	"fmt"
	"sync"

	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/workflow/internal/models"

	"gorm.io/gorm"
)

// WorkflowService 工作流服务
type WorkflowService struct {
	templateService         *WorkflowTemplateService
	nodeService             *WorkflowNodeService
	taskService             *WorkflowNodeTaskService
	workflowRepo            *models.WorkflowDao
	workflowApplicationRepo *models.WorkflowApplicationDao
	workflowAttachmentsRepo *models.WorkflowAttachmentsDao
	workflowGuidanceRepo    *models.WorkflowGuidanceDao
	workflowSingleRepo      *models.WorkflowSingleDao
}

// NewWorkflowService 创建工作流服务实例
func NewWorkflowService(
	templateService *WorkflowTemplateService,
	nodeService *WorkflowNodeService,
	taskService *WorkflowNodeTaskService,
	workflowRepo *models.WorkflowDao,
	workflowApplicationRepo *models.WorkflowApplicationDao,
	workflowAttachmentsRepo *models.WorkflowAttachmentsDao,
	workflowGuidanceRepo *models.WorkflowGuidanceDao,
	workflowSingleRepo *models.WorkflowSingleDao,
) *WorkflowService {
	return &WorkflowService{
		templateService:         templateService,
		nodeService:             nodeService,
		taskService:             taskService,
		workflowRepo:            workflowRepo,
		workflowApplicationRepo: workflowApplicationRepo,
		workflowAttachmentsRepo: workflowAttachmentsRepo,
		workflowGuidanceRepo:    workflowGuidanceRepo,
		workflowSingleRepo:      workflowSingleRepo,
	}
}

func InitializeWorkflowService() *WorkflowService {
	// 初始化任务服务
	taskService := NewWorkflowNodeTaskService(
		models.NewWorkflowNodeTaskDao(),
	)

	// 初始化节点服务
	nodeService := NewWorkflowNodeService(
		models.NewWorkflowNodeDao(),
		taskService,
	)

	// 初始化模板服务
	templateService := NewWorkflowTemplateService(&JsonFileTemplate{
		// FilePath: WorkflowTemplateFilePath,
	})

	ws := NewWorkflowService(
		templateService,
		nodeService,
		taskService,
		models.NewWorkflowDao(),
		models.NewWorkflowApplicationDao(),
		models.NewWorkflowAttachmentsDao(),
		models.NewWorkflowGuidanceDao(),
		models.NewWorkflowSingleDao(),
	)
	return ws
}

// InitializeWorkflowServiceWithTx 创建工作流服务实例(事务)
func InitializeWorkflowServiceWithTx(tx *gorm.DB) (*WorkflowService, error) {
	// 初始化任务服务
	taskService := NewWorkflowNodeTaskService(
		models.NewWorkflowNodeTaskDaoWithTx(tx),
	)

	// 初始化节点服务
	nodeService := NewWorkflowNodeService(
		models.NewWorkflowNodeDaoWithTx(tx),
		taskService,
	)

	// 初始化模板服务
	templateService := NewWorkflowTemplateService(&JsonFileTemplate{
		// FilePath: WorkflowTemplateFilePath,
	})

	ws := NewWorkflowService(
		templateService,
		nodeService,
		taskService,
		models.NewWorkflowDaoWithTx(tx),
		models.NewWorkflowApplicationDaoWithTx(tx),
		models.NewWorkflowAttachmentsDaoWithTx(tx),
		models.NewWorkflowGuidanceDaoWithTx(tx),
		models.NewWorkflowSingleDaoWithTx(tx),
	)

	if ws == nil {
		return nil, fmt.Errorf("failed to initialize workflow service")
	}

	return ws, nil
}

// validateRequest 通用请求验证
func validateRequest(ctx context.Context, req interface{}) error {
	if req == nil {
		logger.CtxErrorf(ctx, "request cannot be nil")
		return fmt.Errorf("request cannot be nil")
	}
	// other common validations
	return nil
}

// 全局变量定义
var (
	workflowServiceInstance *WorkflowService
	oc                      sync.Once
	initError               error
)

// GetWorkflowServiceSingleInstance 获取工作流服务单例
func GetWorkflowServiceSingleInstance() (*WorkflowService, error) {
	oc.Do(func() {
		workflowServiceInstance = InitializeWorkflowService()
		if workflowServiceInstance == nil {
			initError = fmt.Errorf("failed to initialize workflow service")
			logger.Errorf("%v", initError)
		}
	})
	return workflowServiceInstance, initError
}
