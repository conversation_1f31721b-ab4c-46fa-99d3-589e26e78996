package services

import (
	"context"
	"strings"
	"sync"

	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/workflow"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/ctxmeta"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/workflow/internal/global"
	"uofferv2/server/cmd/workflow/internal/models"
	"uofferv2/server/cmd/workflow/internal/services/rpcs"
)

func ListWorkflowDispatcher(ctx context.Context, req *workflow.ListWorkflowDispatcherReq) (resp *workflow.ListWorkflowDispatcherRsp, err error) {
	resp = &workflow.ListWorkflowDispatcherRsp{}

	// 获取服务实例
	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "获取工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}

	return workflowService.listWorkflowDispatcher(ctx, req)
}

// listWorkflowDispatcher 获取工单列表(工单视图):派单人列表
func (w *WorkflowService) listWorkflowDispatcher(ctx context.Context, req *workflow.ListWorkflowDispatcherReq) (resp *workflow.ListWorkflowDispatcherRsp, err error) {
	resp = new(workflow.ListWorkflowDispatcherRsp)
	var employeeId int64
	var employeeIDs []int64
	isWorkflow := ctxmeta.IsWorkflowFull(ctx)
	auth := ctxmeta.MustGetAuth(ctx)
	if auth.IsEmployee() {
		employeeId = auth.EmployeeId()
		// 1. 获取员工ID列表
		employeeIDs, err = rpcs.GetEmployeeIDs(ctx, employeeId)
		if err != nil {
			logger.CtxErrorf(ctx, "获取员工ID列表失败: %v", err)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_EmployeeNotExists)
			return resp, nil
		}
	}

	if auth.IsCustomer() {
		logger.CtxErrorf(ctx, "客户无法访问此接口")
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_AuthForbidden)
		return resp, nil
	}

	if isWorkflow {
		employeeIDs = []int64{}
	}

	// 2. 并发获取关联数据
	dispatcherData, err := fetchDispatcherData(ctx, w.workflowRepo, employeeIDs, req)
	if err != nil {
		logger.CtxErrorf(ctx, "获取派单人关联数据失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_ThirdPartyError)
		return resp, nil
	}

	// 3. 获取最终的员工ID列表
	var finalIDs []int64
	if req.GetFuzzyField() == "" {
		finalIDs = dispatcherData.DispatcherIDs
	} else if req.GetFuzzyField() != "" && len(dispatcherData.SearchIDs) == 0 {
		finalIDs = dispatcherData.SearchIDs
	} else {
		finalIDs = intersection(dispatcherData.SearchIDs, dispatcherData.DispatcherIDs)
	}
	// 计算总数
	total := int64(len(finalIDs))

	// 4. 获取员工详细信息
	employeeList, err := fetchEmployeeInfoList(ctx, finalIDs)
	if err != nil {
		logger.CtxErrorf(ctx, "获取派单人关联数据失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_EmployeeNotExists)
		return resp, nil
	}
	// 5. 构建响应
	resp = buildListWorkflowDispatcherResponse(ctx, employeeList, int64(total))
	return resp, nil
}

// DispatcherData 派单人关联数据结构
type DispatcherData struct {
	SearchIDs     []int64
	DispatcherIDs []int64
	Total         int64
}

// fetchDispatcherData 并发获取派单人数据
func fetchDispatcherData(ctx context.Context, workflowRepo *models.WorkflowDao, employeeIDs []int64, req *workflow.ListWorkflowDispatcherReq) (*DispatcherData, error) {
	var (
		wg       sync.WaitGroup
		mu       sync.Mutex
		fetchErr error
		data     = &DispatcherData{}
	)

	wg.Add(2)

	// 模糊搜索协程
	go func() {
		defer wg.Done()
		if fuzzyField := strings.TrimSpace(req.GetFuzzyField()); fuzzyField != "" {
			ids, err := rpcs.GetEmployeeIdsByFuzzyKey(ctx, fuzzyField)
			mu.Lock()
			if err != nil {
				fetchErr = err
			} else {
				data.SearchIDs = ids
			}
			mu.Unlock()
		}
	}()

	// 获取派单人列表协程
	go func() {
		defer wg.Done()
		ids, total, err := workflowRepo.ListWorkflowDispatcherV1(
			ctx,
			employeeIDs,
			req.GetWorkflowStatus(),
			int(req.GetPageNum()),
			int(req.GetPageSize()),
		)
		mu.Lock()
		if err != nil {
			fetchErr = err
		} else {
			data.DispatcherIDs = ids
			data.Total = total
		}
		mu.Unlock()
	}()

	wg.Wait()

	if fetchErr != nil {
		return nil, fetchErr
	}

	return data, nil
}

func fetchEmployeeInfoList(ctx context.Context, employeeIDs []int64) (employeeList []*workflow.EmployeeData, err error) {
	if len(employeeIDs) == 0 {
		return nil, nil
	}

	employeeMap, err := rpcs.GetEmployeeInfoByIds(ctx, RemoveDuplicates(employeeIDs, true))
	if err != nil {
		logger.CtxErrorf(ctx, "通过ID获取员工信息失败: %v", err)
		return nil, err
	}

	for _, employee := range employeeMap {
		employeeList = append(employeeList, employee)
	}
	logger.CtxDebugf(ctx, "获取员工信息成功: %v", employeeList)
	return employeeList, nil
}

// buildListWorkflowDispatcherResponse 构建响应
func buildListWorkflowDispatcherResponse(ctx context.Context, employeeList []*workflow.EmployeeData, total int64) *workflow.ListWorkflowDispatcherRsp {
	items := make([]*workflow.ListWorkflowDispatcherRsp_Item, 0, len(employeeList))
	for _, employee := range employeeList {
		items = append(items, &workflow.ListWorkflowDispatcherRsp_Item{
			Id:       employee.GetId(),
			Name:     employee.GetName(),
			Phone:    employee.GetPhone(),
			Email:    employee.GetEmail(),
			DeptName: employee.GetDeptName(),
		})
	}
	logger.CtxDebugf(ctx, "构建派单人列表响应成功: %v", items)
	return &workflow.ListWorkflowDispatcherRsp{
		Items: items,
		Total: total,
	}
}

func ListWorkflowProcessor(ctx context.Context, req *workflow.ListWorkflowProcessorReq) (resp *workflow.ListWorkflowProcessorRsp, err error) {
	resp = &workflow.ListWorkflowProcessorRsp{}

	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "获取工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}
	return workflowService.listWorkflowProcessor(ctx, req)
}

func (w *WorkflowService) listWorkflowProcessor(ctx context.Context, req *workflow.ListWorkflowProcessorReq) (resp *workflow.ListWorkflowProcessorRsp, err error) {
	resp = new(workflow.ListWorkflowProcessorRsp)

	var employeeId int64
	var employeeIDs []int64
	isWorkflow := ctxmeta.IsWorkflowFull(ctx)
	auth := ctxmeta.MustGetAuth(ctx)
	if auth.IsEmployee() {
		employeeId = auth.EmployeeId()
		// 1. 获取员工ID列表
		employeeIDs, err = rpcs.GetEmployeeIDs(ctx, employeeId)
		if err != nil {
			logger.CtxErrorf(ctx, "获取员工ID列表失败: %v", err)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_EmployeeNotExists)
			return resp, nil
		}
	}

	if auth.IsCustomer() {
		logger.CtxErrorf(ctx, "客户无法访问此接口")
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_AuthForbidden)
		return resp, nil
	}

	if isWorkflow {
		employeeIDs = []int64{}
	}

	// 2. 并发获取处理人列表
	processorData, err := fetchProcessorData(ctx, w.workflowRepo, employeeIDs, req)
	if err != nil {
		logger.CtxErrorf(ctx, "获取处理人关联数据失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_EmployeeNotExists)
		return resp, nil
	}

	// 3. 获取最终的员工ID列表
	var finalIDs []int64
	if req.GetFuzzyField() == "" {
		finalIDs = processorData.ProcessorIds
	} else if req.GetFuzzyField() != "" && len(processorData.SearchIDs) == 0 {
		finalIDs = processorData.SearchIDs
	} else {
		finalIDs = intersection(processorData.SearchIDs, processorData.ProcessorIds)
	}

	logger.CtxDebugf(ctx, "从数据库获取 %d 条记录: %v", finalIDs)

	// 4. 获取员工详细信息
	employeeList, err := fetchEmployeeInfoList(ctx, finalIDs)
	if err != nil {
		logger.CtxErrorf(ctx, "获取处理人关联数据失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_EmployeeNotExists)
		return resp, nil
	}

	total := len(finalIDs)
	resp = buildListWorkflowProcessorResponse(ctx, employeeList, int64(total))
	return resp, nil
}

// 获取处理人ID列表
func fetchProcessorIdsByDao(ctx context.Context, workflowRepo *models.WorkflowDao, req *workflow.ListWorkflowProcessorReq, employeeIDs []int64) (processorIds []int64, total int64, err error) {
	return workflowRepo.ListProcessor(ctx, employeeIDs, req.GetWorkflowStatus(), req.GetWorkflowTaskViewStatus(), int(req.GetPageNum()), int(req.GetPageSize()))
}

// ProcessorData 处理人关联数据结构
type ProcessorData struct {
	SearchIDs    []int64
	ProcessorIds []int64
	total        int64
}

// fetchDispatcherData 并发获取处理人数据
func fetchProcessorData(ctx context.Context, workflowRepo *models.WorkflowDao, employeeIDs []int64, req *workflow.ListWorkflowProcessorReq) (*ProcessorData, error) {
	var (
		wg       sync.WaitGroup
		mu       sync.Mutex
		fetchErr error
		data     = &ProcessorData{}
	)

	wg.Add(2)

	// 模糊搜索协程
	go func() {
		defer wg.Done()
		if fuzzyField := strings.TrimSpace(req.GetFuzzyField()); fuzzyField != "" {
			ids, err := rpcs.GetEmployeeIdsByFuzzyKey(ctx, fuzzyField)
			mu.Lock()
			if err != nil {
				fetchErr = err
			} else {
				data.SearchIDs = ids
			}
			mu.Unlock()
		}
	}()

	// 获取派单人列表协程
	go func() {
		defer wg.Done()
		ids, total, err := fetchProcessorIdsByDao(ctx, workflowRepo, req, employeeIDs)
		mu.Lock()
		if err != nil {
			fetchErr = err
		} else {
			data.ProcessorIds = ids
			data.total = total
		}
		mu.Unlock()
	}()

	wg.Wait()

	if fetchErr != nil {
		return nil, fetchErr
	}

	return data, nil
}

// buildListWorkflowDispatcherResponse 构建响应
func buildListWorkflowProcessorResponse(ctx context.Context, employeeList []*workflow.EmployeeData, total int64) *workflow.ListWorkflowProcessorRsp {
	items := make([]*workflow.ListWorkflowProcessorRsp_Item, 0, len(employeeList))
	for _, employee := range employeeList {
		items = append(items, &workflow.ListWorkflowProcessorRsp_Item{
			Id:       employee.GetId(),
			Name:     employee.GetName(),
			Phone:    employee.GetPhone(),
			Email:    employee.GetEmail(),
			DeptName: employee.GetDeptName(),
		})
	}
	logger.CtxDebugf(ctx, "构建处理人列表响应成功: %v", items)
	return &workflow.ListWorkflowProcessorRsp{
		Items: items,
		Total: total,
	}
}

func ListWorkflowNodeDispatcher(ctx context.Context, req *workflow.ListWorkflowNodeDispatcherReq) (resp *workflow.ListWorkflowNodeDispatcherRsp, err error) {
	resp = &workflow.ListWorkflowNodeDispatcherRsp{}

	workflowService, err := GetWorkflowServiceSingleInstance()
	if err != nil {
		logger.CtxErrorf(ctx, "获取工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}
	return workflowService.listWorkflowNodeDispatcher(ctx, req)
}

func (w *WorkflowService) listWorkflowNodeDispatcher(ctx context.Context, req *workflow.ListWorkflowNodeDispatcherReq) (resp *workflow.ListWorkflowNodeDispatcherRsp, err error) {
	resp = new(workflow.ListWorkflowNodeDispatcherRsp)

	var employeeId int64
	var employeeIDs []int64
	isWorkflow := ctxmeta.IsWorkflowFull(ctx)
	auth := ctxmeta.MustGetAuth(ctx)
	if auth.IsEmployee() {
		employeeId = auth.EmployeeId()
		// 1. 获取auth员工ID列表
		employeeIDs, err = rpcs.GetEmployeeIDs(ctx, employeeId)
		if err != nil {
			logger.CtxErrorf(ctx, "获取员工ID列表失败: %v", err)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_EmployeeNotExists)
			return resp, nil
		}
	}

	if auth.IsCustomer() {
		logger.CtxErrorf(ctx, "客户无法访问此接口")
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_AuthForbidden)
		return resp, nil
	}

	if isWorkflow {
		employeeIDs = []int64{}
	}

	// 2. 获取分发人列表
	dispatcherIdData, err := fetchWorkflowNodeDispatcherData(ctx, w.workflowRepo, req, employeeIDs)
	if err != nil {
		logger.CtxErrorf(ctx, "获取节点分发人ID列表失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_ThirdPartyError)
		return resp, nil
	}

	// 3. 获取最终的员工ID列表
	var finalIDs []int64
	if req.GetFuzzyField() == "" {
		finalIDs = dispatcherIdData.ProcessorIds
	} else if req.GetFuzzyField() != "" && len(dispatcherIdData.SearchIDs) == 0 {
		finalIDs = dispatcherIdData.SearchIDs
	} else {
		finalIDs = intersection(dispatcherIdData.SearchIDs, dispatcherIdData.ProcessorIds)
	}

	// 4. 获取员工详细信息
	employeeList, err := fetchEmployeeInfoList(ctx, finalIDs)
	if err != nil {
		logger.CtxErrorf(ctx, "获取员工列表失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_EmployeeNotExists)
		return resp, nil
	}

	total := len(finalIDs)
	// 5. 构建响应
	resp = buildListWorkflowNodeDispatcherResponse(ctx, employeeList, int64(total))
	return resp, nil
}

type NodeDispatcherData struct {
	SearchIDs    []int64
	ProcessorIds []int64
}

// fetchWorkflowNodeDispatcherData 获取节点分发人数据
func fetchWorkflowNodeDispatcherData(ctx context.Context, workflowRepo *models.WorkflowDao, req *workflow.ListWorkflowNodeDispatcherReq, employeeIDs []int64) (*NodeDispatcherData, error) {
	var (
		wg       sync.WaitGroup
		mu       sync.Mutex
		fetchErr error
		data     = &NodeDispatcherData{}
	)

	// 修正WaitGroup计数
	wg.Add(2)

	// 模糊搜索协程
	go func() {
		defer wg.Done()
		if fuzzyField := strings.TrimSpace(req.GetFuzzyField()); fuzzyField != "" {
			ids, err := rpcs.GetEmployeeIdsByFuzzyKey(ctx, fuzzyField)
			mu.Lock()
			if err != nil {
				fetchErr = err
			} else {
				data.SearchIDs = ids
			}
			mu.Unlock()
		}
	}()

	// 获取分发人ID列表协程
	go func() {
		defer wg.Done()
		ids, err := fetchDispatcherIdsByDao(ctx, workflowRepo, req, employeeIDs)
		mu.Lock()
		if err != nil {
			fetchErr = err
		} else {
			data.ProcessorIds = ids
		}
		mu.Unlock()
	}()

	wg.Wait()

	if fetchErr != nil {
		return nil, fetchErr
	}

	return data, nil
}

// fetchDispatcherIdsByDao 获取节点分发人ID列表
func fetchDispatcherIdsByDao(ctx context.Context, workflowRepo *models.WorkflowDao, req *workflow.ListWorkflowNodeDispatcherReq, employeeIDs []int64) ([]int64, error) {
	return workflowRepo.ListNodeDispatcher(ctx, employeeIDs, req.GetWorkflowTaskViewStatus(), int(req.GetPageNum()), int(req.GetPageSize()))
}

func buildListWorkflowNodeDispatcherResponse(ctx context.Context, employeeList []*workflow.EmployeeData, total int64) *workflow.ListWorkflowNodeDispatcherRsp {
	items := make([]*workflow.ListWorkflowNodeDispatcherRsp_Item, 0, len(employeeList))
	for _, employee := range employeeList {
		items = append(items, &workflow.ListWorkflowNodeDispatcherRsp_Item{
			Id:       employee.GetId(),
			Name:     employee.GetName(),
			Phone:    employee.GetPhone(),
			Email:    employee.GetEmail(),
			DeptName: employee.GetDeptName(),
		})
	}
	logger.CtxDebugf(ctx, "构建节点分发人列表响应成功: %v", items)
	return &workflow.ListWorkflowNodeDispatcherRsp{
		Items: items,
		Total: total,
	}
}

func ChangeWorkflowEmployee(ctx context.Context, req *workflow.ChangeWorkflowEmployeeReq) (resp *workflow.ChangeWorkflowEmployeeRsp, err error) {
	resp = &workflow.ChangeWorkflowEmployeeRsp{}

	tx := global.DB.Begin()
	if tx.Error != nil {
		logger.CtxErrorf(ctx, "创建事务失败: %v", tx.Error)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logger.CtxErrorf(ctx, "修改处理人发生panic: %v", r)
		} else if tx.Error != nil || (resp != nil && resp.GetBase() != nil && resp.GetBase().GetCode() != 0) {
			tx.Rollback()
			logger.CtxErrorf(ctx, "修改处理人时提交事务失败: %v, err: %v", tx.Error, err)
		} else {
			tx.Commit()
			logger.CtxDebugf(ctx, "修改处理人时提交事务成功")
		}
	}()

	// 使用事务创建工作流服务实例
	workflowServiceWithTx, err := InitializeWorkflowServiceWithTx(tx)
	if err != nil {
		logger.CtxErrorf(ctx, "初始化工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}

	resp, err = workflowServiceWithTx.changeWorkflowEmployee(ctx, req)
	return resp, err
}

// ChangeWorkflowEmployeeBatch 批量变更处理人
func ChangeWorkflowEmployeeBatch(ctx context.Context, req *workflow.ChangeWorkflowEmployeeBatchReq) (resp *workflow.ChangeWorkflowEmployeeBatchRsp, err error) {
	resp = &workflow.ChangeWorkflowEmployeeBatchRsp{}

	tx := global.DB.Begin()
	if tx.Error != nil {
		logger.CtxErrorf(ctx, "创建事务失败: %v", tx.Error)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logger.CtxErrorf(ctx, "修改处理人发生panic: %v", r)
		} else if tx.Error != nil || (resp != nil && resp.GetBase() != nil && resp.GetBase().GetCode() != 0) {
			tx.Rollback()
			logger.CtxErrorf(ctx, "修改处理人时提交事务失败: %v, err: %v", tx.Error, err)
		} else {
			tx.Commit()
			logger.CtxDebugf(ctx, "修改处理人时提交事务成功")
		}
	}()

	// 使用事务创建工作流服务实例
	workflowServiceWithTx, err := InitializeWorkflowServiceWithTx(tx)
	if err != nil {
		logger.CtxErrorf(ctx, "初始化工作流服务实例失败: %v", err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowServiceInitFailed)
		return resp, nil
	}

	resp, err = workflowServiceWithTx.changeWorkflowEmployeeBatch(ctx, req)
	return resp, err
}

func (w *WorkflowService) changeWorkflowEmployeeBatch(ctx context.Context, req *workflow.ChangeWorkflowEmployeeBatchReq) (*workflow.ChangeWorkflowEmployeeBatchRsp, error) {
	resp := &workflow.ChangeWorkflowEmployeeBatchRsp{}
	customerIds := req.GetCustomerIds()
	beforeEmployeeId := req.GetBefore()
	afterEmployeeId := req.GetAfter()

	if beforeEmployeeId == afterEmployeeId {
		logger.CtxWarnf(ctx, "skip change employee from %d to %d", beforeEmployeeId, afterEmployeeId)
		return resp, nil
	}

	if beforeEmployeeId <= 0 || afterEmployeeId <= 0 {
		logger.CtxErrorf(ctx, "Invalid employee change: %+v", req)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
		return resp, nil
	}

	if err := w.nodeService.workflowNodeRepo.ChangeWorkflowEmployee(ctx, customerIds, beforeEmployeeId, afterEmployeeId); err != nil {
		logger.CtxErrorf(ctx, "Failed to change workflow employee from %d to %d: %v", beforeEmployeeId, afterEmployeeId, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	return resp, nil
}

func (w *WorkflowService) changeWorkflowEmployee(ctx context.Context, req *workflow.ChangeWorkflowEmployeeReq) (*workflow.ChangeWorkflowEmployeeRsp, error) {
	resp := &workflow.ChangeWorkflowEmployeeRsp{}
	employeeInfo := req.GetChangeEmployeeList()
	customerId := req.GetCustomerId()
	employeeChanges := make(map[int64]int64)
	for _, change := range employeeInfo {
		logger.CtxDebugf(ctx, "change employee from %d to %d", change.GetBeforeEmployeeId(), change.GetAfterEmployeeId())
		if change.GetBeforeEmployeeId() <= 0 || change.GetAfterEmployeeId() <= 0 {
			logger.CtxErrorf(ctx, "Invalid employee change: %+v", change)
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams)
			return resp, nil
		}
		if change.GetBeforeEmployeeId() == change.GetAfterEmployeeId() {
			logger.CtxWarnf(ctx, "skip change employee from %d to %d", change.GetBeforeEmployeeId(), change.GetAfterEmployeeId())
			continue
		}
		employeeChanges[change.GetBeforeEmployeeId()] = change.GetAfterEmployeeId()
	}

	if len(employeeChanges) == 0 {
		logger.CtxWarnf(ctx, "没有员工变更: %+v", employeeChanges)
		return resp, nil
	}

	if err := w.nodeService.workflowNodeRepo.ChangeWorkflowEmployees(ctx, customerId, employeeChanges); err != nil {
		logger.CtxErrorf(ctx, "Failed to change workflow employee from %+v: %v", employeeChanges, err)
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	return resp, nil
}
