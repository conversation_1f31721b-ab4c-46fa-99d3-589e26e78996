package services

import (
	"context"
	"runtime/debug"
	"sync"
	"time"

	"uofferv2/pkg/ctxspan"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/message/internal/cross_models"
	"uofferv2/server/cmd/message/internal/models"
)

type ManagerMessageBoardCache struct {
	cache map[int64][]int64 // userID -> messageIDs
	lock  sync.Mutex
}

var ManagerMessageBoardCacheInstance *ManagerMessageBoardCache

func InitManagerMessageBoardCache() {
	ManagerMessageBoardCacheInstance = NewManagerMessageBoardCache()
}

func NewManagerMessageBoardCache() *ManagerMessageBoardCache {
	ins := &ManagerMessageBoardCache{
		cache: make(map[int64][]int64),
	}
	go ins.loop()
	return ins
}

func (m *ManagerMessageBoardCache) GetMessageIDs(userID int64) []int64 {
	m.lock.Lock()
	defer m.lock.Unlock()
	return m.cache[userID]
}

func (m *ManagerMessageBoardCache) Exists(userID int64) bool {
	m.lock.Lock()
	defer m.lock.Unlock()
	_, exists := m.cache[userID]
	return exists
}

func (m *ManagerMessageBoardCache) loop() {
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("%s panic: %v\n%s", "ManagerMessageBoardCache loop", r, debug.Stack())
		}
	}()

	m.refreshCache()

	interval := 2 * time.Hour
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for range ticker.C {
		m.refreshCache()
	}
}

func (m *ManagerMessageBoardCache) refreshCache() {
	startTime := time.Now()
	ctx := ctxspan.FillSpanContext(context.Background())
	logger.CtxInfof(ctx, "[message board cache] refresh cache start")

	managerIds, err := cross_models.GetManagerIdsWithNChild(ctx, 50)
	if err != nil {
		logger.CtxErrorf(ctx, "[message board cache] refresh cache get manager ids failed, err: %v", err)
		return
	}

	managerIdMap := make(map[int64]struct{})
	for _, managerId := range managerIds {
		managerIdMap[managerId] = struct{}{}
	}
	// 先清理缓存
	m.lock.Lock()
	for managerId := range m.cache {
		if _, exists := managerIdMap[managerId]; !exists {
			delete(m.cache, managerId)
		}
	}
	m.lock.Unlock()

	for _, managerId := range managerIds {
		m.refreshCacheByManagerId(ctx, managerId)
	}

	costTime := time.Since(startTime)
	logger.CtxInfof(ctx, "[message board cache] refresh cache end, len cache: %d, cost time: %v", len(m.cache), costTime)
}

func (m *ManagerMessageBoardCache) refreshCacheByManagerId(ctx context.Context, managerId int64) {
	staffIds, err := cross_models.GetStaffIdListByManagerId(ctx, managerId)
	if err != nil {
		logger.CtxErrorf(ctx, "[message board cache] refresh cache get staff ids failed, err: %v", err)
		return
	}

	messageBoardIds, err := models.NewMessageBoardStaffMapDao().GetMessageBoardIdListByStaffIds(ctx, staffIds)
	if err != nil {
		logger.CtxErrorf(ctx, "[message board cache] refresh cache get message board ids failed, err: %v", err)
		return
	}
	m.lock.Lock()
	defer m.lock.Unlock()
	m.cache[managerId] = messageBoardIds
}
