package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"path"
	"strconv"
	"strings"
	"time"
	"uofferv2/pkg/utils"

	"uofferv2/kitex_gen/constants"
	"uofferv2/kitex_gen/errno"
	"uofferv2/kitex_gen/server/cmd/message"
	"uofferv2/pkg/coderror"
	"uofferv2/pkg/ctxmeta"
	"uofferv2/pkg/dao/model"
	daomodel "uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/message/internal/cross_models"
	"uofferv2/server/cmd/message/internal/global"
	"uofferv2/server/cmd/message/internal/models"

	im "github.com/dobyte/tencent-im"
	"github.com/dobyte/tencent-im/account"
	"github.com/dobyte/tencent-im/group"
	"github.com/dobyte/tencent-im/private"
	"github.com/dobyte/tencent-im/profile"
	"github.com/duke-git/lancet/v2/condition"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type ImService struct{}

func NewImService() *ImService {
	return &ImService{}
}

// GetImSetting 获取Im配置
func (s *ImService) GetImSetting(ctx context.Context, req *message.GetImSettingReq) (*message.GetImSettingRsp, error) {
	dao := models.NewQqImUserDao()
	qqImUser, err := dao.GetByUserId(ctx, req.UserId, int32(constants.ImUserType_TYPE_STAFF))
	if err != nil {
		return nil, err
	}
	if qqImUser == nil {
		// 没用启用自动创建
		if !req.GetAutoCreate() {
			return &message.GetImSettingRsp{Base: coderror.MakeBaseRsp(ctx, errno.Errno_IMEmployeeNotRegistered)}, nil
		}

		// 启动了自动创建
		r, err := s.CreateImUser(ctx, &message.CreateImUserReq{
			ImUserType: constants.ImUserType_TYPE_STAFF,
			UserId:     req.GetUserId(),
		})
		warpErr := coderror.Warp(r, err)
		if warpErr != nil {
			return &message.GetImSettingRsp{Base: warpErr.BaseRsp()}, warpErr.SysError()
		}

		qqImUser, err = dao.GetByUserId(ctx, req.UserId, int32(constants.ImUserType_TYPE_STAFF))
		if err != nil {
			return nil, err
		}

		if qqImUser == nil {
			return &message.GetImSettingRsp{Base: coderror.MakeBaseRsp(ctx, errno.Errno_IMEmployeeNotRegistered)}, nil
		}
	}

	imConfig, _ := models.LoadImConfig()

	// 获取签名
	userSig := global.ServerConfig.TencentIM.GetUserSig(qqImUser.QqImUserID, 86400)

	return &message.GetImSettingRsp{
		ImUserSig: userSig.UserSig,
		ImUserId:  qqImUser.QqImUserID,
		ImAppId:   imConfig.AppId,
		Expire:    userSig.ExpireAt,
	}, nil
}

// BatchGetAvatar 批量获取头像
func (s *ImService) BatchGetAvatar(ctx context.Context, req *message.BatchGetAvatarReq) (resp *message.BatchGetAvatarRsp, err error) {
	resp = &message.BatchGetAvatarRsp{}

	dao := models.NewQqImUserDao()
	qqImUser, err := dao.GetByUserIds(ctx, req.UserId, []int32{req.UserType})
	if err != nil {
		return nil, err
	}

	if len(qqImUser) <= 0 {
		return resp, nil
		// return nil, errors.New("IM用户ID不存在")
	}

	imUserIds := make([]string, 0, len(qqImUser))
	for _, user := range qqImUser {
		imUserIds = append(imUserIds, user.QqImUserID)
	}

	// 获取头像
	profiles, err := global.ServerConfig.TencentIM.Profile().GetProfiles(imUserIds, []string{
		profile.StandardAttrAvatar,
	})
	if err != nil {
		return nil, err
	}

	// 创建一个map来存储头像信息,方便后续查找
	avatarMap := make(map[string]string)
	for _, item := range profiles {
		avatar, _ := item.GetAvatar()
		avatarMap[item.GetUserId()] = avatar
	}

	// 构建返回结果
	result := make(map[int64]*message.AvatarItem)
	for _, user := range qqImUser {
		userId, _ := strconv.ParseInt(user.UserID, 10, 64)
		result[userId] = &message.AvatarItem{
			ImUserId: user.QqImUserID,
			UserId:   userId,
			Avatar:   avatarMap[user.QqImUserID],
		}
	}

	resp.Items = result

	return resp, nil
}

// setMemberData 设置群成员资料
func setMemberData(groupID string, memberAccount *GroupMemberInfo) error {
	imServer := global.ServerConfig.TencentIM

	groupMember := group.NewMember(memberAccount.UserId)

	if memberAccount.Avatar != "" {
		groupMember.SetCustomData("Avatar", memberAccount.Avatar)
	}

	if memberAccount.UserType != 0 {
		groupMember.SetCustomData("UserType", strconv.Itoa(int(memberAccount.UserType)))
	}

	groupMember.SetNameCard(memberAccount.NickName)
	groupMember.SetRole(memberAccount.Role)
	groupMember.SetMsgFlag("Discard") //Discard 代表不接收也不提示消息
	err := imServer.Group().UpdateMember(groupID, groupMember)
	if err != nil {
		return fmt.Errorf("update member failed: %w", err)
	}
	return nil
}

// AddGroupMembers 添加群成员
func (s *ImService) AddGroupMembers(ctx context.Context, req *message.AddGroupMembersReq) (resp *message.AddGroupMembersRsp, err error) {
	resp = &message.AddGroupMembersRsp{}

	// 检查群组是否存在
	imGroupDao := models.NewQqImGroupDao()
	_, err = imGroupDao.GetByGroupId(ctx, req.GroupId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_IMGroupNotExist)
			return resp, nil
		}
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}
	var staffs []*cross_models.StaffsImUsers
	var customers []*cross_models.CustomerImUsers
	// 获取要添加的成员的IM用户信息
	staffIdList := make([]int32, 0)
	for _, userId := range req.UserIdList {
		staffIdList = append(staffIdList, int32(userId))
	}

	if len(staffIdList) > 0 {
		staffs, err = cross_models.GetStaffImIds(ctx, staffIdList)
		if err != nil {
			return nil, err
		}

		// 如果传入了员工ID但查不到数据，直接返回错误
		if len(staffs) != len(staffIdList) {
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_IMEmployeeNotRegistered)
			return resp, nil
		}
	}

	parentIdList := make([]int32, 0)
	for _, userId := range req.ParentIdList {
		parentIdList = append(parentIdList, int32(userId))
	}

	if len(parentIdList) > 0 {
		customers, err = cross_models.GetWithImIdByIds(ctx, parentIdList)
		if err != nil {
			return nil, err
		}

		// 如果传入了家长ID但查不到数据，直接返回错误
		if len(customers) != len(parentIdList) {
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_IMCustomerNotRegistered)
			return resp, nil
		}
	}

	// 组装群成员信息
	memberList := make([]string, 0, len(staffs)+len(customers))
	memberNames := make([]string, 0, len(staffs)+len(customers))
	groupMembers := make([]*GroupMemberInfo, 0, len(staffs)+len(customers))

	// 组装员工成员数据
	for _, staff := range staffs {
		memberList = append(memberList, staff.QqImUserId)
		memberNames = append(memberNames, staff.EmployeeName)

		groupMembers = append(groupMembers, &GroupMemberInfo{
			UserId:   staff.QqImUserId,
			NickName: condition.TernaryOperator(staff.RoleName != "", staff.RoleName, staff.EmployeeName),
			Avatar:   global.ServerConfig.AliOss.CdnUrlLondon + "/images/im/avatars/other.png",
			UserType: int32(constants.ImUserType_TYPE_STAFF),
			Role:     "Admin",
			NameCard: condition.TernaryOperator(staff.RoleName != "", staff.RoleName+staff.EmployeeName, staff.EmployeeName),
		})
	}

	// 组装客户成员数据
	for _, user := range customers {
		memberList = append(memberList, user.QqImUserId)
		memberNames = append(memberNames, user.RealName)

		groupMembers = append(groupMembers, &GroupMemberInfo{
			UserId:   user.QqImUserId,
			NickName: user.RealName,
			Avatar: fmt.Sprintf("%s%s%s", global.ServerConfig.AliOss.CdnUrlLondon, "/images/im/avatars",
				condition.TernaryOperator(user.UserType == int32(constants.ImUserType_TYPE_CUSTOMER), "/customer.png", "/parent.png")),
			UserType: int32(condition.TernaryOperator(user.UserType == int32(constants.ImUserType_TYPE_CUSTOMER),
				constants.ImUserType_TYPE_CUSTOMER, constants.ImUserType_TYPE_PARENT)),
			NameCard: user.RealName,
		})
	}

	// 调用腾讯IM添加群成员
	if _, err = global.ServerConfig.TencentIM.Group().AddMembers(req.GroupId, memberList); err != nil {
		var e im.Error
		if errors.As(err, &e) {
			logger.CtxErrorf(ctx, "add group members failed, code:%d, message:%s.", e.Code(), e.Message())
			if e.Code() == 10037 {
				resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_IMTooManayGroups)
				return resp, nil
			}
			if e.Code() == 10007 {
				resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_ImGroupUserAlreadyExists)
				return resp, nil
			}
		}
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	// 查询是否已存在群成员
	existingMembers, err := models.NewQqImGroupsMemberDao().GetByGroupIdAndUserIds(ctx, req.GroupId, memberList)
	if err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	// 过滤掉已存在的成员
	if len(existingMembers) > 0 {
		existingMap := make(map[string]struct{})
		for _, member := range existingMembers {
			existingMap[member.ImUserID] = struct{}{}
		}

		filteredMembers := make([]*GroupMemberInfo, 0)
		for _, member := range groupMembers {
			if _, exists := existingMap[member.UserId]; !exists {
				filteredMembers = append(filteredMembers, member)
			}
		}
		groupMembers = filteredMembers
	}

	if len(groupMembers) == 0 {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_ImGroupUserAlreadyExists)
		return resp, nil
	}

	// 设置群成员资料并保存到数据库
	err = setGroupMemberData(ctx, req.GroupId, groupMembers)
	if err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	// 发送入群欢迎消息
	//if len(groupMembers) > 0 {
	//	names := make([]string, 0, len(groupMembers))
	//	for _, member := range groupMembers {
	//		names = append(names, member.NameCard)
	//	}
	//	welcomeMsg := "欢迎" + strings.Join(names, "、")
	//	if err = sendSystemMessage(req.GroupId, welcomeMsg); err != nil {
	//		logger.CtxWarnf(ctx, "send welcome message failed: %v", err)
	//		// 这里只记录日志，不返回错误，因为欢迎消息发送失败不影响主流程
	//	}
	//}

	// 记录操作日志
	authCtx := ctxmeta.MustGetAuth(ctx)
	texts := fmt.Sprintf("%s邀请群成员%s", authCtx.EmployeeName(), strings.Join(memberNames, "、"))
	operationLog := &daomodel.QqImGroupOperationLog{
		QqImGroupID: req.GroupId,
		EmployeeID:  authCtx.EmployeeId(),
		Content:     texts,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err = global.DB.WithContext(ctx).Create(operationLog).Error; err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	resp.Base = coderror.MakeSuccessBaseRsp()
	return resp, nil
}

// RemoveGroupMembers 移除群成员
func (s *ImService) RemoveGroupMembers(ctx context.Context, req *message.RemoveGroupMembersReq) (resp *message.RemoveGroupMembersRsp, err error) {
	resp = &message.RemoveGroupMembersRsp{}

	// 检查群组是否存在
	imGroupDao := models.NewQqImGroupDao()
	_, err = imGroupDao.GetByGroupId(ctx, req.GroupId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_IMGroupNotExist)
			return resp, nil
		}
		return nil, err
	}

	// 调用腾讯IM删除群成员
	if err = global.ServerConfig.TencentIM.Group().DeleteMembers(req.GroupId, req.ImUserIdList); err != nil {
		var e im.Error
		if errors.As(err, &e) {
			logger.CtxErrorf(ctx, "remove group members failed, code:%d, message:%s.", e.Code(), e.Message())
		}
		return nil, fmt.Errorf("remove group members failed: %w", err)
	}

	// 从数据库删除群成员记录
	imGroupsMemberDao := models.NewQqImGroupsMemberDao()
	if err = imGroupsMemberDao.DeleteByGroupIdAndUserIds(ctx, req.GroupId, req.ImUserIdList); err != nil {
		return nil, fmt.Errorf("delete group members from database failed: %w", err)
	}
	// 记录操作日志
	authCtx := ctxmeta.MustGetAuth(ctx)
	operationLog := &daomodel.QqImGroupOperationLog{
		QqImGroupID: req.GroupId,
		EmployeeID:  authCtx.EmployeeId(),
		Content:     fmt.Sprintf("%s将成员%s移出群聊", authCtx.EmployeeName(), strings.Join(req.ImUserIdList, "、")),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err = global.DB.WithContext(ctx).Create(operationLog).Error; err != nil {
		return nil, fmt.Errorf("create group operation log failed: %w", err)
	}

	return resp, nil
}

// CreateImUser 创建腾讯Im用户
func (s *ImService) CreateImUser(ctx context.Context, req *message.CreateImUserReq) (resp *message.CreateImUserRsp, err error) {
	resp = &message.CreateImUserRsp{}
	ImUserId := uuid.New().String()

	dao := models.NewQqImUserDao()
	userName, err := dao.GetUserInfo(ctx, req.GetUserId(), req.ImUserType)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
			return resp, nil
		}
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	// 注册腾讯im
	avatar := ""
	switch req.ImUserType {
	case constants.ImUserType_TYPE_STAFF:
		avatar = "other.png"
	case constants.ImUserType_TYPE_CUSTOMER:
		avatar = "customer.png"
	default:
		avatar = "parent.png"
	}
	// Todo 地址先写死
	avatar = fmt.Sprintf("%s%s%s", global.ServerConfig.AliOss.CdnUrlLondon, "/images/im/avatars/", avatar)

	record := daomodel.QqImUser{
		UserID:     req.UserId,
		UserType:   int32(req.ImUserType),
		QqImUserID: ImUserId,
	}

	// 使用事务
	err = global.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		err = dao.CreateWithTx(ctx, tx, &record)
		if err != nil {
			return err
		}

		// 导入账号
		if err = global.ServerConfig.TencentIM.Account().ImportAccount(&account.Account{
			UserId:   ImUserId,
			Nickname: userName,
			FaceUrl:  avatar,
		}); err != nil {
			var e im.Error
			if errors.As(err, &e) {
				logger.CtxErrorf(ctx, "import account failed, code:%d, message:%s.", e.Code(), e.Message())
				return err
			}
		}

		return nil
	})
	if err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	resp.ImUserId = ImUserId
	return resp, nil
}

// GetImUserInfo implements the MessageServiceImpl interface.
func (s *ImService) GetImUserInfo(ctx context.Context, req *message.GetImUserInfoReq) (*message.GetImUserInfoRsp, error) {
	dao := models.NewQqImUserDao()
	var imUserTypeList []int32
	for i := range req.ImUserTypeList {
		imUserTypeList = append(imUserTypeList, int32(req.ImUserTypeList[i]))
	}
	qqImUser, err := dao.GetByUserId(ctx, req.UserId, imUserTypeList...)
	if err != nil {
		return nil, err
	}

	if qqImUser == nil {
		return nil, nil
	}

	return &message.GetImUserInfoRsp{ImUserInfo: &message.ImUserInfo{
		ImUserId:   qqImUser.QqImUserID,
		ImUserType: constants.ImUserType(qqImUser.UserType),
	}}, nil
}

// GetImUserInfoByCustomerIds implements the MessageServiceImpl interface.
func (s *ImService) GetImUserInfoByCustomerIds(ctx context.Context, req *message.GetImUserInfoByCustomerIdsReq) (*message.GetImUserInfoByCustomerIdsRsp, error) {
	dao := models.NewQqImUserDao()
	qqImUser, err := dao.GetByUserIds(ctx, req.CustomerIds, req.ImUserTypes)
	if err != nil {
		return nil, err
	}
	if qqImUser == nil {
		return nil, nil
	}
	items := make([]*message.ImUserInfo, 0, len(qqImUser))
	for _, c := range qqImUser {
		items = append(items, &message.ImUserInfo{
			ImUserId:   c.QqImUserID,
			CustomerId: c.UserID,
			ImUserType: constants.ImUserType(c.UserType),
		})
	}
	return &message.GetImUserInfoByCustomerIdsRsp{ImUserList: items}, nil
}

// GetImUserType 获取用户类型
func (s *ImService) GetImUserType(ctx context.Context, req *message.GetImUserTypeReq) (resp *message.GetImUserTypeRsp, err error) {
	resp = &message.GetImUserTypeRsp{}
	dao := models.NewQqImUserDao()
	qqImUser, err := dao.GetByImId(ctx, req.ImUserId)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	if qqImUser == nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_IMUserNotExist)
		return resp, nil
	}

	resp.ImUserType = constants.ImUserType(qqImUser.UserType)
	resp.UserId = qqImUser.UserID

	return resp, nil
}

// updateGroupInfoAndLog 更新群组信息并记录日志的通用方法
func (s *ImService) updateGroupInfoAndLog(
	ctx context.Context,
	groupId string,
	employeeId int64,
	modifyGroup *group.Group,
	updateField string,
	updateValue interface{},
	logContent string,
) error {
	if err := global.ServerConfig.TencentIM.Group().UpdateGroup(modifyGroup); err != nil {
		var e im.Error
		if errors.As(err, &e) {
			logger.CtxErrorf(ctx, "modify group chat info failed, code:%d, message:%s.", e.Code(), e.Message())
		}
		return err
	}

	if updateField == "-1" {
		return nil
	}

	err := global.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 构建更新字段
		updates := map[string]interface{}{
			updateField:  updateValue,
			"updated_at": time.Now(),
		}

		// 如果是修改公告，也需要更新 notice_at 字段
		if updateField == "notification" {
			now := time.Now()
			updates["notice_at"] = &now
		}

		if err := tx.Model(&daomodel.QqImGroup{}).
			Where("qq_im_group_id = ?", groupId).
			Updates(updates).Error; err != nil {
			return err
		}

		operationLog := &daomodel.QqImGroupOperationLog{
			QqImGroupID: groupId,
			EmployeeID:  employeeId,
			Content:     logContent,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}
		if err := tx.Create(operationLog).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		logger.CtxErrorf(ctx, "update group chat info in database failed: %v", err)
	}
	return err
}

// ModifyGroupChatName 修改群名称
func (s *ImService) ModifyGroupChatName(ctx context.Context, req *message.ModifyGroupChatNameReq) (resp *message.ModifyGroupChatNameRsp, err error) {
	resp = &message.ModifyGroupChatNameRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	// 设置腾讯IM的群组信息
	modifyGroup := group.NewGroup()
	modifyGroup.SetGroupId(req.GroupId)
	modifyGroup.SetName(req.GroupName)

	authCtx := ctxmeta.MustGetAuth(ctx)
	err = s.updateGroupInfoAndLog(
		ctx,
		req.GroupId,
		authCtx.EmployeeId(),
		modifyGroup,
		"name",
		req.GroupName,
		fmt.Sprintf("%s修改群名称为：%s", authCtx.EmployeeName(), req.GroupName),
	)
	if err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	return resp, nil
}

// ModifyGroupChatNotice 修改群公告
func (s *ImService) ModifyGroupChatNotice(ctx context.Context, req *message.ModifyGroupChatNoticeReq) (resp *message.ModifyGroupChatNoticeRsp, err error) {
	resp = &message.ModifyGroupChatNoticeRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	// 设置腾讯IM的群组信息
	modifyGroup := group.NewGroup()
	modifyGroup.SetGroupId(req.GroupId)
	modifyGroup.SetNotification(req.Notice)

	authCtx := ctxmeta.MustGetAuth(ctx)
	err = s.updateGroupInfoAndLog(
		ctx,
		req.GroupId,
		authCtx.EmployeeId(),
		modifyGroup,
		"notification",
		req.Notice,
		fmt.Sprintf("%s修改群公告为：%s", authCtx.EmployeeName(), req.Notice),
	)
	if err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	return resp, nil
}

// ModifyGroupChatMemberNickname implements the MessageServiceImpl interface.
func (s *ImService) ModifyGroupChatMemberNickname(ctx context.Context, req *message.ModifyGroupChatMemberNicknameReq) (resp *message.ModifyGroupChatMemberNicknameRsp, err error) {
	resp = &message.ModifyGroupChatMemberNicknameRsp{}

	groupMember := &group.Member{}
	groupMember.SetUserId(req.ImUserId)
	groupMember.SetNameCard(req.NickName)

	err = global.ServerConfig.TencentIM.Group().UpdateMember(req.GroupId, groupMember)
	if err != nil {
		return nil, err
	}

	err = global.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		dao := models.NewQqImGroupsMemberDao()
		return dao.SaveMemberNameCard(ctx, tx, req.GroupId, req.ImUserId, req.NickName)
	})
	if err != nil {
		return nil, err
	}

	return resp, nil
}

// DismissGroupChat implements the MessageServiceImpl interface.
func (s *ImService) DismissGroupChat(ctx context.Context, req *message.DismissGroupChatReq) (resp *message.DismissGroupChatRsp, err error) {
	resp = &message.DismissGroupChatRsp{}

	// 解散群组
	if err = global.ServerConfig.TencentIM.Group().DestroyGroup(req.GetGroupId()); err != nil {
		return resp, err
	}

	// 更新数据库并记录操作日志
	err = global.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新群组状态为已解散
		if err := tx.Model(&daomodel.QqImGroup{}).
			Where("qq_im_group_id = ?", req.GetGroupId()).
			Updates(map[string]interface{}{
				"valid":      constants.GroupStatus_GROUP_STATUS_DISMISSED,
				"updated_at": time.Now(),
			}).Error; err != nil {
			return err
		}
		content := fmt.Sprintf("%s已解散群组", ctxmeta.MustGetAuth(ctx).EmployeeName())
		// 记录操作日志
		return s.logGroupOperation(tx, req.GetGroupId(), ctxmeta.MustGetAuth(ctx).EmployeeId(), content)
	})
	if err != nil {
		return resp, err
	}
	return resp, nil
}

// logGroupOperation 记录群操作日志
func (s *ImService) logGroupOperation(tx *gorm.DB, groupId string, employeeId int64, content string) error {
	operationLog := &daomodel.QqImGroupOperationLog{
		QqImGroupID: groupId,
		EmployeeID:  employeeId,
		Content:     content,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	return tx.Create(operationLog).Error
}

// QuitGroupChat implements the MessageServiceImpl interface.
func (s *ImService) QuitGroupChat(ctx context.Context, req *message.QuitGroupChatReq) (resp *message.QuitGroupChatRsp, err error) {
	resp = &message.QuitGroupChatRsp{}

	// Get current user's IM ID
	authCtx := ctxmeta.MustGetAuth(ctx)
	dao := models.NewQqImUserDao()
	qqImUser, err := dao.GetByUserId(ctx, fmt.Sprintf("%d", authCtx.EmployeeId()), int32(constants.ImUserType_TYPE_STAFF))
	if err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	// Call Tencent IM API to quit the group
	if err = global.ServerConfig.TencentIM.Group().DeleteMembers(req.GetGroupId(), []string{qqImUser.QqImUserID}); err != nil {
		var e im.Error
		if errors.As(err, &e) {
			logger.CtxErrorf(ctx, "quit group chat failed, code:%d, message:%s.", e.Code(), e.Message())
		}
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	// Update database and record operation log
	err = global.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Delete member from group_members table
		if err := tx.Where("im_group_id = ? AND im_user_id = ?", req.GetGroupId(), qqImUser.QqImUserID).
			Delete(&daomodel.QqImGroupsMember{}).Error; err != nil {
			return err
		}

		// Record operation log
		content := fmt.Sprintf("%s退出群组", authCtx.EmployeeName())
		return s.logGroupOperation(tx, req.GetGroupId(), authCtx.EmployeeId(), content)
	})
	if err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	return resp, nil
}

// TransferGroupChat implements the MessageServiceImpl interface.
func (s *ImService) TransferGroupChat(ctx context.Context, req *message.TransferGroupChatReq) (resp *message.TransferGroupChatRsp, err error) {
	resp = &message.TransferGroupChatRsp{}

	// Get new owner's info
	dao := models.NewQqImUserDao()
	qqImUser, err := dao.GetByImId(ctx, req.GetImUserId())
	if err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	newOwner, err := dao.GetUserInfo(ctx, qqImUser.UserID, constants.ImUserType(qqImUser.UserType))
	if err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	// Call Tencent IM API to transfer group ownership
	if err = global.ServerConfig.TencentIM.Group().ChangeGroupOwner(req.GetGroupId(), req.GetImUserId()); err != nil {
		var e im.Error
		if errors.As(err, &e) {
			logger.CtxErrorf(ctx, "transfer group chat failed, code:%d, message:%s.", e.Code(), e.Message())
		}
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	// Update database and record operation log
	err = global.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Update group owner in database
		if err := tx.Model(&daomodel.QqImGroup{}).
			Where("qq_im_group_id = ?", req.GetGroupId()).
			Update("owner_account", req.GetImUserId()).Error; err != nil {
			return err
		}

		// Record operation log
		content := fmt.Sprintf("%s将群主转让给%s", ctxmeta.MustGetAuth(ctx).EmployeeName(), newOwner)
		return s.logGroupOperation(tx, req.GetGroupId(), ctxmeta.MustGetAuth(ctx).EmployeeId(), content)
	})
	if err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
		return resp, nil
	}

	return resp, nil
}

// CreateGroupChat 创建群聊
func (s *ImService) CreateGroupChat(ctx context.Context, req *message.CreateGroupChatReq) (resp *message.CreateGroupChatRsp, err error) {
	groupName := "群聊"
	resp = &message.CreateGroupChatRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	// 群名称长度不允许超过33个字符
	if len(req.GroupName) > 100 {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_IMGroupMaxNameLength)
		return resp, nil
	}

	if strings.TrimSpace(req.GroupName) != "" {
		groupName = strings.TrimSpace(req.GroupName)
	}

	// 获取当前登录用户
	authCtx := ctxmeta.MustGetAuth(ctx)
	employeeId := authCtx.EmployeeId()

	staffIdSli := append(req.StaffIds, int32(employeeId))
	staffIdSli = slice.Unique(staffIdSli)

	customerIdSli := append(req.ParentIds, req.CustomerId)
	customerIdSli = slice.Unique(customerIdSli)

	// step 1 客户成员限制（仅有一个学生，学生家长必须是当前学生的家长）
	parentIds, _ := cross_models.GetUserListBindParentList(ctx, []int32{req.CustomerId})
	parentIdMap := make(map[int32]struct{})
	for _, id := range parentIds {
		parentIdMap[id] = struct{}{}
	}

	for _, id := range req.ParentIds {
		if _, ok := parentIdMap[id]; !ok {
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_ImGroupStudentParentNotMatch)
			return resp, nil
		}
	}
	// step 2 组合创建群聊的信息（群主，群操作日志内容，群成员）
	ownerImId, groupOperationLogContent, groupMember, err := s.getImGroupInfoForCreate(ctx, staffIdSli, customerIdSli)
	if err != nil {
		return nil, err
	}
	// step 3 创建群聊，绑定工单号
	exists, _ := cross_models.GetGroupByWorkflowNoAndGroupId(ctx, req.WorkflowNo)
	if exists {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_ImGroupAlreadyBind)
		return resp, nil
	}
	// 通过工单号获取工单ID
	workflowInfo, err := cross_models.GetWorkflowByWorkflowNo(ctx, req.WorkflowNo)
	if err != nil {
		return nil, err
	}
	if workflowInfo == nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowNotExist)
		return resp, nil
	}

	workflowId := strconv.FormatInt(workflowInfo.ID, 10)

	return s.CreateImGroupBindOrders(ctx, authCtx.EmployeeId(), req.CustomerId, ownerImId, groupName, groupOperationLogContent, groupMember, workflowId)

	// step 4 返回群聊信息
}

type GroupMemberInfo struct {
	// 用户ID
	UserId string `json:"userId"`
	// 用户昵称
	NickName string `json:"nickName"`
	// 用户类型，1用户，2员工,3家长
	UserType int32 `json:"userType"`
	// 用户头像
	Avatar string `json:"avatar"`
	// 角色
	Role string `json:"role"`
	// NameCard
	NameCard string `json:"nameCard"`
}

// Description: 组合创建群聊的信息（群主，群操作日志内容，群成员）
// receiver s
// param ctx
// param createdUid 操作者UID
// param staffIdSli 员工UID
// param customerIdSli 客户UID（学生+家长）
// return string 群所有者
// return string 群操作日志
// return []*request.GroupMemberInfo 群成员
// return error
func (s *ImService) getImGroupInfoForCreate(ctx context.Context, staffIdSli, customerIdSli []int32) (string, string, []*GroupMemberInfo, error) {
	// 群主ID（当前登录用户为群组）
	var ownerImId, groupOperationLogContent string
	// 群成员
	groupMember := make([]*GroupMemberInfo, 0)

	// step 1 获取员工列表
	staffImUsers, err := cross_models.GetStaffImIds(ctx, staffIdSli)
	if err != nil {
		return ownerImId, groupOperationLogContent, groupMember, err
	}

	// step 2 获取客户列表
	customerImUsers, err := cross_models.GetWithImIdByIds(ctx, customerIdSli)
	if err != nil {
		return ownerImId, groupOperationLogContent, groupMember, err
	}

	if len(customerImUsers) == 0 {
		return ownerImId, groupOperationLogContent, groupMember, errors.New("im user not found")
	}

	members := make([]string, 0, len(customerImUsers)+len(staffImUsers))
	// 用户
	for _, val := range customerImUsers {
		members = append(members, fmt.Sprintf(`"%s"`, val.RealName))
	}

	// 员工昵称
	for _, val := range staffImUsers {
		members = append(members, fmt.Sprintf(`"%s"`, val.EmployeeName))
	}

	authCtx := ctxmeta.MustGetAuth(ctx)
	groupOperationLogContent = fmt.Sprintf(`"%s"创建群聊，群成员%s`, authCtx.EmployeeName(), strings.Join(members, "、"))

	// 组装群成员数据（员工）
	for _, val := range staffImUsers {
		groupMember = append(groupMember, &GroupMemberInfo{
			UserId:   val.QqImUserId,
			NickName: condition.TernaryOperator(val.RoleName != "", val.RoleName, val.EmployeeName),
			Avatar:   global.ServerConfig.AliOss.CdnUrlLondon + "/images/im/avatars/other.png",
			UserType: int32(constants.ImUserType_TYPE_STAFF),
			NameCard: condition.TernaryOperator(val.RoleName != "", val.RoleName+val.EmployeeName, val.EmployeeName),
		})
		if val.UserId == authCtx.EmployeeId() {
			ownerImId = val.QqImUserId
		}
	}

	// 组装群成员数据（客户）
	for _, val := range customerImUsers {
		groupMember = append(groupMember, &GroupMemberInfo{
			UserId:   val.QqImUserId,
			NickName: val.RealName,
			Avatar: fmt.Sprintf("%s%s%s", global.ServerConfig.AliOss.CdnUrlLondon, "/images/im/avatars",
				condition.TernaryOperator(val.UserType == int32(constants.ImUserType_TYPE_CUSTOMER), "/customer.png", "/parent.png")),
			UserType: int32(condition.TernaryOperator(val.UserType == int32(constants.ImUserType_TYPE_CUSTOMER), constants.ImUserType_TYPE_CUSTOMER, constants.ImUserType_TYPE_PARENT)),
			NameCard: val.RealName,
		})
	}

	return ownerImId, groupOperationLogContent, groupMember, nil
}

// CreateImGroupBindOrders 创建群聊并绑定工单号
func (s *ImService) CreateImGroupBindOrders(ctx context.Context, staffId int64, customerId int32, ownerImId, groupName, groupOperationLogContent string, groupMember []*GroupMemberInfo, bindWorkflowNo string) (*message.CreateGroupChatRsp, error) {
	var err error

	// 生成群组ID
	newUuid := uuid.New()
	qqImGroupId := strings.Replace(newUuid.String(), "-", "", -1)

	// customerId 转string
	customerIdStr := strconv.Itoa(int(customerId))
	// 创建群组
	g := group.NewGroup()
	g.SetGroupId(qqImGroupId)
	g.SetName(groupName)
	g.SetGroupType("Public")
	g.SetAvatar(generateGroupAvatar())
	g.SetOwner(ownerImId)
	g.SetMaxMemberNum(2000)
	g.SetApplyJoinOption("FreeAccess")
	g.SetCustomData("ServiceStatus", "1")
	g.SetCustomData("CustomerId", customerIdStr)
	g.SetCustomData("WorkflowNo", bindWorkflowNo)

	// 创建新的 Member 实例，并批量添加到成员列表
	var newMembers []*group.Member
	for _, member := range groupMember {
		if member.UserType == int32(constants.ImUserType_TYPE_STAFF) && member.UserId != ownerImId {
			member.Role = "Admin"
		}
		// 创建新的 Member 实例
		tMember := group.NewMember(member.UserId)
		// 将新的成员实例加入临时数组
		newMembers = append(newMembers, tMember)
	}

	// 一次性写入所有成员
	g.AddMembers(newMembers...)

	// 调用腾讯IM创建群组
	_, err = global.ServerConfig.TencentIM.Group().CreateGroup(g)
	if err != nil {
		return nil, err
	}

	// 发送系统通知
	if err = sendSystemMessage(qqImGroupId, "群聊已创建"); err != nil {
		return nil, fmt.Errorf("send system message failed: %w", err)
	}
	// 创建群聊，入库
	imGroupDao := models.NewQqImGroupDao()
	if err = imGroupDao.CreateImGroup(ctx, &daomodel.QqImGroup{
		GroupType:    int32(constants.ImUserType_TYPE_STAFF),
		CustomerID:   int64(customerId),
		QqImGroupID:  qqImGroupId,
		Notification: g.GetNotification(),
		Name:         g.GetName(),
		Introduction: g.GetIntroduction(),
		FaceURL:      g.GetAvatar(),
		OwnerAccount: g.GetOwner(),
	}); err != nil {
		return nil, err
	}

	err = setGroupMemberData(ctx, qqImGroupId, groupMember)
	if err != nil {
		return nil, err
	}

	// 绑定工单号
	if err = cross_models.BindWorkflowNoAndGroupId(ctx, qqImGroupId, bindWorkflowNo); err != nil {
		return nil, err
	}
	// 添加群操作日志
	operationLog := &daomodel.QqImGroupOperationLog{
		QqImGroupID: qqImGroupId,
		EmployeeID:  staffId,
		Content:     groupOperationLogContent,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err = global.DB.WithContext(ctx).Create(operationLog).Error; err != nil {
		return nil, fmt.Errorf("create group operation log failed: %w", err)
	}

	resp := &message.CreateGroupChatRsp{
		Base:    coderror.MakeSuccessBaseRsp(),
		GroupId: qqImGroupId,
	}
	return resp, nil
}

// GetGroupChatByCustomerId 通过客户id获取相关群聊
func (s *ImService) GetGroupChatByCustomerId(ctx context.Context, req *message.GetGroupChatByCustomerIdReq) (resp *message.GetGroupChatByCustomerIdRsp, err error) {
	resp = &message.GetGroupChatByCustomerIdRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	// 查询客户所在的群组信息
	imGroupDao := models.NewQqImGroupDao()
	groups, err := imGroupDao.GetGroupsByCustomerId(ctx, int64(req.CustomerId))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return resp, nil
		}
		return nil, err
	}

	// 如果提供了员工ID，只返回员工和客户共同所在的群聊
	if req.EmployeeId > 0 {
		// 获取员工所在的群聊
		employeeGroupIds, err := cross_models.GetGroupsByEmployeeId(ctx, req.EmployeeId)
		if err != nil {
			return nil, err
		}

		// 如果员工有加入的群聊，则取交集
		if len(employeeGroupIds) > 0 {
			// 创建一个员工群聊ID的哈希表，用于快速查找
			employeeGroupMap := make(map[string]struct{}, len(employeeGroupIds))
			for _, groupId := range employeeGroupIds {
				employeeGroupMap[groupId] = struct{}{}
			}

			// 过滤客户群聊，只保留员工也在的群聊
			commonGroups := make([]*model.QqImGroup, 0)
			for _, group := range groups {
				// 如果群聊ID在员工的群聊列表中，则添加到结果中
				if _, exists := employeeGroupMap[group.QqImGroupID]; exists {
					commonGroups = append(commonGroups, group)
				}
			}

			// 替换为交集结果
			groups = commonGroups
		} else {
			// 如果员工没有所在的群聊，返回空列表
			groups = []*model.QqImGroup{}
		}
	}

	// 构建返回数据
	groupList := make([]*message.GroupChatInfo, 0, len(groups))
	for _, item := range groups {
		groupList = append(groupList, ConvertModelGroupToRpcGroupChatInfo(item))
	}

	resp.Items = groupList
	return resp, nil
}

// BindWorkflowNoToGroupChat 工单号绑定群聊
func (s *ImService) BindWorkflowNoToGroupChat(ctx context.Context, req *message.BindWorkflowNoToGroupChatReq) (resp *message.BindWorkflowNoToGroupChatRsp, err error) {
	resp = &message.BindWorkflowNoToGroupChatRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	// 检查群组是否存在
	imGroupDao := models.NewQqImGroupDao()
	imGroup, err := imGroupDao.GetByGroupId(ctx, req.GroupId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_IMGroupNotExist)
			return resp, nil
		}
		return nil, err
	}

	// 检查工单是否存在
	workflow, err := cross_models.GetWorkflowByWorkflowNo(ctx, req.WorkflowNo)
	if err != nil {
		return nil, err
	}
	if workflow == nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowNotExist)
		return resp, nil
	}

	// 检查工单是否已经绑定群聊
	exists, err := cross_models.GetGroupByWorkflowNoAndGroupId(ctx, req.WorkflowNo)
	if err != nil {
		return nil, err
	}
	if exists {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_ImGroupAlreadyBind)
		return resp, nil
	}

	// 绑定工单号
	if err = cross_models.BindWorkflowNoAndGroupId(ctx, req.GroupId, req.WorkflowNo); err != nil {
		return nil, err
	}

	// 通过群ID获取工单号
	workFlowList, err := cross_models.GetWorkflowByQqIMGroupIds(ctx, req.GroupId)
	if err != nil {
		return nil, err
	}
	workflowNoArr := make([]string, 0)
	if len(workFlowList) != 0 {
		for _, item := range workFlowList {
			// 把workFlow.ID转成字符串
			workflowNoArr = append(workflowNoArr, strconv.FormatInt(item.ID, 10))
		}
	}

	workflowNoArr = append(workflowNoArr, strconv.FormatInt(workflow.ID, 10))
	// 把workflowNoArr转成字符串用英文逗号隔开
	workflowNoStr := strings.Join(workflowNoArr, ",")

	// 更新群组自定义字段
	modifyGroup := group.NewGroup()
	modifyGroup.SetName(imGroup.Name)
	modifyGroup.SetGroupId(req.GroupId)
	modifyGroup.SetCustomData("ServiceStatus", "1")
	modifyGroup.SetCustomData("WorkflowNo", workflowNoStr)
	if err = global.ServerConfig.TencentIM.Group().UpdateGroup(modifyGroup); err != nil {
		var e im.Error
		if errors.As(err, &e) {
			logger.CtxErrorf(ctx, "update group custom data failed, code:%d, message:%s.", e.Code(), e.Message())
		}
		return nil, fmt.Errorf("update group custom data failed: %w", err)
	}

	// 记录操作日志
	authCtx := ctxmeta.MustGetAuth(ctx)
	operationLog := &daomodel.QqImGroupOperationLog{
		QqImGroupID: req.GroupId,
		EmployeeID:  authCtx.EmployeeId(),
		Content:     fmt.Sprintf("%s将群聊绑定工单号：%s", authCtx.EmployeeName(), req.WorkflowNo),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err = global.DB.WithContext(ctx).Create(operationLog).Error; err != nil {
		return nil, fmt.Errorf("create group operation log failed: %w", err)
	}

	return resp, nil
}

func (s *ImService) GetStaffInfo(ctx context.Context, req *message.GetStaffInfoReq) (*message.GetStaffInfoRsp, error) {
	dao := models.NewQqImUserDao()
	qqImUser, err := dao.GetByImId(ctx, req.ImUserId)
	if err != nil {
		return nil, err
	}
	if qqImUser == nil {
		return &message.GetStaffInfoRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams),
		}, nil
	}
	if qqImUser.UserType != int32(constants.ImUserType_TYPE_STAFF) {
		return &message.GetStaffInfoRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams),
		}, nil
	}
	// 查询员工信息
	employeeInfo, err := cross_models.GetEmployeeById(ctx, qqImUser.UserID)
	if err != nil {
		return nil, err
	}
	if employeeInfo == nil {
		return &message.GetStaffInfoRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams),
		}, nil
	}
	return &message.GetStaffInfoRsp{
		MainRole:      employeeInfo.RoleName,
		JobFunction:   employeeInfo.JobFunction,
		WorkStartTime: employeeInfo.WorkStartTime,
		WorkEndTime:   employeeInfo.WorkEndTime,
	}, nil
}

func (s *ImService) GetNameCard(ctx context.Context, req *message.GetNameCardReq) (*message.GetNameCardRsp, error) {
	info := &message.IMNameCardInfo{
		UserId:                          0,
		UserName:                        "",
		RealName:                        "",
		Email:                           "",
		Phone:                           "",
		ImUserId:                        "",
		Avatar:                          "",
		TotalTableStep:                  0, // TODO 好像没用到
		FinishTableStep:                 0, // TODO 好像没用到
		TotalFileNum:                    0, // TODO 好像没用到
		FinishFileNum:                   0, // TODO 好像没用到
		Role:                            0,
		UserDetailedPermission:          0,
		DeleteStatusDocumentPermission:  0,
		UpdateApplicationFormPermission: 0,
		DepartmentId:                    0,
		DepartmentType:                  0, // TODO 好像没用到
		Departments:                     "",
		RegisteredName:                  "",
		CustomerTag:                     nil,
	}
	var avatar string

	switch req.ImUserType {
	case constants.ImUserType_TYPE_STAFF: // 员工
		avatar = fmt.Sprintf("%s%s", global.ServerConfig.AliOss.CdnUrlLondon, "/images/im/avatars/other.png")
		var employee *cross_models.EmployeeBaseInfo
		var err error
		if req.UserId > 0 {
			// 直接查询员工的信息
			employee, err = cross_models.GetEmployeeById(ctx, fmt.Sprintf("%d", req.UserId))
			if err != nil {
				return nil, err
			}
			if employee == nil {
				return &message.GetNameCardRsp{
					Base: coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams),
				}, nil
			}
		} else {
			// 根据im信息去查员工信息
			employee, err = cross_models.GetEmployeeByIMId(ctx, req.ImUserId)
			if err != nil {
				return nil, err
			}
			if employee == nil {
				return &message.GetNameCardRsp{
					Base: coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams),
				}, nil
			}
		}
		// 查询所有的部门信息
		deptList, err := cross_models.ListDeptByEmpId(ctx, req.UserId)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
		var departmentName []string
		for _, dept := range deptList {
			departmentName = append(departmentName, dept.Name)
		}
		// 再查im信息
		imDao := models.NewQqImUserDao()
		qqImUser, err := imDao.GetByUserId(ctx, fmt.Sprintf("%d", req.UserId), int32(constants.ImUserType_TYPE_STAFF))
		if err != nil {
			return nil, err
		}
		if qqImUser == nil {
			return &message.GetNameCardRsp{
				Base: coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams),
			}, nil
		}
		info.Role = constants.ImUserType_TYPE_STAFF
		info.UserId = employee.ID
		info.UserName = employee.AppName
		info.RealName = employee.Name
		info.Email = employee.Email
		info.Phone = employee.PhoneNumber
		info.ImUserId = qqImUser.QqImUserID
		info.RegisteredName = employee.Name
		info.Avatar = avatar
		info.DepartmentId = employee.DeptID
		info.Departments = strings.Join(departmentName, ":")
		info.UserDetailedPermission = 1
		info.DeleteStatusDocumentPermission = 1
		info.UpdateApplicationFormPermission = 1
	case constants.ImUserType_TYPE_CUSTOMER, constants.ImUserType_TYPE_PARENT:
		avatar = fmt.Sprintf("%s%s%s", global.ServerConfig.AliOss.CdnUrlLondon, "/images/im/avatars",
			condition.TernaryOperator(req.ImUserType == constants.ImUserType_TYPE_CUSTOMER, "/customer.png", "/parent.png"))
		var customer *cross_models.CustomerBaseInfo
		var err error
		if req.UserId > 0 {
			// 直接查询员工的信息
			customer, err = cross_models.GetCustomerById(ctx, req.UserId)
			if err != nil {
				return nil, err
			}
			if customer == nil {
				return &message.GetNameCardRsp{
					Base: coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams),
				}, nil
			}
		} else {
			// 根据im信息去查员工信息
			customer, err = cross_models.GetCustomerByImId(ctx, req.ImUserId, req.ImUserType)
			if err != nil {
				return nil, err
			}
			if customer == nil {
				return &message.GetNameCardRsp{
					Base: coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams),
				}, nil
			}
		}
		// 查询所有的标签信息
		tags, err := cross_models.GetCustomerTagByCustomerId(ctx, customer.ID)
		if err != nil {
			return nil, err
		}
		var tagList []*message.CustomerTag
		for _, tag := range tags {
			tagList = append(tagList, &message.CustomerTag{
				CustomerId: tag.CustomerId,
				Id:         tag.ID,
				Name:       tag.Name,
				Icon:       tag.Icon,
			})
		}
		// 判断用户角色
		if customer.UserType == 1 {
			info.Role = constants.ImUserType_TYPE_CUSTOMER
		} else {
			info.Role = constants.ImUserType_TYPE_PARENT
		}

		info.UserId = customer.ID
		info.UserName = customer.RealName // TODO 2.0去掉了username字段了
		info.RealName = customer.RealName
		info.Email = customer.Email
		info.Phone = customer.Phone
		info.ImUserId = customer.QqImUserId
		info.Avatar = avatar
		info.UserDetailedPermission = 1
		info.DeleteStatusDocumentPermission = 1
		info.UpdateApplicationFormPermission = 1
		info.CustomerTag = tagList
	}

	return &message.GetNameCardRsp{
		Info: info,
	}, nil
}

func (s *ImService) GetBatchNameCard(ctx context.Context, req *message.GetBatchNameCardReq) (*message.GetBatchNameCardRsp, error) {
	// TODO 不知道前端是否需要该接口，等需要在补充逻辑
	return &message.GetBatchNameCardRsp{}, nil
}

func generateGroupAvatar() string {
	return global.ServerConfig.AliOss.CdnUrlLondon + "/images/im/avatars/group_default.png"
}

func sendSystemMessage(groupID string, content string) error {
	msg := group.NewMessage()
	msg.SetSender("administrator")
	msg.SetPriority("first")
	msg.SetContent(private.MsgTextContent{
		Text: content,
	})

	// 发送消息需要提供 groupID 和 message
	_, err := global.ServerConfig.TencentIM.Group().SendMessage(groupID, msg)
	if err != nil {
		return err
	}

	return nil
}

func (s *ImService) SendSystemTextMessage(groupID string, content string) error {
	msg := group.NewMessage()
	msg.SetSender("小助理")
	msg.SetPriority("first")
	msg.SetContent(private.MsgTextContent{
		Text: content,
	})

	// 发送消息需要提供 groupID 和 message
	_, err := global.ServerConfig.TencentIM.Group().SendMessage(groupID, msg)
	if err != nil {
		return err
	}

	return nil
}

type ImageInfoArray struct {
	Type   int    `json:"Type"`
	Size   int    `json:"Size"`
	Width  int    `json:"Width"`
	Height int    `json:"Height"`
	URL    string `json:"URL"`
}

func (s *ImService) SendSystemImageMessage(groupID string, text string) error {
	// 读取并解析请求体
	var imageInfo ImageInfoArray
	if err := json.Unmarshal([]byte(text), &imageInfo); err != nil {
		return fmt.Errorf("failed to unmarshal image info: %v", err)
	}

	// 验证输入数据
	if imageInfo.URL == "" || imageInfo.Size == 0 || imageInfo.Width == 0 || imageInfo.Height == 0 {
		return fmt.Errorf("invalid image info: %+v", imageInfo)
	}

	// 获取路径中的文件名
	fileNameWithExt := path.Base(imageInfo.URL)
	if fileNameWithExt == "" {
		return fmt.Errorf("invalid URL: %s", imageInfo.URL)
	}

	// 截取文件名（去掉扩展名）
	uuidStr := strings.TrimSuffix(fileNameWithExt, path.Ext(fileNameWithExt))

	// 创建消息
	msg := group.NewMessage()
	msg.SetSender("小助理")
	msg.SetNoLastMsg()
	msg.SetPriority("first")
	msg.SetContent(private.MsgImageContent{
		UUID:        uuidStr,
		ImageFormat: GetImageFormatCode(imageInfo.URL), // 获取图片格式代码
		ImageInfos:  createImageInfos(&imageInfo),
	})

	// 发送消息需要提供 groupID 和 message
	if _, err := global.ServerConfig.TencentIM.Group().SendMessage(groupID, msg); err != nil {
		return fmt.Errorf("failed to send message: %v", err)
	}

	return nil
}

// 辅助函数：创建 ImageInfo 切片
func createImageInfos(imageInfo *ImageInfoArray) []*private.ImageInfo {
	return []*private.ImageInfo{
		createImageInfo(1, imageInfo),
		createImageInfo(2, imageInfo),
		createImageInfo(3, imageInfo),
	}
}

// 辅助函数：创建单个 ImageInfo
func createImageInfo(imageType int, imageInfo *ImageInfoArray) *private.ImageInfo {
	return &private.ImageInfo{
		Type:   imageType,
		Size:   imageInfo.Size,
		Width:  imageInfo.Width,
		Height: imageInfo.Height,
		Url:    imageInfo.URL,
	}
}

func setGroupMemberData(ctx context.Context, groupID string, members []*GroupMemberInfo) error {
	imGroupsMemberDao := models.NewQqImGroupsMemberDao()
	// 将群成员信息保存到数据库
	for _, member := range members {
		err := setMemberData(groupID, member)
		if err != nil {
			return err
		}

		groupMember := &daomodel.QqImGroupsMember{
			ImGroupID: groupID,
			ImUserID:  member.UserId,
			NameCard:  member.NameCard,
		}

		if err := imGroupsMemberDao.Create(ctx, groupMember); err != nil {
			return err
		}
	}

	return nil
}

func GetImChatTitle(ctx context.Context, req *message.GetImChatTitleReq) (resp *message.GetImChatTitleRsp, err error) {
	resp = &message.GetImChatTitleRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	dao := models.NewQqImUserDao()
	qqImUser, err := dao.GetByImId(ctx, req.ImUserId)
	if err != nil {
		return nil, err
	}

	if qqImUser == nil {
		return nil, errors.New("im user not found")
	}

	employee := new(cross_models.ImChatTitleEmployee)
	customer := new(cross_models.ImChatTitleCustomer)

	userId, _ := strconv.ParseInt(qqImUser.UserID, 10, 64)

	if qqImUser.UserType == int32(constants.ImUserType_TYPE_STAFF) { // 员工
		employee, err = cross_models.GetEmployeeInfoById(ctx, userId)
		if err != nil {
			return nil, err
		}
	} else { // 客户和家长
		customer, err = cross_models.GetCustomerInfoById(ctx, userId)
		if err != nil {
			return nil, err
		}
	}

	if employee != nil {
		resp.Employee = ConvertImChatTitleEmployee2Http(employee)
	}
	if customer != nil {
		resp.Customer = ConvertImChatTitleCustomer2Http(customer)
	}

	return resp, nil
}

// ModifyGroupCustomData 修改群自定义字段
func (s *ImService) ModifyGroupCustomData(ctx context.Context, req *message.ModifyGroupCustomDataReq) (resp *message.ModifyGroupCustomDataRsp, err error) {
	resp = &message.ModifyGroupCustomDataRsp{}

	var serviceStatus string
	// 获取群名称
	groupInfo, err := global.ServerConfig.TencentIM.Group().GetGroup(req.GroupId)

	modifyGroup := group.NewGroup()
	modifyGroup.SetGroupId(req.GroupId)
	if req.ServiceStatus != "" {
		serviceStatus = "service_status"
		modifyGroup.SetCustomData("ServiceStatus", req.ServiceStatus)
	}
	if req.CustomerId != "" {
		serviceStatus = "-1"
		modifyGroup.SetCustomData("CustomerId ", req.CustomerId)
	}

	modifyGroup.SetName(groupInfo.GetName())

	authCtx := ctxmeta.MustGetAuth(ctx)

	err = s.updateGroupInfoAndLog(
		ctx,
		req.GroupId,
		authCtx.EmployeeId(),
		modifyGroup,
		serviceStatus,
		req.ServiceStatus,
		fmt.Sprintf("%s修改群自定义字段为：%s%s", authCtx.EmployeeName(), req.ServiceStatus, req.CustomerId),
	)
	if err != nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_InternalError)
	}

	return resp, nil
}

// UpdateGroupInfo 迁移IM群数据更新自定义字段
func (s *ImService) UpdateGroupInfo(ctx context.Context) error {
	const batchSize = 200 // 每批处理的数据量
	var offset int64 = 0  // 偏移量

	for {
		// 分批获取群组成员信息
		groupMembers, err := cross_models.GetImGroupMemberListWithPage(ctx, batchSize, offset)
		if err != nil {
			return fmt.Errorf("get group member list failed: %w", err)
		}

		// 如果没有数据了，退出循环
		if len(groupMembers) == 0 {
			break
		}

		// 使用map来合并相同群组的数据
		groupMap := make(map[string]*struct {
			ServiceStatus int32
			UserIds       []string
		})

		// 合并相同群组的数据，收集所有用户ID
		for _, member := range groupMembers {
			if group, exists := groupMap[member.QqImGroupId]; exists {
				group.UserIds = append(group.UserIds, member.UserId)
			} else {
				groupMap[member.QqImGroupId] = &struct {
					ServiceStatus int32
					UserIds       []string
				}{
					ServiceStatus: member.ServiceStatus,
					UserIds:       []string{member.UserId},
				}
			}
		}

		// 更新每个群组的自定义字段
		for groupId, info := range groupMap {
			serviceStatusStr := strconv.Itoa(int(info.ServiceStatus))
			userIdsStr := strings.Join(info.UserIds, ",")

			_, err := s.ModifyGroupCustomData(ctx, &message.ModifyGroupCustomDataReq{
				GroupId:       groupId,
				ServiceStatus: serviceStatusStr,
				CustomerId:    userIdsStr,
			})
			if err != nil {
				logger.CtxErrorf(ctx, "update group %s custom data failed: %v", groupId, err)
				continue
			}
		}

		// 更新偏移量，继续处理下一批数据
		offset += int64(len(groupMembers))
	}

	return nil
}

// UnBindWorkflowNoToGroupChat 工单号绑定群聊
func (s *ImService) UnBindWorkflowNoToGroupChat(ctx context.Context, req *message.UnBindWorkflowNoToGroupChatReq) (resp *message.UnBindWorkflowNoToGroupChatRsp, err error) {
	resp = &message.UnBindWorkflowNoToGroupChatRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	// 检查群组是否存在
	imGroupDao := models.NewQqImGroupDao()
	imGroup, err := imGroupDao.GetByGroupId(ctx, req.GroupId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_IMGroupNotExist)
			return resp, nil
		}
		return nil, err
	}

	// 检查工单是否存在
	workflow, err := cross_models.GetWorkflowByWorkflowNo(ctx, req.WorkflowNo)
	if err != nil {
		return nil, err
	}
	if workflow == nil {
		resp.Base = coderror.MakeBaseRsp(ctx, errno.Errno_WorkflowNotExist)
		return resp, nil
	}
	// 解除绑定工单号
	if err = cross_models.UnBindWorkflowNoAndGroupId(ctx, req.GroupId, req.WorkflowNo); err != nil {
		return nil, err
	}
	// 通过群ID获取工单号
	workFlowList, err := cross_models.GetWorkflowByQqIMGroupIds(ctx, req.GroupId)
	if err != nil {
		return nil, err
	}

	workflowNoArr := make([]string, 0)
	for _, item := range workFlowList {
		// 把workFlow.ID转成字符串
		workflowNoArr = append(workflowNoArr, strconv.FormatInt(item.ID, 10))
	}

	//从工单号列表中删除当前工单号
	workflowNoArr = utils.RemoveStringFromSlice(workflowNoArr, strconv.FormatInt(workflow.ID, 10))

	var workflowNoStr string
	//判断workflowNoArr 是否为空
	if len(workflowNoArr) == 0 {
		workflowNoStr = ""
	} else {
		// 把workflowNoArr转成字符串用英文逗号隔开
		workflowNoStr = strings.Join(workflowNoArr, ",")
	}

	// 更新群组自定义字段
	modifyGroup := group.NewGroup()
	modifyGroup.SetName(imGroup.Name)
	modifyGroup.SetGroupId(req.GroupId)
	modifyGroup.SetCustomData("ServiceStatus", "1")
	modifyGroup.SetCustomData("WorkflowNo", workflowNoStr)
	if err = global.ServerConfig.TencentIM.Group().UpdateGroup(modifyGroup); err != nil {
		var e im.Error
		if errors.As(err, &e) {
			logger.CtxErrorf(ctx, "update group custom data failed, code:%d, message:%s.", e.Code(), e.Message())
		}
		return nil, fmt.Errorf("update group custom data failed: %w", err)
	}

	// 记录操作日志
	authCtx := ctxmeta.MustGetAuth(ctx)
	operationLog := &daomodel.QqImGroupOperationLog{
		QqImGroupID: req.GroupId,
		EmployeeID:  authCtx.EmployeeId(),
		Content:     fmt.Sprintf("%s工单号解绑群聊：%s群聊id：%s", authCtx.EmployeeName(), req.WorkflowNo, req.GroupId),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err = global.DB.WithContext(ctx).Create(operationLog).Error; err != nil {
		return nil, fmt.Errorf("create group operation log failed: %w", err)
	}

	return resp, nil
}

// IsInGroupChat 工单号绑定群聊
func (s *ImService) IsInGroupChat(ctx context.Context, req *message.IsInGroupChatReq) (resp *message.IsInGroupChatRsp, err error) {
	resp = &message.IsInGroupChatRsp{
		Base: coderror.MakeSuccessBaseRsp(),
	}

	// 获取当前用户ID
	authCtx := ctxmeta.MustGetAuth(ctx)
	employeeId := authCtx.EmployeeId()

	// 查询当前用户是否在群聊中
	isIn, err := cross_models.GetImGroupMemberByUserId(ctx, employeeId, req.GroupId)
	if err != nil {
		return nil, err
	}
	resp.IsIn = isIn

	return resp, nil
}

// GetWorkFlowDetails 根据群组获取详情
func (s *ImService) GetWorkFlowDetails(ctx context.Context, req *message.GetWorkFlowDetailsReq) (resp *message.GetWorkFlowDetailsRsp, err error) {
	resp = &message.GetWorkFlowDetailsRsp{}

	workFlowList, err := cross_models.GetWorkflowByQqIMGroupIds(ctx, req.GroupId)
	if err != nil {
		return nil, err
	}

	if len(workFlowList) == 0 {
		return &message.GetWorkFlowDetailsRsp{
			Base: coderror.MakeBaseRsp(ctx, errno.Errno_InvalidParams),
		}, nil
	}
	var workOrder []*message.WorkFlowItem
	var customers []*message.WorkFlowCustomerItem
	var customerId []int64
	for _, workflow := range workFlowList {
		customerId = append(customerId, workflow.CustomerID)
	}
	customerList, err := cross_models.GetCustomerByIds(ctx, customerId)
	if err != nil {
		return nil, err
	}
	customerMap := make(map[int64]int32)
	for _, customer := range customerList {
		customerMap[customer.ID] = customer.UserIdentity
	}
	// 分两个数组可能会导致不一致
	for _, workflow := range workFlowList {
		workOrder = append(workOrder, &message.WorkFlowItem{
			BusinessName: workflow.BusinessName,
			BusinessType: workflow.BusinessType,
			WorkflowId:   workflow.ID,
			WorkflowName: workflow.WorkflowName,
		})
		customers = append(customers, &message.WorkFlowCustomerItem{
			CustomerId:   workflow.CustomerID,
			CustomerType: customerMap[workflow.CustomerID],
		})
	}

	resp.WorkOder = workOrder
	resp.Customer = customers

	return resp, nil
}

func (s *ImService) GetGroupChatNotice(ctx context.Context, req *message.GetGroupChatNoticeReq) (*message.GetGroupChatNoticeRsp, error) {
	dao := models.NewQqImGroupDao()
	groups, err := dao.GetByGroupId(ctx, req.GroupId)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if groups == nil || groups.NoticeAt == nil {
		return &message.GetGroupChatNoticeRsp{
			NoticeAt: 0,
		}, nil
	}
	return &message.GetGroupChatNoticeRsp{
		NoticeAt: groups.NoticeAt.UnixMilli(),
	}, nil
}
