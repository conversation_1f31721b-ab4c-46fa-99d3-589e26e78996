package rpc

import (
	"github.com/cloudwego/kitex/client"
	"github.com/cloudwego/kitex/pkg/loadbalance"
	"github.com/cloudwego/kitex/pkg/rpcinfo"

	financial "uofferv2/kitex_gen/server/cmd/order/financialservice"
	order "uofferv2/kitex_gen/server/cmd/order/service"
	"uofferv2/pkg/consts"
	"uofferv2/pkg/ctxspan"
	"uofferv2/pkg/kitex_middleware"
	"uofferv2/pkg/logger"
	"uofferv2/pkg/nacos"
	"uofferv2/server/cmd/user/internal/global"
)

func initOrderService() {
	groupName := consts.GroupSvc
	svcName := consts.DataIdOrder

	r, err := nacos.GetKResolve(groupName)
	if err != nil {
		logger.Fatalf("nacos resolve %s failed: %s", groupName, err.Error())
	}

	logger.Infof("start init rpc client %s %s", groupName, svcName)

	connNum := func() int {
		if nacos.IsLocalEnv() {
			return 1
		}
		return 4
	}()

	{
		c, err := order.NewClient(
			svcName,
			client.WithResolver(r), // service discovery
			client.WithLoadBalancer(loadbalance.NewWeightedBalancer()), // load balance
			client.WithMuxConnection(connNum),                          // multiplexing
			client.WithMiddleware(kitex_middleware.CommonMiddleware),
			client.WithInstanceMW(kitex_middleware.ClientMiddleware),
			client.WithTransportProtocol(ctxspan.TTHeader),
			client.WithMetaHandler(ctxspan.ClientTTHeaderHandler),
			client.WithClientBasicInfo(&rpcinfo.EndpointBasicInfo{ServiceName: svcName}),
		)

		if err != nil {
			logger.Fatalf("cannot init rpc client %s %s client! err: %v", groupName, svcName, err)
		}

		logger.Infof("init rpc client %s %s success", groupName, svcName)

		global.OrderClient = c
	}

	{
		c, err := financial.NewClient(
			svcName,
			client.WithResolver(r), // service discovery
			client.WithLoadBalancer(loadbalance.NewWeightedBalancer()), // load balance
			client.WithMuxConnection(connNum),                          // multiplexing
			client.WithMiddleware(kitex_middleware.CommonMiddleware),
			client.WithInstanceMW(kitex_middleware.ClientMiddleware),
			client.WithTransportProtocol(ctxspan.TTHeader),
			client.WithMetaHandler(ctxspan.ClientTTHeaderHandler),
			client.WithClientBasicInfo(&rpcinfo.EndpointBasicInfo{ServiceName: svcName}),
		)

		if err != nil {
			logger.Fatalf("cannot init rpc client %s %s client! err: %v", groupName, svcName, err)
		}

		logger.Infof("init rpc client %s %s success", groupName, svcName)

		global.FinancialClient = c
	}
}
