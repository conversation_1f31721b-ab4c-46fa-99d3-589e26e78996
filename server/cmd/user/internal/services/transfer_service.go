package services

import (
	"context"
	"errors"
	"fmt"
	"time"
	"uofferv2/kitex_gen/server/cmd/user"
	"uofferv2/pkg/ctxmeta"
	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/user/internal/models"
	"uofferv2/server/cmd/user/internal/task"

	"gorm.io/gorm"
)

type TransferService struct {
}

func NewTransferService() *TransferService {
	return &TransferService{}
}

// CreateTransferTask 创建转移任务
func (s *TransferService) CreateTransferTask(ctx context.Context, req *user.CreateTransferTaskReq) (*user.CreateTransferTaskRsp, error) {
	logger.CtxInfof(ctx, "创建转移任务: FromEmployeeID=%d, ToEmployeeID=%d, CustomerIDs=%v",
		req.FromEmployeeId, req.ToEmployeeId, req.CustomerIds)

	// 参数校验
	if req.FromEmployeeId <= 0 {
		return nil, fmt.Errorf("交出方员工ID不能为空")
	}
	if req.ToEmployeeId <= 0 {
		return nil, fmt.Errorf("接收方员工ID不能为空")
	}
	if len(req.CustomerIds) == 0 {
		return nil, fmt.Errorf("客户ID列表不能为空")
	}
	authCtx := ctxmeta.MustGetAuth(ctx)
	// 获取当前用户ID，这里简化处理，使用交出方员工ID作为创建人ID
	createdBy := authCtx.EmployeeId()

	// 创建转移任务DAO
	taskDao := models.NewTransferTaskDao()
	detailDao := models.NewTransferTaskDetailDao()

	// 生成任务编号
	taskNo, err := taskDao.GenerateTaskNo(ctx)
	if err != nil {
		logger.CtxErrorf(ctx, "生成任务编号失败: %v", err)
		return nil, fmt.Errorf("生成任务编号失败")
	}

	// 创建转移任务记录
	now := time.Now()
	transferTask := &model.TransferTask{
		TaskNo:         taskNo,
		FromEmployeeID: req.FromEmployeeId,
		ToEmployeeID:   req.ToEmployeeId,
		Status:         int32(models.TransferTaskStatusPending),
		TotalCount:     int32(len(req.CustomerIds)),
		SuccessCount:   0,
		FailedCount:    0,
		CreatedBy:      createdBy,
		CreatedAt:      now,
		UpdatedAt:      now,
	}

	// 开启事务
	err = models.WithTransaction(ctx, func(txCtx context.Context) error {
		// 创建转移任务
		if err := taskDao.Create(txCtx, transferTask); err != nil {
			logger.CtxErrorf(txCtx, "创建转移任务失败: %v", err)
			return err
		}

		// 创建转移任务详情
		var details []*model.TransferTaskDetail
		for _, customerID := range req.CustomerIds {
			detail := &model.TransferTaskDetail{
				TaskID:     transferTask.ID,
				CustomerID: customerID,
				Status:     int32(models.TransferTaskDetailStatusPending),
				Priority:   5, // 默认优先级
				RetryCount: 0,
				CreatedAt:  now,
				UpdatedAt:  now,
			}
			details = append(details, detail)
		}

		if err := detailDao.BatchCreate(txCtx, details); err != nil {
			logger.CtxErrorf(txCtx, "创建转移任务详情失败: %v", err)
			return err
		}

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("创建转移任务失败: %v", err)
	}

	// 发送MQ消息，异步处理转移任务
	go func() {
		// 获取所有任务详情
		details, err := detailDao.GetByTaskID(context.Background(), transferTask.ID)
		if err != nil {
			logger.Errorf("获取转移任务详情失败: TaskID=%d, Error=%v", transferTask.ID, err)
			return
		}

		// 更新任务状态为处理中
		err = taskDao.UpdateStatus(context.Background(), transferTask.ID, int8(models.TransferTaskStatusProcessing))
		if err != nil {
			logger.Errorf("更新转移任务状态失败: TaskID=%d, Error=%v", transferTask.ID, err)
			return
		}

		// 批量发布消息，每批50个客户ID
		const batchSize = 50
		detailBatches := make([][]*model.TransferTaskDetail, 0)

		for i := 0; i < len(details); i += batchSize {
			end := i + batchSize
			if end > len(details) {
				end = len(details)
			}
			detailBatches = append(detailBatches, details[i:end])
		}

		for _, batch := range detailBatches {
			// 构建批量消息
			var customerIDs []int64
			var detailIDs []int64

			for _, detail := range batch {
				customerIDs = append(customerIDs, detail.CustomerID)
				detailIDs = append(detailIDs, detail.ID)
			}

			// 发送批量转移任务消息
			msg := &task.TransferTaskBatchMessage{
				TaskID:         transferTask.ID,
				DetailIDs:      detailIDs,
				CustomerIDs:    customerIDs,
				FromEmployeeID: transferTask.FromEmployeeID,
				ToEmployeeID:   transferTask.ToEmployeeID,
			}

			if err := task.PublishTransferTask(context.Background(), msg); err != nil {
				logger.Errorf("发布批量转移任务消息失败: TaskID=%d, BatchSize=%d, Error=%v",
					transferTask.ID, len(batch), err)
			}
		}
	}()

	return &user.CreateTransferTaskRsp{
		TaskId: transferTask.ID,
		TaskNo: transferTask.TaskNo,
	}, nil
}

// GetTransferTaskDetail 获取转移任务详情
func (s *TransferService) GetTransferTaskDetail(ctx context.Context, req *user.GetTransferTaskDetailReq) (*user.GetTransferTaskDetailRsp, error) {
	logger.CtxInfof(ctx, "获取转移任务详情: FromEmployeeID=%d", req.FromEmployeeId)

	// 参数校验
	if req.FromEmployeeId <= 0 {
		return nil, fmt.Errorf("交出方员工ID不能为空")
	}

	// 获取转移任务
	taskDao := models.NewTransferTaskDao()
	detailDao := models.NewTransferTaskDetailDao()

	taskDetail, err := taskDao.GetByFromEmployeeID(ctx, req.FromEmployeeId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("未找到该员工的转移任务")
		}
		logger.CtxErrorf(ctx, "获取转移任务失败: %v", err)
		return nil, fmt.Errorf("获取转移任务失败")
	}

	// 统计各状态的任务数量
	counts, err := detailDao.CountByTaskIDAndStatus(ctx, taskDetail.ID)
	if err != nil {
		logger.CtxErrorf(ctx, "统计任务状态失败: %v", err)
		return nil, fmt.Errorf("统计任务状态失败")
	}

	// 计算处理中的任务数量
	processingCount := counts[models.TransferTaskDetailStatusPending] + counts[models.TransferTaskDetailStatusProcessing]

	// 构建响应
	resp := &user.GetTransferTaskDetailRsp{
		TaskId:          taskDetail.ID,
		TaskNo:          taskDetail.TaskNo,
		FromEmployeeId:  taskDetail.FromEmployeeID,
		ToEmployeeId:    taskDetail.ToEmployeeID,
		TotalCount:      int64(taskDetail.TotalCount),
		SuccessCount:    int64(taskDetail.SuccessCount),
		FailCount:       int64(taskDetail.FailedCount),
		ProcessingCount: int64(processingCount),
		TaskStatus:      taskDetail.Status,
		CreatedAt:       taskDetail.CreatedAt.Unix(),
		UpdatedAt:       taskDetail.UpdatedAt.Unix(),
	}

	return resp, nil
}

// GetTransferUser 获取交接中的用户
func (s *TransferService) GetTransferUser(ctx context.Context, req *user.GetTransferUserReq) (*user.GetTransferUserRsp, error) {
	logger.CtxInfof(ctx, "获取交接中的用户: FromEmployeeID=%d", req.FromEmployeeId)

	// 获取交接中的用户
	taskDao := models.NewTransferTaskDao()

	tasks, err := taskDao.GetTransferTaskByFromEmployeeID(ctx, req.FromEmployeeId)
	if err != nil {
		logger.CtxErrorf(ctx, "获取交接中的用户失败: %v", err)
		return nil, fmt.Errorf("获取交接中的用户失败")
	}

	// 构建响应
	rsp := &user.GetTransferUserRsp{
		Items: make([]*user.TransferObject, 0),
	}

	// 如果没有找到任务
	if len(tasks) == 0 {
		return rsp, nil
	}

	// 员工信息缓存，避免重复查询
	employeeCache := make(map[int64]*model.SysEmployee)

	// 部门信息缓存
	deptCache := make(map[int64]*model.SysDept)

	// 处理每个任务
	for _, task := range tasks {
		// 获取接收方员工信息
		toEmployee, exists := employeeCache[task.ToEmployeeID]
		if !exists {
			empDao := &models.Employee{}
			emp, err := empDao.GetEmployeeById(ctx, task.ToEmployeeID)
			if err != nil {
				logger.CtxWarnf(ctx, "获取员工信息失败, ID=%d, err=%v", task.ToEmployeeID, err)
				continue
			}
			toEmployee = emp
			employeeCache[task.ToEmployeeID] = emp
		}

		// 获取员工部门信息
		deptName := ""
		if toEmployee.DeptID > 0 {
			dept, exists := deptCache[toEmployee.DeptID]
			if !exists {
				deptDao := &models.Dept{}
				d, err := deptDao.GetDeptById(ctx, toEmployee.DeptID)
				if err != nil {
					logger.CtxWarnf(ctx, "获取部门信息失败, ID=%d, err=%v", toEmployee.DeptID, err)
				} else {
					dept = d
					deptCache[toEmployee.DeptID] = d
				}
			}
			if dept != nil {
				deptName = dept.Name
			}
		}

		// 获取任务相关的客户信息
		detailDao := models.NewTransferTaskDetailDao()
		details, err := detailDao.GetByTaskID(ctx, task.ID)
		if err != nil {
			logger.CtxWarnf(ctx, "获取任务详情失败, TaskID=%d, err=%v", task.ID, err)
			continue
		}

		// 为每个客户创建一个交接对象
		for _, detail := range details {
			transferObj := &user.TransferObject{
				UserId:                   detail.CustomerID, // 使用CustomerID作为用户ID
				ToEmployeeId:             task.ToEmployeeID,
				ToEmployeeName:           toEmployee.Name,
				ToEmployeeDepartmentName: deptName,
			}
			rsp.Items = append(rsp.Items, transferObj)
		}
	}

	return rsp, nil
}
