package task

import (
	"context"
	"encoding/json"
	"sync"
	"time"
	"uofferv2/kitex_gen/server/cmd/customer"
	"uofferv2/kitex_gen/server/cmd/message"
	"uofferv2/kitex_gen/server/cmd/order"
	"uofferv2/kitex_gen/server/cmd/workflow"
	"uofferv2/pkg/ctxspan"
	"uofferv2/pkg/logger"
	"uofferv2/pkg/mq"
	"uofferv2/server/cmd/user/internal/global"
	"uofferv2/server/cmd/user/internal/models"
)

// TransferTaskBatchMessage 批量转移任务消息结构
type TransferTaskBatchMessage struct {
	TaskID         int64   `json:"task_id"`          // 任务ID
	DetailIDs      []int64 `json:"detail_ids"`       // 任务详情ID列表
	CustomerIDs    []int64 `json:"customer_ids"`     // 客户ID列表
	FromEmployeeID int64   `json:"from_employee_id"` // 交出方员工ID
	ToEmployeeID   int64   `json:"to_employee_id"`   // 接收方员工ID
}

// InitTransferTaskConsumer 初始化转移任务消费者
func InitTransferTaskConsumer() {
	// 创建多个消费者goroutine，提高并发处理能力
	workerCount := 5
	for i := 0; i < workerCount; i++ {
		go consumeTransferTask(i)
	}
}

// consumeTransferTask 消费转移任务消息
func consumeTransferTask(workerID int) {
	ctx := ctxspan.FillSpanContext(context.Background())

	deliver, err := mq.NewDeliver(global.ServerConfig.TransferTaskConsume)
	if err != nil {
		logger.CtxErrorf(ctx, "[Worker-%d] 初始化转移任务消费者失败: %v", workerID, err)
		return
	}

	if deliver == nil {
		logger.CtxErrorf(ctx, "[Worker-%d] 转移任务消费者初始化为空", workerID)
		return
	}

	logger.CtxInfof(ctx, "[Worker-%d] 转移任务消费者初始化成功", workerID)

	// 消费处理
	ch := deliver.GetDeliverChan()
	for {
		d, ok := <-ch
		if !ok {
			logger.CtxErrorf(ctx, "[Worker-%d] 转移任务消费者通道关闭", workerID)
			break
		}

		// 解析批量消息
		var batchMsg TransferTaskBatchMessage
		if err := json.Unmarshal(d.Data, &batchMsg); err != nil {
			logger.CtxErrorf(ctx, "[Worker-%d] 解析批量转移任务消息失败: %v", workerID, err)
			continue
		}

		// 处理批量转移任务
		if err := processBatchTransferTask(ctx, &batchMsg); err != nil {
			logger.CtxErrorf(ctx, "[Worker-%d] 处理批量转移任务失败: %v", workerID, err)
		}
	}
}

// processBatchTransferTask 处理批量转移任务
func processBatchTransferTask(ctx context.Context, msg *TransferTaskBatchMessage) error {
	logger.CtxInfof(ctx, "开始处理批量转移任务: TaskID=%d, 客户数量=%d",
		msg.TaskID, len(msg.CustomerIDs))

	// 1. 更新任务详情状态为处理中
	detailDao := models.NewTransferTaskDetailDao()
	for _, detailID := range msg.DetailIDs {
		if err := detailDao.UpdateStatus(ctx, detailID, int8(models.TransferTaskDetailStatusProcessing), ""); err != nil {
			logger.CtxErrorf(ctx, "更新任务详情状态失败: DetailID=%d, Error=%v", detailID, err)
			// 继续处理其他详情，不中断整个批次
		}
	}

	// 2. 执行客户转移逻辑，处理服务间依赖关系
	errChan := make(chan error, 5) // 用于收集错误

	// 第一组：可以并行执行的服务（客户资料和财务）
	var firstGroupWg sync.WaitGroup

	// 客户资料转移
	firstGroupWg.Add(1)
	go func() {
		defer firstGroupWg.Done()
		_, err := global.CustomerProfileClient.BatchUpdateCustomerSupportAgent(ctx, &customer.BatchUpdateCustomerSupportAgentReq{
			Before:      msg.FromEmployeeID,
			After:       msg.ToEmployeeID,
			CustomerIds: msg.CustomerIDs,
		})
		if err != nil {
			errChan <- err
			logger.CtxErrorf(ctx, "客户资料转移失败: %v", err)
		} else {
			logger.CtxInfof(ctx, "客户资料转移成功: 客户数量=%d", len(msg.CustomerIDs))
		}
	}()

	// 财务转移
	firstGroupWg.Add(1)
	go func() {
		defer firstGroupWg.Done()
		_, err := global.FinancialClient.UpdateFinancialFundEmployee(ctx, &order.UpdateFinancialFundEmployeeReq{
			CustomerIds: msg.CustomerIDs,
			Before:      msg.FromEmployeeID,
			After:       msg.ToEmployeeID,
		})
		if err != nil {
			errChan <- err
			logger.CtxErrorf(ctx, "财务转移失败: %v", err)
		} else {
			logger.CtxInfof(ctx, "财务转移成功: 客户数量=%d", len(msg.CustomerIDs))
		}
	}()

	// 订单转移
	firstGroupWg.Add(1)
	go func() {
		defer firstGroupWg.Done()
		_, err := global.OrderClient.BatchUpdateOrderOwnId(ctx, &order.BatchUpdateOrderOwnIdReq{
			CustomerIds: msg.CustomerIDs,
			Before:      msg.FromEmployeeID,
			After:       msg.ToEmployeeID,
		})
		if err != nil {
			errChan <- err
		}
	}()

	// 等待第一组完成
	firstGroupWg.Wait()

	// 第二组：有依赖关系的服务（留言板必须在工单之前）

	// 留言板转移（必须在工单转移之前执行，存在CDC依赖）
	logger.CtxInfof(ctx, "开始执行留言板转移: 客户数量=%d", len(msg.CustomerIDs))
	_, err := global.MessageClient.TransferMessageBoard(ctx, &message.TransferMessageBoardReq{
		CustomerIds: msg.CustomerIDs,
		Before:      msg.FromEmployeeID,
		After:       msg.ToEmployeeID,
	})
	if err != nil {
		errChan <- err
		logger.CtxErrorf(ctx, "留言板转移失败: %v", err)
	} else {
		logger.CtxInfof(ctx, "留言板转移成功: 客户数量=%d", len(msg.CustomerIDs))
	}

	// 工单转移（在留言板转移之后执行）
	logger.CtxInfof(ctx, "开始执行工单转移: 客户数量=%d", len(msg.CustomerIDs))
	_, err = global.WorkflowClient.ChangeWorkflowEmployeeBatch(ctx, &workflow.ChangeWorkflowEmployeeBatchReq{
		CustomerIds: msg.CustomerIDs,
		Before:      msg.FromEmployeeID,
		After:       msg.ToEmployeeID,
	})
	if err != nil {
		errChan <- err
		logger.CtxErrorf(ctx, "工单转移失败: %v", err)
	} else {
		logger.CtxInfof(ctx, "工单转移成功: 客户数量=%d", len(msg.CustomerIDs))
	}

	// 关闭错误通道
	close(errChan)

	// 检查是否有错误发生
	var hasError bool
	for err := range errChan {
		logger.CtxErrorf(ctx, "客户转移操作失败: %v", err)
		hasError = true
	}

	// 3. 根据转移结果更新任务详情状态
	for i, detailID := range msg.DetailIDs {
		status := int8(models.TransferTaskDetailStatusCompleted)
		errMsg := ""

		if hasError {
			status = int8(models.TransferTaskDetailStatusFailed)
			errMsg = "批量处理过程中发生错误"
		}

		if err := detailDao.UpdateStatus(ctx, detailID, status, errMsg); err != nil {
			logger.CtxErrorf(ctx, "更新任务详情状态失败: DetailID=%d, CustomerID=%d, Error=%v",
				detailID, msg.CustomerIDs[i], err)
		}
	}

	// 4. 更新任务统计信息
	taskDao := models.NewTransferTaskDao()

	// 获取当前任务的状态统计
	counts, err := detailDao.CountByTaskIDAndStatus(ctx, msg.TaskID)
	if err != nil {
		return err
	}

	// 更新任务的成功和失败计数
	successCount := counts[models.TransferTaskDetailStatusCompleted]
	failedCount := counts[models.TransferTaskDetailStatusFailed]
	err = taskDao.UpdateCounts(ctx, msg.TaskID, successCount, failedCount)
	if err != nil {
		return err
	}

	// 检查是否所有详情都已处理完成
	pendingCount := counts[models.TransferTaskDetailStatusPending]
	processingCount := counts[models.TransferTaskDetailStatusProcessing]

	// 如果没有待处理和处理中的任务，则更新任务状态为已完成
	if pendingCount == 0 && processingCount == 0 {
		task, err := taskDao.GetByID(ctx, msg.TaskID)
		if err != nil {
			return err
		}

		// 更新任务状态为已完成，并设置完成时间
		task.Status = models.TransferTaskStatusCompleted
		now := time.Now()
		task.CompletedAt = &now

		err = taskDao.Update(ctx, task)
		if err != nil {
			return err
		}

		logger.CtxInfof(ctx, "转移任务已全部完成: TaskID=%d", msg.TaskID)
	}

	logger.CtxInfof(ctx, "批量转移任务处理成功: TaskID=%d, 客户数量=%d",
		msg.TaskID, len(msg.CustomerIDs))

	return nil
}

// PublishTransferTask 发布批量转移任务消息
func PublishTransferTask(ctx context.Context, msg *TransferTaskBatchMessage) error {
	data, err := json.Marshal(msg)
	if err != nil {
		logger.CtxErrorf(ctx, "序列化批量转移任务消息失败: %v", err)
		return err
	}

	err = global.TransferTaskPubManager.Publish(ctx, mq.PublishElement{
		Topic: global.ServerConfig.TransferTaskPublishTopic,
		Data:  data,
	})

	if err != nil {
		logger.CtxErrorf(ctx, "发布批量转移任务消息失败: %v", err)
		return err
	}

	logger.CtxInfof(ctx, "发布批量转移任务消息成功: TaskID=%d, 客户数量=%d",
		msg.TaskID, len(msg.CustomerIDs))

	return nil
}
