package models

import (
	"context"
	"time"
	"uofferv2/pkg/dao"
	"uofferv2/pkg/dao/model"
	"uofferv2/server/cmd/user/internal/global"

	"gorm.io/gorm"
)

// 转移任务详情状态
const (
	TransferTaskDetailStatusPending    = 1 // 待处理
	TransferTaskDetailStatusProcessing = 2 // 处理中
	TransferTaskDetailStatusCompleted  = 3 // 已完成
	TransferTaskDetailStatusFailed     = 4 // 失败
)

// TransferTaskDetailDao 转移任务详情数据访问对象
type TransferTaskDetailDao struct {
	tx *dao.Query
}

// NewTransferTaskDetailDao 创建转移任务详情DAO
func NewTransferTaskDetailDao() *TransferTaskDetailDao {
	return &TransferTaskDetailDao{
		tx: dao.Use(global.DB),
	}
}

// BatchCreate 批量创建转移任务详情
func (dao *TransferTaskDetailDao) BatchCreate(ctx context.Context, details []*model.TransferTaskDetail) error {
	return dao.tx.WithContext(ctx).TransferTaskDetail.Create(details...)
}

// GetByTaskID 根据任务ID获取所有详情
func (dao *TransferTaskDetailDao) GetByTaskID(ctx context.Context, taskID int64) ([]*model.TransferTaskDetail, error) {
	return dao.tx.WithContext(ctx).TransferTaskDetail.Where(dao.tx.TransferTaskDetail.TaskID.Eq(taskID)).Find()
}

// GetByTaskIDAndStatus 根据任务ID和状态获取详情
func (dao *TransferTaskDetailDao) GetByTaskIDAndStatus(ctx context.Context, taskID int64, status int8) ([]*model.TransferTaskDetail, error) {
	return dao.tx.WithContext(ctx).TransferTaskDetail.
		Where(dao.tx.TransferTaskDetail.TaskID.Eq(taskID)).
		Where(dao.tx.TransferTaskDetail.Status.Eq(int32(status))).
		Find()
}

// UpdateStatus 更新转移任务详情状态
func (dao *TransferTaskDetailDao) UpdateStatus(ctx context.Context, id int64, status int8, errorMsg string) error {
	updates := map[string]interface{}{
		"status": status,
	}
	if status == TransferTaskDetailStatusCompleted || status == TransferTaskDetailStatusFailed {
		updates["process_time"] = time.Now()
	}
	if errorMsg != "" {
		updates["error_message"] = errorMsg
	}
	_, err := dao.tx.WithContext(ctx).TransferTaskDetail.
		Where(dao.tx.TransferTaskDetail.ID.Eq(id)).
		Updates(updates)
	return err
}

// IncrementRetryCount 增加重试次数
func (dao *TransferTaskDetailDao) IncrementRetryCount(ctx context.Context, id int64) error {
	_, err := dao.tx.WithContext(ctx).TransferTaskDetail.
		Where(dao.tx.TransferTaskDetail.ID.Eq(id)).
		UpdateColumn(dao.tx.TransferTaskDetail.RetryCount, gorm.Expr("retry_count + ?", 1))
	return err
}

// CountByTaskIDAndStatus 统计任务中各状态的详情数量
func (dao *TransferTaskDetailDao) CountByTaskIDAndStatus(ctx context.Context, taskID int64) (map[int]int, error) {
	details, err := dao.tx.WithContext(ctx).TransferTaskDetail.
		Where(dao.tx.TransferTaskDetail.TaskID.Eq(taskID)).
		Find()

	if err != nil {
		return nil, err
	}

	// 手动统计各状态数量
	counts := make(map[int]int)
	for _, detail := range details {
		counts[int(detail.Status)]++
	}
	return counts, nil
}
