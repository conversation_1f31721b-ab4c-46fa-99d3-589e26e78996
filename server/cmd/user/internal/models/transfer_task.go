package models

import (
	"context"
	"errors"
	"fmt"
	"time"
	"uofferv2/pkg/dao"
	"uofferv2/pkg/dao/model"
	"uofferv2/pkg/logger"
	"uofferv2/server/cmd/user/internal/global"

	"gorm.io/gorm"
)

// 转移任务状态
const (
	TransferTaskStatusPending    = 1 // 待处理
	TransferTaskStatusProcessing = 2 // 处理中
	TransferTaskStatusCompleted  = 3 // 已完成
	TransferTaskStatusCancelled  = 4 // 已取消
)

// TransferTaskDao 转移任务数据访问对象
type TransferTaskDao struct {
	tx *dao.Query
}

// NewTransferTaskDao 创建转移任务DAO
func NewTransferTaskDao() *TransferTaskDao {
	return &TransferTaskDao{
		tx: dao.Use(global.DB),
	}
}

// Create 创建转移任务
func (dao *TransferTaskDao) Create(ctx context.Context, task *model.TransferTask) error {
	return dao.tx.WithContext(ctx).TransferTask.Create(task)
}

// GetByID 根据ID获取转移任务
func (dao *TransferTaskDao) GetByID(ctx context.Context, id int64) (*model.TransferTask, error) {
	return dao.tx.WithContext(ctx).TransferTask.Where(dao.tx.TransferTask.ID.Eq(id)).First()
}

// GetByFromEmployeeID 根据交出方员工ID获取转移任务
func (dao *TransferTaskDao) GetByFromEmployeeID(ctx context.Context, fromEmployeeID int64) (*model.TransferTask, error) {
	return dao.tx.WithContext(ctx).TransferTask.Where(dao.tx.TransferTask.FromEmployeeID.Eq(fromEmployeeID)).First()
}

// Update 更新转移任务
func (dao *TransferTaskDao) Update(ctx context.Context, task *model.TransferTask) error {
	_, err := dao.tx.WithContext(ctx).TransferTask.Updates(task)
	return err
}

// UpdateStatus 更新转移任务状态
func (dao *TransferTaskDao) UpdateStatus(ctx context.Context, id int64, status int8) error {
	_, err := dao.tx.WithContext(ctx).TransferTask.Where(dao.tx.TransferTask.ID.Eq(id)).Update(dao.tx.TransferTask.Status, status)
	return err
}

// UpdateCounts 更新转移任务计数
func (dao *TransferTaskDao) UpdateCounts(ctx context.Context, id int64, successCount, failedCount int) error {
	_, err := dao.tx.WithContext(ctx).TransferTask.Where(dao.tx.TransferTask.ID.Eq(id)).Updates(map[string]interface{}{
		"success_count": successCount,
		"failed_count":  failedCount,
	})
	return err
}

// GenerateTaskNo 生成任务编号
func (dao *TransferTaskDao) GenerateTaskNo(ctx context.Context) (string, error) {
	now := time.Now()
	prefix := fmt.Sprintf("TT%s", now.Format("20060102"))
	var taskNos []string
	err := dao.tx.WithContext(ctx).TransferTask.
		Where(dao.tx.TransferTask.TaskNo.Like(prefix+"%")).
		Order(dao.tx.TransferTask.TaskNo.Desc()).
		Limit(1).
		Pluck(dao.tx.TransferTask.TaskNo, &taskNos)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return "", err
	}
	var sequence int
	if len(taskNos) > 0 && taskNos[0] != "" {
		_, err := fmt.Sscanf(taskNos[0][len(prefix):], "%06d", &sequence)
		if err != nil {
			logger.CtxErrorf(ctx, "解析任务编号序列号失败: %v", err)
			sequence = 0
		}
	}
	sequence++
	taskNo := fmt.Sprintf("%s%06d", prefix, sequence)
	return taskNo, nil
}

// GetTransferTaskByFromEmployeeID 根据交出方员工ID获取交接中的转移任务
func (dao *TransferTaskDao) GetTransferTaskByFromEmployeeID(ctx context.Context, fromEmployeeID int64) ([]*model.TransferTask, error) {
	// 获取待处理和处理中的任务
	return dao.tx.WithContext(ctx).TransferTask.
		Where(dao.tx.TransferTask.FromEmployeeID.Eq(fromEmployeeID)).
		Where(dao.tx.TransferTask.Status.In(TransferTaskStatusPending, TransferTaskStatusProcessing)).
		Find()
}
