package models

import (
	"context"
	"uofferv2/server/cmd/user/internal/global"
)

// WithTransaction 事务辅助函数
func WithTransaction(ctx context.Context, fn func(txCtx context.Context) error) error {
	tx := global.DB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r) // re-throw panic after Rollback
		}
	}()

	if err := fn(context.WithValue(ctx, "tx", tx)); err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}
