syntax = "proto3";
package errno;
option go_package = "/errno";

enum Errno {
  SUCCESS = 0;             // 成功
  InternalError = -1000;   // 系统开小差了，请稍后再试
  ThirdPartyError = -1001; // 服务暂时不可用，请稍后重试（第三方服务异常）
  InvalidParams = 1000;    // 参数错误
  InvalidBody = 1001;      // 数据格式异常，请联系研发中心处理

  // 用户和组织 2xxx
  DomainExist = 2100;         // 集团已存在
  BrandExist = 2101;          // 品牌已存在
  DeptExist = 2102;           // 部门已存在
  DeptHasChildExist = 2103;   // 此部门下存在员工或子部门，无法删除成功
  RoleHasEmployee = 2104;     // 此角色下存在员工，无法删除成功
  EmployeePhoneExist = 2105;  // 手机号已被注册
  EmployeeEmailExist = 2106;  // 邮箱已被注册
  EmployeeNameExist = 2107;   // 姓名已存在
  MainDeptParamsError = 2108; // 主要部门不在部门内
  DeptHasEmployee = 2109;     // 此部门下存在员工，无法删除成功
  NameAlreadyExists = 2110;   // 名称已存在
  EmployeeNotExists = 2111;   // 员工ID不存在
  RoleNotExists = 2112;       // 角色ID不存在
  DeptNotExists = 2113;       // 部门ID不存在
  MenIdNotEqParentId = 2114;  // 父级id不能等于自己的id
  PermAlreadyExists = 2115;   // 权限名称已存在
  MenuAlreadyExists = 2116;   // 菜单名称已存在
  RoleAlreadyExists = 2117;   // 角色名称已存在

  // 业务错误码 (3000+)
  // 订单 和财务 3xxx
  OrderNotFound = 3000;                 // 未找到符合条件的订单
  OrderStatusNotPermission = 3001;      // 当前订单状态下，不允许此操作
  OrderCurrencyNotMatch = 3002;         // 请选择正确的币种
  OrderDisbursementAmountExceed = 3003; // 支款金额不能超过剩余可支款总额
  OrderCustomerRequired = 3004;         // 请输入客户信息
  OrderStringToDecimalErr = 3005;       // 字符串转数字错误（请检查输入的金额）
  OrderGoodsRequired = 3006;            // 请选择商品后再提交订单
  OrderRevokeWithWorkflowExist = 3007;  // 此订单已生成关联工单，不支持撤销。
  OrderCloseWithWorkflowExist = 3008;   // 此订单已生成关联工单，不支持关闭。
  OrderCloseWithDepositPaid = 3009;     // 此订单已收取定金，不支持关闭。
  OrderPayDepositRequired = 3010;       // 请选择定金实际付款日期
  FinancialAccountExists = 3500;        // 已添加该账户，不允许重复添加
  TransactionNoExists = 3501;           // 交易单号已经存在
  AccountNotSupportLink = 3502;         // 所选收款账户暂不支持获取支付链接
  SaveOrderNeedAccount = 3503;          // 请完善财务账号信息
  FinancialFundNotFound = 3504;         // 收款单不存在
  FinancialFundApprovedEditFailed = 3505; // 编辑已审核通过的收款单失败
  FinancialFundStatusNotAllowEdit = 3506; // 当前收款单状态不允许编辑
  FinancialFundNoEditableFields = 3507;   // 至少需要提供一个可编辑的字段
  FinancialFundInvalidUserType =
      3508; // 客户类型必须为1(新客户)、2(老客户)或3(其他)
  FinancialFundInvalidOperator = 3509;     // 更新操作人ID不能为空
  FinancialFundContractInfoInvalid = 3510; // 合同信息格式错误

  // product  4xxx
  ProductNotFound = 4000;      // 商品ID不存在
  CategoryNotFound = 4001;     // 专业大类不存在
  CategoryHasSub = 4002;       // 专业大类下存在专业小类，无法删除成功
  ContractInUse = 4003;        // 此合同模板被应用于服务项目中，当前不支持删除
  ProductNotAllowEdit = 4004;  // 当前商品状态下，不允许此操作
  ProductInvalidStatus = 4005; // 修改的状态无法完成
  BusinessExist = 4006;        // 业务线已存在，请重新输入
  ServiceItemExist = 4007;     // 服务项目已存在，请重新输入
  ProductNameExist = 4008;     // 商品名称已存在，请重新输入
  BusinessHasRelatedServiceId = 4009; // 此业务线下存在服务项目，当前不支持删除
  ContractHasRelatedServiceItem = 4010; // 合同使用中无法删除
  ServiceItemHasRelatedProduct = 4011; // 此条服务项目下存在商品，当前不支持删除
  ProductSpecRepeated = 4012;          // 存在重复的规格，请检查
  UniversityHasSub = 4013;             // 此大学存在绑定专业，当前不支持删除
  TopMessageBoardExceedLimit = 4014;   // 置顶留言板数量超过限制
  MessageBoardMessageForbidDelete = 4015; // 留言板消息不允许删除，超出时间限制
  ContractCntExceedLimit = 4016;          // 合同数量超过限制
  FreeProductNameExist = 4017;            // 赠品名称已存在
  UniversityCnNameExist = 4018;           // 大学中文名称已存在
  UniversityEnNameExist = 4019;           // 大学英文名称已存在
  MajorCnNameExist = 4020;                // 此大学下已存在该专业中文名称
  MajorEnNameExist = 4021;                // 此大学下已存在该专业英文名称
  MessageBoardNotFound = 4022;            // 留言板不存在
  // 授权 错误码 5xxx
  // 授权：以下状态大概率是缺陷要修复无需提醒给用户
  // 保底处理是清空token跳到登陆页
  AuthForbidden = 5000; // 您没有访问该资源的权限
  // 授权：以下状态需要重新登陆
  AuthExpired = 5010; // 会话过期
  AuthKickOut = 5011; // 其他设备登录导致强制登出
  AuthLogout = 5012;  // 您已退出登录
  // 授权：以下状态是业务错误 自行处理
  AuthInvalidUserOrPasswd = 5020; // 用户名或密码有误
  AuthForbiddenUser = 5022;       // 用户状态被禁用
  AuthUserCanceled = 5023;        // 用户已注销
  AuthUserLimitLogin = 5024;      // 客户状态异常，请联系您的服务顾问

  // 业务错误码 (6000+)
  CustomerNotFound = 6000;                      // 客户不存在
  PhoneExists = 6001;                           // 手机号已存在
  EmailExists = 6002;                           // 邮箱已被注册
  VerifyCodeInvalid = 6003;                     // 无效的验证码
  VerifyCodeExpired = 6004;                     // 验证码已过期
  PasswordInvalid = 6005;                       // 密码错误
  TagNameEmpty = 6006;                          // 标签名称不能为空
  TagIconEmpty = 6007;                          // 标签图标不能为空
  TagNameExists = 6008;                         // 标签名称已存在
  TagPreset = 6009;                             // 智能标签不支持删除
  StudentIdCannotBeNull = 6010;                 // 绑定学生Id不能为空
  PleaseVerifyPhone = 6011;                     // 请先校验手机号
  PleaseVerifyEmail = 6012;                     // 请先校验邮箱
  CustomerAccountCanceled = 6013;               // 客户已注销
  CustomerProvisionedEmailAlreadyExists = 6014; // 客户邮箱账号已存在
  CustomerProvisionedEmailNotExist = 6015;      // 客户邮箱账号不存在
  // workflow业务错误码 (7000+)
  WorkflowNotFound = 7000;                // 工单不存在
  WorkflowNodeNotFound = 7001;            // 工单节点不存在
  WorkflowTemplateNotFound = 7002;        // 工单模板不存在
  WorkflowServiceInitFailed = 7003;       // 工单实例初始化失败
  WorkflowCreateFailed = 7004;            // 工单创建失败
  WorkflowNodeCreateFailed = 7005;        // 工单节点创建失败
  WorkflowNodeTaskCreateFailed = 7006;    // 工单当前节点下的任务创建失败
  WorkflowAttachmentsCreateFailed = 7007; // 派单信息保存失败
  WorkflowTypeInvalid = 7008;             // 工单类型错误
  WorkflowNodePreviewNotCompleted = 7009; // 当前节点的前置节点存在未完成节点
  WorkflowNodeRoleInvalid = 7010;         // 处理人角色错误
  WorkflowAcceptError =
      7011; // 接收派单失败，您的角色暂无权限处理当前类型的工单
  WorkflowGroupAlreadyExists = 7012;       // 当前工单已绑定群聊
  WorkflowMessageBoardCreateFailed = 7013; // 工单留言创建失败
  WorkflowApplicationNotFound = 7014;      // 工单申请类信息附加信息未找到
  WorkflowGuidanceNotFound = 7015;         // 工单辅导类信息附加信息未找到
  WorkflowSingleNotFound = 7017;           // 工单单项类信息附加信息未找到
  WorkflowNodeTaskNotCompleted = 7016;     // 工单当前节点下的任务未完成
  WorkflowAlreadyAccepted = 7018;          // 工单已处理,请勿重复操作

  // 业务错误码 (8000+)
  SmsSendFailed = 8000;             // 短信发送失败
  TemplateParamsError = 8001;       // 模板参数错误
  ReceiverNil = 8002;               // 收件人为空
  MaxSendTimes = 8003;              // 超过最大重试次数
  ParseTemplateParamsFailed = 8004; // 解析模板参数失败
  CountryNotFound = 8005;           // 国家信息不存在

  // 业务错误码消息相关 (9000+)
  IMTooManayGroups = 9000; // 单个用户可创建和加入的群组数量超过了限制
  ImGroupStudentParentNotMatch = 9003; // 家长与学生账号不匹配
  ImGroupUserAlreadyExists = 9007;     // 成员已存在群聊中
  ImGroupAlreadyBind = 9009;           // 当前工单已绑定群聊
  IMEmployeeNotRegistered = 9010;      // 员工未注册IM
  IMGroupNotExist = 9011;              // 群组不存在
  WorkflowNotExist = 9012;             // 工单不存在
  IMCustomerNotRegistered = 9013;      // 客户未注册IM
  IMUserNotExist = 9014;               // IM用户不存在
  IMGroupMaxNameLength = 9015;         // 群名称超过100字节限制
}
