syntax = "proto3";
package admin_api;
option go_package = "server/cmd/admin_api";

import "hz.proto"; // hz框架注解
import "order_entity.proto";
import "order_enum.proto";

// 排序
message OrderBy {
  string field = 1;     // 排序字段
  OrderByType type = 2; // 排序类型
}

// 获取订单列表
message GetOrderListReq {
  int32 page_num = 1 [ (api.vd) = "$>0;msg:'页数不能为空'" ]; // <必填> 页数
  int32 page_size = 2
      [ (api.vd) = "$>0;msg:'每页数量不能为空'" ]; // <必填> 每页数量

  string order_no = 3;    // 订单编号（模糊匹配）
  string contract_no = 4; // 合同编号（模糊匹配）

  repeated int64 customer_ids = 5; // 客户UID
  repeated int64 updater_ids = 6;  // 操作人UID（出单成员）
  repeated int64 reviewer_ids = 7; // 审核人UID（最后审核人UID）
  repeated int64 executor_ids = 8; // 派单对象UID
  repeated int64 closed_ids = 9;   // 关闭人UID

  repeated int64 brand_ids = 10;    // 品牌ID
  repeated int64 business_ids = 11; // 业务线ID
  repeated int64 service_ids = 12;  // 服务项目ID

  OrderInstallmentType installment_type = 13;             // 付款方式：分期方式
  repeated OrderDisbursementType disbursement_types = 14; // 支款类型

  repeated StatusOrder statuses = 15;                 // 订单状态
  repeated StatusOrderReview status_reviews = 16;     // 审核状态
  repeated StatusOrderWorkflow status_workflows = 17; // 工单服务状态

  repeated StatusOrderPay status_pay_deposits = 21;      // 定金支付状态
  repeated StatusOrderPay status_pay_firsts = 22;        // 首款支付状态
  repeated StatusOrderPay status_pay_finals = 23;        // 尾款支付状态
  repeated StatusOrderPay status_pay_disbursements = 24; // 支款支付状态

  repeated int64 pay_deposit_at =
      31; // 定金付款时间（定金实际付款日期，单位：毫秒）（[start, end]）
  repeated int64 pay_first_at =
      32; // 首款付款时间（首款实际付款日期，单位：毫秒）（[start, end]）
  repeated int64 pay_final_at =
      33; // 尾款付款时间（尾款实际付款日期，单位：毫秒）（[start, end]）
  repeated int64 pay_disbursement_at = 34; // 支款付款时间（支款实际付款日期，单位：毫秒）（多次，仅记录最新一次）（[start,
                                           // end]）

  repeated int64 apply_deposit_at =
      41; // 定金申请审核时间（单位：毫秒）（[start, end]）
  repeated int64 apply_first_at =
      42; // 首款申请审核时间（单位：毫秒）（[start, end]）
  repeated int64 apply_final_at =
      43; // 尾款申请审核时间（单位：毫秒）（[start, end]）
  repeated int64 apply_disbursement_at = 44; // 支款申请审核时间（多次，仅记录最新一次）（单位：毫秒）（[start,
                                             // end]）

  repeated int64 pass_deposit_at =
      51; // 定金审核通过时间（单位：毫秒）（[start, end]）
  repeated int64 pass_first_at =
      52; // 首款审核通过时间（单位：毫秒）（[start, end]）
  repeated int64 pass_final_at =
      53; // 尾款审核通过时间（单位：毫秒）（[start, end]）
  repeated int64 pass_disbursement_at = 54; // 支款审核通过时间（多次，仅记录最新一次）（单位：毫秒）（[start,
                                            // end]）

  repeated int64 reject_at = 60; // 审核驳回审核时间（多次，仅记录最新一次）（单位：毫秒）（[start,
                                 // end]）

  repeated int64 created_at = 61; // 创建时间（单位：毫秒）（[start, end]）
  repeated int64 updated_at = 62; // 更新时间（单位：毫秒）（[start, end]）
  repeated int64 closed_at = 63;  // 关闭时间（单位：毫秒）（[start, end]）

  string brand_name = 71;    // 品牌名称（模糊匹配）
  string business_name = 72; // 业务线名称（模糊匹配）
  string service_name = 73;  // 服务项目名称（模糊匹配）
  string goods_name = 74;    // 商品名称（模糊匹配）

  StatusYesNo foreign_currency = 81; // 收入外币
  StatusYesNo urgent_service = 82;   // 服务加急
  StatusYesNo exempt_final = 83;     // 免除尾款
  StatusYesNo has_scholarship = 84;  // 奖学金
  StatusYesNo auto_schedule = 85;    // 提交订单后自动排单

  repeated OrderBy order_by = 200; // 排序，越靠前权重越高
}

// 列表公用返回字段
// 订单主键，订单编号（订单ID），订单状态，审核状态，发起申请时间，审核通过/驳回时间，创建时间，更新时间，客户信息，操作人信息，审核人信息
message OrderListItemRsp {
  OrderEntity order = 1;                     // 订单信息
  OrderPayEntity order_pay = 2;              // 订单支付信息
  repeated OrderGoodsEntity order_goods = 3; // 商品信息
  CustomerEntity customer = 5;               // 客户信息
  UpdaterEntity creator = 6;                 // 创建人信息
  UpdaterEntity updater = 7;                 // 操作人信息（创建人）
  ReviewerEntity reviewer = 8;               // 审核人信息
  UpdaterEntity closer = 9;                  // 关闭人信息
  repeated string contract_no = 11;          // 合同编号
}

// 获取订单列表
message GetOrderListRsp {
  repeated OrderListItemRsp items = 1;
  int64 total = 2;
}

// 定金订单列表（已下定金）
// 小状态（审核状态）：草稿，待审核（草稿待审核，驳回待审核），待下单（审核通过），审核驳回
// 搜索：订单ID，客户UID，创建人UID，创建日期，提交日期，审核通过日期，审核驳回日期
message GetOrderDepositListReq {
  int32 page_num = 1 [ (api.vd) = "$>0;msg:'页数不能为空'" ]; // <必填> 页数
  int32 page_size = 2
      [ (api.vd) = "$>0;msg:'每页数量不能为空'" ]; // <必填> 每页数量

  string order_no = 3; // 订单编号（模糊匹配）

  repeated int64 customer_ids = 5; // 客户UID
  repeated int64 updater_ids = 6;  // 操作人UID（出单成员）
  repeated int64 reviewer_ids = 7; // 审核人UID（最后审核人UID）

  repeated StatusOrder statuses = 15;               // 订单状态
  repeated StatusOrderReview status_reviews = 16;   // 审核状态
  repeated StatusOrderPay status_pay_deposits = 17; // 定金支付状态

  repeated int64 pay_deposit_at =
      21; // 定金付款时间（定金实际付款日期，单位：毫秒）

  repeated int64 apply_deposit_at =
      25; // 定金申请审核时间（单位：毫秒）（[start, end]）

  repeated int64 pass_deposit_at =
      29; // 定金审核通过时间（单位：毫秒）（[start, end]）

  repeated int64 reject_at = 33; // 审核驳回审核时间（多次，仅记录最新一次）（单位：毫秒）（[start,
                                 // end]）

  repeated int64 created_at = 34; // 创建时间（单位：毫秒）（[start, end]）
  repeated int64 updated_at = 35; // 更新时间（单位：毫秒）（[start, end]）

  repeated OrderBy order_by = 200; // 排序，越靠前权重越高
}

// 定金订单列表item
// 字段：订单ID，客户信息，创建人信息，创建时间，收款金额
message OrderDepositListItem {
  OrderEntity order = 1;        // 订单信息
  OrderPayEntity order_pay = 2; // 订单支付信息
  CustomerEntity customer = 5;  // 客户信息
  UpdaterEntity creator = 6;    // 创建人信息
  UpdaterEntity updater = 7;    // 操作人信息（创建人）
  ReviewerEntity reviewer = 8;  // 审核人信息
  UpdaterEntity closer = 9;     // 关闭人信息
}

// 定金订单列表（已下定金）
message GetOrderDepositListRsp {
  repeated OrderDepositListItem items = 1;
  int64 total = 2;
}

// 图片信息
message ImagePath {
  string url = 1;           // 图片地址
  string name = 2;          // 合同名称
  string thumbnail_url = 3; // 缩略图
  int64 created_at = 4;     // 创建时间
}

// 收款交易信息
message FinancialPaidList {
  int64 payment_account_id = 1;       // 支付账户ID
  repeated ImagePath images_path = 2; // 付款凭证截图
  string amount_other = 3;            // 收款金额
  string account_name = 4;            // 收款账户名称
  string amount_cny = 5;              // 收款金额 人民币
  string currency = 6;                // 币种
  string exchange_rate = 7;           // 汇率 1.00000
}

// 创建定金订单（已下定金）（提交财务审核/审核驳回重新提交）
message SaveOrderDepositReq {
  int64 id = 1;                          // 订单ID
  int64 customer_id = 2;                 // 客户UID
  StatusOrderPay status_pay_deposit = 3; // 定金支付状态
  StatusOrderReview status_review = 4;   // 审核状态
  StatusYesNo foreign_currency = 5;      // 收入外币（由定金确认）
  int64 pay_deposit_at = 6;    // 定金付款时间（定金实际付款日期，单位：毫秒）
  string amount_deposit = 7;   // 定金实际支付金额（已收定金）
  string currency_deposit = 8; // 定金币种
  repeated FinancialPaidList financial_paid_list = 9; // 财务收款支付信息
  repeated ContractObj contract_info = 10;            // 合同信息
  string remark = 11;                                 // 备注
}

// 创建定金订单（已下定金）
message SaveOrderDepositRsp {
  int64 id = 1;        // 订单ID
  string order_no = 2; // 订单编号(时间戳+6位随机数)
}

// 撤销定金订单审核（已下定金）
message DisbursementOrderDepositReq {
  int64 id = 1 [ (api.vd) = "$>0;msg:'订单id不能为空'" ]; //  <必填> 订单ID
}

// 撤销定金订单审核（已下定金）
message DisbursementOrderDepositRsp {
  int64 id = 1; // 订单ID
}

// 关闭定金订单（已下定金）
message CloseOrderDepositReq {
  int64 id = 1 [ (api.vd) = "$>0;msg:'订单id不能为空'" ]; //  <必填> 订单ID
}

// 关闭定金订单（已下定金）
message CloseOrderDepositRsp {
  int64 id = 1; // 订单ID
}

// 定金订单申请退款（已下定金）
message OrderDepositRefundReq {
  int64 id = 1 [ (api.vd) = "$>0;msg:'订单id不能为空'" ]; //  <必填> 订单ID
  string amount_disbursement_review = 2 [
    (api.vd) = "$>0;msg:'退款金额不能为空'"
  ]; // <必填> 待审核支款-金额（多次，仅记录最新一次）
  string currency_disbursement_review = 3 [
    (api.vd) = "$!='';msg:'退款币种不能为空'"
  ]; // <必填> 待审核支款-币种（多次，仅记录最新一次）
  int32 refund_receive_account_type =
      4; // 退款接收账号类型(1=支付宝 2微信 3银行卡)
  string refund_receive_account = 5;         // 退款接收账号
  string refund_reason = 6;                  // 退款原因
  string exchange_rate = 7;                  // 汇率
  repeated ContractObj refund_agreement = 8; // 支款协议
  string currency = 9;                       // 币种 财务退款用
  string amount_disbursement_foreigin = 10;  // 外币金额
}

// 定金订单申请退款（已下定金）
message OrderDepositRefundRsp {
  int64 id = 1; // 订单ID
}

// 首款订单列表（支付待确认）
// 小状态（审核状态）：草稿，待审核（草稿待审核，驳回待审核），审核驳回
// 搜索：订单ID，客户UID，创建人UID，创建日期，提交日期，审核通过日期，审核驳回日期，
//      出单品牌（多选），出单业务线（多选），服务项目（单选），商品ID/名称（模糊匹配），付款方式，合同编号
message GetOrderFirstListReq {}

// 首款订单列表item
// 字段：订单ID，服务项目，购买商品，客户信息，合同金额，付款方式，首期款金额，尾款预计金额，品牌，业务线，合同编号，创建时间，创建人
message OrderFirstListItem {}

// 首款订单列表（支付待确认）
message GetOrderFirstListRsp {
  repeated OrderListItemRsp items = 1;
  int64 total = 2;
}

message ContractObj {
  string url = 1;           // 图片地址
  string name = 2;          // 图片名称
  string thumbnail_url = 3; // 缩略图 没有不填
  int64 created_at = 4;     // 创建时间 没有不填
}

// 创建/编辑销售订单（支付待确认）
message SaveOrderFirstReq {
  OrderEntity Order = 1;                              // 订单信息
  OrderPayEntity order_pay = 2;                       // 订单支付信息
  repeated OrderGoodsEntity order_goods = 3;          // 商品信息
  repeated FinancialPaidList financial_paid_list = 4; // 财务信息

  // !调整逻辑，创建订单不再自动派单
  // DispatchWorkflowEntity dispatch_workflow = 5;  // 一键派单信息
  repeated ContractObj contract_info = 6
      [ (api.vd) = "$!='';msg:'合同不能为空'" ]; // 合同信息
  repeated int64 source_ids = 7;                 // 订单来源用户ID集合（员工）
  repeated int64 submission_ids = 8;             // 共同提交人ID集合（员工）
  string remark = 9;                             // 备注
}

// 创建/编辑销售订单（支付待确认）
message SaveOrderFirstRsp {
  int64 id = 1;        // 订单ID
  string order_no = 2; // 订单编号(时间戳+6位随机数)
}

// 撤销销售订单审核（支付待确认）
message DisbursementOrderFirstReq {
  int64 id = 1 [ (api.vd) = "$>0;msg:'订单id不能为空'" ]; //  <必填> 订单ID
}

// 撤销销售订单审核（支付待确认）
message DisbursementOrderFirstRsp {
  int64 id = 1; // 订单ID
}

// 关闭销售订单（支付待确认）
message CloseOrderFirstReq {
  int64 id = 1 [ (api.vd) = "$>0;msg:'订单id不能为空'" ]; //  <必填> 订单ID
}

// 关闭销售订单（支付待确认）
message CloseOrderFirstRsp {
  int64 id = 1; // 订单ID
}

// 尾款订单列表（尾款待支付）
// 小状态（审核状态）：服务未完成，尾款待提交，尾款待审核（草稿待审核，驳回待审核），尾款审核驳回
// 搜索：订单ID，客户UID，服务项目，商品ID/名称，创建日期，提交日期，支付日期，付款方式，出单品牌，出单业务线，
//      创建人，合同编号
message GetOrderFinalListReq {}

// 支款订单列表item
// 字段：订单ID，服务项目，购买商品，客户信息，合同金额，付款方式，首期款金额，尾款预计金额，品牌，业务线，
//      合同编码，创建时间，提交时间，支付时间，创建人
message GetOrderFinalListItem {}

// 尾款订单列表（尾款待支付）
message GetOrderFinalListRsp {
  repeated OrderListItemRsp items = 1;
  int64 total = 2;
}

// 提交尾款（尾款待支付）
message UpdateOrderFinalReq {
  int64 id = 1 [ (api.vd) = "$>0;msg:'订单id不能为空'" ]; //  <必填> 订单ID
  StatusOrderPay status_pay_final = 2;                    // 尾款支付状态
  int64 pay_final_at = 3;   // 尾款付款时间（尾款实际付款日期，单位：毫秒）
  int64 apply_final_at = 4; // 尾款-申请-审核时间（单位：毫秒）
  StatusOrderReview status_review = 5;                 // 尾款审核状态
  StatusYesNo exempt_final = 6;                        // 免除尾款
  string amount_final = 7;                             // 实收尾款金额
  string currency_final = 8;                           // 实收尾款-币种
  string exempt_final_reason = 9;                      // 免除尾款原因
  repeated FinancialPaidList financial_paid_list = 10; // 财务收款支付信息
  repeated ContractObj contract_info = 11;             // 合同信息
  string remark = 12;                                  // 备注
}

// 提交尾款（尾款待支付）
message UpdateOrderFinalRsp {
  int64 id = 1; // 订单ID
}

// 撤销尾款审核（尾款待支付）
message DisbursementOrderFinalReq {
  int64 id = 1 [ (api.vd) = "$>0;msg:'订单id不能为空'" ]; // <必填>  订单ID
}

// 撤销尾款审核（尾款待支付）
message DisbursementOrderFinalRsp {
  int64 id = 1; // 订单ID
}

// 支款订单列表（支款订单）
// 小状态（审核状态）：支款待审核，待打款，支款完成，支款审核驳回
// 搜索：订单ID，客户UID，服务项目，商品ID/名称，付款方式，出单品牌，出单业务线，创建人，合同编号，
//      创建日期，提交日期，支付日期，尾款支付日期，申请支款日期，支款类型
message GetOrderDisbursementListReq {}

// 支款订单列表item
// 字段：订单ID，服务项目，购买商品，客户信息，支款类型，支款金额，合同金额，付款方式，首期款金额，尾款金额，
//      品牌，业务线，合同编码，创建时间，用户实际付款日期，提交时间，支付时间，尾款支付时间，支款申请时间，创建人
message OrderDisbursementListItem {}

// 支款订单列表（支款订单）
message GetOrderDisbursementListRsp {
  repeated OrderListItemRsp items = 1;
  int64 total = 2;
}

// 编辑支款订单（支款订单）
message UpdateOrderDisbursementReq {
  int64 id = 1 [ (api.vd) = "$>0;msg:'订单id不能为空'" ]; //  <必填> 订单ID
  OrderDisbursementType disbursement_type = 2;            // 支款类型
  StatusOrderPay status_pay_disbursement = 3;             // 支款-支付状态
  int64 pay_disbursement_at =
      4; // 支款-付款时间（支款-实际付款日期，单位：毫秒）（多次，仅记录最新一次）
  int64 apply_disbursement_at =
      5; // 支款-申请-审核时间（多次，仅记录最新一次）（单位：毫秒）
  string refund_reason = 8;                  // 支款原因
  repeated ContractObj refund_agreement = 9; // 支款协议
  repeated ContractObj approve_log = 10;     // 支款审批记录
  int32 refund_receive_account_type =
      11; // 退款接收账号类型(1=支付宝 2银行卡 3原路退回)
  string refund_receive_account = 12;              // 支款接受账户
  repeated ContractObj scholarship_agreement = 13; // 奖学金协议
  repeated ContractObj visa = 14;                  // 签证
  repeated ContractObj student_card = 15;          // 学生卡
  repeated ContractObj tuition_payment_proof = 16; // 全额缴纳学费证明
  string exchange_rate = 17;                       // 汇率
  string real_amount_rmb = 18;                     // 人民币金额
  string currency = 19;                            // 币种
  int64 workflow_id = 20;                          // 工单ID
  string workflow_no = 21;                         // 工单编号
  string workflow_name = 22;                       // 工单名称
  string real_amount_other = 23;                   // 其他币种金额
  StatusOrderReview status_review = 24;            // 支款审核状态
  string amount_disbursement_foreigin = 25;        // 外币金额

  string amount_disbursement_review =
      41; // 待审核支款-金额（多次，仅记录最新一次）
  string currency_disbursement_review = 42; // 待审核支款-币种
  string amount_liquidated_review =
      43; // 待审核违约金-金额（多次，仅记录最新一次）
  string currency_liquidated_review = 44; // 待审核违约金-币种
  string amount_bursary_review =
      45; // 待审核奖学金-金额（多次，仅记录最新一次）
  string currency_bursary_review = 46; // 待审核奖学金-币种
  int64 refund_deadline = 47;          // 退款截止时间
}

// 编辑支款订单（支款订单）
message UpdateOrderDisbursementRsp {
  int64 id = 1; // 订单ID
}

// 撤销支款订单审核（支款订单）
message DisbursementOrderDisbursementReq {
  int64 id = 1 [ (api.vd) = "$>0;msg:'订单id不能为空'" ]; //  <必填> 订单ID
}

// 撤销支款订单审核（支款订单）
message DisbursementOrderDisbursementRsp {
  int64 id = 1; // 订单ID
}

// 支付成功订单列表（支付成功）
// 搜索：订单ID，客户UID，服务项目，商品ID/名称，付款方式，出单品牌，出单业务线，创建人，合同编码
//      创建日期，提交日期，支付日期，尾款支付日期
message GetOrderSuccessListReq {}

// 支付成功订单列表item
// 字段：订单ID，服务项目，购买商品，客户信息，合同金额（首期款+尾款，币种：人民币），付款方式，首期款金额，尾款金额，
//      品牌，业务下，合同编码，用户实际付款日期，创建时间，提交时间，首款支付时间，尾款支付时间，创建人
message OrderSuccessListItem {}

// 支付成功订单列表（支付成功）
message GetOrderSuccessListRsp {
  repeated OrderListItemRsp items = 1;
  int64 total = 2;
}

// 关闭订单列表（交易关闭）
// 搜索：订单ID，客户UID，服务项目名称，商品ID/商品名称，关闭日期，付款方式，出单品牌，出单业务线，操作人，合同编码
message GetOrderCloseListReq {}

// 关闭订单列表item
// 字段：订单ID，服务项目，购买商品，客户，实收定金金额（币种+金额），实收首期款金额（币种+金额），尾款预计金额，付款方式，
//      品牌，业务线，合同编码，创建时间，关闭时间，操作人
message OrderCloseListItem {}

// 关闭订单列表（交易关闭）
message GetOrderCloseListRsp {
  repeated OrderListItemRsp items = 1;
  int64 total = 2;
}

// 订单ID获取订单详情（订单详情）
message GetOrderInfoReq {
  int64 id = 1 [ (api.vd) = "$>0;msg:'订单id不能为空'" ]; //  <必填> 订单ID
}

message OrderGoodsWithWorkflow {
  OrderGoodsEntity order_good = 1;          // 商品信息
  OrderWorkFlowStatusStatic work_flows = 2; // 工单信息
  int32 work_flow_total = 3;                // 工单数量
}

message OrderWorkFlowStatusStatic {
  repeated OrderWorkFlowInfo pending = 1;    // 待接收
  repeated OrderWorkFlowInfo in_service = 2; // 服务中
  repeated OrderWorkFlowInfo paused = 3;     // 服务暂停
  repeated OrderWorkFlowInfo completed = 4;  // 服务完成
  repeated OrderWorkFlowInfo terminated = 5; // 服务终止
}

message OrderWorkFlowInfo {
  int64 workflow_id = 1;                   // 工单ID
  string workflow_no = 2;                  // 工单号
  string workflow_name = 3;                // 工单名称
  StatusOrderWorkflow workflow_status = 4; // 工单状态
  int64 order_id = 5;                      // 订单ID
  int64 product_id = 6;                    // 商品ID
  int64 spec_id = 7;                       // 规格id
  int64 goods_type = 8;                    // 商品类型
  int64 goods_index = 9;                   // 商品规格索引
}

// 订单ID获取订单详情（订单详情）
message GetOrderInfoRsp {
  OrderEntity order = 1;                                    // 订单信息
  OrderPayEntity order_pay = 2;                             // 订单支付信息
  repeated OrderGoodsWithWorkflow order_goods = 3;          // 商品信息
  repeated OrderOperationLogEntity order_operation_log = 4; // 操作日志信息
  CustomerEntity customer = 5;                              // 客户信息
  UpdaterEntity creator = 6;                                // 创建人信息
  UpdaterEntity updater = 7;                           // 操作人信息（创建人）
  ReviewerEntity reviewer = 8;                         // 审核人信息
  UpdaterEntity closer = 9;                            // 关闭人信息
  repeated FinancialPaidList financial_paid_list = 10; // 财务收款支付信息
  repeated ContractObj contract_info = 11;             // 合同信息
  repeated int64 source_ids = 12;               // 订单来源用户ID集合（员工）
  repeated int64 submission_ids = 13;           // 共同提交人ID集合（员工）
  repeated UpdaterEntity source_users = 14;     // 订单来源用户ID集合（员工）
  repeated UpdaterEntity submission_users = 15; // 共同提交人ID集合（员工）
  string remark = 16;                           // 备注
}

// 订单编号获取订单详情（订单详情）
message GetOrderInfoByOrderNoReq {
  string order_no = 1
      [ (api.vd) = "$!='';msg:'订单编号不能为空'" ]; //  <必填> 订单编号
}

// 订单编号获取订单详情（订单详情）
message GetOrderInfoByOrderNoRsp {
  OrderEntity order = 1;                                    // 订单信息
  OrderPayEntity order_pay = 2;                             // 订单支付信息
  repeated OrderGoodsWithWorkflow order_goods = 3;          // 商品信息
  repeated OrderOperationLogEntity order_operation_log = 4; // 操作日志信息
  CustomerEntity customer = 5;                              // 客户信息
  UpdaterEntity creator = 6;                                // 创建人信息
  UpdaterEntity updater = 7;                                // 操作人信息
  ReviewerEntity reviewer = 8;                              // 审核人信息
  UpdaterEntity closer = 9;                                 // 关闭人信息
  repeated FinancialPaidList financial_paid_list = 10; // 财务收款支付信息
  repeated ContractObj contract_info = 11;             // 合同信息
  repeated int64 source_ids = 12;               // 订单来源用户ID集合（员工）
  repeated int64 submission_ids = 13;           // 共同提交人ID集合（员工）
  repeated UpdaterEntity source_users = 14;     // 订单来源用户ID集合（员工）
  repeated UpdaterEntity submission_users = 15; // 共同提交人ID集合（员工）
  string remark = 16;                           // 备注
}

// 通过订单id批量获取订单基本信息
message GetOrderInfoByIdsReq {
  repeated int64 ids = 1;        // 订单ID
  repeated string order_nos = 2; // 订单编号
}

// 订单基础信息
message OrderInfoItem {
  OrderEntity order = 1;                     // 订单信息
  OrderPayEntity order_pay = 2;              // 订单支付信息
  repeated OrderGoodsEntity order_goods = 3; // 商品信息
}

// 通过订单id批量获取订单
message GetOrderInfoByIdsRsp { repeated OrderInfoItem items = 1; }

// 获取货币币种信息列表
message GetCurrencyListReq {}

// 获取货币币种信息列表
message GetCurrencyListRsp { repeated CurrencyEntity items = 1; }

message AddExportOrderListReq {
  map<string, string> download_fields = 1
      [ (api.vd) = "len($) > 0; msg:'下载字段不能为空'" ]; // 下载字段  <必填>
  repeated StatusOrder statuses = 2
      [ (api.vd) = "len($) > 0; msg:'订单状态不得为空'" ]; // 订单状态  <必填>
  repeated int64 created_at = 3; // 创建时间（单位：毫秒）（[start, end]）
  repeated int64 pay_first_at =
      4; // 首款付款时间（首款实际付款日期，单位：毫秒）（[start, end]）
  repeated int64 pay_final_at =
      5; // 尾款付款时间（尾款实际付款日期，单位：毫秒）（[start, end]）
  repeated int64 apply_disbursement_at = 6; // 支款申请审核时间（多次，仅记录最新一次）（单位：毫秒）（[start,
                                            // end]）
  repeated int64 pass_disbursement_at = 7; // 支款审核通过时间（多次，仅记录最新一次）（单位：毫秒）（[start,
                                           // end]）

  string goods_name = 8;                      // 商品名称（模糊匹配） <必填>
  repeated int64 brand_ids = 9;               // 品牌ID  <必填>
  repeated int64 business_ids = 10;           // 业务线ID  <必填>
  repeated int64 service_ids = 11;            // 服务项目ID  <必填>
  OrderInstallmentType installment_type = 12; // 付款方式：分期方式  <必填>
  // 下载字段顺序 <必填>
  repeated string download_fields_order = 13
      [ (api.vd) = "len($) > 0; msg:'下载字段顺序不能为空'" ];
}

message AddExportOrderListRsp {}

// 批量更新订单工单状态
message BatchUpdateWorkflowStatusReq {
  repeated int64 order_ids = 1;            // 订单ID
  StatusOrderWorkflow workflow_status = 2; // 工作流状态
}

// 批量更新订单工单状态
message BatchUpdateWorkflowStatusRsp {}

// 获取退款高危客户列表
message GetRefundHighRiskCustomersReq {}

// 获取退款高危客户列表
message GetRefundHighRiskCustomersRsp {
  repeated int64 add_ids = 1;    // 待添加的用户ID集合
  repeated int64 remove_ids = 2; // 待移除的用户ID集合
}

// 获取红线高危客户列表
message GetRedLineRiskCustomersReq {}

// 获取红线高危客户列表
message GetRedLineRiskCustomersRsp {
  repeated int64 add_ids = 1;    // 待添加的用户ID集合
  repeated int64 remove_ids = 2; // 待移除的用户ID集合
}

// 获取工单全部为筛选状态的订单
message GetWorkflowCompleteOrdersReq {
  repeated StatusOrderWorkflow workflow_status = 1; // 工作流状态
}

// 获取工单全部为筛选状态的订单
message GetWorkflowCompleteOrdersRsp {
  repeated int64 add_ids = 1;    // 订单ID
  repeated int64 remove_ids = 2; // 订单ID
}

// 获取最新订单操作日志
message GetLatestOrderOperationLogByOrderIdReq {
  int64 order_id = 1; // 订单ID
}

// 获取最新订单操作日志
message GetLatestOrderOperationLogByOrderIdRsp {
  OrderOperationLogEntity order_operation_log = 1; // 最新订单操作日志
}

// 通过用户ID获取最新订单信息
message GetLatestOrderInfoByCustomerIdsReq {
  repeated int64 customer_id = 1; // 客户ID
}

// 通过用户ID获取最新订单信息
message GetLatestOrderInfoByCustomerIdsRsp {
  repeated SingleOrderInfoItem order_info = 1; // 最新订单信息
}

message SingleOrderInfoItem {
  OrderEntity order = 1;            // 订单信息
  OrderPayEntity order_pay = 2;     // 订单支付信息
  OrderGoodsEntity order_goods = 3; // 第一条订单商品信息
}

// 获取新旧客户列表
message GetOldNewCustomersReq {}

// 获取新旧客户列表
message GetOldNewCustomersRsp {
  repeated int64 old = 1; // 客户ID
  repeated int64 new = 2; // 客户ID
}

// 通过用户ID获取用户订单数量
message GetOrderCountByCustomerIdReq {
  int64 customer_id = 1; // 客户ID
  repeated StatusOrder status =
      2; // 订单状态（不传表示获取全部状态，传就是且逻辑）
  repeated StatusOrderReview status_review =
      3; // 订单审核状态（不传表示获取全部状态，传就是且逻辑）
}

// 通过用户ID获取用户订单数量
message GetOrderCountByCustomerIdRsp { int64 total = 1; }

// 通过用户ID批量获取用户订单数量
message GetOrderCountByCustomerIdsReq {
  repeated int64 customer_ids = 1; // 客户ID
  repeated StatusOrder status =
      2; // 订单状态（不传表示获取全部状态，传就是且逻辑）
  repeated StatusOrderReview status_review =
      3; // 订单审核状态（不传表示获取全部状态，传就是且逻辑）
}

// 通过用户ID批量获取用户订单数量
message GetOrderCountByCustomerIdsItem {
  int64 customer_id = 1;
  int64 total = 2;
}

// 通过用户ID批量获取用户订单数量
message GetOrderCountByCustomerIdsRsp {
  repeated GetOrderCountByCustomerIdsItem items = 1;
}

// 通过订单ID获取订单的操作日志
message GetOrderOperationLogListReq {
  int32 page_num = 1;  // 页数
  int32 page_size = 2; // 每页数量

  repeated int64 order_ids = 3;  // 订单ID（优先）
  repeated string order_nos = 4; // 订单编号（id=0，order_no!= 0时）

  repeated OrderBy order_by = 200; // 排序，越靠前权重越高
}

// 通过订单ID获取订单的操作日志
message GetOrderOperationLogListRsp {
  repeated OrderOperationLogEntity items = 1; // 订单操作日志
  int64 total = 2;                            // 订单数量
}

// 更换最后编辑者ID
message UpdateUpdaterIdItem {
  int64 before = 1;
  int64 after = 2;
}

// 批量更换最后编辑者ID
message BatchUpdateUpdaterIdReq {
  repeated UpdateUpdaterIdItem items = 1;
  int64 customer_id = 2;
}

// 批量更换最后编辑者ID
message BatchUpdateUpdaterIdRsp {}

// 资产转移（多用户）
message BatchUpdateOrderOwnIdReq {
  // 客户id列表
  repeated int64 customer_ids = 1;
  // 变更前的员工ID
  int64 before = 2;
  // 变更后的员工ID
  int64 after = 3;
}

// 资产转移（多用户）
message BatchUpdateOrderOwnIdRsp {}