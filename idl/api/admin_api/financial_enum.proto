syntax = "proto3";
package admin_api;
option go_package = "server/cmd/admin_api";

// 收款账户类型
enum PaymentAccountType {
  PAYMENT_ACCOUNT_TYPE_UNSPECIFIED = 0;
  PAYMENT_ACCOUNT_TYPE_ALIPAY = 1;          // 支付宝
  PAYMENT_ACCOUNT_TYPE_WECHAT = 2;          // 微信
  PAYMENT_ACCOUNT_TYPE_BANK_ACCOUNT = 3;    // 中国银行账户
  PAYMENT_ACCOUNT_TYPE_UK_BANK_ACCOUNT = 4; // 英国银行账户
  PAYMENT_ACCOUNT_TYPE_POS = 5;             // pos机
  PAYMENT_ACCOUNT_TYPE_PAYPAL = 6;          // paypal
  PAYMENT_ACCOUNT_TYPE_OTHER = 7;           // 其他
}