syntax = "proto3";
package admin_api;
option go_package = "server/cmd/admin_api";

import "hz.proto"; // hz框架注解
import "financial_enum.proto";

message FinancialOrderInfo {
  int64 order_id = 1;      // 订单ID自增ID
  string order_no = 2;     // 订单号
  string service_name = 3; // 服务项目名称
  string goods_name = 4;   // 商品名称
  string goods_spec = 5;   // 商品规格
  string goods_num = 6;    // 数量
}

message CustomerSimpleTag {
  int64 id = 1;    // 标签id
  string name = 2; // 标签名称
  string icon = 3; // 标签图标
}

message CustomerWithTag {
  int64 id = 1;                              // 客户id
  string name = 2;                           // 姓名
  repeated CustomerSimpleTag tags = 3;       // 标签
  repeated CustomerSimpleTag smart_tags = 4; // 智能标签
}

message IdNameDept {
  int64 id = 1;      // id
  string name = 2;   // 姓名
  string depart = 3; // 部门
}

// 收款列表返回
message FinancialFundInfo {
  int64 id = 1;
  string fund_no = 2;                // 收款单号
  FinancialOrderInfo order_info = 3; // 关联订单
  int64 customer_id = 4;             // 客户
  CustomerWithTag customer_info = 5; // 客户信息
  string should_amount_other = 6;    // 没有应收金额 这里用来存合同金额了
  string real_amount_other = 7;      // 本期实收金额
  string currency = 8;               // 币种
  int32 pay_type = 9;                // 付款方式(1=一次性分款;2=分期付款)
  int32 fund_type =
      10; // 款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)
  int32 paid_type = 11;    // 收款账号类型 1=支付宝 2=微信 3=中国银行账户
                           // 4=英国银行账户 5=pos机 6=paypal 7=其他
  string contract_no = 12; // 合同编码
  int64 created_at = 13;   // 创建时间
  int64 paid_time = 14;    // 付款日期
  int64 submit_id = 15;    // 提交人id
  string submit_name = 16; // 提交人

  repeated FinancialPaidInfo financial_pai_info = 17; // 收款账号信息
  repeated string service_name = 18;                  // 服务项目名称
  string brand_name = 19;                             // 品牌
  repeated string business_name = 20;                 // 业务线
  int64 pass_time = 21;                               // 审核通过时间
  int64 reject_time = 23;                             // 审核驳回时间
  int32 approve_status = 24; // 审批状态(1=待审批;2=审批通过;3=驳回审批;0=全部);
  string real_amount_rmb = 25;                  // 实收金额人民币
  string exchange_rate = 26;                    // 汇率
  repeated FundContractInfo contract_info = 27; // 合同信息
  int64 workflow_id = 28;                       // 工单ID
  string workflow_no = 29;                      // 工单编号
  int32 user_type = 30;          // 用户类型（1新客户，2老客户,3其他）
  string should_amount_rmb = 31; // 本期应收rmb
  repeated AmountInfo third_amount_list = 32; // 第三方申请费收款总额
  repeated IdNameDept user_source = 33;       // 客户来源
  repeated IdNameDept order_source = 34;      // 订单来源
  repeated IdNameDept submit_source = 35;     // 提交人
  int32 order_status =
      36; // 订单状态#1%已下定金（定金）|2%支付待确认（首款）|3%尾款待支付（尾款）|4%支付成功|5%支款订单|6%交易关闭
  string submit_dept = 37; // 提交部门
  string remark = 38;      // 备注
}

// 收款订单列表请求参数
message FinancialFundListReq {
  int32 approve_status = 1; // 审批状态(1=待审批;2=审批通过;3=驳回审批;0=全部);
  string fund_no = 2;       // 收款单号
  string order_no = 3;      // 订单号
  int64 customer_id = 4;    // 客户ID
  int64 created_at_start = 5; // 创建日期开始时间
  int64 created_at_end = 6;   // 创建日期结束时间
  int32 fund_type =
      7; // 款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)
  string contract_no = 8;           // 合同编码
  repeated string service_name = 9; // 服务项目名称
  string goods_name = 10;           // 商品名称
  string brand_name = 11;           // 品牌
  string business_name = 12;        // 业务名称
  int64 pay_type = 13;              // 付款方式(1=一次性付款;2=分期付款)
  string currency = 14;             // 币种
  repeated int64 submit_id = 15;    // 提交人
  int64 payment_account_id = 16; // 收款账户ID,已作废，请使用payment_account_ids
  int64 pass_time_start = 17;    // 审核通过开始时间
  int64 pass_time_end = 18;      // 审核通过结束时间
  int64 reject_time_start = 19;  // 审核驳回开始时间
  int64 reject_time_end = 20;    // 审核驳回结束时间
  string order_by = 21;          // 排序字段 created_at desc
  int32 page_num = 22;           // 页数
  int32 page_size = 23;          // 每页几条
  repeated int32 approve_status_list =
      24; // 审批状态(1=待审批;2=审批通过;3=驳回审批;0=全部);
  string transaction_no = 25;              // 交易单号
  int32 user_type = 26;                    // 用户类型（1新客户，2老客户,3其他）
  int32 is_control = 27;                   // 权限控制 1控制，其他不传不控制
  string customer_name = 28;               // 客户姓名
  int64 paid_time_start = 29;              // 实际付款日期开始时间
  int64 paid_time_end = 30;                // 实际付款日期结束时间
  repeated int64 user_source = 31;         // 客户来源
  repeated int32 user_source_depart = 32;  // 客户来源部门
  repeated int64 order_source = 33;        // 订单来源
  repeated int32 order_source_depart = 34; // 订单来源部门
  repeated int32 submit_source_depart = 35; // 提交人部门
  repeated int32 order_status =
      36; // 订单状态#1%已下定金（定金）|2%支付待确认（首款）|3%尾款待支付（尾款）|4%支付成功|5%支款订单|6%交易关闭
  repeated int64 payment_account_ids = 37;                // 收款账户ID列表
  repeated PaymentAccountType payment_account_types = 38; // 收款账户类型
}

message FinancialFundListRsp {
  repeated FinancialFundInfo items = 1;
  int64 total = 2; // 总数
}

message TransactionInfo {
  int64 financial_paid_id = 1; // 收款支付ID
  string transaction_no = 2;   // 交易单号
}

// 审核通过或者驳回
message FinancialFundStatusUpdateReq {
  int64 id = 1;                                  // 收款单ID
  int32 status = 2;                              // 2通过 3//驳回
  repeated TransactionInfo transaction_info = 3; // 交易单号
  int32 approve_comment = 4;                     // 审核意见
}

message FinancialFundUpdateRsp { int64 id = 1; }

// 收款单详情
message FinancialFundDetailReq {
  int64 id = 1;        // 自增ID
  string fund_no = 2;  // 收款单号
  int64 order_id = 3;  // 订单ID
  string order_no = 4; // 订单号
  int32 fund_type =
      5; // 款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)
  int64 workflow_id = 6; // 工单ID
}

// 合同信息
message FundContractInfo {
  string url = 1;           // 图片地址
  string name = 2;          // 合同名称
  string thumbnail_url = 3; // 缩略图
}

// 第三方申请费收款总额
message AmountInfo {
  string currency = 1; // 币种
  string amount = 2;   // 金额
}

// 收款单详情返回
message FinancialFundDetail {
  int64 id = 1;                   // ID自增ID
  int64 customer_id = 2;          // 客户ID
  int64 order_id = 3;             // 订单ID自增ID
  string order_no = 4;            // 订单号
  string fund_no = 5;             // 收款单号
  string real_amount_other = 6;   // 实际收款金额
  string currency = 7;            // 币种
  string should_amount_other = 8; // 没有应收金额 这里用来存合同金额了
  int64 submit_id = 9;            // 提交者ID
  int64 created_at = 10;          // 创建时间
  int32 fund_type =
      11; // 款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)'
  int32 pay_type = 12;       // 付款方式(1=一次性付款;2=分期付款)
  string contract_no = 13;   // 合同编号
  string exchange_rate = 14; // 汇率
  int32 approve_status = 15; // 审批状态(1=待审批;2=审批通过;3=驳回审批);
  int32 discount = 16;       // 是否有折扣1 无2有
  string discount_rate = 17; // 折扣率 100.00000就是没有
  string urgent_speed = 18;  // 加急速度 1.00000就是不加急
  int64 paid_time = 19;      // 付款时间
  repeated FundContractInfo contract_info = 20;        // 合同信息
  repeated FinancialPaidInfo financial_paid_list = 21; // 收款信息
  string real_amount_rmb = 22;                         // 实收金额人民币
  string submit_name = 23;                             // 提交者姓名
  string submit_department = 24;                       // 提交者部门
  repeated AmountInfo ThirdAmountList = 25;            // 第三方申请费收款总额
  int64 approve_by = 26;                               // 审核人ID
  string approve_name = 27;                            // 审核人名字
  string approve_department = 28;                      // 审核人部门
  string approve_comment = 29;                         // 审批意见
  FinancialCustomerType user_type = 30;       // 用户类型（1新客户，2老客户）
  string remark = 31;                         // 备注
  repeated IdNameDept order_source = 34;      // 订单来源
  string should_amount_rmb = 35;              // 本期应收rmb
  IdNameDept submit_info = 36;                // 提交人信息
  repeated IdNameDept other_submit_info = 37; // 共同提交人信息
}

// 支付信息
message FinancialPaidInfo {
  int64 financial_paid_id = 1;       // 支付ID
  int64 payment_account_id = 2;      // 账户ID
  PaymentAccountType paid_type = 3;  // 账户类型 1=支付宝 2=微信 3=中国银行账户
                                     // 4=英国银行账户 5=pos机 6=paypal 7=其他
  string financial_account_name = 4; // 账户名称
  string amount_other = 5;           // 支付金额其他币种
  string currency = 6;               // 币种
  repeated FundContractInfo images_path = 7; // 收款凭证图片地址
  repeated string transaction_no = 8;        // 交易单号
  string amount_cny = 9;                     // 支付金额人民币
  string exchange_rate = 10;                 // 汇率
  string account_type_name = 11;             // 账户类型名称
}

// 审核记录
message FinancialFundApproveLog {
  int64 approve_by = 1;               // 审批人id
  string approve_name = 2;            // 审批人
  string approve_comment = 3;         // 审批意见
  repeated string transaction_no = 4; // 交易单号
  int32 status = 5;                   // 审核结果 2通过 3驳回
  int64 created_at = 6;               // 审核时间
  string approve_department = 7;      // 审批人部门
}

// 客户信息
message FundCustomer {
  int64 customer_id = 1;    // 客户ID
  string customer_name = 2; // 客户姓名
}

// 收款管理详情
message FinancialFundDetailRsp {
  CustomerWithTag customer_info = 1;                        // 客户信息
  FinancialFundDetail financial_info = 2;                   // 本次收款信息
  repeated FinancialFundApproveLog approve_log = 4;         // 审核记录
  repeated FinancialFundDetail relation_financial_info = 5; // 关联收款信息
}

// 审核通过或者不通过
message FinancialFundApproveReq {
  int64 financial_fund_id = 1;                   // 收款单ID
  string approve_comment = 2;                    // 审核意见
  repeated TransactionInfo transaction_info = 3; // 交易单号
  int32 status = 4 [ (api.vd) = "$>0;msg:'审批状态不能为空'" ];
  ;                   // 审批状态 通过2，驳回3
  int64 order_id = 5; // 订单ID自增ID
}

message FinancialFundApproveRsp {}

message PaidInfo {
  int64 payment_account_id = 1;              // 账户ID
  string amount_other = 2;                   // 支付金额其他币种
  repeated FundContractInfo images_path = 3; // 收款凭证图片地址
}

// 保存收款信息草稿
message FinancialFundDraftReq {
  int64 order_id = 1; // 订单ID
  int32 fund_type =
      2; // 款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)
  repeated PaidInfo financial_paid_list = 3; // 内容
}

message FinancialFundDraftRsp {}

// 获取收款信息草稿
message GetFinancialFundDraftReq {
  int64 order_id = 1; // 订单ID
  int32 fund_type =
      2; // 款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)
}

message GetFinancialFundDraftRsp {
  int64 order_id = 1; // 订单ID
  int32 fund_type =
      2; // 款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)
  repeated PaidInfo financial_paid_list = 3; // 内容
  string remark = 4;                         // 备注
}

message AddExportFinancialFundListReq {
  map<string, string> download_fields = 1
      [ (api.vd) = "len($) > 0; msg:'下载字段不能为空'" ]; // 下载字段 <必填>

  repeated int32 approve_status_list = 2 [
    (api.vd) = "len($) > 0; msg:'收款状态不能为空'"
  ]; // 审批状态(1=待审批;2=审批通过;3=驳回审批); <必填>
  repeated int64 created_at = 3;  // 创建时间（单位：毫秒）（[start, end]）
  repeated int64 pass_time = 4;   // 审核通过时间 （单位：毫秒）（[start, end]）
  repeated int64 reject_time = 5; // 审核驳回时间  （单位：毫秒）（[start,
                                  // end]）
  string goods_name = 6;          // 商品名称
  repeated string service_name = 7; // 服务项目名称
  string brand_name = 8;            // 品牌
  string business_name = 9;         // 业务线
  int32 pay_type = 10;              // 付款方式(1=一次性付款;2=分期付款)
  int32 fund_type =
      11; // 款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)
  // 下载字段顺序 <必填>
  repeated string download_fields_order = 12
      [ (api.vd) = "len($) > 0; msg:'下载字段顺序不能为空'" ];
  int32 user_type = 14;          // 用户类型（1新客户，2老客户,3其他)
  repeated int64 paid_time = 15; // 实际付款日期 （单位：毫秒）（[start, end]）
  repeated int32 user_source_depart = 16;   // 客户来源部门
  repeated int32 order_source_depart = 17;  // 订单来源部门
  repeated int32 submit_source_depart = 18; // 提交人部门
  repeated int32 order_status =
      19; // 订单状态#1%已下定金（定金）|2%支付待确认（首款）|3%尾款待支付（尾款）|4%支付成功|5%支款订单|6%交易关闭
}

message AddExportFinancialFundListRsp {}

message FinancialOperationLogInfo {
  int64 id = 1;
  int64 customer_id = 2;         // 客户ID
  int64 financial_id = 3;        // 收款支款单ID
  int32 oper_type = 4;           // 类型 1=收款 2=支款
  string content = 5;            // 操作内容
  int64 op_user_id = 6;          // 操作人ID
  int64 create_at = 7;           // 创建时间
  string op_user_name = 8;       // 操作人姓名
  string op_uesr_department = 9; // 操作人部门
}

// 日志列表
message FinancialOperationLogListReq {
  int64 customer_id = 1;  // 客户ID
  int64 financial_id = 2; // 收款支款单ID
  int32 oper_type = 3;    // 类型 1=收款 2=支款
  int32 page_num = 4;
  int32 page_size = 5;
  string order_by = 6; // 排序"created_at desc"
}

message FinancialOperationLogListRsp {
  repeated FinancialOperationLogInfo financial_operation_log_list = 1;
  int64 total = 2; // 总数
}

//  创建第三方申请费
message ThirdFundCreateReq {
  int64 workflow_id = 1;                               // 工单ID
  string workflow_name = 2;                            // 工单名称
  int64 customer_id = 3;                               // 客户ID
  string currency = 4;                                 // 币种
  string real_amount_other = 5;                        // 金额
  string real_amount_rmb = 6;                          // 金额人民币
  string exchange_rate = 7;                            // 汇率
  int64 paid_time = 8;                                 // 付款时间
  string workflow_no = 9;                              // 工单编号
  repeated FinancialPaidInfo financial_paid_info = 10; // 支付信息
  string remark = 11;                                  // 备注
}

// 创建第三方申请费
message ThirdFundCreateRsp {
  int64 id = 1; // ID
}

// 获取关联收款汇率请求
message RelationExchangeRateReq {
  int64 order_id = 1;    // 订单ID
  string currency = 2;   // 币种
  string order_no = 3;   // 订单编号
  int64 workflow_id = 4; // 工单ID
}

// 获取关联收款汇率返回
message RelationExchangeRateRsp {
  string exchange_rate = 1; // 汇率
}

// 获取工单关联收款信息
message WorkflowFundReq {
  string search_key = 1;    // 工单编号或者名称
  int32 page_num = 2;       // 第几页
  int32 page_size = 3;      // 每页条数
  int64 customer_id = 4;    // 客户ID
  int32 approve_status = 5; // 1=待审批;2=审批通过;3=驳回审批)
}

// 获取工单关联收款信息
message WorkflowFund {
  int64 id = 1;             // 主键ID
  string fund_no = 2;       // 收款单号
  string workflow_name = 3; // 工单名称
  string workflow_no = 4;   // 工单编号
}

message WorkflowFundRsp {
  repeated WorkflowFund items = 1; // 收款信息
  int64 total = 2;                 // 总数
}
/**
 * 编辑已审核通过的收款信息
 * 支持部分字段的重新编辑功能
 */
message FinancialFundApprovedEditReq {
  int64 financial_fund_id = 1;         // 收款单ID（必填）
  int64 paid_time = 2;                 // 实际付款日期
  int32 user_type = 3;                 // 客户类型（1新客户，2老客户，3其他）
  repeated int64 order_source_ids = 4; // 订单来源IDS
  repeated FundContractInfo contract_info = 5; // 合同文件信息
  repeated int64 other_submit_ids = 6;         // 共同提交人ID列表
  string edit_reason = 7;                      // 编辑原因（可选，用于审计）
}

/**
 * 编辑已审核通过的收款信息响应
 */
message FinancialFundApprovedEditRsp {
  int64 financial_fund_id = 1; // 收款单ID
  int64 updated_at = 2;        // 更新时间
}