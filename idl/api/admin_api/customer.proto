syntax = "proto3";
package admin_api;
option go_package = "server/cmd/admin_api";
import "hz.proto"; // hz框架注解
import "constants.proto";
import "financial_fund.proto";
import "financial_refund.proto";
import "order_enum.proto";
import "workflow_enum.proto";

// 客户来源
enum CustomerSource {
  CustomerSource_UNSPECIFIED = 0;       // 未指定
  CustomerSource_OFFICIAL_WEBSITE = 1;  // 官网
  CustomerSource_APP = 2;               // APP
  CustomerSource_CHANNEL_OPERATION = 3; // 渠道运营
}

// 客户类型，1.新客户。2  老客户
enum CustomerType {
  CustomerType_UNSPECIFIED = 0; // 未指定
  CustomerType_NEW = 1;         // 新客户
  CustomerType_OLD = 2;         // 老客户
}
// 客户状态,1,正常，2 注销，3限制登录
enum CustomerStatus {
  CustomerStatus_UNSPECIFIED = 0; // 未指定
  CustomerStatus_NORMAL = 1;      // 正常
  CustomerStatus_CANCEL = 2;      // 注销
  CustomerStatus_LIMIT_LOGIN = 3; // 限制登录
}

// 客户列表排序字段, 关联订单数，关联工单数，最近一次编辑时间
enum CustomerListSortField {
  CUSTOMER_LIST_SORT_FIELD_UNSPECIFIED = 0;    // 未指定
  CUSTOMER_LIST_SORT_FIELD_ORDER_COUNT = 1;    // 关联订单数
  CUSTOMER_LIST_SORT_FIELD_TICKET_COUNT = 2;   // 关联工单数
  CUSTOMER_LIST_SORT_FIELD_LAST_EDIT_TIME = 3; // 最近一次编辑时间
  CUSTOMER_LIST_SORT_FIELD_LAST_STATUS_CHANGE_TIME =
      4; // 最近状态变更时间， 工单/订单
}
// 定义客户文件字段常量
enum CustomerFileField {
  FIELD_UNSPECIFIED = 0;                   // 未指定
  FIELD_RESUME = 1;                        // 个人简历
  FIELD_PASSPORT = 2;                      // 护照
  FIELD_UK_VISA = 3;                       // 英国签证
  FIELD_UK_BIOMETRIC_RESIDENCY_CARD = 4;   // 英国生物识别居留卡
  FIELD_GRADUATE_DEGREE_CERTIFICATE = 5;   // 毕业证书和学位证书
  FIELD_TRANSCRIPT = 6;                    // 成绩单
  FIELD_STUDYING_CERTIFICATE = 7;          // 在读证明
  FIELD_LANGUAGE_TRANSCRIPT = 8;           // 语言成绩单
  FIELD_PERSONAL_STATEMENT_FORM = 9;       // 个人陈述或个人陈述表
  FIELD_PORTFOLIO = 10;                    // 作品集 (艺术设计类专业等)
  FIELD_WORK_AWARD_CERTIFICATE = 11;       // 工作或实习证明和获奖证书
  FIELD_RECOMMENDATION_LETTER = 12;        // 推荐信
  FIELD_COURSE_LIST = 13;                  // 课程模块清单
  FIELD_OTHER = 14;                        // 其他
  FIELD_INTERNAL_OUTPUT_CV = 15;           // 内部产出文件CV
  FIELD_INTERNAL_OUTPUT_RL = 16;           // 内部产出文件RL
  FIELD_INTERNAL_OUTPUT_PS = 17;           // 内部产出文件ps
  FIELD_INTERNAL_OUTPUT_TCX = 18;          // 内部产出文件-套磁信
  FIELD_INTERNAL_OUTPUT_SQCLB = 19;        // 内部产出文件-申请材料包
  FIELD_INTERNAL_OUTPUT_OTHER = 20;        // 内部产出文件-其他
  FIELD_RESULT_FILE_OFFER = 21;            // 结果文件-offer
  FIELD_RESULT_FILE_VISA = 22;             // 结果文件-签证
  FIELD_RESULT_FILE_REJECTION_LETTER = 23; // 结果文件-拒信
  FIELD_RESULT_FILE_OTHER = 24;            // 结果文件-其他
  FIELD_PROCESS_DOCUMENT = 25;             // 过程文件
}

// 学校信息
message SchoolInfo {
  int64 id = 1;
  // 地区
  string country = 2;
  // 院校名字
  string name = 3;
  // 院校分类
  int32 category = 4;
  // 专业分类
  int32 major_type = 5;
  // 专业
  string major = 6;
  // 学历
  int32 degree = 7;
  // 在读时间 起始年月
  string start_time = 8;
  // 在读时间 结束年月
  string end_time = 9;
  // 绩点
  string gpa = 10;
  // 平均分
  string average_score = 11;
  // 学校id
  int64 school_id = 12;
  // 地区id
  int64 country_id = 13;
  // 专业大类
  int64 major_cate = 14;
  // 专业小类
  int64 major_subcate = 15;
}
// 学生诉求
message StudentAppeal {
  int64 id = 1;
  // 学生当前所在国家/地区
  int32 current_country = 2;
  // 意向申请国家/地区
  int32 intentional_apply_country = 3;
  // 意向学校
  string intentional_school = 4;
  // 意向学校排名
  string intentional_school_rank = 5;
  // 意向专业方向
  string intentional_major = 6;
  // 能否接受交叉学科
  int32 is_accept_cross_major = 7;
  // 是否双证齐全
  int32 is_dual_degree = 8;
  // 紧急联系人
  string emergency_contact = 9;
  // 紧急联系人电话区号
  int64 emergency_contact_tel_code = 10;
  // 紧急联系人电话区号
  string emergency_contact_tel = 11;
  // 备注
  string remark = 12;
}
// 语言成绩
message LanguageAchievement {
  int64 id = 1;
  // 考试类型
  int32 exam_type = 2;
  // 考试日期
  string exam_date = 3;
  // 成绩查询用户名
  string score_inquiry_username = 4;
  // 成绩查询链接
  string score_inquiry_link = 5;
  // 成绩查询密码
  string score_inquiry_password = 6;
  // 考试总分数
  string exam_total_score = 7;
  // 考试听力分数
  string exam_listening_score = 8;
  // 考试阅读分数
  string exam_reading_score = 9;
  // 考试写作分数
  string exam_writing_score = 10;
  // 考试口语分数
  string exam_oral_score = 11;
}
// 获奖经历
message AwardExperience {
  int64 id = 1;
  // 奖项名称
  string award_name = 2;
  // 获奖时间
  string award_date = 3;
  // 获奖地点
  int32 award_location = 4;
  // 奖项级别
  int32 award_level = 5;
  // 奖项描述
  string award_desc = 6;
}
// 工作实习经历
message WorkInternshipExperience {
  int64 id = 1;
  // 职务
  string position = 2;
  // 工作单位
  string workplace = 3;
  // 开始日期
  string start_time = 4;
  // 结束日期
  string end_time = 5;
  // 工作内容
  string work_content = 6;
}
// 推荐人信息
message ReferrerInformation {
  int64 id = 1;
  // 推荐人姓名
  string name = 2;
  // 推荐人职务
  string position = 3;
  // 推荐人邮箱
  string email = 4;
  // 推荐人电话
  string tel = 5;
  // 邮编
  string postcode = 6;
  // 详细地址
  string address = 7;
}
// 内部产出文件
message InternalOutputDocument {
  // 内部产出文件CV, 支持上传多个文件
  repeated File internal_output_cv = 1;
  // 内部产出文件RL，支持上传多个文件
  repeated File internal_output_rl = 2;
  // 内部产出文件ps，支持上传多个文件
  repeated File internal_output_ps = 3;
  // 内部产出文件-套磁信，支持上传多个文件
  repeated File internal_output_tcx = 4;
  // 内部产出文件-申请材料包，支持上传多个文件
  repeated File internal_output_sqclb = 5;
  // 内部产出文件-其他，支持上传多个文件
  repeated File internal_output_other = 6;
}
// 结果文件
message ResultDocument {
  // 结果文件-offer, 支持上传多个文件
  repeated File result_file_offer = 1;
  // 结果文件-签证，支持上传多个文件
  repeated File result_file_visa = 2;
  // 结果文件-拒信，支持上传多个文件
  repeated File result_file_rejection_letter = 3;
  // 结果文件-其他，支持上传多个文件
  repeated File result_file_other = 4;
}
// 过程文件
message ProcessDocument {
  // 过程文件
  repeated File process_document = 1;
}
// 教育信息包含 3 大块。1. 教育经历。 2. 其他信息， 3. 学生资料
// 教育经历 包含多个 SchoolInfo
message EducationInfo {
  repeated SchoolInfo schools = 1;
  // 其他信息
  OtherInfo other_info = 2;
  // 学生资料
  StudentInfo student_info = 3;
}
// 其他信息
message OtherInfo {
  // 是否双证齐全
  int32 is_dual_degree = 1;
  // 意向学校
  string intentional_school = 2;
  // 意向学校排名
  string intentional_school_rank = 3;
  // 意向专业方向
  string intentional_major = 4;
  // 学生当前所在国家/地区
  int32 current_country = 5;
  // 能否接受交叉学科
  int32 is_accept_cross_major = 6;
  // 备注
  string remark = 7;
}
//  学生资料
message StudentInfo {
  // 个人简历, 支持上传多个文件
  repeated File resume = 1;
  // 护照照片页，支持上传多个文件
  repeated File passport = 2;
  // 英国签证页，支持上传多个文件
  repeated File uk_visa = 3;
  // 英国生物识别居留卡，支持上传多个文件
  repeated File uk_biometric_residence_permit = 4;
  // 毕业证书和学位证书，支持上传多个文件
  repeated File graduation_certificate = 5;
  // 成绩单，支持上传多个文件
  repeated File transcript = 6;
  // 在读证明，支持上传多个文件
  repeated File in_school_certificate = 7;
  // 语言成绩单，支持上传多个文件
  repeated File language_certificate = 8;
  // 个人陈述或个人陈述表，支持上传多个文件
  repeated File personal_statement = 9;
  // 作品集（艺术设计类专业），支持上传多个文件
  repeated File portfolio = 10;
  // 工作或实习证明和获奖证书，支持上传多个文件
  repeated File work_certificate = 11;
  // 其他资料，支持上传多个文件
  repeated File other_info = 12;
  // 推荐信，支持上传多个文件
  repeated File recommendation_letter = 13;
  // 课程模块清单，支持上传多个文件
  repeated File course_list = 14;
}

// 获取教育信息
message GetEducationInfoReq {
  // 客户id
  int64 customer_id = 1;
}

message GetEducationInfoRsp { EducationInfo education_info = 1; }
// 获取教育经历
message GetEducationExperienceReq {
  // 客户id
  int64 customer_id = 1;
}

message GetEducationExperienceRsp { repeated SchoolInfo schools = 1; }
// 获取客户扩展信息
message GetExtendedInfoReq {
  // 客户id
  int64 customer_id = 1;
}

message GetExtendedInfoRsp {
  // 学生诉求
  StudentAppeal student_appeal = 1;
  // 语言成绩
  LanguageAchievement language_achievement = 2;
  // 学生资料
  StudentInfo student_info = 3;
  // 内部产出文件
  InternalOutputDocument internal_output_document = 4;
  // 结果文件
  ResultDocument result_document = 5;
  // 过程文件
  ProcessDocument process_document = 6;
}
// 获取获奖经历
message GetAwardExperienceReq {
  // 客户id
  int64 customer_id = 1;
}

message GetAwardExperienceRsp {
  // 获奖经历
  repeated AwardExperience award_experience = 1;
}
// 获取工作实习经历
message GetWorkInternshipExperienceReq {
  // 客户id
  int64 customer_id = 1;
}

message GetWorkInternshipExperienceRsp {
  // 工作实习经历
  repeated WorkInternshipExperience work_internship_experience = 1;
}
// 获取推荐人信息
message GetReferrerInformationReq {
  // 客户id
  int64 customer_id = 1;
}

message GetReferrerInformationRsp {
  // 推荐人信息
  repeated ReferrerInformation referrer_information = 1;
}
// 更新教育信息
message UpdateEducationInfoReq {
  // 客户id
  int32 customer_id = 1;
  // 教育信息
  EducationInfo education_info = 2;
}

message UpdateEducationInfoRsp { EducationInfo education_info = 1; }

// 更新教育经历
message UpdateEducationExperienceReq {
  // 客户id
  int64 customer_id = 1;
  // 学校信息
  repeated SchoolInfo schools = 2;
}
message UpdateEducationExperienceRsp { repeated SchoolInfo schools = 1; }
// 更新学生诉求
message UpdateStudentAppealReq {
  // 客户id
  int64 customer_id = 1;
  // 学生诉求
  StudentAppeal student_appeal = 2;
}
message UpdateStudentAppealRsp { StudentAppeal student_appeal = 1; }
// 更新语言成绩
message UpdateLanguageAchievementReq {
  // 客户id
  int64 customer_id = 1;
  // 语言成绩
  LanguageAchievement language_achievement = 2;
}
message UpdateLanguageAchievementRsp {
  LanguageAchievement language_achievement = 1;
}
// 更新获奖经历
message UpdateAwardExperienceReq {
  // 客户id
  int64 customer_id = 1;
  // 获奖经历
  repeated AwardExperience award_experience = 2;
}
message UpdateAwardExperienceRsp {
  // 获奖经历
  repeated AwardExperience award_experience = 2;
}
// 更新工作实习经历
message UpdateWorkInternshipExperienceReq {
  // 客户id
  int64 customer_id = 1;
  // 工作实习经历
  repeated WorkInternshipExperience work_internship_experience = 2;
}
message UpdateWorkInternshipExperienceRsp {
  repeated WorkInternshipExperience work_internship_experience = 1;
}
// 更新推荐人信息
message UpdateReferrerInformationReq {
  // 客户id
  int64 customer_id = 1;
  // 推荐人信息
  repeated ReferrerInformation referrer_information = 2;
}
message UpdateReferrerInformationRsp {
  repeated ReferrerInformation referrer_information = 1;
}
// 更新学生资料
message UpdateStudentInfoReq {
  // 客户id
  int64 customer_id = 1;
  // 学生资料
  StudentInfo student_info = 2;
}
message UpdateStudentInfoRsp { StudentInfo student_info = 1; }
// 更新内部产出文件
message UpdateInternalOutputDocumentReq {
  // 客户id
  int64 customer_id = 1;
  // 内部产出文件
  InternalOutputDocument internal_output_document = 2;
}
message UpdateInternalOutputDocumentRsp {
  InternalOutputDocument internal_output_document = 1;
}
// 更新结果文件
message UpdateResultDocumentReq {
  // 客户id
  int64 customer_id = 1;
  // 结果文件
  ResultDocument result_document = 2;
}
message UpdateResultDocumentRsp { ResultDocument result_document = 2; }

// 国家信息
message CountryInfo {
  // id
  int64 id = 1;
  // 国家电话区号 (例如: 86)
  string country_code = 2;
  // 中文名称
  string country_name_cn = 3;
  // 英文名称
  string country_name_en = 4;
}

// 身份类型枚举
enum IdentityType {
  IDENTITY_TYPE_UNSPECIFIED = 0; // 未指定
  IDENTITY_TYPE_STUDENT = 1;     // 学生
  IDENTITY_TYPE_PARENT = 2;      // 家长
}

// 验证码用途枚举
enum VerifyCodePurpose {
  VERIFY_CODE_PURPOSE_UNSPECIFIED = 0;    // 未指定
  VERIFY_CODE_PURPOSE_REGISTER = 1;       // 注册
  VERIFY_CODE_PURPOSE_LOGIN = 2;          // 登录
  VERIFY_CODE_PURPOSE_RESET_PASSWORD = 3; // 重置密码
  // 验证真实性
  VERIFY_CODE_PURPOSE_VERIFY_REALITY = 4;
  // 修改密码
  VERIFY_CODE_PURPOSE_CHANGE_PASSWORD = 5;
}

// 验证通过方式
enum VerifyMethod {
  VERIFY_METHOD_UNSPECIFIED = 0; // 未指定
  VERIFY_METHOD_MANUAL = 1;      // 手动
  VERIFY_METHOD_VERIFY_CODE = 2; // 验证码
}

// 获取所有国家号
message GetAllCountryCodesReq {
  // 关键字, 国家名称/国家号
  string keyword = 1;
}

message GetAllCountryCodesRsp { repeated CountryInfo country_infos = 1; }

// 创建客户
message CreateCustomerReq {
  // 身份
  IdentityType identity = 1 [ (api.vd) = "$!=0;msg:'身份不能为空'" ];
  // 绑定客户，如果identity是家长，则不能为空
  int32 bind_customer_id = 2;
  // 姓名, 不能为空, 最大长度20
  string name = 3 [ (api.vd) = "$!=0;msg:'姓名不能为空'" ];
  // 国家id (必填)
  int64 country_id = 4 [ (api.vd) = "$!=0;msg:'国家id不能为空'" ];
  // 手机号, 不能为空
  string phone = 5 [ (api.vd) = "$!=0;msg:'手机号不能为空'" ];
  // 邮箱, 不能为空
  string email = 6 [ (api.vd) = "$!=0;msg:'邮箱不能为空'" ];
  // 密码
  string password = 7 [ (api.vd) = "$!=0;msg:'密码不能为空'" ];
  // 客户标签, 数组，不能为空
  repeated int64 tag_ids = 8;
  // 推荐员工id
  int64 employee_id = 9;
  // 客户来源
  CustomerSource customer_source = 10;
  // 推荐员工ids
  repeated int64 employee_ids = 11;
  // 绑定学生或者家长的ids
  repeated int64 bind_customer_ids = 12;
}

message CreateCustomerRsp {
  // 客户id
  int32 id = 1;
}

// 简单客户列表（用于下拉选择）
message ListCustomerOptionsReq {
  // 可选的简单筛选条件
  string keyword = 1;        // 关键词搜索
  IdentityType identity = 2; // 身份类型筛选
  // 页码 <必填>
  int32 page_num = 3 [ (api.vd) = "$>0;msg:'页码不能为空'" ];
  // 每页条数 <必填>
  int32 page_size = 4 [ (api.vd) = "$>0;msg:'每页条数不能为空'" ];
  // 是否需要头像
  constants.YesNo is_need_avatar = 5;
}

message ListCustomerOptionsRsp {
  repeated SimpleCustomerInfo items = 1;
  int64 total = 2;
}
message ListCustomersSort {
  CustomerListSortField sort_by = 1;
  // 是否升序
  bool is_asc = 2;
}
// 完整客户列表（用于列表页）
message ListCustomersReq {
  // 页码 <必填>
  int32 page_num = 1 [ (api.vd) = "$>0;msg:'页码不能为空'" ];
  // 每页条数 <必填>
  int32 page_size = 2 [ (api.vd) = "$>0;msg:'每页条数不能为空'" ];
  // 客户关键词,客户名称，客户id, 手机号，邮箱
  string customer_keyword = 3;
  // 跟进员工关键词, 员工名称，员工id, 员工手机号, 员工邮箱
  string employee_keyword = 4;
  // 标签ID列表
  repeated int64 tag_ids = 5;
  // 客户来源
  repeated CustomerSource customer_source = 6;
  // 客户类型
  CustomerType customer_type = 7;
  // 创建时间范围-开始
  int64 create_start = 8;
  // 创建时间范围-结束
  int64 create_end = 9;
  // 身份类型
  IdentityType identity = 10;
  // 客户状态
  CustomerStatus customer_status = 11;
  // 服务状态
  CustomerWorkOrderStatus service_status = 12;
  // 排序
  ListCustomersSort sort = 13;
  // 员工id
  int64 staff_id = 14;
  // 客户id
  int64 customer_id = 15;
  // 推荐人id
  repeated int64 staff_ids = 16;
  //  推荐人部门
  repeated int64 department_ids = 17;
  // 是否交接资产
  constants.YesNo is_transfer_assets = 18;
}

message ListCustomersRsp {
  repeated ListCustomerItem items = 1; // 客户列表
  int64 total = 2;                     // 总数
}

message ListCustomerItem {
  // 客户id
  int32 id = 1;
  // 姓名
  string name = 2;
  // 身份
  IdentityType identity = 3;
  // 客户标签
  repeated SimpleTag tags = 4;
  // 客户类型
  CustomerType customer_type = 5;
  // 关联订单数
  int64 order_count = 6;
  // 关联工单数
  int64 work_order_count = 7;
  // 最近一次编辑时间
  int64 updated_at = 8;
  // 编辑人
  SimpleEmployeeInfo operator = 9;
  // 退款标志
  constants.YesNo refund_flag = 10;
  // 客户来源
  CustomerSource customer_source = 11;
  // 创建员工
  SimpleEmployeeInfo creator = 12;
  // 最新订单信息
  LastOrderInfo latest_order = 13;
  // 最新工单信息
  CustomerWorkOrderInfo latest_work_order = 14;
  // 最近状态变更时间， 工单/订单
  int64 latest_status_change_time = 15;
  // 最近一次编辑时间，指编辑客户信息/跟进人
  int64 latest_edit_time = 16;
  // 客户状态
  CustomerStatus customer_status = 17;
  // 服务状态
  CustomerWorkOrderStatus service_status = 18;
  // 智能标签
  repeated SimpleTag smart_tags = 19;
  // 推荐员工列表
  repeated CustomerReferralEmployee employee_list = 21;
  // 手机号
  string phone = 20;
  // 邮箱
  string email = 22;
  // 跟进员工列表
  repeated EmployeeInfo follow_employees = 23;
  // 交接对象
  // 交接对象
  TransferObject transfer_object = 24;
}

message LastOrderInfo {
  // 订单号
  string order_no = 1;
  // 订单状态
  StatusOrder status = 2;
  // 审核状态
  StatusOrderReview status_review = 3;
  // 服务项目名称
  string service_name = 4;
  // 商品展示内容
  string goods_content = 5;
  // 工单服务状态
  StatusOrderWorkflow status_workflow = 6;
  // 支付状态
  StatusOrderPay status_pay_disbursement = 7;
}
// 简单客户信息
message SimpleCustomerInfo {
  // id
  int64 id = 1;
  // 姓名
  string name = 2;
  // 手机号
  string phone = 3;
  // 头像
  string avatar = 4;
  // 邮箱
  string email = 5;
  // 身份
  IdentityType identity = 6;
}

// 获取客户详情
message GetCustomerDetailReq {
  // 客户id
  int64 id = 1;
}

message GetCustomerDetailRsp {
  // 客户信息
  CustomerInfo customer_info = 1;
}

// 客户跟进员工信息
message EmployeeInfo {
  // 员工角色
  string role_name = 1;
  // role id
  int32 role_id = 2;
  // 员工id
  int32 id = 3;
  // 员工名称
  string name = 4;
  // 部门id
  int64 dept_id = 5;
  // 部门名称
  string dept_name = 6;
}
// 简单员工信息
message SimpleEmployeeInfo {
  int32 id = 1;
  string name = 2;
}

// 展示员工信息
message ShowEmployeeInfo {
  // 员工信息
  SimpleEmployeeInfo employee = 1;
  // 员工角色
  RoleInfo role = 2;
  // 部门
  DepartmentInfo department = 3;
}

// 角色信息
message RoleInfo {
  int32 id = 1;
  string name = 2;
}

// 部门信息
message DepartmentInfo {
  int32 id = 1;
  string name = 2;
}

// 客户信息
message CustomerInfo {
  // 客户id
  int32 id = 1;
  // 姓名
  string name = 2;
  // 身份
  IdentityType identity = 3;
  // 绑定客户
  repeated SimpleCustomerInfo bind_customers = 4;
  // 国家id
  int64 country_id = 5;
  // 国家号
  string country_code = 6;
  // 手机号
  string phone = 7;
  // 邮箱
  string email = 8;
  // 客户标签
  repeated SimpleTag tags = 9;
  // 智能标签
  repeated SimpleTag smart_tags = 10;
  // 客户跟进员工列表，按角色分组
  repeated EmployeeInfo follow_employees = 11;
  // 推荐员工
  SimpleEmployeeInfo employee = 12;
  // 用户画像
  string profile = 13;
  // 手机号是否已验证
  constants.YesNo phone_verified = 14;
  // 邮箱是否已验证
  constants.YesNo email_verified = 15;
  // 用户类型
  CustomerType user_type = 16;
  // 客户来源
  CustomerSource customer_source = 17;
  // 客户状态
  CustomerStatus status = 18;
  // 员工id
  int32 employee_id = 19;
  // 退款标志
  constants.YesNo refund_flag = 20;
  // 推荐员工列表
  repeated EmployeeInfo employee_list = 21;
  // 是否允许变更身份
  constants.YesNo is_allow_change_identity = 22;
  // 备注
  string remark = 23;
}

// 更新客户信息, 出了身份都能编辑
message UpdateCustomerReq {
  // 客户id
  int32 id = 1;
  // 姓名
  string name = 2;
  // 绑定客户
  int32 bind_customer_id = 3;
  // 国家id
  int64 country_id = 4;
  // 手机号
  string phone = 5;
  // 邮箱
  string email = 6;
  // 密码
  string password = 7;
  // 客户标签
  repeated int64 tag_ids = 8;
  // 推荐员工id
  int64 employee_id = 9;
  // 客户来源
  CustomerSource customer_source = 10;
  // 推荐员工ids
  repeated int64 employee_ids = 11;
  // 身份
  IdentityType identity = 12;
  // 绑定学生或者家长的ids
  repeated int64 bind_customer_ids = 13;
}

message UpdateCustomerRsp {
  // 客户id
  int32 id = 1;
}

// 获取手机验证码
message GetPhoneVerifyCodeReq {
  // 客户id
  int32 customer_id = 1;
  // 国家id
  int64 country_id = 2;
  // 手机号
  string phone = 3;
  // 验证码用途
  VerifyCodePurpose verify_code_purpose = 4;
}

// 获取邮箱验证码
message GetEmailVerifyCodeReq {
  // 客户id
  int32 customer_id = 1;
  // 邮箱, 必填
  string email = 2 [ (api.vd) = "$!='';msg:'邮箱不能为空'" ];
  // 验证码用途
  VerifyCodePurpose verify_code_purpose = 3; // 验证码用途
}

// 手机验证通过
message VerifyPhoneReq {
  // 客户id
  int32 customer_id = 1;
  // 验证码
  string verify_code = 2;
  // 国家id (必填)
  int64 country_id = 3;
  // 手机号
  string phone = 4;
  // 验证通过方式
  VerifyMethod verify_method = 5;
}

// 邮箱验证通过
message VerifyEmailReq {
  // 客户id
  int32 customer_id = 1;
  // 验证码, 手动验证的时候不需要填
  string verify_code = 2;
  // 邮箱, 必填
  string email = 3 [ (api.vd) = "$!='';msg:'邮箱不能为空'" ];
  // 验证通过方式
  VerifyMethod verify_method = 4;
}

// 更新密码
message UpdatePasswordReq {
  // 客户id
  int32 customer_id = 1;
  // 旧密码
  string old_password = 2;
  // 新密码
  string new_password = 3;
}

message UpdatePasswordRsp {
  // 成功 or 失败
  bool success = 1;
}

// 标签类型枚举
enum TagType {
  TAG_TYPE_UNSPECIFIED = 0; // 未指定类型(proto3 建议第一个枚举值为0)
  TAG_TYPE_MANUAL = 1;      // 手动标签
  TAG_TYPE_SMART = 2;       // 智能标签, 不支持创建，预设
}

// 所有标签， 只返回id和名字
message TagAllReq {
  TagType tag_type = 1; // 标签类型
}

message TagAllRsp {
  repeated SimpleTag tags = 1; // 标签列表
}
message SimpleTag {
  int64 id = 1;    // 标签id
  string name = 2; // 标签名称
  string icon = 3; // 标签图标
}
enum TagListSortField {
  TAG_LIST_SORT_UNSPECIFIED = 0;
  TAG_LIST_SORT_OPERATE_TIME_DESC = 1;
  TAG_LIST_SORT_CUSTOMER_COUNT_DESC = 2;
}
message TagListSort {
  TagListSortField sort_by = 1;
  // 是否升序
  bool is_asc = 2;
}
// 标签列表
message TagListReq {
  TagType tag_type = 1; // 标签类型
  // 页码 <必填>
  int32 page_num = 2 [ (api.vd) = "$>0;msg:'页码不能为空'" ];
  // 每页条数 <必填>
  int32 page_size = 3 [ (api.vd) = "$>0;msg:'每页条数不能为空'" ];
  string keyword = 4;         // 关键词
  int32 tag_id = 5;           // 标签id, 和关键词互斥
  constants.YesNo status = 6; // 状态
  // 排序
  TagListSort sort = 7;
}

message TagListRsp {
  repeated Tag items = 1; // 标签列表
  int64 total = 2;        // 总数
}

message Tag {
  int64 id = 1;             // 标签id
  string name = 2;          // 标签名称
  string icon = 3;          // 标签图标
  int32 customer_count = 4; // 关联客户数
  string description = 5;   // 标签说明, 智能标签才有
  int64 updated_at = 6;     // 最近一次编辑时间
  UserInfo operator = 7;    // 操作人信息
  int32 is_enabled = 8;     // 使用状态
  int32 is_smart = 9;       // 是否是智能标签
}

message UserInfo {
  int32 id = 1;    // 用户id
  string name = 2; // 用户名称
}

// 标签创建请求
message TagCreateReq {
  string name = 1
      [ (api.vd) = "$!='';msg:'标签名称不能为空'" ];           // 标签名称，必填
  string icon = 2 [ (api.vd) = "$!='';msg:'标签图不能为空'" ]; // 标签图标
}

message TagCreateRsp {
  int32 id = 1; // 标签id
}

// 删除标签
message TagDeleteReq {
  int32 id = 1; // 标签id
}

// 更新标签
message TagUpdateReq {
  int64 id = 1; // 标签id
  string name = 2
      [ (api.vd) = "$!='';msg:'标签名称不能为空'" ];           // 标签名称，必填
  string icon = 3 [ (api.vd) = "$!='';msg:'标签图不能为空'" ]; // 标签图标
}

message TagUpdateRsp {
  int64 id = 1; // 标签id
}
// 更新状态
message TagUpdateStatusReq {
  int64 id = 1;                // 标签id
  constants.Switch status = 2; // 状态 1启用，2禁用
}

message TagUpdateStatusRsp { int64 id = 1; }
// 客户查询 手机号是否已注册
message CheckPhoneRegisteredReq {
  // 国家id, 必填
  int64 country_id = 1 [ (api.vd) = "$!='';msg:'国家code不能为空'" ];
  // 手机号, 必填
  string phone = 2 [ (api.vd) = "$!='';msg:'手机号不能为空'" ];
  // 排除的客户id, 选填
  int32 exclude_customer_id = 3;
}

message CheckPhoneRegisteredRsp {
  // 是否已注册
  bool registered = 1;
  // 注册信息
  string registered_by = 2;
  // 已注册msg
  string msg = 3;
}

// 客户查询 邮箱是否已注册
message CheckEmailRegisteredReq {
  // 邮箱, 必填
  string email = 1 [ (api.vd) = "$!='';msg:'邮箱不能为空'" ];
  // 排除的客户id, 选填
  int32 exclude_customer_id = 2;
}

message CheckEmailRegisteredRsp {
  // 是否已注册
  bool registered = 1;
  // 注册信息
  string registered_by = 2;
  // 已注册msg
  string msg = 3;
}

// 订单状态,已下定金,支付待确认,尾款待支付,支付成功,支付失败,交易关闭
enum OrderStatus {
  ORDER_STATUS_UNSPECIFIED = 0;          // 未指定
  ORDER_STATUS_DEPOSIT_PAID = 1;         // 已下定金
  ORDER_STATUS_PAYMENT_PENDING = 2;      // 支付待确认
  ORDER_STATUS_LAST_PAYMENT_PENDING = 3; // 尾款待支付
  ORDER_STATUS_PAYMENT_SUCCESS = 4;      // 支付成功
  ORDER_STATUS_PAYMENT_FAILED = 5;       // 支付失败
  ORDER_STATUS_TRADE_CLOSED = 6;         // 交易关闭
}

// 订单信息
message OrderInfo {
  // 订单id
  int64 id = 1;
  // 品牌名称
  string brand_name = 2;
  // 业务名称
  string business_name = 3;
  // 服务项目名称
  string service_name = 4;
  // 订单状态
  OrderStatus status = 5;
  // 订单金额
  string amount = 6;
  // 支付方式
  string payment_method = 7;
  // 首期支付金额
  string first_payment = 8;
  // 首期支付折扣比例
  string first_payment_discount = 9;
  // 尾款预计金额
  string last_payment = 10;
  // 出单员工
  ShowEmployeeInfo employee = 11;
  // 创建时间
  int64 created_at = 12;
  // 商品列表
  repeated OrderItem items = 13;
}

// 工单信息
message CustomerWorkOrderInfo {
  // 工单id
  int32 id = 1;
  // 工单号
  string work_order_no = 2;
  // 工单状态
  WorkflowStatus status = 3;
  // 工单名称
  string name = 4;
}

// 订单商品
message OrderItem {
  // 商品id
  int32 id = 1;
  // 商品名称
  string name = 2;
  // 商品数量
  int32 quantity = 3;
  // 商品规格
  string specification = 4;
  // 首期款
  float first_payment = 5;
  // 尾款
  float last_payment = 6;
  // 关联工单号
  string work_order_no = 7;
}

// 客户订单列表
message CustomerOrderListReq {
  // 客户id <必填>
  int32 customer_id = 1 [ (api.vd) = "$>0;msg:'客户id不能为空'" ];
  // 页码 <必填>
  int32 page_num = 2 [ (api.vd) = "$>0;msg:'页码不能为空'" ];
  // 每页条数 <必填>
  int32 page_size = 3 [ (api.vd) = "$>0;msg:'每页条数不能为空'" ];
}

message CustomerOrderListRsp {
  // 订单信息
  repeated OrderInfo items = 1;
  // 总数
  int64 total = 2;
}

// 客户工单列表
message CustomerWorkOrderListReq {
  // 客户id <必填>
  int32 customer_id = 1 [ (api.vd) = "$>0;msg:'客户id不能为空'" ];
  // 页码 <必填>
  int32 page_num = 2 [ (api.vd) = "$>0;msg:'页码不能为空'" ];
  // 每页条数 <必填>
  int32 page_size = 3 [ (api.vd) = "$>0;msg:'每页条数不能为空'" ];
}

message CustomerWorkOrderListRsp {
  // 工单信息
  repeated WorkOrderInfo items = 1;
  // 总数
  int64 total = 2;
}
// 客户工单状态
// 新增客户工单状态定义，包含服务中、服务完结和未服务三种状态
enum CustomerWorkOrderStatus {
  CUSTOMER_WORK_ORDER_STATUS_UNSPECIFIED = 0;  // 未指定
  CUSTOMER_WORK_ORDER_STATUS_IN_SERVICE = 1;   // 服务中
  CUSTOMER_WORK_ORDER_STATUS_COMPLETED = 2;    // 服务完结
  CUSTOMER_WORK_ORDER_STATUS_NOT_SERVICED = 3; // 未服务
}
// 工单状态
enum WorkOrderStatus {
  WORK_ORDER_STATUS_UNSPECIFIED = 0; // 未指定
  WORK_ORDER_STATUS_PENDING = 1;     // 待处理
  WORK_ORDER_STATUS_PROCESSING = 2;  // 处理中
  WORK_ORDER_STATUS_COMPLETED = 3;   // 已完成
  WORK_ORDER_STATUS_CANCELLED = 4;   // 已取消
}
// 工单信息
message WorkOrderInfo {
  // 工单id
  int32 id = 1;
  // 工单号
  string work_order_no = 2;
  // 工单状态
  WorkOrderStatus status = 3;
  // 工单名称
  string name = 4;
  // 派单人
  ShowEmployeeInfo dispatcher = 5;
  // 当前处理人
  ShowEmployeeInfo handler = 6;
  // 派单时间
  int64 dispatched_at = 7;
  // 1 级业务类型
  string business_type = 8;
  // 2 级业务类型
  string business_sub_type = 9;
  // 处理进度
  string progress = 10;
  // 内部状态
  string internal_status = 11;
  // 终止原因
  string termination_reason = 12;
}

// 客户的财务信息列表
message CustomerFinancialInfoListReq {
  // 客户id <必填>
  int32 customer_id = 1 [ (api.vd) = "$>0;msg:'客户id不能为空'" ];
  // 页码 <必填>
  int32 page_num = 2 [ (api.vd) = "$>0;msg:'页码不能为空'" ];
  // 每页条数 <必填>
  int32 page_size = 3 [ (api.vd) = "$>0;msg:'每页条数不能为空'" ];
}

message CustomerFinancialInfoListRsp {
  // 财务信息
  repeated FinancialInfo items = 1;
  // 总数
  int64 total = 2;
}
// 客户的财务支款列表
message CustomerFinancialExpenseListReq {
  // 客户id <必填>
  int32 customer_id = 1;
  // 页码 <必填>
  int32 page_num = 2 [ (api.vd) = "$>0;msg:'页码不能为空'" ];
  // 每页条数 <必填>
  int32 page_size = 3 [ (api.vd) = "$>0;msg:'每页条数不能为空'" ];
  // 订单ID
  int64 order_id = 4;
}

message CustomerFinancialRefundInfo {
  int64 id = 1;                 // 主键ID
  string refund_no = 2;         // 支款单号
  string real_amount_other = 3; // 支款金额
  int32 refund_type =
      4; // 支款类型(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
  int64 created_at = 5;     // 创建时间
  string currency = 6;      // 币种
  int32 approve_status = 7; // 状态(1=待审批;2=待支款;3=支款完成;4=审核驳回)
  int64 pass_time = 8;      // 审核通过时间
  int64 reject_time = 9;    // 驳回时间
  int64 complete_time = 10; // 支款完成时间
  repeated FundContractInfo transaction_no = 11; // 交易单号
  int32 refund_receive_account_type = 12; // 账户类型 1=支付宝 2微信 3银行卡
  string refund_receive_account = 13;     // 接收账户
  string refund_reason = 14;              // 支款原因
  string account_name = 15;               // 支款账户名称
  int64 order_id = 16;                    // 订单ID
  string order_no = 17;                   // 订单号
  int64 customer_id = 18;                 // 客户ID
  repeated FundContractInfo refund_agreement = 19;      // 支款协议
  repeated FundContractInfo approve_log = 20;           // 审批记录
  repeated FundContractInfo scholarship_agreement = 21; // 奖学金协议
  repeated FundContractInfo visa = 22;                  // 签证
  repeated FundContractInfo student_card = 23;          // 学生卡
  repeated FundContractInfo tuition_payment_proof = 24; // 全额缴纳学费证明
  string workflow_name = 25;                            // 关联工单名称
  int64 submit_id = 26;                                 // 提交人ID
  string submit_name = 27;                              // 提交者名称
  string submit_department = 28;                        // 提交人部门
  int64 approve_by = 29;                                // 审核人ID
  string approve_name = 30;                             // 审核人姓名
  string approve_department = 31;                       // 审核人部门
  string approve_comment = 32;                          // 审核意见
  string real_amount_rmb = 33;                          // 金额人民币
  string exchange_rate = 34;                            // 汇率
  string workflow_no = 35;                              // 工单编号
  int64 workflow_id = 36;                               // 工单ID
  int64 refund_deadline = 37;                           // 退款截止时间
}

message CustomerFinancialExpenseListRsp {
  // 财务信息
  repeated CustomerFinancialRefundInfo items = 1;
  // 总数
  int64 total = 2;
}

// 客户模块收款信息
message CustomerFinancialFundInfo {
  int64 id = 1;                   // ID自增ID
  int64 customer_id = 2;          // 客户ID
  int64 order_id = 3;             // 订单ID自增ID
  string order_no = 4;            // 订单号
  string fund_no = 5;             // 收款单号
  string real_amount_other = 6;   // 收款单总额
  string currency = 7;            // 币种
  string should_amount_other = 8; // 应收款金额 收款总额
  int64 submit_id = 9;            // 提交者ID
  int64 created_at = 10;          // 提交时间
  int32 fund_type =
      11; // 款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)'
  int32 pay_type = 12;       // 付款方式(1=分期;2=一次性)
  string contract_no = 13;   // 合同编号
  string exchange_rate = 14; // 汇率
  int32 approve_status = 15; // 审批状态(1=待审批;2=审批通过;3=驳回审批);
  int32 discount = 16;            // 是否有折扣1 无2有
  string discount_rate = 17;      // 折扣率 100.00000就是没有
  string urgent_speed = 18;       // 加急速度 1.00000就是不加急
  int64 paid_time = 19;           // 实际付款时间
  string submit_name = 20;        // 提交者名称
  string submit_department = 21;  // 提交人部门
  int64 pass_time = 22;           // 审核通过时间
  int64 reject_time = 23;         // 审核驳回时间
  int64 approve_by = 24;          // 审核人ID
  string approve_name = 25;       // 审核人姓名
  string approve_department = 26; // 审核人部门
  repeated FundContractInfo contract_info = 27;        // 合同信息
  repeated FinancialPaidInfo financial_paid_list = 28; // 收款账号信息
  string approve_comment = 29;                         // 审批意见
  string real_amount_rmb = 30;                         // 实收金额人民币
  repeated AmountInfo ThirdAmountList = 31; // 第三方申请费收款总额
  string remark = 37;                       // 备注
  repeated IdNameDept submit_source = 38;   // 订单原提交人
  repeated IdNameDept user_source = 39;     // 客户来源
  repeated IdNameDept order_source = 40;    // 订单来源
  string should_amount_rmb = 41;            // 本期应收rmb
}
// 客户的财务收款列表
message CustomerFinancialReceiptListReq {
  // 客户id
  int32 customer_id = 1;
  // 页码 <必填>
  int32 page_num = 2 [ (api.vd) = "$>0;msg:'页码不能为空'" ];
  // 每页条数 <必填>
  int32 page_size = 3 [ (api.vd) = "$>0;msg:'每页条数不能为空'" ];
  // 订单ID
  int64 order_id = 4;
}

message CustomerFinancialReceiptListRsp {
  // 财务信息
  repeated CustomerFinancialFundInfo items = 1;
  // 总数
  int64 total = 2;
}
// 审批状态
enum FinancialApprovalStatus {
  FINANCIAL_APPROVAL_STATUS_UNSPECIFIED = 0; // 未指定
  FINANCIAL_APPROVAL_STATUS_PENDING = 1;     // 待审批
  FINANCIAL_APPROVAL_STATUS_APPROVED = 2;    // 已审批
  FINANCIAL_APPROVAL_STATUS_REJECTED = 3;    // 已拒绝
}

// 财务类型
enum FinancialType {
  FINANCIAL_TYPE_UNSPECIFIED = 0; // 未指定
  FINANCIAL_TYPE_PAYMENT = 1;     // 付款
  FINANCIAL_TYPE_RECEIPT = 2;     // 收款
}

// 财务信息
message FinancialInfo {
  // 财务id
  int32 id = 1;
  // 财务类型
  FinancialType financial_type = 2;
  // 收款总额
  float total_amount = 3;
  // 折扣比例
  float discount = 4;
  // 收款账号列表
  repeated FinancialAccount accounts = 5;
  // 合同列表
  repeated ContractInfo contracts = 6;
  // 用户实际付款时间
  int64 payment_time = 7;
  // 提交时间
  int64 created_at = 8;
  // 提交人
  ShowEmployeeInfo creator = 9;
  // 审批状态
  FinancialApprovalStatus approval_status = 10;
}

// 合同
message ContractInfo {
  // 合同id
  int32 id = 1;
  // 合同名称
  string name = 2;
  // 编号
  string number = 3;
}
// 账号类型
enum FinancialAccountType {
  FINANCIAL_ACCOUNT_TYPE_UNSPECIFIED = 0; // 未指定
  FINANCIAL_ACCOUNT_TYPE_BANK = 1;        // 银行
  FINANCIAL_ACCOUNT_TYPE_ALIPAY = 2;      // 支付宝
  FINANCIAL_ACCOUNT_TYPE_WECHAT = 3;      // 微信
}

// 收款账号
message FinancialAccount {
  // 账号
  string account = 1;
  // 账号类型
  FinancialAccountType account_type = 2;
  // 收款金额
  float amount = 3;
  // 付款凭证列表
  repeated string payment_proof = 4;
}

// 客户的群聊列表
message CustomerGroupChatListReq {
  // 客户id
  int32 customer_id = 1;
  // 页码
  int32 page_num = 2;
  // 每页条数
  int32 page_size = 3;
}

message CustomerGroupChatListRsp {
  // 群聊列表
  repeated GroupChatInfo items = 1;
  // 总数
  int64 total = 2;
}

// 群聊客户信息
message GroupChatInfo {
  // 用户id
  int32 id = 1;
  // 用户名称
  string name = 2;
  // 用户头像
  string avatar = 3;
  // im 群id
  string im_group_id = 4;
}
enum CustomerOperationLogSortField {
  CUSTOMER_OPERATION_LOG_SORT_FIELD_UNSPECIFIED = 0; // 未指定
  CUSTOMER_OPERATION_LOG_SORT_FIELD_CREATED_AT = 1;  // 创建时间
}
// 客户操作日志列表
message CustomerOperationLogListReq {
  // 客户id <必填>
  int32 customer_id = 1 [ (api.vd) = "$>0;msg:'客户id不能为空'" ];
  // page <必填>
  int32 page_num = 2 [ (api.vd) = "$>0;msg:'page不能为空'" ];
  // page size <必填>
  int32 page_size = 3 [ (api.vd) = "$>0;msg:'page_size不能为空'" ];
  // 排序
  CustomerOperationLogListSort sort = 4;
}
message CustomerOperationLogListSort {
  CustomerOperationLogSortField sort_by = 1;
  // 是否升序
  bool is_asc = 2;
}
message CustomerOperationLogListRsp {
  // 总数
  int64 total = 1;
  // 操作日志列表
  repeated OperationLog items = 2;
}

// 操作日志
message OperationLog {
  // 操作时间
  int64 created_at = 1;
  // 操作人
  EmployeeInfo operator = 2;
  // 操作内容
  string content = 3;
  // 操作类型
  string operation_type = 4;
  // 客户id
  int64 customer_id = 5;
}

// 操作类型
enum OperationLogType {
  OPERATION_LOG_TYPE_UNSPECIFIED = 0; // 未指定
  OPERATION_LOG_TYPE_CREATE = 1;      // 创建
  OPERATION_LOG_TYPE_UPDATE = 2;      // 更新
  OPERATION_LOG_TYPE_DELETE = 3;      // 删除
  OPERATION_LOG_TYPE_OTHER = 4;       // 其他
}

// 获取手机号明文
message GetPhonePlainTextReq {
  // 客户id
  int32 customer_id = 1;
}

message GetPhonePlainTextRsp {
  // 手机号明文
  string phone = 1;
}

// 获取邮箱明文
message GetEmailPlainTextReq {
  // 客户id
  int32 customer_id = 1;
}

message GetEmailPlainTextRsp {
  // 邮箱明文
  string email = 1;
}

// 交接资产
message TransferAssetReq {
  // 客户id
  int32 customer_id = 1;
  // 移交资产员工列表
  repeated TransferAssetEmployee transfer_asset_employee = 2;
}
// 资产交接人
message TransferAssetEmployee {
  // 交接前员工id
  int64 before_employee_id = 1 [ (api.vd) = "$>0;msg:'交接前员工id不能为空'" ];
  ;
  // 交接后员工id
  int64 after_employee_id = 2;
}
// 修改跟进员工
message UpdateCustomerFollowEmployeeReq {
  // 客户id
  int32 customer_id = 1;
  // 跟进员工id列表
  repeated int64 employee_ids = 2;
}

message UpdateCustomerFollowEmployeeRsp {
  // 跟进员工列表
  repeated EmployeeInfo employees = 1;
}

// 根据id列表获取客户列表
message GetCustomersByIdsReq {
  // 客户id列表
  repeated int64 customer_ids = 1;
}

message GetCustomersByIdsRsp {
  // 客户列表
  repeated CustomerInfo customers = 1;
}

// 批量获取tag
message TagBatchGetReq {
  // tag id列表
  repeated int64 ids = 1;
}

message TagBatchGetRsp {
  // tag列表
  repeated SimpleTag tags = 1;
}

// 新增跟进员工
message AddCustomerFollowEmployeeReq {
  // 客户id
  int32 customer_id = 1;
  // 跟进员工id列表
  repeated int64 employee_ids = 2;
}

// 获取员工可跟进的客户列表
message GetEmployeeFollowCustomerListReq {}

message GetEmployeeFollowCustomerListRsp {
  // 客户列表
  repeated SimpleCustomerInfo customers = 1;
}
// 文件信息
message File {
  // 文件名
  string name = 1;
  // 文件路径
  string url = 2;
  // 文件缩略图
  string thumbnail_url = 3;
  // 上传时间
  int64 upload_time = 4;
}
// 获取客户绑定列表
message GetBindCustomersReq {
  // 客户id
  int64 customer_id = 1 [ (api.vd) = "$>0;msg:'客户id不能为空'" ];
  // 用户身份（1用户，2家长）
  int32 identity = 2 [ (api.vd) = "$>0;msg:'客户身份不能为空'" ];
}
message GetBindCustomersRsp { repeated GroupChatInfo customers = 1; }

message AddExportCustomersListReq {

  map<string, string> download_fields = 1
      [ (api.vd) = "len($) > 0; msg:'下载字段不能为空'" ]; // 下载字段 <必填>

  repeated int64 tag_ids = 2;                  // 客户标签
  repeated CustomerSource customer_source = 3; // 客户来源
  CustomerType customer_type = 4;              // 客户类型
  IdentityType identity = 5;                   // 客户身份
  CustomerStatus customer_status = 6;          // 客户状态
  CustomerWorkOrderStatus service_status = 7;  // 服务状态
  repeated int64 created_at = 8; // 创建时间（单位：毫秒）（[start, end]）
  // 下载字段顺序 <必填>
  repeated string download_fields_order = 9
      [ (api.vd) = "len($) > 0; msg:'下载字段顺序不能为空'" ];
}

message AddExportCustomersListRsp {}
// 客户标签信息
message GetCustomerTagReq { int64 customer_id = 1; }
message GetCustomerTagRsp {
  // 手动标签
  repeated SimpleTag tags = 1;
  // 智能标签
  repeated SimpleTag smart_tags = 2;
  // 客户名称
  string name = 3;
  // 客户id
  int64 customer_id = 4;
  // 退款标记
  constants.YesNo refund_flag = 5;
}
// 保存客户文件
message UpdateCustomerFileReq {
  // 文件字段
  CustomerFileField field = 1;
  // 文件列表
  repeated File files = 2;
  // 客户id
  int64 customer_id = 3;
}
message UpdateCustomerFileRsp {}
// 获取客户跟进员工列表
message GetCustomerFollowEmployeeListReq { int64 customer_id = 1; }
message GetCustomerFollowEmployeeListRsp {
  repeated EmployeeInfo employees = 1;
}
// 校验新老密码是否一致
message CheckCustomerNewPasswordAndOldPasswordIsSameReq {
  int64 customer_id = 1;
  string new_password = 2;
}
message CheckCustomerNewPasswordAndOldPasswordIsSameRsp {
  constants.YesNo is_same = 1;
}
message CustomerReferralEmployeeList {
  repeated CustomerReferralEmployee items = 1;
}
// 客户推荐人id
message CustomerReferralEmployee {
  // 客户id
  int64 customer_id = 1;
  // 推荐人id
  int64 referral_employee_id = 2;
  // 推荐人明细
  EmployeeInfo employee = 3;
}
message ListUniversityReq {
  // keyword 关键词
  string keyword = 1;
  // page_num 页码
  int32 page_num = 2;
  // page_size 每页数量
  int32 page_size = 3;
  // location 地区ID
  Location location = 4;
}
enum Location {
  // 未指定
  LOCATION_UNSPECIFIED = 0;
  // 英国
  LOCATION_BRITISH = 178;
  // 中国
  LOCATION_CHINESE = 36;
}
message ListUniversityRsp {
  // 总数
  int64 total = 1;
  // 列表
  repeated UniversityInfo items = 2;
}
message UniversityInfo {
  // id 大学ID
  int64 id = 1;
  // chinese_name 大学中文名称
  string chinese_name = 2;
  // english_name 大学英文名称
  string english_name = 3;
  // location 地区ID
  int64 location = 4;
}

// 更新状态
message CustomerUpdateStatusReq {
  int64 id = 1; // 客户id
  CustomerStatus status = 2;
}

message CustomerUpdateStatusRsp { int64 id = 1; }

// 交接对象
message TransferObject {
  // 用户ID
  int64 user_id = 1;
  // 交接对象
  int64 to_employee_id = 2;
  // 交接对象昵称
  string to_employee_name = 3;
  // 交接对象部门名称
  string to_employee_department_name = 4;
}

// 客户托管邮箱账号
message CustomerProvisionedEmail {
  int64 id = 1;
  // 客户id
  int64 customer_id = 2;
  // 描述
  string describe = 3;
  // 邮箱地址
  string url = 4;
  // 密码
  string password = 5;
  // 是否设置转发
  int32 forward = 6; // 1: 否, 2: 是
  // 转发地址
  string forward_url = 7;
  // 创建时间
  int64 created_at = 8;
  // 更新时间
  int64 updated_at = 9;
}

// 创建
message CreateCustomerProvisionedEmailReq {
  // 客户id
  int64 customer_id = 1 [ (api.vd) = "$>0;msg:'客户id不能为空'" ];
  // 描述
  string describe = 2;
  // 邮箱地址
  string url = 3 [ (api.vd) = "$>0;msg:'邮箱地址不能为空'" ];
  // 密码
  string password = 4 [ (api.vd) = "$>0;msg:'密码不能为空'" ];
  // 是否设置转发, 1: 否, 2: 是
  int32 forward = 5 [ (api.vd) = "$>0;msg:'是否设置转发不能为空'" ];
  // 转发地址
  string forward_url = 6;
}
message CreateCustomerProvisionedEmailRsp {
  CustomerProvisionedEmail email = 1;
}

// 更新
message UpdateCustomerProvisionedEmailReq {
  // 客户邮箱账号id，0表示新增，>0表示更新
  int64 id = 1 [ (api.vd) = "$>=0;msg:'客户邮箱账号id不能为负数'" ];
  // 描述
  string describe = 2;
  // 邮箱地址
  string url = 3 [ (api.vd) = "$>0;msg:'邮箱地址不能为空'" ];
  // 密码
  string password = 4 [ (api.vd) = "$>0;msg:'密码不能为空'" ];
  // 是否设置转发, 1: 否, 2: 是
  int32 forward = 5 [ (api.vd) = "$>0;msg:'是否设置转发不能为空'" ];
  // 转发地址
  string forward_url = 6;
}
message UpdateCustomerProvisionedEmailRsp {
  CustomerProvisionedEmail email = 1;
}

// 删除
message DeleteCustomerProvisionedEmailReq {
  int64 id = 1 [ (api.vd) = "$>0;msg:'客户邮箱账号id不能为空'" ];
}
message DeleteCustomerProvisionedEmailRsp {}

// 列表（全量，无分页）
message ListCustomerProvisionedEmailsReq {
  int64 customer_id = 1 [ (api.vd) = "$>0;msg:'客户id不能为空'" ];
}
message ListCustomerProvisionedEmailsRsp {
  repeated CustomerProvisionedEmail emails = 1;
}

// 保存客户备注
message SaveCustomerRemarkReq {
  // 客户id
  int64 customer_id = 1;
  // 备注
  string remark = 2;
}

message SaveCustomerRemarkRsp {}

// 批量更新邮箱账号
message BatchUpdateCustomerProvisionedEmailsReq {
  // 客户id，用于新增操作
  int64 customer_id = 1 [ (api.vd) = "$>0;msg:'客户id不能为空'" ];
  // 更新请求列表，空列表表示删除所有
  repeated UpdateCustomerProvisionedEmailReq updates = 2;
}

message BatchUpdateCustomerProvisionedEmailsRsp {
  repeated CustomerProvisionedEmail emails = 1;
}