syntax = "proto3";
package admin_api;
option go_package = "server/cmd/admin_api";

import "hz.proto"; // hz框架注解
import "constants.proto";
import "customer.proto";
import "workflow_enum.proto";
import "financial_fund.proto";
import "employee.proto";
import "order_entity.proto";

// -----------------------------API DEFINE----------------------------------

// 获取工单允许的角色列表请求(派单页面)
message GetAllowRoleListReq {
  WorkflowDispatchBusinessType business_type = 1
      [ (api.vd) = "$>0;msg:'业务类型不能为空'" ];
}

// 获取工单允许的角色列表响应(派单页面)
message GetAllowRoleListRsp { repeated string allow_role_list = 1; }

// 工单详情ALL IN ONE 请求
message WorkflowDetailsReq {
  int64 id = 1 [ (api.vd) = "$>0;msg:'工单ID不能为空'" ];
  string no = 2;
}

message WorkflowDetailsRsp {
  NewWorkflowEntity workflow_info = 1;
  NewWorkflowApplicationEntity application_info = 2;
  NewWorkflowGuideEntity guide_info = 3;
  NewWorkflowSingleEntity single_info = 4;

  repeated NewWorkflowNodeEntity nodes_info = 6;
  repeated NewWorkflowNodeEntity steps_info = 7;
}

// 批量转单请求
message BatchTransferWorkflowNodeReq {
  repeated TransferWorkflowNodeInfo transfer_info = 1
      [ (api.vd) = "len($)>0;msg:'转单对象不得为空'" ];
}

// 批量转单响应
message BatchTransferWorkflowNodeRsp {
  repeated FailedTransferInfo failed_transfers = 1;
}

// 转单失败信息
message FailedTransferInfo {
  int64 workflow_id = 1;      // 工单ID
  int64 workflow_node_id = 2; // 工单节点ID
  int64 transfer_id = 3;      // 转单对象ID
  int64 customer_id = 4;      // 客户ID
  string error_msg = 5;       // 错误信息
}

// 跳过节点请求
message SkipWorkflowNodeReq {
  int64 workflow_id = 1 [ (api.vd) = "$>0;msg:'工单ID不能为空'" ];
  int64 workflow_node_id = 2 [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ];
  constants.YesNo sync_node = 4;
}

// 跳过节点响应
message SkipWorkflowNodeRsp {
  int64 workflow_id = 1;
  int64 workflow_node_id = 2;
  int64 completed_at = 3; // 跳过时间戳(毫秒)
}

// 通过订单ID获取派单信息请求
message GetAttachmentsByOrderIdReq {
  int64 order_id = 1 [ (api.vd) = "$>0;msg:'订单ID不能为空'" ];
}

// 通过订单ID获取派单信息响应
message GetAttachmentsByOrderIdRsp {
  int64 order_id = 1;     // <必填> 订单ID
  int64 customer_id = 2;  // <必填> 客户ID
  int64 processor_id = 3; // <必填> 被派单对象
  WorkflowDispatchBusinessType dispatch_business_type =
      4; // <必填> 派单业务类型

  WorkflowUgApplicationEntity ug_application = 5;
  WorkflowPmApplicationEntity pm_application = 6;
  WorkflowCoachEntity co_application = 7;
  WorkflowSingleItemEntity single_item = 8;
}

// 废弃-根据订单ID获取关联的工单列表信息请求
message GetWorkflowListByOrderIdReq {
  int64 order_id = 1 [ (api.vd) = "$>0;msg:'订单ID不能为空'" ]; // <必填> 工单ID
}

// 根据订单ID获取关联的工单列表信息响应
message GetWorkflowListByOrderIdRsp {
  repeated WorkflowInfoByOrderId items = 1;
  int64 total = 2;
}

// 工单解绑群聊请求
message UnbindWorkflowGroupChatReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  int64 workflow_node_id = 2
      [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ]; // <必填> 工单节点ID
  string workflow_no = 3;                            // 工单号
  constants.YesNo sync_node = 4;                     // <必填>  是否同步节点
}

// 工单解绑群聊响应
message UnbindWorkflowGroupChatRsp {}

// 工单操作日志请求
message WorkflowOperationLogReq {
  int32 page_num = 1 [ (api.vd) = "$>0;msg:'页数不能为空'" ]; // <必填> 页数
  int32 page_size = 2
      [ (api.vd) = "$>0;msg:'每页数量不能为空'" ]; // <必填> 每页数量
  repeated WorkflowOrderBy order_by = 3;           // 排序

  int64 workflow_id = 4; // <必填> 工单ID
}

// 工单操作日志响应
message WorkflowOperationLogRsp {
  int64 total = 1;
  repeated WorkflowOperationLogEntity workflow_operator_log_list = 2;
}

// 客户确认节点推送请求
message WorkflowConfirmPushReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  int64 workflow_node_id = 2
      [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ]; // <必填> 工单当前节点ID
  constants.YesNo sync_node = 5;                     // <必填> 是否同步节点
  repeated int64 customer_ids = 3
      [ (api.vd) = "len($)>0;msg:'顾客ID不得为空'" ]; // <必填> 顾客ID
  WorkflowBusinessType business_type = 6
      [ (api.vd) = "$>0;msg:'业务类型不能为空'" ]; // <必填> 工单业务类型
  string business_name = 7
      [ (api.vd) = "$!='';msg:'业务名称不能为空'" ]; // <必填> 业务名称
  repeated UpdateWorkflowNodeProcessorEntity node_processors = 4;
}

// 客户确认节点推送响应
message WorkflowConfirmPushRsp {
  int64 workflow_id = 1;                  // 工单ID
  WorkflowNodeTaskStatus task_status = 2; // 任务状态
}

// 获取工单模板详情请求
message WorkflowTemplateDetailReq {
  WorkflowBusinessType business_type = 2; // 业务类型
  repeated int32 template_ids = 1;        // 工单模板ID列表
}

// 工单模板详情响应
message WorkflowTemplateDetailRsp {
  repeated WorkflowTemplateInfo templates = 1; // 工单模板列表
}

message TaskTemplate {
  int32 id = 1;    // 任务ID
  string name = 2; // 任务名称
}

message WorkflowNodeTemplate {
  string name = 1;                    // 节点名称
  repeated string role = 2;           // 角色列表
  int32 type = 3;                     // 节点类型
  repeated string next_nodes = 4;     // 下一个节点列表
  repeated string previous_nodes = 5; // 上一个节点列表
  repeated TaskTemplate tasks = 6;    // 任务列表
}

message WorkflowTemplateMap {
  map<string, WorkflowNodeTemplate> nodes = 1; // 工作流节点
}

message WorkflowTemplateInfo {
  int32 template_id = 1;                         // 工单模板ID
  string template_name = 2;                      // 工单模板名称
  int32 business_type = 3;                       // 业务类型
  string business_name = 4;                      // 业务名称
  int64 created_at = 5;                          // 创建时间
  WorkflowTemplateMap workflow_template_map = 6; // 工单模板信息
}

// 客户标签上的工单信息流列表请求
message GetCustomerWorkflowListReq {
  int64 customer_id = 1 [ (api.vd) = "$>0;msg:'顾客ID不能为空'" ]; // 顾客ID
  WorkflowStatus status = 2; // 工单状态，若需要获取全部工单，则status传0即可
}

// 客户标签上的工单信息流列表响应
message GetCustomerWorkflowListRsp {
  int64 total = 1;

  repeated CustomerWorkflowList customer_workflow_list = 2;
}

// 保存文件(群聊+留言板)请求
message SaveWorkflowFileReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  WorkflowBusinessType business_type = 2
      [ (api.vd) = "$>0;msg:'业务类型不能为空'" ]; // <必填> 业务类型
  string business_name = 3
      [ (api.vd) = "$!='';msg:'业务名称不能为空'" ]; // <必填> 业务名称
  repeated SaveWorkflowFileEntity save_files = 4
      [ (api.vd) = "len($)>0;msg:'文件信息不得为空'" ]; // 文件信息
}

// 保存文件(群聊+留言板)响应
message SaveWorkflowFileRsp {}

// 工单节点：快速建群请求
message WorkflowCreateGroupChatReq {
  int64 workflow_id = 1 [ (api.vd) = "$>0;msg:'工单ID不能为空'" ];
  string workflow_no = 2;
  int64 workflow_node_id = 3 [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ];
  constants.YesNo sync_node = 4; // <必填> 是否同步节点
  repeated int32 staff_ids = 5
      [ (api.vd) = "len($)>0;msg:'用户ID不得为空'" ]; // 用户ID
  repeated int32 parent_ids = 6;                      // 家长ID
  repeated int32 customer_ids = 7
      [ (api.vd) = "len($)>0;msg:'顾客ID不得为空'" ]; // 客户ID
}

// 工单节点：快速建群响应
message WorkflowCreateGroupChatRsp {
  int64 workflow_id = 1;
  string workflow_no = 2;
  string qq_im_group_id = 3;
}

// 工单节点：关联群聊请求
message RelationshipWorkflowGroupChatReq {
  int64 workflow_id = 1 [ (api.vd) = "$>0;msg:'工单ID不能为空'" ];
  string workflow_no = 2;
  string qq_im_group_id = 3 [ (api.vd) = "$!='';msg:'群ID不得为空'" ];
  int64 workflow_node_id = 4 [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ];
  constants.YesNo sync_node = 5; // <必填> 是否同步节点
}

// 工单节点：关联群聊响应
message RelationshipWorkflowGroupChatRsp {
  int64 workflow_id = 1;
  string workflow_no = 2;
  string qq_im_group_id = 3;
}

// 更新工单内部状态请求
message UpdateWorkflowInnerStatusReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // 工单ID(ID优先)
  string workflow_no = 2;                        // 工单号(ID优先)
  InnerStatus inner_status = 3
      [ (api.vd) = "$>0;msg:'工单内部状态不能为空'" ]; // 工单内部状态
}

// 更新工单内部状态响应
message UpdateWorkflowInnerStatusRsp {
  int64 workflow_id = 1;        // 工单ID
  string workflow_no = 2;       // 工单号
  InnerStatus inner_status = 3; // 工单内部状态
}

// 更新工单名称请求
message UpdateWorkflowNameReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // 工单ID(ID优先)
  string workflow_no = 2;                        // 工单号(ID优先)
  string workflow_name = 3
      [ (api.vd) = "$!='';msg:'工单名称不得为空'" ]; // 工单名称
}

// 更新工单名称响应
message UpdateWorkflowNameRsp {
  int64 workflow_id = 1;    // 工单ID
  string workflow_no = 2;   // 工单号
  string workflow_name = 3; // 工单名称
}

// 工单节点(单项类)：更新结果文件的获取与展示请求
message UpdateApplyWorkflowNodeSingleFileReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID

  repeated UrlEntity single_file_links = 2;
}

// 工单节点(单项类)：更新结果文件的获取与展示响应
message UpdateApplyWorkflowNodeSingleFileRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  repeated UrlEntity single_file_links = 2;
}

// 工单节点(单项类)：获取结果文件的获取与展示请求
message GetApplyWorkflowNodeSingleFileReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
}

// 工单节点(单项类)：获取结果文件的获取与展示响应
message GetApplyWorkflowNodeSingleFileRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  repeated UrlEntity single_file_links = 2;
}

// 工单节点(单项类)：更新需求信息的获取与展示请求
message UpdateApplyWorkflowNodeSingleRequirementReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID

  string single_requirement_info = 3;
}

// 工单节点(单项类)：更新需求信息的获取与展示响应
message UpdateApplyWorkflowNodeSingleRequirementRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  string single_requirement_info = 3;
}

// 工单节点(单项类)：获取需求信息的获取与展示请求
message GetApplyWorkflowNodeSingleRequirementReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
}

// 工单节点(单项类)：获取需求信息的获取与展示响应
message GetApplyWorkflowNodeSingleRequirementRsp {
  int64 workflow_id = 1; // <必填> 工单ID
  string single_requirement_info = 3;
}

// 工单节点(单项类)：更新是否转案到业务处理节点请求
message UpdateSingleWorkflowDispatchReq {
  int64 workflow_id = 1 [ (api.vd) = "$>0;msg:'工单ID不能为空'" ];
  constants.YesNo dispatch_required = 2
      [ (api.vd) =
            "$>0;msg:'请设置是否转案到业务处理节点'" ]; // 是否转案到业务处理
}

// 工单节点(单项类)：更新是否转案到业务处理节点响应
message UpdateSingleWorkflowDispatchRsp {
  int64 workflow_id = 1;
  constants.YesNo dispatch_required = 2; // 是否转案到业务处理
}

// 工单节点(单项类)：获取是否转案到业务处理节点请求
message GetSingleWorkflowDispatchReq {
  int64 workflow_id = 1 [ (api.vd) = "$>0;msg:'工单ID不能为空'" ];
}

// 工单节点(单项类)：获取是否转案到业务处理节点响应
message GetSingleWorkflowDispatchRsp {
  int64 workflow_id = 1;
  constants.YesNo dispatch_required = 2; // 是否转案到业务处理
}

// 工单服务异常完成请求
message WorkflowServiceAbnormalDoneReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  int64 workflow_node_id = 2
      [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ]; // <必填> 工单节点ID
  string abnormal_name_ch =
      4; // 终止后需要修改终止节点展示的中文名称，则需传入此参数
  string abnormal_reason = 3; // 终止原因
}

// 工单服务异常完成响应
message WorkflowServiceAbnormalDoneRsp {
  int64 workflow_id = 1; // 工单ID
}

// 工单服务完成请求
message WorkflowServiceDoneReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  int64 workflow_node_id = 2
      [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ]; // <必填> 工单节点ID
}

// 工单服务完成响应
message WorkflowServiceDoneRsp {
  int64 workflow_id = 1;                                 // 工单ID
  repeated SimpleWorkflowNodeEntity incomplete_node = 2; // 未完成的上游节点列表
}

// 工单节点：获取套磁信息请求
message GetApplyWorkflowNodeClosenessLetterReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
}

// 工单节点：获取套磁信息响应
message GetApplyWorkflowNodeClosenessLetterRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  repeated UrlEntity closeness_material_links = 2;        // 套磁素材列表
  repeated UrlEntity closeness_letter_link = 3;           // 首封套磁信
  repeated UrlEntity closeness_polished_letter_links = 4; // 润色后的套磁信列表
}

// 工单节点：更新套磁信息请求
message UpdateApplyWorkflowNodeClosenessLetterReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  int64 workflow_node_id = 5
      [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ]; // <必填> 工单节点ID
  constants.YesNo sync_node = 6;                     // <必填> 是否同步节点

  repeated UrlEntity closeness_material_links = 2;        // 套磁素材列表
  repeated UrlEntity closeness_letter_link = 3;           // 首封套磁信
  repeated UrlEntity closeness_polished_letter_links = 4; // 润色后的套磁信列表
}

// 工单节点：更新套磁信息响应
message UpdateApplyWorkflowNodeClosenessLetterRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  repeated UrlEntity closeness_material_links = 2;        // 套磁素材列表
  repeated UrlEntity closeness_letter_link = 3;           // 首封套磁信
  repeated UrlEntity closeness_polished_letter_links = 4; // 润色后的套磁信列表
}

// 工单节点：获取择导信息请求
message GetApplyWorkflowNodeChooseGuideReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
}

// 工单节点：获取择导信息响应
message GetApplyWorkflowNodeChooseGuideRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  repeated GuideList guide_group = 2; // 择导信息列表
}

// 工单节点：更新择导信息请求
message UpdateApplyWorkflowNodeChooseGuideReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  int64 workflow_node_id = 3
      [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ]; // <必填> 工单节点ID
  constants.YesNo sync_node = 4;                     // <必填> 是否同步节点

  repeated GuideList guide_group = 2; // 择导信息列表
}

// 工单节点：更新择导信息响应
message UpdateApplyWorkflowNodeChooseGuideRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  repeated GuideList guide_group = 2; // 择导信息列表
}

// 工单节点：获取确认信请求
message GetApplyWorkflowNodeConfirmReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
}

// 工单节点：获取确认信响应
message GetApplyWorkflowNodeConfirmRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  repeated UrlEntity admission_confirmation_list = 2;
}

// 工单节点：更新确认信请求
message UpdateApplyWorkflowNodeConfirmReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID

  repeated UrlEntity admission_confirmation_list = 2; // 录取确认函
}

message UpdateApplyWorkflowNodeConfirmRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  repeated UrlEntity admission_confirmation_list = 2;
}

// 工单节点：获取拒绝信请求
message GetApplyWorkflowNodeRejectReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
}

// 工单节点：获取拒绝信响应
message GetApplyWorkflowNodeRejectRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  repeated UrlEntity reject_letter_list = 2;
}

// 工单节点：更新拒绝信请求
message UpdateApplyWorkflowNodeRejectReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID

  repeated UrlEntity reject_letter_list = 2;
}

// 工单节点：更新拒绝信响应
message UpdateApplyWorkflowNodeRejectRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  repeated UrlEntity reject_letter_list = 2;
}

// 工单节点：获取签证信息请求
message GetApplyWorkflowNodeVisaReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
}

// 工单节点：获取签证信息响应
message GetApplyWorkflowNodeVisaRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  constants.YesNo visa_required = 2;      // 是否申请签证
  repeated UrlEntity visa_file_links = 3; // 签证文件列表
}

// 工单节点：更新签证信息请求
message UpdateApplyWorkflowNodeVisaReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID

  constants.YesNo visa_required = 2;      // 是否申请签证
  repeated UrlEntity visa_file_links = 3; // 签证文件列表
}

// 工单节点：更新签证信息响应
message UpdateApplyWorkflowNodeVisaRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  constants.YesNo visa_required = 2;      // 是否申请签证
  repeated UrlEntity visa_file_links = 3; // 签证文件列表
}

// 工单节点：获取offer信息请求
message GetApplyWorkflowNodeOfferReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
}

// 工单节点：获取offer信息请求
message GetApplyWorkflowNodeOfferRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  repeated UrlEntity offer_file_links = 2;    // offer文件列表
  int64 offer_confirmation_deadline = 3;      // offer确认截止时间
  constants.YesNo offer_deposit_required = 4; // offer是否交押金
  string offer_deposit_amount = 5;            // offer押金金额
  string offer_deposit_currency = 6;          // offer押金币种
  int64 offer_deposit_deadline = 7;           // offer押金截止时间
  constants.YesNo offer_cas_required = 8;     // offer是否交CAS押金
  string offer_cas_deposit_amount = 9;        // offerCAS押金金额
  string offer_cas_deposit_currency = 10;     // offerCAS押金币种
  int64 offer_cas_deadline = 11;              // offerCAS押金截止时间
  string offer_deposit_link = 12;             // offer押金链接
  string offer_accommodation_link = 13;       // offer住宿链接
  WorkflowAcademicRequirement offer_academic_requirement = 14; // 学术要求
  string offer_academic_score = 15;                            // offer学术成绩
  WorkflowLanguageScoreType offer_language_test_type = 16; // offer语言成绩类型
  WorkflowLanguageScore offer_language_test_score = 17;    // offer语言成绩
  WorkflowRecommendationLetterRequirement offer_reference_requirement =
      18;                   // offer推荐信要求
  string offer_remark = 19; // offer备注
}

// 工单节点：更新offer信息请求
message UpdateApplyWorkflowNodeOfferReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID

  repeated UrlEntity offer_file_links = 2;    // offer文件列表
  int64 offer_confirmation_deadline = 3;      // offer确认截止时间
  constants.YesNo offer_deposit_required = 4; // offer是否交押金
  string offer_deposit_amount = 5;            // offer押金金额
  string offer_deposit_currency = 6;          // offer押金币种
  int64 offer_deposit_deadline = 7;           // offer押金截止时间
  constants.YesNo offer_cas_required = 8;     // offer是否交CAS押金
  string offer_cas_deposit_amount = 9;        // offerCAS押金金额
  string offer_cas_deposit_currency = 10;     // offerCAS押金币种
  int64 offer_cas_deadline = 11;              // offerCAS押金截止时间
  string offer_deposit_link = 12;             // offer押金链接
  string offer_accommodation_link = 13;       // offer住宿链接
  WorkflowAcademicRequirement offer_academic_requirement = 14; // 学术要求
  string offer_academic_score = 15;                            // offer学术成绩
  WorkflowLanguageScoreType offer_language_test_type = 16; // offer语言成绩类型
  WorkflowLanguageScore offer_language_test_score = 17;    // offer语言成绩
  WorkflowRecommendationLetterRequirement offer_reference_requirement =
      18;                   // offer推荐信要求
  string offer_remark = 19; // offer备注
}

// 工单节点：更新offer信息请求
message UpdateApplyWorkflowNodeOfferRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  repeated UrlEntity offer_file_links = 2;    // offer文件列表
  int64 offer_confirmation_deadline = 3;      // offer确认截止时间
  constants.YesNo offer_deposit_required = 4; // offer是否交押金
  string offer_deposit_amount = 5;            // offer押金金额
  string offer_deposit_currency = 6;          // offer押金币种
  int64 offer_deposit_deadline = 7;           // offer押金截止时间
  constants.YesNo offer_cas_required = 8;     // offer是否交CAS押金
  string offer_cas_deposit_amount = 9;        // offerCAS押金金额
  string offer_cas_deposit_currency = 10;     // offerCAS押金币种
  int64 offer_cas_deadline = 11;              // offerCAS押金截止时间
  string offer_deposit_link = 12;             // offer押金链接
  string offer_accommodation_link = 13;       // offer住宿链接
  WorkflowAcademicRequirement offer_academic_requirement = 14; // 学术要求
  string offer_academic_score = 15;                            // offer学术成绩
  WorkflowLanguageScoreType offer_language_test_type = 16; // offer语言成绩类型
  WorkflowLanguageScore offer_language_test_score = 17;    // offer语言成绩
  WorkflowRecommendationLetterRequirement offer_reference_requirement =
      18;                   // offer推荐信要求
  string offer_remark = 19; // offer备注
}

// 工单节点：获取申请信息请求
message GetApplyWorkflowNodeInfoReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
}

// 工单节点：获取申请信息响应
message GetApplyWorkflowNodeInfoRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  string apply_no = 2;
  repeated UrlEntity application_receipt_link = 3; // 申请回执
  string application_info = 4;                     // 申请信息
  string application_tracking_info = 5;            // 追踪信息
  string application_security_record = 6;          // 安全记录
}

// 工单节点：更新申请信息请求
message UpdateApplyWorkflowNodeInfoReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID

  string apply_no = 2;
  repeated UrlEntity application_receipt_link = 3; // 申请回执
  string application_info = 4;                     // 申请信息
  string application_tracking_info = 5;            // 追踪信息
  string application_security_record = 6;          // 安全记录
}

// 工单节点：更新申请信息响应
message UpdateApplyWorkflowNodeInfoRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  string apply_no = 2;
  repeated UrlEntity application_receipt_link = 3; // 申请回执
  string application_info = 4;                     // 申请信息
  string application_tracking_info = 5;            // 追踪信息
  string application_security_record = 6;          // 安全记录
}

// 工单节点：获取申请材料包请求
message GetApplyWorkflowNodeMaterialPackageReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
}

// 工单节点：获取申请材料包响应
message GetApplyWorkflowNodeMaterialPackageRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  repeated UrlEntity package_list = 2;
}

// 工单节点：更新申请材料包请求
message UpdateApplyWorkflowNodeMaterialPackageReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  int64 workflow_node_id = 3
      [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ]; // <必填> 工单节点ID
  constants.YesNo sync_node = 4;                     // <必填> 是否同步节点

  repeated UrlEntity package_list = 2;
}

// 工单节点：更新申请材料包响应
message UpdateApplyWorkflowNodeMaterialPackageRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  repeated UrlEntity package_list = 2;
}

// 工单节点：获取申请文件请求
message GetApplyWorkflowNodeFileReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
}

// 工单节点：获取申请文件响应
message GetApplyWorkflowNodeFileRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  repeated UrlEntity cv_list = 2;
  repeated UrlEntity rl_list = 3;
  repeated UrlEntity ps_list = 4;
}

// 工单节点：更新申请文件请求
message UpdateApplyWorkflowNodeFileReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  int64 workflow_node_id = 5
      [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ]; // <必填> 工单节点ID
  constants.YesNo sync_node = 6;                     // <必填> 是否同步节点

  repeated UrlEntity cv_list = 2;
  repeated UrlEntity rl_list = 3;
  repeated UrlEntity ps_list = 4;
}

// 工单节点：更新申请文件响应
message UpdateApplyWorkflowNodeFileRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  repeated UrlEntity cv_list = 2;
  repeated UrlEntity rl_list = 3;
  repeated UrlEntity ps_list = 4;
}

// 工单节点：获取选校信息请求
message GetWorkflowNodeSchoolInfoReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
}

// 工单节点：获取选校信息响应
message GetWorkflowNodeSchoolInfoRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  int64 selection_university_id = 2;
  string selection_university_zh = 3;
  string selection_university_en = 4;
  int64 selection_major_id = 5;
  string selection_major_zh = 6;
  string selection_major_en = 7;
  WorkflowCourseLevel selection_course_level = 8;
  int64 selection_expected_entry = 9;
  Channel selection_channel = 10;
  string selection_major_link = 11;
  repeated UrlEntity selection_offer_agreement_link = 12;
  int64 selection_university_location_id = 13;
}

// 工单节点：更新选校信息请求
message UpdateWorkflowNodeSchoolInfoReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  int64 workflow_node_id = 15
      [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ]; // <必填> 工单节点ID
  constants.YesNo sync_node = 14;                    // <必填> 是否同步节点

  int64 selection_university_id = 2;
  string selection_university_zh = 3;
  string selection_university_en = 4;
  int64 selection_university_location_id = 13;
  int64 selection_major_id = 5;
  string selection_major_zh = 6;
  string selection_major_en = 7;
  WorkflowCourseLevel selection_course_level = 8;
  int64 selection_expected_entry = 9;
  Channel selection_channel = 10;
  string selection_major_link = 11;
  repeated UrlEntity selection_offer_agreement_link = 12;
}

// 工单节点：更新选校信息响应
message UpdateWorkflowNodeSchoolInfoRsp {
  int64 workflow_id = 1; // <必填> 工单ID

  int64 selection_university_id = 2;
  string selection_university_zh = 3;
  string selection_university_en = 4;
  int64 selection_major_id = 5;
  string selection_major_zh = 6;
  string selection_major_en = 7;
  WorkflowCourseLevel selection_course_level = 8;
  int64 selection_expected_entry = 9;
  Channel selection_channel = 10;
  string selection_major_link = 11;
  repeated UrlEntity selection_offer_agreement_link = 12;
}

// 工单节点：接收请求
message ReceiveWorkflowNodeReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  int64 workflow_node_id = 2
      [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ]; // <必填> 工单节点ID
  constants.YesNo sync_node = 5;                     // <必填> 是否同步节点
}

// 工单节点：接收响应
message ReceiveWorkflowNodeRsp {
  int64 workflow_id = 1;
  int64 workflow_node_id = 2;
  int64 started_at = 3; // 接收时间
}

// 工单节点：转单请求
message TransferWorkflowNodeReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  int64 workflow_node_id = 2
      [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ]; // <必填> 工单节点ID
  int64 transfer_id = 3
      [ (api.vd) = "$>0;msg:'转单对象ID不能为空'" ]; // <必填> 转单对象ID
  int64 customer_id = 4
      [ (api.vd) = "$>0;msg:'客户ID不能为空'" ]; // <必填> 客户ID
}

// 工单节点：转单响应
message TransferWorkflowNodeRsp {
  int64 workflow_id = 1;
  int64 workflow_node_id = 2;
  int64 transferred_at = 3; // 转单时间
}

// 工单节点：重启请求
message RestartWorkflowNodeReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  int64 workflow_node_id = 2;    // 工单节点ID(任务视图必填，工单视图不填)
  constants.YesNo sync_node = 5; // <必填> 是否同步节点
}

// 工单节点：重启响应
message RestartWorkflowNodeRsp {
  int64 workflow_id = 1;
  int64 workflow_node_id = 2;
  int64 restarted_at = 3; // 重启时间
}

// 工单节点：(任务视图)搁置请求
message SuspendWorkflowNodeReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  int64 workflow_node_id = 2
      [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ]; // <必填> 工单节点ID
  string pause_reason = 3;                           // 暂停原因
  constants.YesNo sync_node = 5;                     // <必填> 是否同步节点
}

// 工单节点：(任务视图)搁置响应
message SuspendWorkflowNodeRsp {
  int64 workflow_id = 1;
  int64 workflow_node_id = 2;
  int64 paused_at = 3; // 暂停时间
}

// 工单节点：暂停请求
message PauseWorkflowNodeReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  int64 workflow_node_id = 2
      [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ]; // <必填> 工单节点ID
  string pause_reason = 3;                           // 暂停原因
  constants.YesNo sync_node = 5;                     // <必填> 是否同步节点
}

// 工单节点：暂停响应
message PauseWorkflowNodeRsp {
  int64 workflow_id = 1;
  int64 workflow_node_id = 2;
  int64 paused_at = 3; // 暂停时间
}

// 工单节点：终止节点请求
message TerminateWorkflowNodeReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  int64 workflow_node_id = 2
      [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ]; // <必填> 工单节点ID
  string abnormal_reason = 3;                        // <必填> 终止原因
  constants.YesNo sync_node = 5;                     // <必填> 是否同步节点
}

// 工单节点：终止节点响应
message TerminateWorkflowNodeRsp {
  int64 workflow_id = 1;
  int64 workflow_node_id = 2;
  int64 terminated_at = 3; // 终止时间
}

// 工单节点：完成节点请求
message FinishWorkflowNodeReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  int64 workflow_node_id = 2
      [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ]; // <必填> 工单节点ID
  int64 customer_id = 6
      [ (api.vd) = "$>0;msg:'客户ID不能为空'" ]; // <必填> 客户ID
  int64 next_workflow_node_id = 3; // 当前节点是决策类型的节点时需要传
  constants.YesNo sync_node = 5;   // <必填> 是否同步节点
  repeated UpdateWorkflowNodeProcessorEntity node_processors =
      4; // 转案参数(有则传，没有不传)
}

// 工单节点：完成节点响应
message FinishWorkflowNodeRsp {
  int64 workflow_id = 1;
  int64 workflow_node_id = 2;
  int64 next_workflow_node_id = 3;
}

message UpdateWorkflowNodeProcessorEntity {
  string node_name = 1;         // 节点英文名称
  string node_chinese_name = 4; // 节点中文名称
  string node_role = 3;         // 节点角色
  int64 processor_id = 2;       // 指定处理人ID
  string processor_name = 5;    // 处理人名称
}

// 工单节点：操作转案-指定处理人请求
message UpdateWorkflowNodeProcessorReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  int64 workflow_node_id = 2
      [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ]; // <必填> 工单节点ID
  constants.YesNo sync_node = 5;                     // <必填> 是否同步节点
  int64 customer_id = 3
      [ (api.vd) = "$>0;msg:'客户ID不能为空'" ]; // <必填> 客户ID
  repeated UpdateWorkflowNodeProcessorEntity node_processors = 4;
}

// 工单节点：操作转案-指定处理人响应
message UpdateWorkflowNodeProcessorRsp {
  int64 workflow_id = 1;
  int64 workflow_node_id = 2;

  repeated UpdateWorkflowNodeProcessorEntity node_processors = 3;
}

// 工单节点：更新任务状态请求
message UpdateWorkflowNodeTaskStatusReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  int64 workflow_node_id = 2
      [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ];   // <必填> 工单节点ID
  constants.YesNo sync_node = 5;                       // <必填> 是否同步节点
  repeated SimpleWorkflowNodeTaskEntity task_list = 3; // 任务列表
}

// 工单节点：更新任务状态响应
message UpdateWorkflowNodeTaskStatusRsp {
  int64 workflow_id = 1;                         // <必填> 工单ID
  int64 workflow_node_id = 2;                    // <必填> 工单节点ID
  repeated WorkflowNodeTaskEntity task_list = 3; // 任务列表
}

// 工单节点：获取工单跟进情况请求
message GetWorkflowFollowReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
}

// 工单节点：获取工单跟进情况响应
message GetWorkflowFollowRsp {
  int64 workflow_id = 1;
  string customer_profile = 2; // 客户画像
  string remark = 3;           // 备注
}

// 工单节点：更新工单跟进情况请求
message UpdateWorkflowFollowReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  int64 workflow_node_id = 2
      [ (api.vd) = "$>0;msg:'工单节点ID不能为空'" ]; // <必填> 工单节点ID
  constants.YesNo sync_node = 5;                     // <必填> 是否同步节点
  string customer_profile = 3;                       // 客户画像
  string remark = 4;                                 // 备注
}

// 工单节点：更新工单跟进情况响应
message UpdateWorkflowFollowRsp {
  int64 workflow_id = 1;
  string customer_profile = 2; // 客户画像
  string remark = 3;           // 备注
}

// 工单审核：接收派单请求
message AcceptWorkflowDispatchReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
  int64 workflow_template_id = 2
      [ (api.vd) = "$>0;msg:'工单模板ID不能为空'" ]; // <必填> 工单模板ID
  WorkflowBusinessType workflow_business_type = 3
      [ (api.vd) = "$>0;msg:'工单业务类型不能为空'" ]; // <必填> 工单业务类型
  constants.YesNo is_accept_all =
      4; // <必填> 是否接受订单内其他工单。0未知，1接收，2拒绝
}

// 工单审核：接收派单响应
message AcceptWorkflowDispatchRsp {
  repeated int64 workflow_ids = 3; // 接收的所有工单列表
}

// 工单审核：拒绝请求
message RejectWorkflowDispatchReq {
  int64 workflow_id = 1 [ (api.vd) = "$>0;msg:'工单ID不能为空'" ];
  string abnormal_reason = 2; // 驳回原因
}

// 工单审核：拒绝响应
message RejectWorkflowDispatchRsp { int64 workflow_id = 1; }

// 获取工单对应的订单+商品+收款信息请求
message GetWorkflowOrderGoodsPaymentReq {
  int64 workflow_id = 1
      [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // <必填> 工单ID
}

// 获取工单对应的订单+商品+收款信息响应
message GetWorkflowOrderGoodsPaymentRsp {
  // 付款方式
  enum OrderInstallmentType {
    INSTALLMENT_TYPE_UNSPECIFIED = 0;  // 未指定
    INSTALLMENT_TYPE_FULL_PAYMENT = 1; // 一次性付款（全款）
    INSTALLMENT_TYPE_BY_STAGES = 2;    // 分期付款
  }
  // 支款类型
  enum OrderDisbursementType {
    DISBURSEMENT_TYPE_UNSPECIFIED = 0;        // 未指定
    DISBURSEMENT_TYPE_REFUND_DEPOSIT = 1;     // 退定金
    DISBURSEMENT_TYPE_REFUND_SERVICE = 2;     // 退服务费
    DISBURSEMENT_TYPE_SCHOLARSHIP = 3;        // 奖学金
    DISBURSEMENT_TYPE_REFUND_DIFFERENCE = 4;  // 退差价
    DISBURSEMENT_TYPE_LIQUIDATED_DAMAGES = 5; // 支付违约金
  }
  // 订单状态
  enum StatusOrder {
    STATUS_ORDER_UNSPECIFIED = 0;  // 未指定
    STATUS_ORDER_DEPOSIT = 1;      // 定金（已下定金）
    STATUS_ORDER_FIRST = 2;        // 首款（支付待确认）
    STATUS_ORDER_FINAL = 3;        // 尾款（尾款待支付）
    STATUS_ORDER_SUCCESS = 4;      // 支付成功
    STATUS_ORDER_DISBURSEMENT = 5; // 支款（支款订单）
    STATUS_ORDER_CLOSE = 6;        // 已关闭（交易关闭）
  }
  // 审核状态
  enum StatusOrderReview {
    STATUS_REVIEW_UNSPECIFIED = 0;  // 未指定
    STATUS_REVIEW_PASS = 1;         // 审核通过
    STATUS_REVIEW_DRAFT = 2;        // 草稿
    STATUS_REVIEW_DRAFT_AUDIT = 3;  // 草稿待审核
    STATUS_REVIEW_REJECT_AUDIT = 4; // 驳回待审核
    STATUS_REVIEW_REJECT = 5;       // 审核驳回
  }
  // 支付状态
  enum StatusOrderPay {
    STATUS_PAY_UNSPECIFIED = 0; // 未指定
    STATUS_PAY_PAID = 1;        // 已支付
    STATUS_PAY_PENDING = 2;     // 待支付
    STATUS_PAY_ING = 3;         // 支付中
    STATUS_PAY_FAIL = 4;        // 支付失败
  }

  // 工单服务状态
  enum StatusOrderWorkflow {
    ORDER_WORKFLOW_STATUS_UNKNOWN = 0;    // 未知状态
    ORDER_WORKFLOW_STATUS_PENDING = 1;    // 待接收
    ORDER_WORKFLOW_STATUS_IN_SERVICE = 2; // 服务中
    ORDER_WORKFLOW_STATUS_PAUSED = 3;     // 服务暂停
    ORDER_WORKFLOW_STATUS_COMPLETED = 4;  // 服务完成
    ORDER_WORKFLOW_STATUS_TERMINATED = 5; // 服务终止
  }

  // 订单信息
  message OrderEntity {
    int64 id = 1; // 订单ID

    string order_no = 2; // 订单编号(时间戳+6位随机数)

    int64 customer_id = 3; // 客户UID
    int64 reviewer_id = 4; // 审核人UID（最后审核人UID）
    int64 executor_id = 5; // 派单对象UID

    int64 brand_id = 7;    // 品牌ID
    int64 business_id = 8; // 业务线ID
    int64 service_id = 9;  // 服务项目ID

    string brand_name = 10;    // 品牌名称
    string business_name = 11; // 业务线名称
    string service_name = 12;  // 服务项目名称

    OrderInstallmentType installment_type = 13;   // 付款方式：分期方式
    OrderDisbursementType disbursement_type = 14; // 支款类型

    StatusOrder status = 15;                  // 订单状态
    StatusOrderReview status_review = 16;     // 审核状态
    StatusOrderWorkflow status_workflow = 17; // 工单服务状态
    StatusOrder status_prev = 18; // 订单状态（撤销审核驳回支款专用）
    StatusOrderReview status_review_prev =
        19; // 审核状态（撤销审核驳回支款专用）

    StatusOrderPay status_pay_deposit = 21;      // 定金-支付状态
    StatusOrderPay status_pay_first = 22;        // 首款-支付状态
    StatusOrderPay status_pay_final = 23;        // 尾款-支付状态
    StatusOrderPay status_pay_disbursement = 24; // 支款-支付状态

    int64 pay_deposit_at = 31; // 定金-付款时间（定金-实际付款日期，单位：毫秒）
    int64 pay_first_at = 32;   // 首款-付款时间（首款-实际付款日期，单位：毫秒）
    int64 pay_final_at = 33;   // 尾款-付款时间（尾款-实际付款日期，单位：毫秒）
    int64 pay_disbursement_at =
        34; // 支款-付款时间（支款-实际付款日期，单位：毫秒）（多次，仅记录最新一次）

    int64 apply_deposit_at = 41; // 定金-申请-审核时间（单位：毫秒）
    int64 apply_first_at = 42;   // 首款-申请-审核时间（单位：毫秒）
    int64 apply_final_at = 43;   // 尾款-申请-审核时间（单位：毫秒）
    int64 apply_disbursement_at =
        44; // 支款-申请-审核时间（多次，仅记录最新一次）（单位：毫秒）

    int64 pass_deposit_at = 51; // 定金-审核通过时间（单位：毫秒）
    int64 pass_first_at = 52;   // 首款-审核通过时间（单位：毫秒）
    int64 pass_final_at = 53;   // 尾款-审核通过时间（单位：毫秒）
    int64 pass_disbursement_at =
        54; // 支款-审核通过时间（多次，仅记录最新一次）（单位：毫秒）

    int64 reject_at =
        60; // 审核驳回审核时间（多次，仅记录最新一次）（单位：毫秒）

    int64 created_by = 61; // 操作人UID（出单成员）
    int64 updated_by = 62; // 最后操作人UID
    int64 closed_by = 63;  // 关闭人UID
    int64 created_at = 64; // 创建时间（单位：毫秒）
    int64 updated_at = 65; // 更新时间（单位：毫秒）
    int64 closed_at = 66;  // 关闭时间（单位：毫秒）

    string workflow_by = 70; // 工单跟进人UID（，分割）
  }

  // 是否状态
  enum StatusYesNo {
    STATUS_UNSPECIFIED = 0; // 未指定
    STATUS_YES = 1;         // 是
    STATUS_NO = 2;          // 否
  }

  // 尾款支付方式
  enum OrderFinalPaymentType {
    ORDER_FINAL_PAYMENT_TYPE_UNKNOWN = 0;     // 未知
    ORDER_FINAL_PAYMENT_TYPE_ONE_TIME = 1;    // 一次性
    ORDER_FINAL_PAYMENT_TYPE_PRODUCT_NUM = 2; // 按照数量
  }

  // 订单付款信息
  message OrderPayEntity {
    int64 id = 1;
    int64 order_id = 2; // 订单ID

    string urgent_times =
        3; // 服务加急倍数（默认1即不加急，1.5表示1.5倍，2表示2倍）
    string discount_rate = 4; // 首期款折扣（默认100即不打折）

    string amount_deposit = 11;     // 定金实际支付金额（已收定金）
    string amount_total_goods = 12; // 订单总额(商品计算金额）
    string amount_total =
        14; // 实际成交金额（实际成交金额 = 定金+实收首款+加急费+实收尾款）
    string amount_contract =
        15; // 合同金额（合同金额 = 定金+实收首款+加急费+预收尾款）
    string amount_first_estimated = 16; // 预估首款（首期款）-金额人民币
    string amount_final_estimated = 17; // 预估尾款-金额人民币
    string amount_first_receivable =
        18; //  应收首款-金额（首期款-已收定金）*折扣+首期款*（加急倍数-1）
    string amount_final_receivable = 19; // 应收尾款-金额
    string amount_first =
        20; // 实收首款-金额（首期款-已收定金）*折扣+首期款*（加急倍数-1）
    string amount_final = 21;        // 实收尾款-金额
    string amount_disbursement = 22; // 实付支款-金额
    string amount_disbursement_review =
        23; // 待审核支款-金额（多次，仅记录最新一次）

    string amount_disbursement_list = 24; // 支款金额（仅列表展示用）

    string amount_liquidated = 25; // 实付违约金-金额
    string amount_liquidated_review =
        26; // 待审核违约金--金额（多次，仅记录最新一次）

    string amount_bursary = 28; // 实付奖学金-金额
    string amount_bursary_review =
        29; // 待审核奖学金-金额（多次，仅记录最新一次）

    string amount_scholarship = 31; // 奖学金-金额
    string amount_urgent = 32;      // 加急费
    string amount_premium = 33;     // 溢价-金额
    string amount_discount = 34;    // 折扣-金额

    string currency_deposit = 41;     // 定金币种
    string currency_total_goods = 42; // 订单总额-币种 人民币
    string currency_total = 44;       // 实际成交金额-币种
    string currency_contract = 45;
    string currency_first_estimated = 46;     // 预估首款（首期款）-币种 人民币
    string currency_final_estimated = 47;     // 预估尾款-币种 人民币
    string currency_first_receivable = 48;    // 应收首款-币种
    string currency_final_receivable = 49;    // 应收尾款-币种
    string currency_first = 50;               // 实收首款-币种
    string currency_final = 51;               // 实收尾款-币种
    string currency_disbursement = 52;        // 实付支款-币种
    string currency_disbursement_review = 53; // 待审核支款-币种

    string currency_disbursement_list = 54; // 支款金额（仅列表展示用）

    string currency_liquidated = 55;        // 实付违约金-币种
    string currency_liquidated_review = 56; // 待审核违约金-币种

    string currency_bursary = 58;        // 实付奖学金-币种
    string currency_bursary_review = 59; // 待审核奖学金-币种

    string currency_scholarship = 61; // 奖学金-币种
    string currency_urgent = 62;      // 加急费-币种
    string currency_premium = 63;     // 溢价金额-币种
    string currency_discount = 64;    // 折扣金额-币种

    StatusYesNo exempt_final = 91;    // 免除尾款
    StatusYesNo urgent_service = 92;  // 服务加急
    StatusYesNo has_scholarship = 94; // 奖学金
    StatusYesNo auto_schedule = 95;   // 提交订单后自动派单
    OrderFinalPaymentType final_payment_type =
        96;                              // 尾款支付方式#1%仅收一次|2%按数量收取
    StatusYesNo is_premium_allowed = 97; // 商品溢价#1%是|2%否
    StatusYesNo is_upgrade_order = 98;   // 升级订单/折扣补差#1%是|2%否

    string exempt_final_reason = 99; // 免除尾款原因
  }

  // 商品类型
  enum OrderGoodsType {
    GOODS_TYPE_UNSPECIFIED = 0; // 未指定
    GOODS_TYPE_NORMAL = 1;      // 普通商品
    GOODS_TYPE_GIFT = 2;        // 赠送商品
  }

  // 订单商品
  message OrderGoodsEntity {
    int64 id = 1;          // 主键
    int64 customer_id = 2; // 用户UID
    int64 order_id = 3;    // 订单ID
    int64 goods_id = 4;    // 商品ID
    int64 spec_id = 5;     // 商品规格ID
    int64 business_id = 7; // 业务线ID
    int64 service_id = 8;  // 服务项目ID

    OrderGoodsType goods_type = 10; // 商品类型

    string business_name = 11; // 业务线名称
    string service_name = 12;  // 服务项目名称

    string goods_no = 20;   // 商品编号
    string goods_name = 21; // 商品名称
    string goods_spec = 22; // 商品规格

    string goods_price = 23;       // 商品单价
    string goods_first_price = 24; // 商品首款
    string goods_final_price = 25; // 商品尾款

    string goods_currency = 26;       // 商品单价-币种
    string goods_first_currency = 27; // 商品首款-币种
    string goods_final_currency = 28; // 商品尾款-币种

    string goods_redundancy = 29; // 商品冗余信息（JSON）
    string goods_num = 30;        // 购买数量

    string amount_first = 31; // 首期款
    string amount_final = 32; // 尾款
    string amount_total = 33; // 总价

    string currency_first = 41; // 首期款-币种
    string currency_final = 42; // 尾款-币种
    string currency_total = 43; // 总价-币种

    StatusYesNo purchase_num_type = 51; // 商品数量政策#1整数2小数
    StatusYesNo is_discount_first = 52; // 首期款折扣#1%是|2%否
    string discount_first = 53;         // 首期款折扣（默认100即不打折）
    OrderFinalPaymentType final_payment_type =
        54; // 尾款支付方式#1%仅收一次|2%按数量收取
  }

  message ContractEntity {
    string contract_no = 1;  // 合同编号
    string contract_url = 2; // 合同URL
  }

  message FundPaidEntity {
    string account_name = 1; // 收款账号
    string amount_cny = 2;   // 收款金额(CNY)
    string amount_other = 3; // 收款金额(外币)
    string currency = 4;     // 币种
    string images_path = 5;  // 收款凭证图片路径
  }

  // 收款信息
  message PaymentEntity {
    string fund_no = 1; // 收款单号
    int32 fund_type =
        2; // 款项类型（1=定金；2=订单首期款；3=订单尾款；4=第三方申请费）
    string real_amount_rmb = 3;     // 实际收款金额（人民币）
    string real_amount_other = 4;   // 实际收款金额（外币）
    string should_amount_rmb = 5;   // 应收款金额（人民币）
    string should_amount_other = 6; // 应收款金额（外币）
    int32 approve_status = 7; // 审核状态（1=待审批；2=审批通过；3=驳回审批）
    int32 pay_type = 8;       // 付款方式（1=分期；2=一次性）
    string currency =
        9; // 币种（1=英镑；2=人民币；3=澳币；4=欧元；5=日元；6=港币；7=加币；8=美元）
    int64 paid_time = 10;                        // 实际付款时间（毫秒）
    repeated ContractEntity contract_list = 11;  // 合同列表
    repeated FundPaidEntity fund_paid_list = 12; // 收款列表
    EmployeeData submitted_by = 13;              // 提交人
    int64 submit_time = 14;                      // 提交时间
  }

  OrderEntity order = 1;            // 订单信息
  OrderGoodsEntity order_goods = 2; // 订单商品信息
  OrderPayEntity order_pay = 3;     // 订单支付信息
  PaymentEntity payment = 4;        // 付款信息
}

// 获取工单列表节点转案人请求
message ListWorkflowNodeDispatcherReq {
  int32 page_num = 1 [ (api.vd) = "$>0;msg:'页数不能为空'" ]; // <必填> 页数
  int32 page_size = 2
      [ (api.vd) = "$>0;msg:'每页数量不能为空'" ]; // <必填> 每页数量

  WorkflowTaskViewStatus workflow_task_view_status = 3
      [ (api.vd) = "$>0;msg:'任务状态不能为空'" ]; // <必填> 节点状态

  string fuzzy_field = 4; // 模糊查询字段(name/phone/email)；精确查找字段(ID)
}

// 获取工单列表节点转案人响应
message ListWorkflowNodeDispatcherRsp {
  message Item {
    int64 id = 1;
    string name = 2;
    string phone = 3;
    string email = 4;
    string dept_name = 5;
  }
  int64 total = 1;
  repeated Item items = 2;
}

// 获取工单列表处理人列表请求
message ListWorkflowProcessorReq {
  int32 page_num = 1 [ (api.vd) = "$>0;msg:'页数不能为空'" ]; // <必填> 页数
  int32 page_size = 2
      [ (api.vd) = "$>0;msg:'每页数量不能为空'" ]; // <必填> 每页数量

  WorkflowStatus workflow_status = 3;                   // 工单视图
  WorkflowTaskViewStatus workflow_task_view_status = 4; // 任务视图

  string fuzzy_field = 5; // 模糊查询字段(name/phone/email)；精确查找字段(ID)
}

// 获取工单列表派单人列表响应
message ListWorkflowProcessorRsp {
  message Item {
    int64 id = 1;
    string name = 2;
    string phone = 3;
    string email = 4;
    string dept_name = 5;
  }
  int64 total = 1;
  repeated Item items = 2;
}

// 获取工单列表派单人列表请求
message ListWorkflowDispatcherReq {
  int32 page_num = 1 [ (api.vd) = "$>0;msg:'页数不能为空'" ]; // <必填> 页数
  int32 page_size = 2
      [ (api.vd) = "$>0;msg:'每页数量不能为空'" ]; // <必填> 每页数量
  WorkflowStatus workflow_status = 3
      [ (api.vd) = "$>0;msg:'工单状态不能为空'" ]; // <必填> 工单状态

  string fuzzy_field = 4; // 模糊查询字段(name/phone/email)；精确查找字段(ID)
}

// 获取工单列表派单人列表响应
message ListWorkflowDispatcherRsp {
  message Item {
    int64 id = 1;
    string name = 2;
    string phone = 3;
    string email = 4;
    string dept_name = 5;
  }
  int64 total = 1;
  repeated Item items = 2;
}

// 获取工单模板ID与业务之间映射关系请求
message GetWorkflowTemplateAndBusinessReq {}

// 获取工单模板ID与所有业务类型配置映射响应
message GetWorkflowTemplateAndBusinessRsp {
  message Item {
    int32 template_id = 1; // templateId
    string cn = 2;         // 中文名称
  }

  message Category {
    string cn = 1;                          // 业务大类中文名
    WorkflowBusinessType business_type = 2; // 业务类型
    map<string, Item> items = 3;            // 具体业务项配置
  }

  map<string, Category> business_types = 1; // 所有业务类型配置
}

message CreateWorkflowByOrderReq {
  int64 order_id = 1 [ (api.vd) = "$>0;msg:'订单ID不能为空'" ]; // <必填> 订单ID
  int64 customer_id = 2
      [ (api.vd) = "$>0;msg:'客户ID不能为空'" ]; // <必填> 客户ID
  int64 processor_id = 3
      [ (api.vd) = "$>0;msg:'派单对象不能为空'" ]; // <必填> 被派单对象
  WorkflowDispatchBusinessType dispatch_business_type =
      4; // <必填> 派单业务类型
  repeated WorkflowGoodsInfo goods_info = 5
      [ (api.vd) = "len($)>0;msg:'商品信息不能为空'" ]; // 商品信息

  WorkflowUgApplicationEntity ug_application = 11;
  WorkflowPmApplicationEntity pm_application = 12;
  WorkflowCoachEntity co_application = 13;
  WorkflowSingleItemEntity single_item = 14;
}

message WorkflowGoodsInfo {
  int64 goods_id = 1;    // 商品ID
  int64 spec_id = 2;     // 规格ID
  int64 goods_type = 3;  // 商品类型
  int64 goods_index = 4; // 规格索引
}

message CreateWorkflowByOrderRsp {
  repeated SimpleWorkflowEntity workflow_list = 1;
}

// 获取工单详情请求
message GetWorkflowDetailReq {
  int64 workflow_id = 1 [ (api.vd) = "$>0;msg:'工单ID不能为空'" ]; // 工单ID
  string workflow_no = 2;                                          // 工单编号
}
// 获取工单详情响应
message GetWorkflowDetailRsp {
  WorkflowEntity workflow = 1;
  repeated SimpleCurrentNodeEntity steps = 2; // 当前节点列表
}

// 获取工单列表 - 工单视图(有权限)
message ListWorkflowReq {
  int32 page_num = 1 [ (api.vd) = "$>0;msg:'页数不能为空'" ];      // 页数
  int32 page_size = 2 [ (api.vd) = "$>0;msg:'每页数量不能为空'" ]; // 每页数量
  WorkflowStatus workflow_status = 3
      [ (api.vd) = "$>0;msg:'工单状态不能为空'" ]; // <必填> 工单状态

  string workflow_no_or_name = 4; // 工单编号或名称（模糊匹配）

  string order_no = 5;     // 订单号（模糊匹配）
  string service_name = 6; // 服务项目名称

  string goods_id_or_name = 7; // 商品ID或名称

  int64 customer_id = 8;  // 客户id
  int64 creator_id = 9;   // 创建者/派单人id
  int64 assignee_id = 10; // 处理人id

  string date_string =
      13; // 筛选时间字段(created_at:创建日期/received_at:接收日期/暂停日期：paused_at/完成日期：completed_at/终止日期：terminated_at)
  int64 date_start = 11;         // 时间开始
  int64 date_end = 12;           // 时间结束
  InnerStatus inner_status = 21; // 内部状态
  repeated WorkflowOrderBy order_by = 22;
  repeated WorkflowBusinessType business_type = 23; // 业务类型
  repeated int64 template_ids = 24;                 // 业务名称(模板ID)
  repeated string current_node_name = 25;           // 当前节点名称(英文)
  repeated BusinessInfo business_info = 27;

  // 申请类参数
  int64 school_id = 14;                           // 学校ID
  int64 major_id = 15;                            // 专业ID
  repeated WorkflowCourseLevel course_level = 16; // 课程等级(多选)
  repeated Channel channel = 17;                  // 渠道(多选)
  repeated int64 locations = 18;                  // 地区(多选)
  int64 expected_entry_start = 19;                // 期望入学时间开始
  int64 expected_entry_end = 20;                  // 期望入学时间结束

  // 辅导类参数
  repeated WorkflowDemandType demand_type = 26;
  string guide_teacher = 28; // 辅导老师
}

// 获取工单列表 - 工单视图(无权限)
message ListWorkflowWithoutCtxReq {
  int32 page_num = 1;                 // 页数
  int32 page_size = 2;                // 每页数量
  WorkflowStatus workflow_status = 3; // 工单状态
  int64 order_id = 4;                 // 订单id
  repeated int64 workflow_ids = 6;    // 工单id列表
  string workflow_no_or_name = 5;     // 工单编号或名称（模糊匹配）
  int64 customer_id = 8;              // 客户id
  repeated WorkflowBusinessType business_type = 23; // 业务类型
}

// 获取工单列表(无权限) - 工单视图响应
message ListWorkflowWithoutCtxRsp {
  repeated WorkflowInfoByOrderId items = 1;
  int32 total = 2;
}

// 获取工单列表 - 工单视图 响应
message ListWorkflowRsp {
  message StepInfo {
    int64 id = 1;                         // 节点ID
    repeated string allow_role_list = 19; // 节点允许的角色列表
    string name = 2;                      // 节点英文名称
    string chinese_name = 3;              // 节点中文名称
    WorkflowNodeStatus status =
        4; // 节点状态(0:未开始,1:进行中,2暂停中,3:已完成,4:已终止)
    WorkflowNodeType type =
        5; // 节点类型(0:UNKNOWN,
           // 1:任务节点,2:决策节点,3:开始节点，4:终止节点，5:动态节点)
    EmployeeData dispatcher = 6;   // 节点派发人
    EmployeeData processor = 7;    // 节点处理人
    string abnormal_reason = 9;    // 异常原因(暂停或终止)
    string attachments = 10;       // 附件信息（存储附件路径或元数据）
    constants.YesNo sync_node = 8; // 是否同步节点

    int64 created_at = 11;
    int64 updated_at = 12;
    int64 dispatched_at = 13;
    int64 started_at = 14;
    int64 paused_at = 15;
    int64 terminated_at = 16;
    int64 completed_at = 17;

    int64 branch = 18; // 分支(0永远为主分支)
  }

  message ListWorkflowViewItem {
    int64 id = 1;                      // 工单ID
    string workflow_no = 2;            // 工单编号
    string workflow_name = 3;          // 工单名称
    int64 workflow_template_id = 4;    // 工单模板ID
    string workflow_template_name = 5; // 工单模板名称
    WorkflowStatus status =
        6; // 工单状态(0:待接收；1:服务中；2:服务暂停；3:服务完成；4:服务终止)
    WorkflowBusinessType business_type =
        7; // 业务类型(0:未指定;1:申请；2:上诉；3:辅导；4:博士专项；5:其他单项)
    string business_name = 8;      // 业务名称
    string order_no = 9;           // 订单编号
    int64 order_id = 10;           // 订单编号
    EmployeeData creator = 11;     // 创建人ID
    EmployeeData updater = 12;     // 更新人ID
    CustomerData customer = 13;    // 客户ID
    int64 created_at = 14;         // 创建时间戳(毫秒)
    int64 updated_at = 15;         // 更新时间戳(毫秒)
    int64 deleted_at = 16;         // 删除时间戳(毫秒)
    int64 dispatched_at = 17;      // 派发时间戳(毫秒)
    int64 received_at = 18;        // 接收时间戳(毫秒)
    int64 paused_at = 19;          // 暂停时间戳(毫秒)
    int64 terminated_at = 20;      // 终止时间戳(毫秒)
    int64 completed_at = 21;       // 完成时间戳(毫秒)
    int64 due_at = 22;             // 截止时间戳(毫秒)
    int64 message_board_id = 23;   // 留言板ID
    string qq_im_group_id = 24;    // 群聊ID
    string remark = 25;            // 备注
    repeated StepInfo steps = 26;  // 当前处理中的节点列表
    string service_name = 27;      // 服务名称
    string goods_name = 28;        // 商品名称
    string goods_num = 29;         // 商品数量
    string goods_spec = 30;        // 商品规格
    int64 version = 31;            // 版本号
    InnerStatus inner_status = 33; // 内部状态

    // 申请类参数
    string school_name_zh = 40;            // 学校中文名称
    string school_name_en = 41;            // 学校英文名称
    string major_name_zh = 42;             // 专业中文名称
    string major_name_en = 43;             // 专业英文名称
    WorkflowCourseLevel course_level = 44; // 课程等级
    Channel channel = 45;                  // 渠道
    int64 expected_entry_at = 46;          // 期望入学时间

    // 辅导类参数 todo
    // 目前没有数据的使用，只在api层处理即可，后面要加数据，要在rpc层添加辅导类对象封装
    WorkflowDemandType demand_type = 47;
    string guide_teacher = 48; // 辅导老师
  }

  int32 total = 1; // <必填> 总数

  repeated ListWorkflowViewItem items = 2;
}

// 获取工单列表 - 任务视图
message ListWorkflowTaskViewReq {
  int32 page_num = 1;  // <必填> 页数
  int32 page_size = 2; // <必填> 每页数量
  WorkflowTaskViewStatus workflow_task_view_status =
      3; // <必填> 工单任务视图状态

  string workflow_no_or_name = 4; // 工单编号（模糊匹配）

  string order_no = 6;     // 订单号（模糊匹配）
  string service_name = 7; // 服务项目名称

  string goods_id_or_name = 8; // 商品ID或名称

  int64 customer_id = 10;   // 客户id
  int64 dispatcher_id = 11; // 转案人id
  int64 assignee_id = 12;   // 处理人id

  string date_string =
      30; // 筛选时间字段(created_at创建时间/updated_at更新时间/dispatched_at转案时间/started_at开始时间(接收时间)/paused_at暂停时间/completed_at完成时间/terminated_at终止时间)
  int64 date_start = 31; // 时间开始
  int64 date_end = 32;   // 时间结束

  repeated WorkflowBusinessType business_type = 15; // 业务类型
  repeated int64 business_name = 16;                // 业务名称(模板ID)
  repeated string current_node_name = 17;           // 当前节点名称
  repeated BusinessInfo business_info = 28;         // 级连查询

  repeated WorkflowStatus workflow_status =
      18; // 工单视图节点状态，支持多选(需求要求不可选择工单为待接收状态)

  InnerStatus inner_status = 19; // 内部状态

  // 申请类参数
  int64 school_id = 20;                           // 学校ID
  int64 major_id = 21;                            // 专业ID
  repeated WorkflowCourseLevel course_level = 22; // 课程等级(多选)
  repeated Channel channel = 23;                  // 渠道(多选)
  repeated int64 locations = 24;                  // 地区(多选)
  int64 expected_entry_start = 25;                // 期望入学时间开始
  int64 expected_entry_end = 26;                  // 期望入学时间结束

  // 辅导类参数
  repeated WorkflowDemandType demand_type = 27;
  string guide_teacher = 29; // 辅导老师

  repeated WorkflowOrderBy order_by =
      40; // 排序字段(created_at创建时间/updated_at更新时间/dispatched_at转案时间/started_at开始时间(接收时间)/paused_at暂停时间/completed_at完成时间/terminated_at终止时间)
}

message ListWorkflowTaskViewRsp {
  int32 total = 1; // <必填> 总数

  repeated WorkflowTaskView items = 2; // 工单任务视图
}

// -----------------------------ENUM DEFINE---------------------------------

// 语言成绩
message WorkflowLanguageScore {
  string total = 1;     // 总分
  string listening = 2; // 听
  string speaking = 3;  // 说
  string reading = 4;   // 读
  string writing = 5;   // 写
}

// -----------------------------Entity DEFINE-------------------------------

message CustomerData {
  int64 id = 1;
  string name = 2;
  string email = 3;
  string phone = 4;
  string profile = 5;
  repeated SimpleTag tags = 6;
  repeated SimpleTag smart_tags = 7;
}

message EmployeeData {
  int64 id = 1;
  string name = 2;
  string email = 3;
  string phone = 4;
  int64 role_id = 5;
  string role_name = 6;
  string role_name_snapshot = 7;
  int64 dept_id = 8;
  string dept_name = 9;
  string dept_name_snapshot = 10;
}

// 工单消息定义
message WorkflowEntity {
  int64 id = 1;                      // 工单ID
  string workflow_no = 2;            // 工单编号
  string workflow_name = 3;          // 工单名称
  int64 workflow_template_id = 4;    // 工单模板ID
  string workflow_template_name = 5; // 工单模板名称
  WorkflowStatus status =
      6; // 工单状态(0:unknown；1:待接收；2:服务中；3:服务暂停；4：服务完成；5:服务终止)
  InnerStatus inner_status =
      32; // 内部状态(0:unknown；1:待接收；2:正常；3:拒绝；4:暂停)
  WorkflowBusinessType business_type =
      7; // 业务类型(0:unknown；1:未指定;2:申请；3:上诉；4:辅导；5:博士专项；6:其他单项)
  string business_name = 8;               // 业务名称
  int64 order_id = 9;                     // 订单Id
  string order_no = 30;                   // 订单编号
  string service_name = 10;               // 服务名称
  int64 goods_id = 34;                    // 商品ID
  string goods_name = 11;                 // 商品名称
  string goods_num = 12;                  // 商品数量
  string goods_spec = 13;                 // 商品规格
  EmployeeData creator = 14;              // 创建人ID
  EmployeeData updater = 15;              // 更新人ID
  CustomerData customer = 16;             // 客户ID
  int64 created_at = 17;                  // 创建时间戳(毫秒)
  int64 updated_at = 18;                  // 更新时间戳(毫秒)
  int64 deleted_at = 19;                  // 删除时间戳(毫秒)
  int64 dispatched_at = 20;               // 派发时间戳(毫秒)
  int64 received_at = 21;                 // 接收时间戳(毫秒)
  int64 paused_at = 22;                   // 暂停时间戳(毫秒)
  int64 terminated_at = 23;               // 终止时间戳(毫秒)
  int64 completed_at = 24;                // 完成时间戳(毫秒)
  int64 due_at = 25;                      // 截止时间戳(毫秒)
  int64 message_board_id = 26;            // 留言板ID
  int64 order_message_board_id = 33;      // 订单留言板ID
  string qq_im_group_id = 27;             // 群聊ID
  string remark = 28;                     // 备注
  repeated WorkflowNodeEntity nodes = 29; // 工单节点列表
  string customer_profile = 31;           // 客户画像
}

message SimpleWorkflowEntity {
  int64 workflow_id = 1;    // 工单ID
  string workflow_no = 2;   // 工单号
  string workflow_name = 3; // 工单名称
}

// 工单节点消息定义
message WorkflowNodeEntity {
  int64 id = 1;            // 节点ID
  string name = 2;         // 节点英文名称
  string chinese_name = 3; // 节点中文名称
  WorkflowNodeStatus status =
      4; // 节点状态(0:未开始,1:进行中,2暂停中,3:已完成,4:已终止)
  WorkflowNodeType type =
      5; // 节点类型(0:UNKNOWN,
         // 1:任务节点,2:决策节点,3:开始节点，4:终止节点，5:动态节点)
  EmployeeData dispatcher = 6;    // 节点派发人
  EmployeeData processor = 7;     // 节点处理人
  int64 created_at = 8;           // 创建时间
  int64 started_at = 9;           // 开始时间
  int64 updated_at = 10;          // 更新时间
  int64 dispatched_at = 11;       // 转案时间戳
  int64 paused_at = 12;           // 暂停时间
  int64 deleted_at = 13;          // 删除时间
  int64 completed_at = 14;        // 完成时间
  int64 terminated_at = 15;       // 终止时间
  int64 due_at = 16;              // 截止时间
  string abnormal_reason = 17;    // 异常原因(暂停或终止)
  string attachments = 18;        // 附件信息（存储附件路径或元数据）
  int64 workflow_id = 19;         // 工单ID
  constants.YesNo sync_node = 23; // <必填> 是否同步节点
  string allow_role_list = 24;
  repeated SimpleWorkflowNodeEntity next = 20;    // 此节点的下游节点列表
  repeated SimpleWorkflowNodeEntity preview = 21; // 此节点的上游节点列表
  repeated WorkflowNodeTaskEntity tasks = 22;     // 节点包含的任务列表
}

message SimpleWorkflowNodeEntity {
  string name = 2;         // 节点英文名称
  string chinese_name = 3; // 节点中文名称
  string role_name = 4;    // 角色名称
}

message SimpleCurrentNodeEntity {
  int64 id = 1;                // 节点ID
  string name = 2;             // 节点英文名称
  string chinese_name = 3;     // 节点中文名称
  string allow_role_list = 24; // 角色列表
  WorkflowNodeStatus status =
      4; // 节点状态(0:UNKNOWN,1:未开始,2:进行中,3:暂停中,4:已完成,5:已终止)
  WorkflowNodeType type =
      5; // 节点类型(0:UNKNOWN,
         // 1:任务节点,2:决策节点,3:开始节点，4:终止节点，5:动态节点)
  EmployeeData dispatcher = 6; // 节点派发人
  EmployeeData processor = 7;  // 节点处理人
  int64 created_at = 8;        // 创建时间
  int64 started_at = 9;        // 开始时间
  int64 updated_at = 10;       // 更新时间
  int64 dispatched_at = 22;    // 派发时间
  int64 paused_at = 11;        // 暂停时间
  int64 deleted_at = 12;       // 删除时间
  int64 completed_at = 13;     // 完成时间
  int64 terminated_at = 14;    // 终止时间
  int64 due_at = 15;           // 截止时间
  string abnormal_reason = 16; // 异常原因(暂停或终止)
  string attachments = 17;     // 附件信息（存储附件路径或元数据）
  repeated WorkflowNodeTaskEntity tasks = 21; // 节点包含的任务列表
  constants.YesNo sync_node = 23;             // <必填> 是否同步节点
}

// 工单节点任务消息定义
message WorkflowNodeTaskEntity {
  int64 id = 1;                      // 任务ID
  string name = 2;                   // 任务名称
  WorkflowNodeTaskStatus status = 3; // 任务状态(0:unknown;1:未完成,2:已完成)
  int64 created_at = 4;              // 创建时间
  int64 started_at = 5;              // 开始时间
  int64 updated_at = 6;              // 更新时间
  int64 deleted_at = 7;              // 删除时间
  int64 completed_at = 8;            // 完成时间
  int64 due_at = 9;                  // 截止时间
  int64 workflow_node_id = 10;       // 节点ID
}

message SimpleWorkflowNodeTaskEntity {
  int64 id = 1;
  WorkflowNodeTaskStatus status = 3; // 任务状态(0:unknown;1:未完成,2:已完成)
}

// 排序参数
message WorkflowOrderBy {
  string field = 1;             // 排序字段
  WorkflowOrderByType type = 2; // 排序类型
}

// 预本硕申请信息
message WorkflowUgApplicationEntity {
  // 申请的common
  string uoffer_app_info = 5;     // 录多app账密信息
  string past_experience = 6;     // 过往特殊经历
  string special_application = 7; // 申请需求特殊情况
  constants.YesNo is_designated_institution =
      8; // 是否指定申请专业院校，0:UNKNOWN；1:指定；2:不指定
  string institution_name = 9; // 指定院校名称
  constants.YesNo is_commitment_success_rate =
      10; // 承诺成功率，0:UNKNOWN；1:是；2:否
  repeated UrlEntity commitment_file_links = 11; // 成功率承诺文件链接
  constants.YesNo is_guaranteed_college_major =
      12; // 是否承诺保底院校专业，0:UNKNOWN；1:是；2:否
  constants.YesNo is_native_document =
      13; // 是否承诺native文书，0:UNKNOWN；1:是；2:否
  constants.YesNo is_native_communication =
      14; // 是否承诺native文书沟通，0:UNKNOWN；1:是；2:否
  constants.YesNo is_document_background =
      15; // 是否承诺文书背景，0:UNKNOWN；1:是；2:否
  constants.YesNo is_commissioner_background =
      16; // 是否承诺沟通专员背景，0:UNKNOWN；1:是；2:否
  repeated UrlEntity cv_file_links = 17; // cv成绩单等文件链接
  int64 selection_expected_entry = 32;   // 期望入学时间

  // 预本硕申请
  WorkflowUGApplicationLevel ug_application_level =
      18; // 申请等级，0:UNKNOWN；1:一硕；2:二硕；3:本科；4:预科
  constants.YesNo ug_home_return_permit =
      19;                            // 是否有港澳通行证，0:UNKNOWN，1是，2否
  string ug_extra_free_service = 20; // 额外/赠送的服务
  string ug_commissioner_info = 21;  // 意向与沟通专员性别与性格特点
  string ug_third_party_info = 22;   // 第三方缴费金额和学校名称
  constants.YesNo ug_is_designated_institution =
      23; // 是否指定申请专业院校，0:UNKNOWN；1:指定；2:不指定
  repeated UrlEntity ug_guaranteed_college_major_file_links =
      25; // 保底院校承诺文件链接
  repeated UrlEntity ug_native_document_file_links =
      26; // native文书承诺文件链接
  repeated UrlEntity ug_native_communication_file_links =
      27; // native文书沟通承诺文件链接
  repeated UrlEntity ug_document_background_file_links =
      28; // 文书背景承诺文件链接
  repeated UrlEntity ug_commissioner_background_file_links =
      29; // 沟通专员背景承诺文件链接
  constants.YesNo ug_is_no_language =
      30; // 海外背景学生是否承诺申请院校可以免语言，0:UNKNOWN；1:是；2:否
  repeated UrlEntity ug_no_language_file_links = 31; // 免语言承诺文件链接
}

// Phd和MPhil申请及单项信息
message WorkflowPmApplicationEntity {
  // 申请的common
  string uoffer_app_info = 5;     // 录多app账密信息
  string past_experience = 6;     // 过往特殊经历
  string special_application = 7; // 申请需求特殊情况
  constants.YesNo is_designated_institution =
      8; // 是否指定申请专业院校，0:UNKNOWN；1:指定；2:不指定
  string institution_name = 9; // 指定院校名称
  constants.YesNo is_commitment_success_rate =
      10; // 承诺成功率，0:UNKNOWN；1:是；2:否
  repeated UrlEntity commitment_file_links = 11; // 成功率承诺文件链接
  constants.YesNo is_guaranteed_college_major =
      12; // 是否承诺保底院校专业，0:UNKNOWN；1:是；2:否
  constants.YesNo is_native_document =
      13; // 是否承诺native文书，0:UNKNOWN；1:是；2:否
  constants.YesNo is_native_communication =
      14; // 是否承诺native文书沟通，0:UNKNOWN；1:是；2:否
  constants.YesNo is_document_background =
      15; // 是否承诺文书背景，0:UNKNOWN；1:是；2:否
  constants.YesNo is_commissioner_background =
      16; // 是否承诺沟通专员背景，0:UNKNOWN；1:是；2:否
  repeated UrlEntity cv_file_links = 17; // cv成绩单等文件链接
  repeated UrlEntity ug_commissioner_background_file_links =
      18;                              // 沟通专员背景承诺文件链接
  int64 selection_expected_entry = 19; // 期望入学时间

  // Phd和MPhil申请及单项
  WorkflowPMApplicationLevel pm_application_level =
      32; // 申请等级，0:UNKNOWN；1:PhD；2:MPhil
  constants.YesNo pm_closeness_letter =
      33;                                 // 是否需要套磁, 0:UNKNOWN；1:是；2:否
  string pm_guaranteed_college_name = 34; // 保底院校名称/排名
  string pm_document_background = 35;     // 文书背景
  string pm_commissioner_background = 36; // 专员背景
  constants.YesNo pm_is_introduce_closeness_letter =
      37; // 是否介绍过套磁流程，0:UNKNOWN；1:是；2:否
  constants.YesNo pm_is_pr_tutorship =
      38; // 是否有PR辅导，0:UNKNOWN；1:是；2:否
  constants.YesNo pm_is_select_teacher =
      39;                            // 是否已匹配老师，0:UNKNOWN；1:是；2:否
  int64 pm_tutoring_started_at = 40; // 期望辅导开始时间
}

// 辅导业务信息
message WorkflowCoachEntity {
  string co_pr_submit = 41; // PR提交时间
  string co_pr_word = 42;   // PR字数要求
  string co_ref_style = 43; // Referencing Style
  constants.YesNo co_is_select_guide =
      44; // 是否择导列表，0:UNKNOWN；1:是；2:否

  // 辅导+单项 common
  string supplements_info = 45;                   // 补充信息
  repeated UrlEntity supplements_file_links = 46; // 补充文件链接
}

// 单项信息
message WorkflowSingleItemEntity {
  string supplements_info = 45;                   // 补充信息
  repeated UrlEntity supplements_file_links = 46; // 补充文件链接
}

message AddExportWorkflowTaskListReq {
  // 下载字段  <必填>
  map<string, string> download_fields = 1
      [ (api.vd) = "len($) > 0; msg:'下载字段不能为空'" ];
  // 任务状态 <必填>
  repeated WorkflowTaskViewStatus workflow_task_view_status = 2;
  // 工单状态 <必填>
  repeated WorkflowStatus workflow_status = 3;
  // 转案日期 （单位：毫秒）（[start, end]）
  repeated int64 trans_plan_at = 4;
  // 接收日期 （单位：毫秒）（[start, end]）
  repeated int64 rec_at = 5;
  // 完成日期 （单位：毫秒）（[start, end]）
  repeated int64 final_at = 6;
  // 搁置日期 （单位：毫秒）（[start, end]）
  repeated int64 shelve_at = 7;
  // 服务项目ID <必填>
  string service = 8;
  string goods_name = 9;                    // 商品名称
  repeated BusinessInfo business_info = 10; // 业务信息 <必填>
  // 下载字段顺序 <必填>
  repeated string download_fields_order = 11
      [ (api.vd) = "len($) > 0; msg:'下载字段顺序不能为空'" ];
}

message AddExportWorkflowTaskListRsp {}

message AddExportWorkflowWorkOrderListReq {
  // 下载字段  <必填>
  map<string, string> download_fields = 1
      [ (api.vd) = "len($) > 0; msg:'下载字段不能为空'" ];
  // 工单状态 <必填>
  repeated WorkflowStatus status = 2;
  repeated int64 created_at = 3;    // 创建时间 （单位：毫秒）（[start, end]）
  repeated int64 rec_at = 4;        // 接收日期 （单位：毫秒）（[start, end]）
  repeated int64 paused_at = 5;     // 暂停时间 （单位：毫秒）（[start, end]）
  repeated int64 completed_at = 6;  // 完成时间 （单位：毫秒）（[start, end]）
  repeated int64 terminated_at = 7; // 终止时间 （单位：毫秒）（[start, end]）

  string goods_name = 8;                    // 商品名称
  string service_name = 9;                  // 服务项目 <必填>
  repeated BusinessInfo business_info = 10; // 业务信息 <必填>
  // 下载字段顺序 <必填>
  repeated string download_fields_order = 11
      [ (api.vd) = "len($) > 0; msg:'下载字段顺序不能为空'" ];
}

message AddExportWorkflowWorkOrderListRsp {}

// 非Phd申请文件
message WorkflowNonPhdApplicationEntity {
  repeated UrlEntity selection_offer_agreement_link = 1; // 定校协议
  repeated UrlEntity cv_list = 2;
  repeated UrlEntity rl_list = 3;
  repeated UrlEntity ps_list = 4;
  repeated UrlEntity package_list = 5;                // 材料包
  repeated UrlEntity application_receipt_link = 6;    // 申请回执
  repeated UrlEntity offer_file_links = 7;            // offer文件列表
  repeated UrlEntity visa_file_links = 8;             // 签证文件列表
  repeated UrlEntity admission_confirmation_list = 9; // 录取确认函
}

// Phd申请文件
message WorkflowPhdApplicationEntity {
  repeated UrlEntity guide_info_first_links = 1;  // 首轮择导信息
  repeated UrlEntity guide_info_second_links = 2; // 二轮择导信息
  repeated UrlEntity guide_info_third_links = 3;  // 三轮择导信息

  repeated UrlEntity cv_list = 4;
  repeated UrlEntity rl_list = 5;
  repeated UrlEntity ps_list = 6;

  repeated UrlEntity closeness_material_links = 7;        // 套磁素材列表
  repeated UrlEntity closeness_letter_link = 8;           // 首封套磁信
  repeated UrlEntity closeness_polished_letter_links = 9; // 润色后的套磁信列表

  repeated UrlEntity selection_offer_agreement_link = 10; // 定校协议
  repeated UrlEntity package_list = 11;                   // 材料包

  repeated UrlEntity application_receipt_link = 12;    // 申请回执
  repeated UrlEntity offer_file_links = 13;            // offer文件列表
  repeated UrlEntity visa_file_links = 14;             // 签证文件列表
  repeated UrlEntity admission_confirmation_list = 15; // 录取确认函
}

// 辅导-论文辅导
message WorkflowCoachPaperEntity {
  // lessonInfo
  repeated UrlEntity lesson_plan_file_url = 1;      // 课程计划文件
  repeated UrlEntity topic_ready_list_file_url = 2; //  备课清单

  // manuscriptInfo
  repeated UrlEntity manuscript_outline_file_url = 3; // 稿件outline
  //  UrlEntity first_draft_file_url = 4; // 初稿
  repeated UrlEntity second_draft_file_url = 5;     // 二稿
  repeated UrlEntity refinement_draft_file_url = 6; // 精修
  repeated UrlEntity final_draft_file_url = 7;      // 终稿
}

// 辅导-期刊发表
message WorkflowCoachJournalEntity {
  repeated UrlEntity result_material_file_url = 1;     // 材料
  repeated UrlEntity result_first_draft_file_url = 2;  // 初稿
  repeated UrlEntity result_final_draft_file_url = 3;  // 终稿
  repeated UrlEntity result_offer_notice_file_url = 4; // 录用通知
}

// 单项业务-结果文件
message WorkflowSingleItemResultEntity {
  repeated UrlEntity single_file_links = 1;
}

message CustomerWorkflowList {
  message NodeNameInfo {
    WorkflowNodeStatus node_status = 3;
    string node_name_zh = 1;
    string node_name_en = 2;
  }
  message NodeInfo {
    NodeNameInfo node = 1;
    repeated string previous_nodes = 2;
    repeated string next_nodes = 3;
  }
  int64 workflow_template_id = 3;
  int64 workflow_id = 4;
  string workflow_name = 5;
  repeated NodeInfo all_nodes = 1;
  repeated NodeNameInfo current_nodes = 2;
}

message SaveWorkflowFileEntity {
  WorkflowSaveFilesEnum save_files_enum = 1;
  repeated UrlEntity files = 2;
}

message WorkflowOperationLogEntity {
  int64 operation_at = 1;                     // 操作时间
  WorkflowOperatorType operator_log_type = 2; // 操作类型
  string operator_info = 3;                   // 操作人信息
  string operator_content = 4;                // 操作内容
  string workflow_no = 5;                     // 工单编号
}

message GuideList {
  repeated UrlEntity guide_info_links = 2; // 择导信息列表
}

// 创建第三方申请费收款
message AddFundWithWorkflowApplyReq {
  int64 customer_id = 1;        // 客户ID
  string currency = 2;          // 币种
  string exchange_rate = 3;     // 汇率
  string real_amount_other = 4; // 金额
  int64 paid_time = 5;          // 支付时间
  int64 submit_id = 6;          // 提交者ID
  repeated FinancialPaidRpcInfo financial_paid_info = 7;
  string service_name = 8;      // 服务项目名称
  string goods_name = 9;        // 商品名称
  string brand_name = 10;       // 品牌
  string business_name = 11;    // 业务名称
  string goods_specs_name = 12; // 商品规格名称
  string num = 13;              // 商品数量
  int64 workflow_id = 14;       // 工单ID
  string workflow_name = 15;    // 工单名称
  string real_amount_rmb = 16;  // 申请金额人民币
  string workflow_no = 17;      // 工单编号
  int64 order_id = 18;          // 订单ID
  string order_no = 19;         // 订单号
  string remark = 20;           // 备注
}

// 支付信息返回
message FinancialPaidRpcInfo {
  int64 financial_fund_id = 1;  // 关联收款单ID
  int64 payment_account_id = 2; // 收款账户ID
  int32 paid_type = 3;
  string currency = 4;                                   // 币种
  string exchange_rate = 5;                              // 汇率
  string amount_cny = 6;                                 // 人民币金额
  string amount_other = 7;                               // 其他币种金额
  repeated FundContractInfoWithWorkflow images_path = 8; // 图片
  string account_name = 9;                               // 收款账户名称
  repeated string transaction_no = 10;                   // 交易单号
  int64 id = 11;
}
message AddFundWithWorkflowApplyRsp {}

message BindFundWithWorkflowApplyReq {
  int64 financial_fund_id = 1; // 收款单ID
  int64 workflow_id = 2;       // 工单ID
}

message BindFundWithWorkflowApplyRsp {}

message CancelFundWithWorkflowApplyReq {
  int64 workflow_id = 1;  // 工单ID
  string free_reason = 2; // 免收费理由
}
message CancelFundWithWorkflowApplyRsp {}

message UpdateWorkflowApplyFundCurrencyInfoReq {
  int64 workflow_id = 1;
  string fund_currency_code = 2;  // 第三方收费单实际申请费用的货币code
  string fund_currency_value = 3; // 第三方收费单实际申请费用的金额
}

message UpdateWorkflowApplyFundCurrencyInfoRsp {}

// 申请类-fund
message WorkflowFundEntity {
  int64 fund_id = 1;              // 第三方收款单id
  string free_reason = 2;         // 免收费理由
  int32 fund_bind_type = 3;       // 收款单绑定类型
  string fund_currency_code = 4;  // 第三方收费单实际申请费用的货币code
  string fund_currency_value = 5; // 第三方收费单实际申请费用的金额
}

// todo 获取fund模块数据
message GetApplyWorkflowFundInfoReq {
  int64 workflow_id = 1; // 工单ID
}

message WorkflowInfoByOrderId {
  NewWorkflowEntity workflow_info = 1;
  NewWorkflowApplicationEntity application_info = 2;
  NewWorkflowGuideEntity guide_info = 3;
  NewWorkflowSingleEntity single_info = 4;

  repeated NewWorkflowNodeEntity nodes_info = 6;
  repeated NewWorkflowNodeEntity steps_info = 7;
}

message WorkflowTaskView {
  NewWorkflowEntity workflow_info = 1;
  NewWorkflowApplicationEntity application_info = 2;
  NewWorkflowGuideEntity guide_info = 3;
  NewWorkflowSingleEntity single_info = 4;
  NewWorkflowNodeEntity step_info = 5;
  OrderGoodsEntity order_info = 6;
}

// 工单节点：转单请求
message TransferWorkflowNodeInfo {
  int64 workflow_id = 1;      // <必填> 工单ID
  int64 workflow_node_id = 2; // <必填> 工单节点ID
  int64 transfer_id = 3;      // <必填> 转单对象ID
  int64 customer_id = 4;      // <必填> 客户ID
}

// 文件数组
message FileList {
  string url = 1;           // 图片地址
  string name = 2;          // 合同名称
  string thumbnail_url = 3; // 缩略图
}

// 申请类工单 - 创建支款
message AddFinancialRefundWithWorkflowApplyReq {
  string order_no = 1; // 订单号
  //  string  fund_no = 2;  //收款单号
  //  string  refund_no = 3; //支款单号
  int64 customer_id = 4; // 客户ID  = orderid 查 orderinfo有
  string currency = 5;   // 退款币种     =   支款币种  1
  //  string exchange_rate = 6;//汇率 ? 产品要确认有没有汇率
  string real_amount_other = 7; // 申请退款金额   支款金额  1
  //  string real_amount_rmb = 8;//申请退款金额人民币 ?
  //  int32 refund_type =
  //  9;//款项类型(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
  //  1 int64 submit_id = 10;//提交者ID
  int32 refund_receive_account_type =
      11; // 退款接收账号类型(1=支付宝 2微信 3银行卡)  = 支款接受账号类型 1
  string refund_receive_account = 12;       // 退款接收账号   =  具体账号 1
  string refund_reason = 13;                // 退款原因  ? 待产品确认
  repeated UrlEntity refund_agreement = 14; // 支款协议  1
  //  int64  approve_by = 19;//更新人ID 1 = sumbitid
  int64 order_id = 20;                 // 订单ID
  repeated UrlEntity approve_log = 21; // 支款审批记录 1
  string workflow_no = 22;             // 工单编号
  int64 workflow_id = 23;              // 工单ID
  string workflow_name = 24;           // 关联工单名称
  //  string goods_name=25;//商品名称   2
  //  string goods_specs_name=26;//商品规格名称 2
  //  string service_name=27;//服务项目名称 2
  //  string brand_name=28;//品牌名称 2
  //  string business_name=29;//业务线名称 2
  //  string num=30;//数量 2
  //  string contract_amount=31;//合同金额 2
  //  int64  goods_id=32;//商品ID 2
  int64 deadline_time = 33; // 截止时间
}

message AddFinancialRefundWithWorkflowApplyRsp {}

message BusinessInfo {
  WorkflowBusinessType business_type = 1; // 业务类型
  int64 business_name = 2;                // 业务名称(模版ID)
  string current_node_name = 3;           // 当前节点名称(中文)
}

// 申请类工单 - 更新支款
message UpdateFinancialRefundWithWorkflowApplyReq {
  int64 refund_id = 1;          // 支款id
  int64 workflow_id = 2;        // 工单ID
  string currency = 5;          // 退款币种     =   支款币种  1
  string real_amount_other = 7; // 申请退款金额   支款金额  1
  //  string real_amount_rmb = 8;//申请退款金额人民币 ?
  //  int32 refund_type =
  //  9;//款项类型(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
  //  1 int64 submit_id = 10;//提交者ID
  int32 refund_receive_account_type =
      11; // 退款接收账号类型(1=支付宝 2微信 3银行卡)  = 支款接受账号类型 1
  string refund_receive_account = 12;       // 退款接收账号   =  具体账号 1
  string refund_reason = 13;                // 退款原因 1
  repeated UrlEntity refund_agreement = 14; // 支款协议  1
  //  int64  approve_by = 19;//更新人ID 1 = sumbitid
  repeated UrlEntity approve_log = 21; // 支款审批记录 1
  int64 refund_deadline = 22;          // 退款截止时间
}

message UpdateFinancialRefundWithWorkflowApplyRsp {}

// 申请类工单 - 更新支款
message DeleteFinancialRefundWithWorkflowApplyReq {
  int64 refund_id = 1;   // 支款id
  int64 workflow_id = 2; // 工单ID
}

message DeleteFinancialRefundWithWorkflowApplyRsp {}

// 工单信息
message NewWorkflowEntity {
  int64 id = 1;                      // 主键ID
  string workflow_no = 2;            // 工单编号
  string workflow_name = 3;          // 工单名称
  int64 workflow_template_id = 4;    // 工单模板 ID
  string workflow_template_name = 5; // 工单模板名称
  WorkflowStatus status = 6;         // 工单状态(0:UNKNOWN, 1:待接收, 2:服务中,
                                     // 3:服务暂停, 4:服务完成, 5:服务终止)
  InnerStatus inner_status = 7; // 内部状态(0:UNKNOWN, 1:正常，2:拒绝，3:暂停)
  WorkflowBusinessType business_type =
      8; // 业务类型(0:UNKNOWN, 1:申请, 2:上诉, 3:辅导, 4:博士单项, 5:其他单项)
  string business_name = 9;          // 业务名称
  int64 order_id = 10;               // 订单ID
  int64 product_id = 11;             // 商品ID
  int64 created_by = 12;             // 创建人 ID
  string created_role_snapshot = 13; // 创建工单时创建人角色快照
  string created_dept_snapshot = 14; // 创建工单时创建人部门快照
  int64 updated_by = 15;             // 更新人 ID
  string updated_role_snapshot = 16; // 处理工单时更新人角色快照
  string updated_dept_snapshot = 17; // 处理工单时更新人部门快照
  string processors = 18;            // 处理人 IDs
  int64 customer_id = 19;            // 客户 ID
  CustomerInfo customer_info = 50;   // 客户详细信息
  EmployeeInfos creator_info = 51;   // 创建人详细信息
  EmployeeInfos updater_info = 52;   // 更新人详细信息
  string customer_profile = 20;      // 用户画像(每个工单一个)
  int64 created_at = 21;             // 创建时间
  int64 updated_at = 22;             // 更新时间
  int64 deleted_at = 23;             // 删除时间
  int64 dispatched_at = 24;          // 派单时间
  int64 received_at = 25;            // 接收时间
  int64 paused_at = 26;              // 暂停时间
  int64 terminated_at = 27;          // 终止时间
  int64 completed_at = 28;           // 完成时间
  int64 due_at = 29;                 // 截止时间
  int64 message_board_id = 30;       // 留言板 ID
  string qq_im_group_id = 31;        // 群id
  string remark = 32;                // 备注信息，长度不超过2000字
  int64 version = 33;                // 工单版本号，最大不超过当前工单的节点数
  string order_no = 34;              // 订单编号
  string product_name = 35;          // 商品名称
  string service_name = 36;          // 服务项目名称
  int64 goods_type = 37;             // 商品类型(商品，赠品)
  int64 goods_spec_index = 38;       // 商品规格索引
  int64 spec_id = 39;                // 商品规格ID
}

// 申请类信息
message NewWorkflowApplicationEntity {
  int64 id = 1;                       // 主键ID
  int64 workflow_id = 2;              // 关联工单ID
  int64 selection_university_id = 3;  // 大学ID
  string selection_university_zh = 4; // 大学中文名称
  string selection_university_en = 5; // 大学英文名称
  int64 selection_major_id = 6;       // 专业ID
  string selection_major_zh = 7;      // 专业中文名称
  string selection_major_en = 8;      // 专业英文名称
  WorkflowCourseLevel selection_course_level =
      9; // 课程等级:0-未知 1-本科预科 2-本科 3-硕士预科 4-硕士 5-博士 6-其他
  int64 selection_expected_entry = 10; // 期望入学日期
  int32 fund_bind_type = 11;    // 1: 新增 2: 关联已有审核通过的收款单 3：免收费
  int64 fund_id = 12;           // 第三方申请费id, -1为免费
  string fund_free_reason = 13; // 免费第三方申请费理由
  Channel selection_channel = 14;                         // 申请渠道
  string selection_major_link = 15;                       // 专业链接
  repeated UrlEntity selection_offer_agreement_link = 16; // 定校协议链接
  repeated UrlEntity documents_cv_links = 17;             // CV链接
  repeated UrlEntity documents_rl_links = 18;             // rl链接
  repeated UrlEntity documents_ps_links = 19;             // PS链接
  repeated UrlEntity materials_package_links = 20;        // 材料包链接数组
  string application_no = 21;                             // 申请号
  repeated UrlEntity application_receipt_link = 22;       // 申请回执链接
  string application_info = 23;                           // 申请详细信息
  string application_tracking_info = 24;                  // 申请跟踪信息
  string application_security_record = 25;                // 申请安全记录
  repeated UrlEntity offer_file_links = 26;               // Offer文件链接
  int64 offer_confirmation_deadline = 27;                 // 确认截止日期
  int32 offer_deposit_required = 28;                      // 押金要求
  string offer_deposit_amount = 29;                       // 押金金额
  string offer_deposit_currency = 30;                     // 押金币种
  int64 offer_deposit_deadline = 31;                      // 押金截止日期
  int32 offer_cas_required = 32;                          // CAS押金要求
  string offer_cas_deposit_amount = 33;                   // CAS押金金额
  string offer_cas_deposit_currency = 34;                 // CAS押金币种
  int64 offer_cas_deadline = 35;                          // CAS截止日期
  string offer_deposit_link = 36;       // 押金支付链接（文本）
  string offer_accommodation_link = 37; // 住宿申请链接（文本）
  WorkflowAcademicRequirement offer_academic_requirement =
      38; // 学术要求类型(0.UNKNOWN;1.None;2.FirstClass;3.2:1;4.2:2;5.3rdClass;6.OrdinaryDegree;7.UKAverage;8.ChinaAverage;9.OtherAverage)
  string offer_academic_score = 39; // 学术成绩
  WorkflowLanguageScoreType offer_language_test_type =
      40; // 语言成绩类型(0.UNKNOWN;1.无;2.雅思;3.托福;4.PTE;5.其他)
  WorkflowLanguageScore offer_language_test_score = 41; // 语言成绩
  WorkflowRecommendationLetterRequirement offer_reference_requirement =
      42;                   // 推荐信要求类型(0.UNKNOWN;1.1 REF;2.2 REF)
  string offer_remark = 43; // Offer备注
  repeated UrlEntity rejection_file_links = 44;            // 拒信文件链接
  int32 visa_required = 45;                                // 是否需要申请签证
  repeated UrlEntity visa_file_links = 46;                 // 签证文件链接
  repeated UrlEntity admission_confirmation_link = 47;     // 录取确认函链接
  int64 created_at = 48;                                   // 创建时间
  int64 updated_at = 49;                                   // 更新时间
  int64 deleted_at = 50;                                   // 删除时间
  repeated UrlEntity closeness_material_links = 51;        // 套磁素材
  repeated UrlEntity closeness_letter_links = 52;          // 套磁信件
  repeated UrlEntity closeness_polished_letter_links = 53; // 润色后的套磁信
  repeated GuideList guide_info_links = 54; // 择导信息列表，最多10轮
  int64 selection_location_id = 55;         // 大学所在地区ID
  string selection_location_zh = 56;        // 大学所在地区的中文名称
  string selection_location_en = 57;        // 大学所在地区的英文名称
  string fund_currency_code = 58;  // 第三方收费单实际申请费用的货币code
  string fund_currency_value = 59; // 第三方收费单实际申请费用值
  string refund_ids_json =
      60; // 第三方消费绑定的支款id列表，内容为一个id数组的json
}

// 辅导类信息
message NewWorkflowGuideEntity {
  int64 id = 1;                 // 主键ID
  int64 workflow_id = 2;        // 关联工单ID
  int32 demand_type = 3;        // 需求类型
  string professional_type = 4; // 专业类型
  string notes = 5;             // 备注
  string teacher = 6;           // 匹配老师
  int64 user_id = 7;            // 匹配人
  string teacher_price_val = 8; // 师资报价
  int32 teacher_price_unit = 9; // 师资报价单位,枚举值,千字、次、课时
  string teacher_cost = 10;     // 师资成本
  int64 init_lesson_date = 11;  // 破冰课上课日期
  int32 init_lesson_time = 12;  // 破冰课上课时间
  repeated UrlEntity lesson_plan_file_url = 13;      // 教案文件URL
  int64 topic_lesson_date = 14;                      // 定题课日期
  int32 topic_lesson_time = 15;                      // 定题课时间
  repeated UrlEntity topic_ready_list_file_url = 16; // 定题课备课清单文件URL
  int64 explain_lesson_date = 17;                    // 讲解课日期
  int32 explain_lesson_time = 18;                    // 讲解课时间
  repeated LessonInfoValue lesson_info_json = 19;    // 课程辅导正课环节信息JSON
  repeated UrlEntity manuscript_outline_file_url = 20;  // 论文大纲文件URL
  int64 first_draft_lesson_date = 21;                   // 初稿批改环节日期
  int32 first_draft_lesson_time = 22;                   // 初稿批改环节时间
  repeated UrlEntity first_draft_file_url = 23;         // 初稿文件URL
  int64 second_draft_lesson_date = 24;                  // 二稿环节日期
  int32 second_draft_lesson_time = 25;                  // 二稿环节时间
  repeated UrlEntity second_draft_file_url = 26;        // 二稿文件URL
  repeated UrlEntity refinement_draft_file_url = 27;    // 精修稿URL
  repeated UrlEntity final_draft_file_url = 28;         // 终稿文件URL
  repeated UrlEntity result_material_file_url = 29;     // 结果资料文件URL
  repeated UrlEntity result_first_draft_file_url = 30;  // 撰稿环节初稿文件URL
  repeated UrlEntity result_final_draft_file_url = 31;  // 已定稿环节定稿文件URL
  repeated UrlEntity result_offer_notice_file_url = 32; // 已收到录用通知文件URL
  string result_publish_url = 33;                       // 见刊链接URL
  int64 created_at = 34;                                // 创建时间
  int64 updated_at = 35;                                // 更新时间
  int64 deleted_at = 36;                                // 删除时间
  string user_name = 37;                                // 匹配人名字
  string match_user_name = 38;                          // 匹配人名字(输入框)
}

// 单项类信息
message NewWorkflowSingleEntity {
  int64 id = 1;                             // 主键ID
  int64 workflow_id = 2;                    // 关联工单ID
  string single_requirement_info = 3;       // 需求信息
  repeated UrlEntity single_file_links = 4; // 结果文件链接
  int64 created_at = 5;                     // 创建时间
  int64 updated_at = 6;                     // 更新时间
  int64 deleted_at = 7;                     // 删除时间
  int32 dispatch_required = 8;              // 是否需要转案到业务处理
}

// 附加信息
message NewWorkflowAttachmentEntity {
  int64 id = 1;          // 主键ID
  int64 order_id = 3;    // 订单ID
  int64 customer_id = 4; // 客户ID
  int32 business_type =
      5; // 业务类型,
         // 0:UNKNOWN；1:预本硕申请；2:PhD或MPhil的申请及单项；3:辅导；4：其他单项
  string uoffer_app_info = 6;     // 录多app账密信息
  string past_experience = 7;     // 过往特殊经历
  string special_application = 8; // 申请需求特殊情况
  int32 is_designated_institution =
      9; // 是否指定申请专业院校，0:UNKNOWN；1:指定；2:不指定
  string institution_name = 10;          // 指定院校名称
  int32 is_commitment_success_rate = 11; // 承诺成功率，0:UNKNOWN；1:是；2:否
  string commitment_file_links = 12;     // 成功率承诺文件链接
  int32 is_guaranteed_college_major =
      13;                        // 是否承诺保底院校专业，0:UNKNOWN；1:是；2:否
  int32 is_native_document = 14; // 是否承诺native文书，0:UNKNOWN；1:是；2:否
  int32 is_native_communication =
      15; // 是否承诺native文书沟通，0:UNKNOWN；1:是；2:否
  int32 is_document_background = 16; // 是否承诺文书背景，0:UNKNOWN；1:是；2:否
  int32 is_commissioner_background =
      17;                    // 是否承诺沟通专员背景，0:UNKNOWN；1:是；2:否
  string cv_file_links = 18; // cv成绩单等文件链接
  int32 ug_application_level =
      19; // 申请等级，0:UNKNOWN；1:一硕；2:二硕；3:本科；4:预科
  int32 ug_home_return_permit = 20;  // 是否有港澳通行证，0:UNKNOWN，1是，2否
  string ug_extra_free_service = 21; // 额外/赠送的服务
  string ug_commissioner_info = 22;  // 意向与沟通专员性别与性格特点
  string ug_third_party_info = 23;   // 第三方缴费金额和学校名称
  string ug_guaranteed_college_major_file_links = 24; // 保底院校承诺文件链接
  string ug_native_document_file_links = 25;          // native文书承诺文件链接
  string ug_native_communication_file_links = 26; // native文书沟通承诺文件链接
  string ug_document_background_file_links = 27;  // 文书背景承诺文件链接
  string ug_commissioner_background_file_links = 28; // 沟通专员背景承诺文件链接
  int32 ug_is_no_language =
      29; // 海外背景学生是否承诺申请院校可以免语言，0:UNKNOWN；1:是；2:否
  string ug_no_language_file_links = 30;  // 免语言承诺文件链接
  int32 pm_application_level = 31;        // 申请等级，0:UNKNOWN；1:PhD；2:MPhil
  int32 pm_closeness_letter = 32;         // 是否需要套磁, 0:UNKNOWN；1:是；2:否
  string pm_guaranteed_college_name = 33; // 保底院校名称/排名
  string pm_document_background = 34;     // 文书背景
  string pm_commissioner_background = 35; // 专员背景
  int32 pm_is_introduce_closeness_letter =
      36;                          // 是否介绍过套磁流程，0:UNKNOWN；1:是；2:否
  int32 pm_is_pr_tutorship = 37;   // 是否有PR辅导，0:UNKNOWN；1:是；2:否
  int32 pm_is_select_teacher = 38; // 是否已匹配老师，0:UNKNOWN；1:是；2:否
  int64 pm_tutoring_started_at = 39;  // 期望辅导开始时间
  string co_pr_submit = 40;           // PR提交时间
  string co_pr_word = 41;             // PR字数要求
  string co_ref_style = 42;           // Referencing Style
  int32 co_is_select_guide = 43;      // 是否择导列表，0:UNKNOWN；1:是；2:否
  string supplements_info = 44;       // 补充信息
  string supplements_file_links = 45; // 补充文件链接
  int64 created_at = 46;              // 创建时间
  int64 updated_at = 47;              // 更新时间
  int64 deleted_at = 48;              // 删除时间
  int64 processor_id = 49;            // 被指派人ID
}

message NewWorkflowNodeEntity {
  int64 id = 1;
  string name = 2;
  string chinese_name = 3;
  WorkflowNodeStatus status = 4;
  WorkflowNodeType type = 5;
  int64 dispatched_by = 6;
  string dispatched_by_role_snapshot = 7;
  string dispatched_by_dept_snapshot = 8;
  int64 processed_by = 9;
  string processed_by_role_snapshot = 10;
  string processed_by_dept_snapshot = 11;
  EmployeeInfos dispatcher_info = 51; // 转案人详细信息
  EmployeeInfos processor_info = 52;  // 处理人详细信息
  int64 created_at = 12;
  int64 updated_at = 13;
  int64 dispatched_at = 14;
  int64 started_at = 15;
  int64 paused_at = 16;
  int64 deleted_at = 17;
  int64 completed_at = 18;
  int64 terminated_at = 19;
  int64 due_at = 20;
  string abnormal_reason = 21;
  string attachments = 22;
  int64 workflow_id = 23;
  repeated string next = 24;
  repeated string preview = 25;
  int32 sync_node = 26;
  repeated string allow_role_list = 27;
  int64 branch = 28;
  int64 level = 29;
  repeated NewWorkflowTaskEntity tasks = 50;
}

message NewWorkflowTaskEntity {
  int64 id = 1;
  string name = 2;
  int32 status = 3;
  int64 created_at = 4;
  int64 started_at = 5;
  int64 updated_at = 6;
  int64 deleted_at = 7;
  int64 completed_at = 8;
  int64 due_at = 9;
  int64 workflow_node_id = 10;
}

message LessonInfoValue {
  string lesson_name = 1; // 课程名称
  int64 lesson_date = 2;  // 上课日期
  int32 lesson_time = 3;  // 上课时间
}