syntax = "proto3";
package admin_api;
option go_package = "server/cmd/admin_api";

import "hz.proto"; // hz框架注解

/* 以下代码用于自动生成切勿修改 */
// AUTOGEN import "constants.proto"; // 常量定义
// AUTOGEN import "errno.proto"; // 错误码定义
/* 以上代码用于自动生成切勿修改 */

import "api_base.proto";          // 空响应
import "health.proto";            // 内置健康检查
import "user.proto";              // 用户
import "product.proto";           // 商品
import "customer.proto";          // 客户
import "workflow.proto";          // 工单
import "workflow_guidance.proto"; // 工单-辅导类
import "order.proto";             // 订单
// import "email.proto"; // 邮件
import "brand.proto";             //品牌
import "financial_account.proto"; // 邮件
import "financial_fund.proto";    // 财务收款
import "financial_refund.proto";  // 财务退款
import "financial_payment.proto"; // 在线支付部分

import "dept.proto";          //部门
import "employee.proto";      // 员工
import "menu.proto";          //权限
import "role.proto";          //角色
import "auth.proto";          //鉴权
import "tools.proto";         //工具
import "message.proto";       //客服会话和留言板
import "message_board.proto"; // 消息
import "user_object.proto";   // 用户对象 能力属于im，接口属于员工
import "transfer.proto";      // 交接任务

import "udata.proto"; // uData

// 健康检查
service Health {
  // 检查当前服务状态
  rpc HealthCheck(HealthCheckReq) returns (HealthCheckRsp) {
    option (api.post) = "/health/Check";
  }
}

// 用户、组织架构、鉴权
service User {
  // 创建部门
  rpc CreateDept(CreateDeptReq) returns (CreateDeptRsp) {
    option (api.post) = "/dept/CreateDept";
  }
  // 编辑部门
  rpc UpdateDept(UpdateDeptReq) returns (UpdateDeptRsp) {
    option (api.post) = "/dept/UpdateDept";
  }
  // 查询所有的部门
  rpc ListDeptByBrandId(ListDeptByBrandIdReq) returns (ListDeptByBrandIdRsp) {
    option (api.post) = "/dept/ListDeptByBrandId";
  }
  // 查询所有的品牌以及部门带人数
  rpc ListDeptAndBrand(ListDeptAndBrandReq) returns (ListDeptAndBrandRsp) {
    option (api.post) = "/dept/ListDeptAndBrand";
  }
  // 删除部门
  rpc DeleteDept(DeleteDeptReq) returns (DeleteDeptRsp) {
    option (api.post) = "/dept/DeleteDeptByBrandId";
  }
  // 添加员工
  rpc CreateEmployee(CreateEmployeeReq) returns (CreateEmployeeRsp) {
    option (api.post) = "/employee/CreateEmployee";
  }
  // 重复手机号
  rpc ExistsPhone(ExistsPhoneReq) returns (ExistsPhoneRsp) {
    option (api.post) = "/employee/ExistsPhone";
  }
  // 重复邮箱
  rpc ExistsEmail(ExistsEmailReq) returns (ExistsEmailRsp) {
    option (api.post) = "/employee/ExistsEmail";
  }
  // 重复姓名
  rpc ExistsName(ExistsNameReq) returns (ExistsNameRsp) {
    option (api.post) = "/employee/ExistsName";
  }
  // 编辑员工信息
  rpc EditEmployee(EditEmployeeReq) returns (EditEmployeeRsp) {
    option (api.post) = "/employee/EditEmployee";
  }
  // 编辑个人信息
  rpc EditSelfEmployee(EditSelfEmployeeReq) returns (EditSelfEmployeeRsp) {
    option (api.post) = "/employee/EditSelfEmployee";
  }
  // 搜索员工
  rpc ListEmployee(ListEmployeeReq) returns (ListEmployeeRsp) {
    option (api.post) = "/employee/ListEmployee";
  }
  // 搜索员工,只返回单表信息
  rpc ListEmployeeBySearchKey(ListEmployeeBySearchKeyReq)
      returns (ListEmployeeBySearchKeyRsp) {
    option (api.post) = "/employee/ListEmployeeBySearchKey";
  }
  // 搜索员工,只返回单表信息,不验证权限
  rpc ListEmployeeBySearchKeyNoAuth(ListEmployeeBySearchKeyNoAuthReq)
      returns (ListEmployeeBySearchKeyNoAuthRsp) {
    option (api.post) = "/employee/ListEmployeeBySearchKeyNoAuth";
  }
  // 根据邮箱搜索
  rpc ListEmployeeByEmail(ListEmployeeByEmailReq)
      returns (ListEmployeeByEmailRsp) {
    option (api.post) = "/employee/ListEmployeeByEmail";
  }

  // 设置员工权限
  rpc SetEmployeeMenu(SetEmployeeMenuReq) returns (SetEmployeeMenuRsp) {
    option (api.post) = "/employee/SetEmployeeMenu";
  }
  // 获取员工信息
  rpc GetEmployeeInfo(GetEmployeeInfoReq) returns (GetEmployeeInfoRsp) {
    option (api.post) = "/employee/GetEmployeeInfo";
  }
  // 获取员工的组织信息
  rpc GetEmployeeOrgInfo(GetEmployeeOrgInfoReq)
      returns (GetEmployeeOrgInfoRsp) {
    option (api.post) = "/employee/GetEmployeeOrgInfoReq";
  }
  // 批量获取员工信息
  rpc GetEmployeeInfoByIds(GetEmployeeInfoByIdsReq)
      returns (GetEmployeeInfoByIdsRsp) {
    option (api.post) = "/employee/GetEmployeeInfoByIds";
  }
  // 设置职级（主管）
  rpc SetPosition(SetPositionRep) returns (SetPositionRsp) {
    option (api.post) = "/employee/SetPosition";
  }
  // 取消职级（主管）
  rpc CancelPosition(CancelPositionRep) returns (CancelPositionRsp) {
    option (api.post) = "/employee/CancelPosition";
  }
  // 离职
  rpc SetEmploymentStatus(SetEmploymentStatusRep)
      returns (SetEmploymentStatusRsp) {
    option (api.post) = "/employee/SetEmploymentStatus";
  }
  // 查询当前主管的部门信息以及部门成员
  rpc GetEmployeeMangerTreeDept(GetEmployeeMangerTreeDeptReq)
      returns (GetEmployeeMangerTreeDeptRsp) {
    option (api.post) = "/employee/GetEmployeeMangerTreeDept";
  }
  // 查询当前部门的人员
  rpc ListEmployeeByDept(ListEmployeeByDeptReq)
      returns (ListEmployeeByDeptRsp) {
    option (api.post) = "/employee/ListEmployeeByDept";
  }
  // 根据角色id获取角色名称查询员工
  rpc ListEmployeeByRoleIdsOrName(ListEmployeeByRoleIdsOrNameReq)
      returns (ListEmployeeByRoleIdsOrNameRsp) {
    option (api.post) = "/employee/ListEmployeeByRoleIdsOrName";
  }
  // 获取所有的管理权限的员工
  rpc GetEmployeeAdmin(GetEmployeeAdminReq) returns (GetEmployeeAdminRsp) {
    option (api.post) = "/employee/GetEmployeeAdmin";
  }
  // 添加权限
  rpc CreateMenu(CreateMenuReq) returns (CreateMenuRsp) {
    option (api.post) = "/menu/CreateMenu";
  }
  // 编辑权限
  rpc UpdateMenu(UpdateMenuReq) returns (UpdateMenuRsp) {
    option (api.post) = "/menu/UpdateMenu";
  }
  // 查询菜单
  rpc ListMenuTree(ListMenuTreeReq) returns (ListMenuTreeRsp) {
    option (api.post) = "/menu/ListMenuTree";
  }
  // 根据Id查询菜单
  rpc ListMenuByMenuParentId(ListMenuByMenuParentIdReq)
      returns (ListMenuByMenuParentIdRsp) {
    option (api.post) = "/menu/ListMenuByMenuParentId";
  }
  // 根据perm查询菜单
  rpc ListMenuByPerm(ListMenuByPermReq) returns (ListMenuByPermRsp) {
    option (api.post) = "/menu/ListMenuByPerm";
  }
  // 创建角色
  rpc CreateRole(CreateRoleReq) returns (CreateRoleRsp) {
    option (api.post) = "/role/CreateRole";
  }
  // 编辑角色
  rpc UpdateRole(UpdateRoleReq) returns (UpdateRoleRsp) {
    option (api.post) = "/role/UpdateRole";
  }
  // 查询角色
  rpc ListRole(ListRoleReq) returns (ListRoleRsp) {
    option (api.post) = "/role/ListRole";
  }
  // 删除角色
  rpc DeleteRole(DeleteRoleReq) returns (DeleteRoleRsp) {
    option (api.post) = "/role/DeleteRole";
  }
  // 删除权限
  rpc DeleteMenu(DeleteMenuReq) returns (DeleteMenuRsp) {
    option (api.post) = "/menu/DeleteMenu";
  }
  // 获取权限详情
  rpc GetMenuByMenuId(GetMenuByMenuIdReq) returns (GetMenuByMenuIdRsp) {
    option (api.post) = "/menu/GetMenuByMenuId";
  }
  // 获取角色下的员工数量
  rpc GetRoleEmpCount(GetRoleEmpCountReq) returns (GetRoleEmpCountRsp) {
    option (api.post) = "/role/GetRoleEmpCount";
  }
  // 获取权限是否重名
  rpc ExistMenuPerm(ExistMenuPermReq) returns (ExistMenuPermRsp) {
    option (api.post) = "/menu/ExistMenuPerm";
  }
  // 根据id查询部门详情
  rpc GetDeptDetailById(GetDeptDetailByIdReq) returns (GetDeptDetailByIdRsp) {
    option (api.post) = "/dept/GetDeptDetailById";
  }
  // 根据员工id查询部门详情
  rpc ListDeptByEmployeeId(ListDeptByEmployeeIdReq)
      returns (ListDeptByEmployeeIdRsp) {
    option (api.post) = "/dept/ListDeptByEmployeeId";
  }
  // 部门拖拽排序
  rpc SortDept(SortDeptReq) returns (SortDeptRsp) {
    option (api.post) = "/dept/SortDept";
  }
  // 我所管理的员工的id的list
  rpc ListChildEmployeeById(ListChildEmployeeByIdReq)
      returns (ListChildEmployeeByIdRsp) {
    option (api.post) = "/employee/ListChildEmployeeById";
  }
  // 根据id查询当前员工的菜单列表
  rpc GetMenuListById(GetMenuListByIdReq) returns (GetMenuListByIdRsp) {
    option (api.post) = "/employee/GetMenuListById";
  }

  // 更新或者添加用户对象
  rpc UserObjectUpsert(UserObjectUpsertReq) returns (UserObjectUpsertRsp) {
    option (api.post) = "/employee/UserObjectUpsert";
  }

  // 查询用户对象
  rpc UserObjectQry(UserObjectQryReq) returns (UserObjectQryRsp) {
    option (api.post) = "/employee/UserObjectQry";
  }

  // 员工与部门列表导出请求
  rpc AddExportEmployeeDeptList(AddExportEmployeeDeptListReq)
      returns (AddExportEmployeeDeptListRsp) {
    option (api.post) = "/employee/AddExportEmployeeDeptList";
  }

  // 员工与部门列表导入请求
  rpc AddImportEmployeeDeptList(AddImportEmployeeDeptListReq)
      returns (AddImportEmployeeDeptListRsp) {
    option (api.post) = "/employee/AddImportEmployeeDeptList";
  }
  // 获取当前员工往上层级的部门主管信息
  rpc ListManagerIdById(ListManagerIdByIdReq) returns (ListManagerIdByIdRsp) {
    option (api.post) = "/employee/ListManagerIdById";
  }
  // 创建交接任务
  rpc CreateTransferTask(CreateTransferTaskReq)
      returns (CreateTransferTaskRsp) {
    option (api.post) = "/employee/CreateTransferTask";
  }
  // 获取交接任务详情
  rpc GetTransferTaskDetail(GetTransferTaskDetailReq)
      returns (GetTransferTaskDetailRsp) {
    option (api.post) = "/employee/GetTransferTaskDetail";
  }
}

// 产品接口
service Products {
  //--------------------品牌业务线相关接口start------------------------------

  // 获取品牌业务线列表
  rpc GetBrandBusinessList(GetBrandBusinessListReq)
      returns (GetBrandBusinessListRsp) {
    option (api.post) = "/product/GetBrandBusinessList";
  }
  // 获取业务线列表
  rpc GetBusinessList(GetBusinessListReq) returns (GetBusinessListRsp) {
    option (api.post) = "/product/GetBusinessList";
  }

  // 获取业务线详情
  rpc GetBusinessDetail(GetBusinessDetailReq) returns (GetBusinessDetailRsp) {
    option (api.post) = "/product/GetBusinessDetail";
  }
  // 新建业务线
  rpc SaveBusiness(SaveBusinessDetailReq) returns (SaveBusinessDetailRsp) {
    option (api.post) = "/product/SaveBusiness";
  }
  // 删除业务线
  rpc DeleteBusiness(DeleteBusinessReq) returns (DeleteBusinessRsp) {
    option (api.post) = "/product/DeleteBusiness";
  }
  //-------------------品牌业务线相关接口end------------------------------------

  //-------------------服务项目相关接口start------------------------------------

  // 分页查询服务项目
  rpc GetServiceItemList(GetServiceItemListReq)
      returns (GetServiceItemListRsp) {
    option (api.post) = "/product/GetServiceItemList";
  }
  // 获取服务项目详情
  rpc GetServiceItemDetail(GetServiceItemDetailReq)
      returns (GetServiceItemDetailRsp) {
    option (api.post) = "/product/GetServiceItemDetail";
  }
  // 保存服务项目
  rpc SaveServiceItem(SaveServiceItemReq) returns (SaveServiceItemRsp) {
    option (api.post) = "/product/SaveServiceItem";
  }
  // 删除服务项目
  rpc DeleteServiceItem(DeleteServiceItemReq) returns (DeleteServiceItemRsp) {
    option (api.post) = "/product/DeleteServiceItem";
  }
  //-------------------服务项目相关接口end ------------------------------------

  //-------------------合同管理相关接口start------------------------------------

  // 分页查询合同列表
  rpc GetContractList(GetContractListReq) returns (GetContractListRsp) {
    option (api.post) = "/product/GetContractList";
  }
  // 保存合同
  rpc SaveContract(SaveContractReq) returns (SaveContractRsp) {
    option (api.post) = "/product/SaveContract";
  }
  // 获取合同详情
  rpc GetContractDetail(GetContractDetailReq) returns (GetContractDetailRsp) {
    option (api.post) = "/product/GetContractDetail";
  }
  // 删除合同
  rpc DeleteContract(DeleteContractReq) returns (DeleteContractRsp) {
    option (api.post) = "/product/DeleteContract";
  }
  //-------------------合同管理相关接口end------------------------------------

  //----------------------商品相关接口start------------------------------------

  // 分页查询商品列表
  rpc GetProductList(GetProductListReq) returns (GetProductListRsp) {
    option (api.post) = "/product/GetProductList";
  }
  // 保存商品
  rpc SaveProduct(SaveProductReq) returns (SaveProductRsp) {
    option (api.post) = "/product/SaveProduct";
  }
  // 删除商品
  rpc DeleteProduct(DeleteProductReq) returns (DeleteProductRsp) {
    option (api.post) = "/product/DeleteProduct";
  }
  // 获取商品详情
  rpc GetProductDetail(GetProductDetailReq) returns (GetProductDetailRsp) {
    option (api.post) = "/product/GetProductDetail";
  }
  // 修改商品状态
  rpc ChangeProductStatus(ChangeProductStatusReq)
      returns (ChangeProductStatusRsp) {
    option (api.post) = "/product/ChangeProductStatus";
  }
  // 获取商品规格列表
  rpc GetProductSpecDetailList(GetProductSpecDetailListReq)
      returns (GetProductSpecDetailListRsp) {
    option (api.post) = "/product/GetProductSpecDetailList";
  }
  //----------------------商品相关接口end------------------------------------

  // 新建/保存专业分类
  rpc CreateMajorCategory(SaveMajorCategoryReq) returns (MajorCategoryRsp) {
    option (api.post) = "/product/CreateMajorCategory";
  }
  // 查询专业大类
  rpc GetMajorCategory(GetMajorCategoryReq) returns (MajorCategoryRsp) {
    option (api.post) = "/product/GetMajorCategory";
  }

  // 删除专业分类
  rpc DeleteMajorCategory(DeleteMajorCategoryReq)
      returns (DeleteMajorCategoryRsp) {
    option (api.post) = "/product/DeleteMajorCategory";
  }
  // 获取专业分类列表
  rpc ListMajorCategoryGroups(ListMajorCategoryGroupsReq)
      returns (ListMajorCategoryGroupsRsp) {
    option (api.post) = "/product/ListMajorCategoryGroups";
  }
  // 获取专业小类列表
  rpc ListSubCategories(ListSubCategoriesReq) returns (ListSubCategoriesRsp) {
    option (api.post) = "/product/ListSubCategories";
  }
  // 获取专业大类列表
  rpc ListMajorCategoriesForNewSub(ListMajorCategoriesForNewSubReq)
      returns (ListMajorCategoriesForNewSubRsp) {
    option (api.post) = "/product/ListMajorCategoriesForNewSub";
  }
  // 获取专业分类层级列表
  rpc GetMajorCategoryHierarchy(GetMajorCategoryHierarchyReq)
      returns (GetMajorCategoryHierarchyRsp) {
    option (api.post) = "/product/GetMajorCategoryHierarchy";
  };
  // 获取地区列表
  rpc GetLocationsList(GetLocationsListReq) returns (GetLocationsListRsp) {
    option (api.post) = "/product/GetLocationsList";
  };

  // 获取课程等级列表
  rpc ListCourseLevels(GetCourseLevelListReq) returns (GetCourseLevelListRsp) {
    option (api.post) = "/product/GetCourseLevelList";
  }

  // 获取大学列表
  rpc ListUniversities(ListUniversitiesReq) returns (ListUniversitiesRsp) {
    option (api.post) = "/product/ListUniversities";
  }

  // 获取专业列表
  rpc ListMajors(ListMajorsReq) returns (ListMajorsRsp) {
    option (api.post) = "/product/ListMajors";
  }

  // 获取大学与专业列表
  rpc ListUniversityMajor(ListUniversityMajorReq)
      returns (ListUniversityMajorRsp) {
    option (api.post) = "/product/ListUniversityMajor";
  }

  // 创建或更新大学
  rpc SaveUniversity(SaveUniversityReq) returns (SaveUniversityRsp) {
    option (api.post) = "/product/SaveUniversity";
  }

  // 创建或更新专业
  rpc SaveMajor(SaveMajorReq) returns (SaveMajorRsp) {
    option (api.post) = "/product/SaveMajor";
  }

  // 删除大学
  rpc DeleteUniversity(DeleteUniversityReq) returns (DeleteUniversityRsp) {
    option (api.post) = "/product/DeleteUniversity";
  }

  // 删除专业
  rpc DeleteMajor(DeleteMajorReq) returns (DeleteMajorRsp) {
    option (api.post) = "/product/DeleteMajor";
  }

  // 获取大学详情
  rpc GetUniversity(GetUniversityReq) returns (GetUniversityRsp) {
    option (api.post) = "/product/GetUniversity";
  }

  // 获取专业详情
  rpc GetMajor(GetMajorReq) returns (GetMajorRsp) {
    option (api.post) = "/product/GetMajor";
  }
}

// 订单接口
service Order {
  // 资产转移（多用户）
  rpc BatchUpdateOrderOwnId(BatchUpdateOrderOwnIdReq)
      returns (BatchUpdateOrderOwnIdRsp) {
    option (api.post) = "/order/BatchUpdateOrderOwnId";
  }

  // 批量更换最后编辑者ID（资产转移）
  rpc BatchUpdateUpdaterId(BatchUpdateUpdaterIdReq)
      returns (BatchUpdateUpdaterIdRsp) {
    option (api.post) = "/order/BatchUpdateUpdaterId";
  }

  // 通过订单ID获取订单的操作日志
  rpc GetOrderOperationLogList(GetOrderOperationLogListReq)
      returns (GetOrderOperationLogListRsp) {
    option (api.post) = "/order/GetOrderOperationLogList";
  }

  // 批量更新订单工单状态
  rpc BatchUpdateWorkflowStatus(BatchUpdateWorkflowStatusReq)
      returns (BatchUpdateWorkflowStatusRsp) {
    option (api.post) = "/order/BatchUpdateWorkflowStatus";
  }

  // 获取退款高危客户列表
  rpc GetRefundHighRiskCustomers(GetRefundHighRiskCustomersReq)
      returns (GetRefundHighRiskCustomersRsp) {
    option (api.post) = "/order/GetRefundHighRiskCustomers";
  }

  // 获取红线高危客户列表
  rpc GetRedLineRiskCustomers(GetRedLineRiskCustomersReq)
      returns (GetRedLineRiskCustomersRsp) {
    option (api.post) = "/order/GetRedLineRiskCustomers";
  }

  // 获取新旧客户列表
  rpc GetOldNewCustomers(GetOldNewCustomersReq)
      returns (GetOldNewCustomersRsp) {
    option (api.post) = "/order/GetOldNewCustomers";
  }

  // 通过用户ID获取用户订单数量
  rpc GetOrderCountByCustomerId(GetOrderCountByCustomerIdReq)
      returns (GetOrderCountByCustomerIdRsp) {
    option (api.post) = "/order/GetOrderCountByCustomerId";
  }

  // 通过用户ID批量获取用户订单数量
  rpc GetOrderCountByCustomerIds(GetOrderCountByCustomerIdsReq)
      returns (GetOrderCountByCustomerIdsRsp) {
    option (api.post) = "/order/GetOrderCountByCustomerIds";
  }

  // 获取工单全部为筛选状态的订单
  rpc GetWorkflowCompleteOrders(GetWorkflowCompleteOrdersReq)
      returns (GetWorkflowCompleteOrdersRsp) {
    option (api.post) = "/order/GetWorkflowCompleteOrders";
  }

  // 获取最新订单操作日志
  rpc GetLatestOrderOperationLogByOrderId(
      GetLatestOrderOperationLogByOrderIdReq)
      returns (GetLatestOrderOperationLogByOrderIdRsp) {
    option (api.post) = "/order/GetLatestOrderOperationLogByOrderId";
  }

  // 通过用户ID获取最新订单信息
  rpc GetLatestOrderInfoByCustomerIds(GetLatestOrderInfoByCustomerIdsReq)
      returns (GetLatestOrderInfoByCustomerIdsRsp) {
    option (api.post) = "/order/GetLatestOrderInfoByCustomerIds";
  }

  // 获取货币币种信息列表
  rpc GetCurrencyList(GetCurrencyListReq) returns (GetCurrencyListRsp) {
    option (api.post) = "/order/GetCurrencyList";
  }

  // 获取订单列表（已下定金、支付待确认、尾款待支付、支付成功、支款订单、交易关闭）
  rpc GetOrderList(GetOrderListReq) returns (GetOrderListRsp) {
    option (api.post) = "/order/GetOrderList";
  }

  // 通过订单ID获取订单详情（订单详情）
  rpc GetOrderInfo(GetOrderInfoReq) returns (GetOrderInfoRsp) {
    option (api.post) = "/order/GetOrderInfo";
  }

  // 通过订单编号获取订单详情（订单详情）
  rpc GetOrderInfoByOrderNo(GetOrderInfoByOrderNoReq)
      returns (GetOrderInfoByOrderNoRsp) {
    option (api.post) = "/order/GetOrderInfoByOrderNo";
  }

  // 通过订单id批量获取订单基本信息
  rpc GetOrderInfoByIds(GetOrderInfoByIdsReq) returns (GetOrderInfoByIdsRsp) {
    option (api.post) = "/order/GetOrderInfoByIds";
  }

  // 定金订单列表（已下定金）
  rpc GetOrderDepositList(GetOrderDepositListReq)
      returns (GetOrderDepositListRsp) {
    option (api.post) = "/order/GetOrderDepositList";
  }

  // 创建/编辑定金订单（已下定金）
  rpc SaveOrderDeposit(SaveOrderDepositReq) returns (SaveOrderDepositRsp) {
    option (api.post) = "/order/SaveOrderDeposit";
  }

  // 撤销定金订单审核（已下定金）
  rpc DisbursementOrderDeposit(DisbursementOrderDepositReq)
      returns (DisbursementOrderDepositRsp) {
    option (api.post) = "/order/DisbursementOrderDeposit";
  }

  // 关闭定金订单（已下定金）
  rpc CloseOrderDeposit(CloseOrderDepositReq) returns (CloseOrderDepositRsp) {
    option (api.post) = "/order/CloseOrderDeposit";
  }

  // 定金订单申请退款（已下定金）
  rpc OrderDepositRefund(OrderDepositRefundReq)
      returns (OrderDepositRefundRsp) {
    option (api.post) = "/order/OrderDepositRefund";
  }

  // 首款订单列表（支付待确认）
  rpc GetOrderFirstList(GetOrderListReq) returns (GetOrderFirstListRsp) {
    option (api.post) = "/order/GetOrderFirstList";
  }

  // 创建/编辑销售订单（支付待确认）
  rpc SaveOrderFirst(SaveOrderFirstReq) returns (SaveOrderFirstRsp) {
    option (api.post) = "/order/SaveOrderFirst";
  }

  // 撤销销售订单审核（支付待确认）
  rpc DisbursementOrderFirst(DisbursementOrderFirstReq)
      returns (DisbursementOrderFirstRsp) {
    option (api.post) = "/order/DisbursementOrderFirst";
  }

  // 关闭销售订单（支付待确认）
  rpc CloseOrderFirst(CloseOrderFirstReq) returns (CloseOrderFirstReq) {
    option (api.post) = "/order/CloseOrderFirst";
  }

  // 尾款订单列表（尾款待支付）
  rpc GetOrderFinalList(GetOrderListReq) returns (GetOrderFinalListRsp) {
    option (api.post) = "/order/GetOrderFinalList";
  }

  // 提交尾款（尾款待支付）
  rpc UpdateOrderFinal(UpdateOrderFinalReq) returns (UpdateOrderFinalRsp) {
    option (api.post) = "/order/UpdateOrderFinal";
  }

  // 撤销尾款审核（尾款待支付）
  rpc DisbursementOrderFinal(DisbursementOrderFinalReq)
      returns (DisbursementOrderFinalRsp) {
    option (api.post) = "/order/DisbursementOrderFinal";
  }

  // 支款订单列表（支款订单）
  rpc GetOrderDisbursementList(GetOrderListReq)
      returns (GetOrderDisbursementListRsp) {
    option (api.post) = "/order/GetOrderDisbursementList";
  }

  // 编辑支款订单（支款订单）
  rpc UpdateOrderDisbursement(UpdateOrderDisbursementReq)
      returns (UpdateOrderDisbursementRsp) {
    option (api.post) = "/order/UpdateOrderDisbursement";
  }

  // 撤销支款订单审核（支款订单）
  rpc DisbursementOrderDisbursement(DisbursementOrderDisbursementReq)
      returns (DisbursementOrderDisbursementRsp) {
    option (api.post) = "/order/DisbursementOrderDisbursement";
  }

  // 支付成功订单列表（支付成功）
  rpc GetOrderSuccessList(GetOrderListReq) returns (GetOrderSuccessListRsp) {
    option (api.post) = "/order/GetOrderSuccessList";
  }

  // 关闭订单列表（交易关闭）
  rpc GetOrderCloseList(GetOrderListReq) returns (GetOrderCloseListRsp) {
    option (api.post) = "/order/GetOrderCloseList";
  }

  // 订单管理列表导出请求
  rpc AddExportOrderList(AddExportOrderListReq)
      returns (AddExportOrderListRsp) {
    option (api.post) = "/order/AddExportOrderList";
  }

  //----------------------财务相关接口 start-----------------------------------

  // 账号类型
  rpc AccountType(FinancialAccountTypeReq) returns (FinancialAccountTypeRsp) {
    option (api.post) = "/financial/AccountType";
  }
  // 创建财务账号
  rpc CreateAccount(FinancialAccountCreateReq)
      returns (FinancialAccountCreateRsp) {
    option (api.post) = "/financial/CreateAccount";
  }
  // 账号详情
  rpc AccountInfo(FinancialAccountInfoReq) returns (FinancialAccountResp) {
    option (api.post) = "/financial/AccountInfo";
  }
  // 账号列表
  rpc AccountList(FinancialAccountListReq) returns (FinancialAccountListRsp) {
    option (api.post) = "/financial/AccountList";
  }
  // 账号删除
  rpc AccountDelete(FinancialAccountDeleteReq)
      returns (FinancialAccountDeleteRsp) {
    option (api.post) = "/financial/AccountDelete";
  }
  // 账号修改
  rpc AccountUpdate(FinancialAccountUpdateReq)
      returns (FinancialAccountUpdateRsp) {
    option (api.post) = "/financial/AccountUpdate";
  }

  // 收款单列表
  rpc FundList(FinancialFundListReq) returns (FinancialFundListRsp) {
    option (api.post) = "/financial/FundList";
  }
  // 收款单详情
  rpc FundDetail(FinancialFundDetailReq) returns (FinancialFundDetailRsp) {
    option (api.post) = "/financial/FundDetail";
  }
  // 收款单审核通过或者驳回
  rpc FundApprove(FinancialFundApproveReq) returns (FinancialFundApproveRsp) {
    option (api.post) = "/financial/FundApprove";
  }

  // 退款单列表
  rpc RefundList(FinancialRefundListReq) returns (FinancialRefundListRsp) {
    option (api.post) = "/financial/RefundList";
  }
  // 退款单详情
  rpc RefundDetail(FinancialRefundDetailReq)
      returns (FinancialRefundDetailRsp) {
    option (api.post) = "/financial/RefundDetail";
  }
  // 退款单审核通过或者驳回
  rpc RefundApprove(FinancialRefundApproveReq)
      returns (FinancialRefundApproveRsp) {
    option (api.post) = "/financial/RefundApprove";
  }
  // 报错草稿
  rpc SaveFundDraft(FinancialFundDraftReq) returns (FinancialFundDraftRsp) {
    option (api.post) = "/financial/SaveFundDraft";
  }
  // 获取草稿
  rpc GetFundDraft(GetFinancialFundDraftReq)
      returns (GetFinancialFundDraftRsp) {
    option (api.post) = "/financial/GetFundDraft";
  }

  // 收款管理列表导出请求
  rpc AddExportFinancialFundList(AddExportFinancialFundListReq)
      returns (AddExportFinancialFundListRsp) {
    option (api.post) = "/financial/AddExportFinancialFundList";
  }
  // 获取财务操作日志
  rpc GetOperationLog(FinancialOperationLogListReq)
      returns (FinancialOperationLogListRsp) {
    option (api.post) = "/financial/GetOperationLog";
  }

  // 创建支付链接
  rpc PaymentLinkCreate(FinancialPaymentCreateReq)
      returns (FinancialPaymentCreateRsp) {
    option (api.post) = "/financial/PaymentLinkCreate";
  }

  // 线上支付下单
  rpc PaymentOrderCreate(PaymentOrderCreateReq)
      returns (PaymentOrderCreateRsp) {
    option (api.post) = "/financial/PaymentOrderCreate";
  }

  // 获取线上支付信息
  rpc PaymentOrderInfo(FinancialPaymentInfoReq)
      returns (FinancialPaymentInfoRsp) {
    option (api.post) = "/financial/PaymentOrderInfo";
  }

  // 支付宝回调
  rpc AlipayWebhook(AlipayWebhookReq) returns (AlipayWebhookRsp) {
    option (api.post) = "/financial/AlipayWebhook/:id";
  }

  // 微信回调
  rpc WechatWebhook(WechatWebhookReq) returns (WechatWebhookRsp) {
    option (api.post) = "/financial/WechatWebhook/:id";
  }

  // 创建第三方申请费
  rpc ThirdFundCreate(ThirdFundCreateReq) returns (ThirdFundCreateRsp) {
    option (api.post) = "/financial/ThirdFundCreate";
  }

  // 获取关联收款汇率
  rpc GetRelationExchangeRate(RelationExchangeRateReq)
      returns (RelationExchangeRateRsp) {
    option (api.post) = "/financial/GetRelationExchangeRate";
  }

  // 获取工单关联收款单
  rpc GetWorkflowFund(WorkflowFundReq) returns (WorkflowFundRsp) {
    option (api.post) = "/financial/GetWorkflowFund";
  }

  // 支款管理列表导出请求
  rpc AddExportFinancialRefundList(AddExportFinancialRefundListReq)
      returns (AddExportFinancialRefundListRsp) {
    option (api.post) = "/financial/AddExportFinancialRefundList";
  }

  // 编辑已审核通过的收款信息
  rpc EditApprovedFinancialFund(FinancialFundApprovedEditReq)
      returns (FinancialFundApprovedEditRsp) {
    option (api.post) = "/financial/EditApprovedFinancialFund";
  }

  //----------------------财务相关接口 end-----------------------------------
}

service Customer {
  // 获取教育信息
  rpc GetEducationInfo(GetEducationInfoReq) returns (GetEducationInfoRsp) {
    option (api.post) = "/customer/GetEducationInfo";
  }
  // 获取教育经历
  rpc GetEducationExperience(GetEducationExperienceReq)
      returns (GetEducationExperienceRsp) {
    option (api.post) = "/customer/GetEducationExperience";
  }
  // 获取客户扩展信息
  rpc GetExtendedInfo(GetExtendedInfoReq) returns (GetExtendedInfoRsp) {
    option (api.post) = "/customer/GetExtendedInfo";
  }
  // 获取获奖经历
  rpc GetAwardExperience(GetAwardExperienceReq)
      returns (GetAwardExperienceRsp) {
    option (api.post) = "/customer/GetAwardExperience";
  }
  // 获取工作实习经历
  rpc GetWorkInternshipExperience(GetWorkInternshipExperienceReq)
      returns (GetWorkInternshipExperienceRsp) {
    option (api.post) = "/customer/GetWorkInternshipExperience";
  }
  // 获取推荐人信息
  rpc GetReferrerInformation(GetReferrerInformationReq)
      returns (GetReferrerInformationRsp) {
    option (api.post) = "/customer/GetReferrerInformation";
  }
  // 更新教育经历
  rpc UpdateEducationExperience(UpdateEducationExperienceReq)
      returns (UpdateEducationExperienceRsp) {
    option (api.post) = "/customer/UpdateEducationExperience";
  }
  // 更新学生诉求
  rpc UpdateStudentAppeal(UpdateStudentAppealReq)
      returns (UpdateStudentAppealRsp) {
    option (api.post) = "/customer/UpdateStudentAppeal";
  }
  // 更新语言成绩
  rpc UpdateLanguageAchievement(UpdateLanguageAchievementReq)
      returns (UpdateLanguageAchievementRsp) {
    option (api.post) = "/customer/UpdateLanguageAchievement";
  }
  // 更新获奖经历
  rpc UpdateAwardExperience(UpdateAwardExperienceReq)
      returns (UpdateAwardExperienceRsp) {
    option (api.post) = "/customer/UpdateAwardExperience";
  }
  // 更新工作实习经历
  rpc UpdateWorkInternshipExperience(UpdateWorkInternshipExperienceReq)
      returns (UpdateWorkInternshipExperienceRsp) {
    option (api.post) = "/customer/UpdateWorkInternshipExperience";
  }
  // 更新推荐人信息
  rpc UpdateReferrerInformation(UpdateReferrerInformationReq)
      returns (UpdateReferrerInformationRsp) {
    option (api.post) = "/customer/UpdateReferrerInformation";
  }
  // 更新学生信息
  rpc UpdateStudentInfo(UpdateStudentInfoReq) returns (UpdateStudentInfoRsp) {
    option (api.post) = "/customer/UpdateStudentInfo";
  }
  // 更新内部产出文件
  rpc UpdateInternalOutputDocument(UpdateInternalOutputDocumentReq)
      returns (UpdateInternalOutputDocumentRsp) {
    option (api.post) = "/customer/UpdateInternalOutputDocument";
  }
  // 更新结果文件
  rpc UpdateResultDocument(UpdateResultDocumentReq)
      returns (UpdateResultDocumentRsp) {
    option (api.post) = "/customer/UpdateResultDocument";
  }
  // 获取所有国家号
  rpc GetAllCountryCodes(GetAllCountryCodesReq)
      returns (GetAllCountryCodesRsp) {
    option (api.post) = "/customer/GetAllCountryCodes";
  }
  // 获取客户选项列表
  rpc ListCustomerOptions(ListCustomerOptionsReq)
      returns (ListCustomerOptionsRsp) {
    option (api.post) = "/customer/ListCustomerOptions";
  }
  // 获取客户列表
  rpc ListCustomers(ListCustomersReq) returns (ListCustomersRsp) {
    option (api.post) = "/customer/ListCustomers";
  }
  // 获取客户详情
  rpc GetCustomerDetail(GetCustomerDetailReq) returns (GetCustomerDetailRsp) {
    option (api.post) = "/customer/GetCustomerDetail";
  }
  // 创建客户
  rpc CreateCustomer(CreateCustomerReq) returns (CreateCustomerRsp) {
    option (api.post) = "/customer/CreateCustomer";
  }
  // 更新客户
  rpc UpdateCustomer(UpdateCustomerReq) returns (UpdateCustomerRsp) {
    option (api.post) = "/customer/UpdateCustomer";
  }
  // 手机验证通过
  rpc VerifyPhone(VerifyPhoneReq) returns (EmptyRsp) {
    option (api.post) = "/customer/VerifyPhone";
  }
  // 邮箱验证通过
  rpc VerifyEmail(VerifyEmailReq) returns (EmptyRsp) {
    option (api.post) = "/customer/VerifyEmail";
  }
  // 获取手机验证码
  rpc GetPhoneVerifyCode(GetPhoneVerifyCodeReq) returns (EmptyRsp) {
    option (api.post) = "/customer/GetPhoneVerifyCode";
  }
  // 获取邮箱验证码
  rpc GetEmailVerifyCode(GetEmailVerifyCodeReq) returns (EmptyRsp) {
    option (api.post) = "/customer/GetEmailVerifyCode";
  }
  // 更新密码
  rpc UpdatePassword(UpdatePasswordReq) returns (UpdatePasswordRsp) {
    option (api.post) = "/customer/UpdatePassword";
  }
  // 标签列表
  rpc TagList(TagListReq) returns (TagListRsp) {
    option (api.post) = "/customer/TagList";
  }
  // 所有标签
  rpc TagAll(TagAllReq) returns (TagAllRsp) {
    option (api.post) = "/customer/TagAll";
  }
  // 创建标签
  rpc TagCreate(TagCreateReq) returns (TagCreateRsp) {
    option (api.post) = "/customer/TagCreate";
  }
  // 删除标签
  rpc TagDelete(TagDeleteReq) returns (EmptyRsp) {
    option (api.post) = "/customer/TagDelete";
  }
  // 更新标签
  rpc TagUpdate(TagUpdateReq) returns (TagUpdateRsp) {
    option (api.post) = "/customer/TagUpdate";
  }
  // 批量获取tag
  rpc TagBatchGet(TagBatchGetReq) returns (TagBatchGetRsp) {
    option (api.post) = "/customer/TagBatchGet";
  }
  // 查询手机号是否已注册
  rpc CheckPhoneRegistered(CheckPhoneRegisteredReq)
      returns (CheckPhoneRegisteredRsp) {
    option (api.post) = "/customer/CheckPhoneRegistered";
  }
  // 查询邮箱是否已注册
  rpc CheckEmailRegistered(CheckEmailRegisteredReq)
      returns (CheckEmailRegisteredRsp) {
    option (api.post) = "/customer/CheckEmailRegistered";
  }
  // 客户订单列表
  rpc CustomerOrderList(CustomerOrderListReq) returns (CustomerOrderListRsp) {
    option (api.post) = "/customer/CustomerOrderList";
  }
  // 客户的财务列表
  rpc CustomerFinancialList(CustomerFinancialInfoListReq)
      returns (CustomerFinancialInfoListRsp) {
    option (api.post) = "/customer/CustomerFinancialList";
  }
  // 客户的财务支款列表
  rpc CustomerFinancialExpenseList(CustomerFinancialExpenseListReq)
      returns (CustomerFinancialExpenseListRsp) {
    option (api.post) = "/customer/FinancialExpenseList";
  }
  // 客户的财务收款列表
  rpc CustomerFinancialReceiptList(CustomerFinancialReceiptListReq)
      returns (CustomerFinancialReceiptListRsp) {
    option (api.post) = "/customer/FinancialReceiptList";
  }
  // 客户工单列表
  rpc CustomerWorkOrderList(CustomerWorkOrderListReq)
      returns (CustomerWorkOrderListRsp) {
    option (api.post) = "/customer/CustomerWorkOrderList";
  }
  // 客户关联群聊
  rpc CustomerGroupChatList(CustomerGroupChatListReq)
      returns (CustomerGroupChatListRsp) {
    option (api.post) = "/customer/CustomerGroupChatList";
  }
  // 客户操作日志列表
  rpc CustomerOperationLogList(CustomerOperationLogListReq)
      returns (CustomerOperationLogListRsp) {
    option (api.post) = "/customer/CustomerOperationLogList";
  }
  // 更新标签状态
  rpc TagUpdateStatus(TagUpdateStatusReq) returns (TagUpdateStatusRsp) {
    option (api.post) = "/customer/TagUpdateStatus";
  }
  // 获取手机号明文
  rpc GetPhonePlainText(GetPhonePlainTextReq) returns (GetPhonePlainTextRsp) {
    option (api.post) = "/customer/GetPhonePlainText";
  }
  // 获取邮箱明文
  rpc GetEmailPlainText(GetEmailPlainTextReq) returns (GetEmailPlainTextRsp) {
    option (api.post) = "/customer/GetEmailPlainText";
  }
  // 修改跟进员工
  rpc UpdateCustomerFollowEmployee(UpdateCustomerFollowEmployeeReq)
      returns (UpdateCustomerFollowEmployeeRsp) {
    option (api.post) = "/customer/UpdateCustomerFollowEmployee";
  }
  // 交接资产
  rpc TransferAsset(TransferAssetReq) returns (EmptyRsp) {
    option (api.post) = "/customer/TransferAsset";
  }
  // 根据id列表获取客户列表
  rpc GetCustomersByIds(GetCustomersByIdsReq) returns (GetCustomersByIdsRsp) {
    option (api.post) = "/customer/GetCustomersByIds";
  }
  // 获取员工可跟进的客户列表
  rpc GetEmployeeFollowCustomerList(GetEmployeeFollowCustomerListReq)
      returns (GetEmployeeFollowCustomerListRsp) {
    option (api.post) = "/customer/GetEmployeeFollowCustomerList";
  }
  // 获取客户绑定列表
  rpc GetBindCustomers(GetBindCustomersReq) returns (GetBindCustomersRsp) {
    option (api.post) = "/customer/GetBindCustomers";
  }

  // 客户管理列表导出请求
  rpc AddExportCustomersList(AddExportCustomersListReq)
      returns (AddExportCustomersListRsp) {
    option (api.post) = "/customer/AddExportCustomersList";
  }
  // 获取客户标签信息
  rpc GetCustomerTag(GetCustomerTagReq) returns (GetCustomerTagRsp) {
    option (api.post) = "/customer/GetCustomerTag";
  }
  // 更新客户文件
  rpc UpdateCustomerFile(UpdateCustomerFileReq)
      returns (UpdateCustomerFileRsp) {
    option (api.post) = "/customer/UpdateCustomerFile";
  }
  // 获取客户跟进员工列表
  rpc GetCustomerFollowEmployeeList(GetCustomerFollowEmployeeListReq)
      returns (GetCustomerFollowEmployeeListRsp) {
    option (api.post) = "/customer/GetCustomerFollowEmployeeList";
  }
  // 校验新老密码是否一致
  rpc CheckCustomerNewPasswordAndOldPasswordIsSame(
      CheckCustomerNewPasswordAndOldPasswordIsSameReq)
      returns (CheckCustomerNewPasswordAndOldPasswordIsSameRsp) {
    option (api.post) =
        "/customer/CheckCustomerNewPasswordAndOldPasswordIsSame";
  }
  // 获取大学列表
  rpc ListUniversity(ListUniversityReq) returns (ListUniversityRsp) {
    option (api.post) = "/customer/ListUniversity";
  }
  // 更新客户状态
  rpc CustomerUpdateStatus(CustomerUpdateStatusReq)
      returns (CustomerUpdateStatusRsp) {
    option (api.post) = "/customer/CustomerUpdateStatus";
  }

  // 创建邮箱账号
  rpc CreateCustomerProvisionedEmail(CreateCustomerProvisionedEmailReq)
      returns (CreateCustomerProvisionedEmailRsp) {
    option (api.post) = "/customer/CreateCustomerProvisionedEmail";
  }

  // 更新邮箱账号
  rpc UpdateCustomerProvisionedEmail(UpdateCustomerProvisionedEmailReq)
      returns (UpdateCustomerProvisionedEmailRsp) {
    option (api.post) = "/customer/UpdateCustomerProvisionedEmail";
  }
  // 删除邮箱账号
  rpc DeleteCustomerProvisionedEmail(DeleteCustomerProvisionedEmailReq)
      returns (DeleteCustomerProvisionedEmailRsp) {
    option (api.post) = "/customer/DeleteCustomerProvisionedEmail";
  }
  // 获取客户邮箱账号列表
  rpc ListCustomerProvisionedEmails(ListCustomerProvisionedEmailsReq)
      returns (ListCustomerProvisionedEmailsRsp) {
    option (api.post) = "/customer/ListCustomerProvisionedEmails";
  }

  // 批量更新邮箱账号
  rpc BatchUpdateCustomerProvisionedEmails(
      BatchUpdateCustomerProvisionedEmailsReq)
      returns (BatchUpdateCustomerProvisionedEmailsRsp) {
    option (api.post) = "/customer/BatchUpdateCustomerProvisionedEmails";
  }

  // 保存客户备注
  rpc SaveCustomerRemark(SaveCustomerRemarkReq)
      returns (SaveCustomerRemarkRsp) {
    option (api.post) = "/customer/SaveCustomerRemark";
  }
}

service Brand {

  // 创建集团
  rpc CreateDomain(CreateDomainReq) returns (CreateDomainRsp) {
    option (api.post) = "/domain/CreateDomain";
  }

  // 查询所有的集团
  rpc ListDomain(ListDomainReq) returns (ListDomainRsp) {
    option (api.post) = "/domain/ListDomain";
  }

  // 创建品牌
  rpc CreateBrand(CreateBrandReq) returns (CreateBrandRsp) {
    option (api.post) = "/brand/CreateBrand";
  }
  // 编辑品牌
  rpc UpdateBrand(UpdateBrandReq) returns (UpdateBrandRsp) {
    option (api.post) = "/brand/UpdateBrand";
  }
  // 删除品牌
  rpc DeleteBrand(DeleteBrandReq) returns (DeleteBrandRsp) {
    option (api.post) = "/brand/DeleteBrand";
  }

  // 查询该集团下是否存在同名的品牌
  rpc ExistBrandNameByDomain(ExistBrandNameByDomainReq)
      returns (ExistBrandNameByDomainRsp) {
    option (api.post) = "/brand/ExistBrandNameByDomain";
  }

  // 查询该集团下的所有品牌
  rpc ListBrandByDomain(ListBrandByDomainReq) returns (ListBrandByDomainRsp) {
    option (api.post) = "/brand/ListBrandByDomain";
  }
  // 搜索品牌
  rpc ListBrandInfoBySearch(ListBrandInfoBySearchReq)
      returns (ListBrandInfoBySearchRsp) {
    option (api.post) = "/brand/ListBrandInfoBySearch";
  }
  // 精准搜索品牌
  rpc GetBrandInfoByName(GetBrandInfoByNameReq)
      returns (GetBrandInfoByNameRsp) {
    option (api.post) = "/brand/GetBrandInfoByName";
  }
  // 查询所有集团对应品牌的树状结构
  rpc ListAllBrandInDomainTree(ListAllBrandInDomainTreeReq)
      returns (ListAllBrandInDomainTreeRsp) {
    option (api.post) = "/brand/ListAllBrandInDomainTree";
  }
}

// 授权
service Auth {
  // 登陆
  rpc AuthLogin(AuthLoginReq) returns (AuthLoginRsp) {
    option (api.post) = "/auth/AuthLogin";
  }
  // 登出
  rpc AuthLogout(AuthLogoutReq) returns (AuthLogoutRsp) {
    option (api.post) = "/auth/AuthLogout";
  }
  // 获取token包含的基础
  rpc AuthInfo(AuthInfoReq) returns (AuthInfoRsp) {
    option (api.post) = "/auth/AuthInfo";
  }
}

// 工具
service Tools {
  // 获取oss文件上传临时token
  rpc GetStsToken(GetStsTokenReq) returns (GetStsTokenRsp) {
    option (api.post) = "/tools/GetStsToken";
  }
  // 搜索邮件黑名单
  rpc SearchEmailBlacklist(SearchEmailBlacklistReq)
      returns (SearchEmailBlacklistRsp) {
    option (api.post) = "/tools/SearchEmailBlacklist";
  }
  // 添加邮件黑名单
  rpc AddEmailBlacklist(AddEmailBlacklistReq) returns (EmptyRsp) {
    option (api.post) = "/tools/AddEmailBlacklist";
  }
  // 删除邮件黑名单
  rpc DeleteEmailBlacklist(DeleteEmailBlacklistReq) returns (EmptyRsp) {
    option (api.post) = "/tools/DeleteEmailBlacklist";
  }
  // 申请配置
  rpc GetApplyConfig(GetApplyConfigReq) returns (GetApplyConfigRsp) {
    option (api.post) = "/tools/GetApplyConfig";
  }
  // 获取export_file_task表数据列表
  rpc GetExportFileTaskList(GetExportFileTaskListReq)
      returns (GetExportFileTaskListRsp) {
    option (api.post) = "/tools/GetExportFileTaskList";
  }
  // 获取import_file_tasks表数据列表
  rpc GetImportFileTaskList(GetImportFileTaskListReq)
      returns (GetImportFileTaskListRsp) {
    option (api.post) = "/tools/GetImportFileTaskList";
  }
  // 获取import_file_tasks表,任务状态
  rpc GetImportFileTaskStatus(GetImportFileTaskStatusReq)
      returns (GetImportFileTaskStatusRsp) {
    option (api.post) = "/tools/GetImportFileTaskStatus";
  }
  // 查询所有的版本信息
  rpc ListAdminVersion(ListAdminVersionReq) returns (ListAdminVersionRsp) {
    option (api.post) = "/tools/ListAdminVersion";
  }
  // 查询单个的版本记录
  rpc GetAdminVersion(GetAdminVersionReq) returns (GetAdminVersionRsp) {
    option (api.post) = "/tools/GetAdminVersion";
  }
  // 查询当前的版本记录
  rpc GetLastAdminVersion(GetLastAdminVersionReq)
      returns (GetLastAdminVersionRsp) {
    option (api.post) = "/tools/GetLastAdminVersion";
  }
  // 修改版本信息
  rpc UpdateAdminVersion(UpdateAdminVersionReq)
      returns (UpdateAdminVersionRsp) {
    option (api.post) = "/tools/UpdateAdminVersion";
  }
  // 添加版本信息
  rpc AddAdminVersion(AddAdminVersionReq) returns (AddAdminVersionRsp) {
    option (api.post) = "/tools/AddAdminVersion";
  }
  // 删除版本信息
  rpc DelAdminVersion(DelAdminVersionReq) returns (DelAdminVersionRsp) {
    option (api.post) = "/tools/DelAdminVersion";
  }
  // 查询帮助中心配置
  rpc ListHelpConfig(ListHelpConfigReq) returns (ListHelpConfigRsp) {
    option (api.post) = "/tools/ListHelpConfig";
  }
  // 添加帮助中心配置
  rpc AddHelpConfig(AddHelpConfigReq) returns (AddHelpConfigRsp) {
    option (api.post) = "/tools/AddHelpConfig";
  }
  // 编辑帮助中心配置
  rpc UpdateHelpConfig(UpdateHelpConfigReq) returns (UpdateHelpConfigRsp) {
    option (api.post) = "/tools/UpdateHelpConfig";
  }
  // 删除帮助中心配置
  rpc DelHelpConfig(DelHelpConfigReq) returns (DelHelpConfigRsp) {
    option (api.post) = "/tools/DelHelpConfig";
  }
  // 查询详情帮助中心配置
  rpc GetHelpConfig(GetHelpConfigReq) returns (GetHelpConfigRsp) {
    option (api.post) = "/tools/GetHelpConfig";
  }
}

// 消息服务
service Message {
  // 获取IM配置
  rpc GetImSetting(GetImSettingReq) returns (GetImSettingRsp) {
    option (api.post) = "/message/GetImSetting";
  }
  // 获取IM用户类型
  rpc GetImUserType(GetImUserTypeReq) returns (GetImUserTypeRsp) {
    option (api.post) = "/message/GetImUserType";
  }
  // 创建群聊会话
  rpc CreateGroupChat(CreateGroupChatReq) returns (CreateGroupChatRsp) {
    option (api.post) = "/message/CreateGroupChat";
  }
  // 获取群聊会话列表
  rpc ListGroupChats(ListGroupChatsReq) returns (ListGroupChatsRsp) {
    option (api.post) = "/message/ListGroupChats";
  }
  // 获取群聊消息列表
  rpc ListGroupMessage(ListGroupMessageReq) returns (ListGroupMessageRsp) {
    option (api.post) = "/message/ListGroupMessage";
  }
  // 解散群
  rpc DismissGroupChat(DismissGroupChatReq) returns (DismissGroupChatRsp) {
    option (api.post) = "/message/DismissGroupChat";
  }
  // 退群
  rpc QuitGroupChat(QuitGroupChatReq) returns (QuitGroupChatRsp) {
    option (api.post) = "/message/QuitGroupChat";
  }
  // 转让群组
  rpc TransferGroupChat(TransferGroupChatReq) returns (TransferGroupChatRsp) {
    option (api.post) = "/message/TransferGroupChat";
  }
  // 获取群详情
  rpc GroupChatDetail(GroupChatDetailReq) returns (GroupChatDetailRsp) {
    option (api.post) = "/message/GroupChatDetail";
  }
  // 修改群名称
  rpc ModifyGroupChatName(ModifyGroupChatNameReq)
      returns (ModifyGroupChatNameRsp) {
    option (api.post) = "/message/ModifyGroupChatName";
  }
  // 修改群公告
  rpc ModifyGroupChatNotice(ModifyGroupChatNoticeReq)
      returns (ModifyGroupChatNoticeRsp) {
    option (api.post) = "/message/ModifyGroupChatNotice";
  }
  // 修改群成员名片
  rpc ModifyGroupChatMemberNickname(ModifyGroupChatMemberNicknameReq)
      returns (ModifyGroupChatMemberNicknameRsp) {
    option (api.post) = "/message/ModifyGroupChatMemberNickname";
  }
  // 批量添加群成员
  rpc AddGroupMembers(AddGroupMembersReq) returns (AddGroupMembersRsp) {
    option (api.post) = "/message/AddGroupChatMembers";
  }
  // 批量删除群成员
  rpc RemoveGroupMembers(RemoveGroupMembersReq)
      returns (RemoveGroupMembersRsp) {
    option (api.post) = "/message/DeleteGroupChatMembers";
  }
  // 通过客户id获取相关群聊
  rpc GetGroupChatByCustomerId(GetGroupChatByCustomerIdReq)
      returns (GetGroupChatByCustomerIdRsp) {
    option (api.post) = "/message/GetGroupChatByCustomerId";
  }
  // 工单号绑定群聊
  rpc BindWorkflowNoToGroupChat(BindWorkflowNoToGroupChatReq)
      returns (BindWorkflowNoToGroupChatRsp) {
    option (api.post) = "/message/BindWorkflowNoToGroupChat";
  }
  // Im回调
  rpc ImCallBack(ImCallBackReq) returns (ImCallBackRsp) {
    option (api.post) = "/message/ImCallBack";
  }
  // 是否有群聊权限
  rpc HasGroupChatPermission(HasGroupChatPermissionReq)
      returns (HasGroupChatPermissionRsp) {
    option (api.post) = "/message/HasGroupChatPermission";
  }
  // 获取消息通知设置
  rpc GetNotifySettings(GetNotifySettingsReq) returns (GetNotifySettingsRsp) {
    option (api.post) = "/message/GetNotifySettings";
  }
  // 编辑消息通知设置
  rpc UpdateNotifySettings(UpdateNotifySettingsReq)
      returns (UpdateNotifySettingsRsp) {
    option (api.post) = "/message/UpdateNotifySettings";
  }
  // 获取获取会话标题
  rpc GetImChatTitle(GetImChatTitleReq) returns (GetImChatTitleRsp) {
    option (api.post) = "/message/GetImChatTitle";
  }
  // 当前用户是否在群聊中
  rpc IsInGroupChat(IsInGroupChatReq) returns (IsInGroupChatRsp) {
    option (api.post) = "/message/IsInGroupChat";
  }
  // 根据群聊id获取其工单的详情
  rpc GetWorkFlowDetails(GetWorkFlowDetailsReq)
      returns (GetWorkFlowDetailsRsp) {
    option (api.post) = "/message/GetWorkFlowDetails";
  }
  rpc GetUserImInfo(GetUserImInfoReq) returns (GetUserImInfoRsp) {
    option (api.post) = "/message/GetUserImInfo";
  }
  // 获取群公告详情
  rpc GetGroupChatNotice(GetGroupChatNoticeReq)
      returns (GetGroupChatNoticeRsp) {
    option (api.post) = "/message/GetGroupChatNotice";
  }
}

service MessageBoard {
  // 获取置顶留言板列表
  rpc GetTopMessageBoardList(GetMessageBoardListReq)
      returns (GetMessageBoardListRsp) {
    option (api.post) = "message/GetTopMessageBoardList";
  }

  // 获取非置顶留言板列表
  rpc GetMessageBoardList(GetMessageBoardListReq)
      returns (GetMessageBoardListRsp) {
    option (api.post) = "message/GetMessageBoardList";
  }

  // 留言板消息列表
  rpc GetMessageBoardMessage(GetMessageBoardMessageReq)
      returns (GetMessageBoardMessageRsp) {
    option (api.post) = "message/GetMessageBoardMessage";
  }

  // 发布留言
  rpc AddMessageBoardMessage(AddMessageBoardMessageReq)
      returns (AddMessageBoardMessageRsp) {
    option (api.post) = "message/AddMessageBoardMessage";
  }

  // 删除留言
  rpc DeleteMessageBoardMessage(DeleteMessageBoardMessageReq)
      returns (DeleteMessageBoardMessageRsp) {
    option (api.post) = "message/DeleteMessageBoardMessage";
  }

  // 顶置留言板
  rpc TopMessageBoard(TopMessageBoardReq) returns (TopMessageBoardRsp) {
    option (api.post) = "message/TopMessageBoard";
  }

  // 标记已读
  rpc MarkMessageBoardRead(MarkMessageBoardReadReq)
      returns (MarkMessageBoardReadRsp) {
    option (api.post) = "message/MarkMessageBoardRead";
  }

  // 获取未读消息数量
  rpc GetMessageBoardUnreadCount(GetMessageBoardUnreadCountReq)
      returns (GetMessageBoardUnreadCountRsp) {
    option (api.post) = "message/GetMessageBoardUnreadCount";
  }

  // 获取留言板跟进人列表
  rpc GetMessageBoardFollowStaffList(GetMessageBoardFollowStaffListReq)
      returns (GetMessageBoardFollowStaffListRsp) {
    option (api.post) = "message/GetMessageBoardFollowStaffList";
  }

  // 获取工单详情
  rpc GetMessageBoardWorkflowInfo(GetMessageBoardWorkflowInfoReq)
      returns (GetMessageBoardWorkflowInfoRsp) {
    option (api.post) = "message/GetMessageBoardWorkflowInfo";
  }

  // 获取订单详情
  rpc GetMessageBoardOrderInfo(GetMessageBoardOrderInfoReq)
      returns (GetMessageBoardOrderInfoRsp) {
    option (api.post) = "message/GetMessageBoardOrderInfo";
  }

  // 保存文件到客户模块
  rpc SaveFileToCustomer(SaveFileToCustomerReq)
      returns (SaveFileToCustomerRsp) {
    option (api.post) = "message/SaveFileToCustomer";
  }

  // 保存文件到工单模块
  rpc SaveFileToWorkflow(SaveFileToWorkflowReq)
      returns (SaveFileToWorkflowRsp) {
    option (api.post) = "message/SaveFileToWorkflow";
  }

  // 根据关联id获取留言板列表
  rpc GetMessageBoardByRelationId(GetMessageBoardByRelationIdReq)
      returns (GetMessageBoardByRelationIdRsp) {
    option (api.post) = "message/GetMessageBoardByRelationId";
  }

  // 获取用户留言板列表
  rpc GetCustomerMessageBoardList(GetCustomerMessageBoardListReq)
      returns (GetCustomerMessageBoardListRsp) {
    option (api.post) = "message/GetCustomerMessageBoardList";
  }

  // 添加留言板跟进人
  rpc AddMessageBoardFollowStaff(AddMessageBoardFollowStaffReq)
      returns (AddMessageBoardFollowStaffRsp) {
    option (api.post) = "message/AddMessageBoardFollowStaff";
  }

  // 删除留言板跟进人
  rpc DeleteMessageBoardFollowStaff(DeleteMessageBoardFollowStaffReq)
      returns (DeleteMessageBoardFollowStaffRsp) {
    option (api.post) = "message/DeleteMessageBoardFollowStaff";
  }
}

// 工单相关接口
service Workflow {
  // 获取工单模板id与业务之间的映射关系
  rpc GetWorkflowTemplateAndBusiness(GetWorkflowTemplateAndBusinessReq)
      returns (GetWorkflowTemplateAndBusinessRsp) {
    option (api.post) = "/workflow/GetWorkflowTemplateAndBusiness";
  }

  // 获取工单模板详情
  rpc WorkflowTemplateDetail(WorkflowTemplateDetailReq)
      returns (WorkflowTemplateDetailRsp) {
    option (api.post) = "/workflow/WorkflowTemplateDetail";
  }

  // 订单创建工单
  rpc CreateWorkflowByOrder(CreateWorkflowByOrderReq)
      returns (CreateWorkflowByOrderRsp) {
    option (api.post) = "/workflow/CreateWorkflowByOrder";
  }

  // Deprecated: 获取工单详情,请使用WorkflowDetail
  // ID或编号任选其一，如都存在则ID优先
  rpc GetWorkflowDetail(GetWorkflowDetailReq) returns (GetWorkflowDetailRsp) {
    option (api.post) = "/workflow/GetWorkflowDetail";
  }

  // 获取工单列表-工单视图(有权限)
  rpc ListWorkflow(ListWorkflowReq) returns (ListWorkflowRsp) {
    option (api.post) = "/workflow/ListWorkflow";
  }

  // 获取工单列表-任务视图
  rpc ListWorkflowTaskView(ListWorkflowTaskViewReq)
      returns (ListWorkflowTaskViewRsp) {
    option (api.post) = "/workflow/ListWorkflowTaskView";
  }

  // 获取工单列表筛工单视图选项 - 派单人列表
  rpc ListWorkflowDispatcher(ListWorkflowDispatcherReq)
      returns (ListWorkflowDispatcherRsp) {
    option (api.post) = "/workflow/ListWorkflowDispatcher";
  }

  // 获取工单列表筛工单视图&任务视图选项 - 处理人列表
  rpc ListWorkflowProcessor(ListWorkflowProcessorReq)
      returns (ListWorkflowProcessorRsp) {
    option (api.post) = "/workflow/ListWorkflowProcessor";
  }

  // 获取工单列表任务视图筛选项 - 转案人列表
  rpc ListWorkflowNodeDispatcher(ListWorkflowNodeDispatcherReq)
      returns (ListWorkflowNodeDispatcherRsp) {
    option (api.post) = "/workflow/ListWorkflowNodeDispatcher";
  }

  // Deprecated: 工单审核：合同与收款 获取工单对应的订单+商品+收款信息
  rpc GetWorkflowOrderGoodsPayment(GetWorkflowOrderGoodsPaymentReq)
      returns (GetWorkflowOrderGoodsPaymentRsp) {
    option (api.post) = "/workflow/GetWorkflowOrderGoodsPayment";
  }

  // 工单审核：拒绝派单
  rpc RejectWorkflowDispatch(RejectWorkflowDispatchReq)
      returns (RejectWorkflowDispatchRsp) {
    option (api.post) = "/workflow/RejectWorkflowDispatch";
  }

  // 工单审核：接收派单
  rpc AcceptWorkflowDispatch(AcceptWorkflowDispatchReq)
      returns (AcceptWorkflowDispatchRsp) {
    option (api.post) = "/workflow/AcceptWorkflowDispatch";
  }

  // 工单节点：获取工单跟进情况
  rpc GetWorkflowFollow(GetWorkflowFollowReq) returns (GetWorkflowFollowRsp) {
    option (api.post) = "/workflow/GetWorkflowFollow";
  }

  // 工单节点：更新工单跟进情况
  rpc UpdateWorkflowFollow(UpdateWorkflowFollowReq)
      returns (UpdateWorkflowFollowRsp) {
    option (api.post) = "/workflow/UpdateWorkflowFollow";
  }

  // 工单节点：获取与更新任务状态(总是返回这个节点下的所有任务)
  rpc UpdateWorkflowNodeTaskStatus(UpdateWorkflowNodeTaskStatusReq)
      returns (UpdateWorkflowNodeTaskStatusRsp) {
    option (api.post) = "/workflow/UpdateWorkflowNodeTaskStatus";
  }

  // 工单节点：操作转案-指定处理人
  rpc UpdateWorkflowNodeProcessor(UpdateWorkflowNodeProcessorReq)
      returns (UpdateWorkflowNodeProcessorRsp) {
    option (api.post) = "/workflow/UpdateWorkflowNodeProcessor";
  }

  // 工单节点: 完成节点
  rpc FinishWorkflowNode(FinishWorkflowNodeReq)
      returns (FinishWorkflowNodeRsp) {
    option (api.post) = "/workflow/FinishWorkflowNode";
  }

  // 工单节点：终止节点
  rpc TerminateWorkflowNode(TerminateWorkflowNodeReq)
      returns (TerminateWorkflowNodeRsp) {
    option (api.post) = "/workflow/TerminateWorkflowNode";
  }

  // 工单节点：跳过节点
  rpc SkipWorkflowNode(SkipWorkflowNodeReq) returns (SkipWorkflowNodeRsp) {
    option (api.post) = "/workflow/SkipWorkflowNode";
  }

  // 工单节点：暂停
  rpc PauseWorkflowNode(PauseWorkflowNodeReq) returns (PauseWorkflowNodeRsp) {
    option (api.post) = "/workflow/PauseWorkflowNode";
  }

  // 工单节点：搁置(任务视图)
  rpc SuspendWorkflowNode(SuspendWorkflowNodeReq)
      returns (SuspendWorkflowNodeRsp) {
    option (api.post) = "/workflow/SuspendWorkflowNode";
  }

  // 工单：重启
  rpc RestartWorkflowNode(RestartWorkflowNodeReq)
      returns (RestartWorkflowNodeRsp) {
    option (api.post) = "/workflow/RestartWorkflowNode";
  }

  // 工单节点：转单
  rpc TransferWorkflowNode(TransferWorkflowNodeReq)
      returns (TransferWorkflowNodeRsp) {
    option (api.post) = "/workflow/TransferWorkflowNode";
  }

  // 工单节点： 批量转单
  rpc BatchTransferWorkflowNode(BatchTransferWorkflowNodeReq)
      returns (BatchTransferWorkflowNodeRsp) {
    option (api.post) = "/workflow/BatchTransferWorkflowNode";
  }

  // 工单节点：接收
  rpc ReceiveWorkflowNode(ReceiveWorkflowNodeReq)
      returns (ReceiveWorkflowNodeRsp) {
    option (api.post) = "/workflow/ReceiveWorkflowNode";
  }

  // 工单节点(申请类)：更新选校信息
  rpc UpdateWorkflowNodeSchoolInfo(UpdateWorkflowNodeSchoolInfoReq)
      returns (UpdateWorkflowNodeSchoolInfoRsp) {
    option (api.post) = "/workflow/UpdateWorkflowNodeSchoolInfo";
  }

  // 工单节点(申请类)：获取选校信息
  rpc GetWorkflowNodeSchoolInfo(GetWorkflowNodeSchoolInfoReq)
      returns (GetWorkflowNodeSchoolInfoRsp) {
    option (api.post) = "/workflow/GetWorkflowNodeSchoolInfo";
  }

  // 工单节点(申请类)：更新申请文件
  rpc UpdateApplyWorkflowNodeFile(UpdateApplyWorkflowNodeFileReq)
      returns (UpdateApplyWorkflowNodeFileRsp) {
    option (api.post) = "/workflow/UpdateApplyWorkflowNodeFile";
  }

  // 工单节点(申请类)：获取申请文件
  rpc GetApplyWorkflowNodeFile(GetApplyWorkflowNodeFileReq)
      returns (GetApplyWorkflowNodeFileRsp) {
    option (api.post) = "/workflow/GetApplyWorkflowNodeFile";
  }

  // 工单节点(申请类)：更新申请材料包
  rpc UpdateApplyWorkflowNodeMaterialPackage(
      UpdateApplyWorkflowNodeMaterialPackageReq)
      returns (UpdateApplyWorkflowNodeMaterialPackageRsp) {
    option (api.post) = "/workflow/UpdateApplyWorkflowNodeMaterialPackage";
  }

  // 工单节点(申请类)：获取申请材料包
  rpc GetApplyWorkflowNodeMaterialPackage(
      GetApplyWorkflowNodeMaterialPackageReq)
      returns (GetApplyWorkflowNodeMaterialPackageRsp) {
    option (api.post) = "/workflow/GetApplyWorkflowNodeMaterialPackage";
  }

  // 工单节点(申请类)：更新申请信息
  rpc UpdateApplyWorkflowNodeInfo(UpdateApplyWorkflowNodeInfoReq)
      returns (UpdateApplyWorkflowNodeInfoRsp) {
    option (api.post) = "/workflow/UpdateApplyWorkflowNodeInfo";
  }

  // 工单节点(申请类)：获取申请信息
  rpc GetApplyWorkflowNodeInfo(GetApplyWorkflowNodeInfoReq)
      returns (GetApplyWorkflowNodeInfoRsp) {
    option (api.post) = "/workflow/GetApplyWorkflowNodeInfo";
  }

  // 工单节点(申请类)：更新offer信息
  rpc UpdateApplyWorkflowNodeOffer(UpdateApplyWorkflowNodeOfferReq)
      returns (UpdateApplyWorkflowNodeOfferRsp) {
    option (api.post) = "/workflow/UpdateApplyWorkflowNodeOffer";
  }

  // 工单节点(申请类)：获取offer信息
  rpc GetApplyWorkflowNodeOffer(GetApplyWorkflowNodeOfferReq)
      returns (GetApplyWorkflowNodeOfferRsp) {
    option (api.post) = "/workflow/GetApplyWorkflowNodeOffer";
  }

  // 工单节点(申请类)：更新签证信息
  rpc UpdateApplyWorkflowNodeVisa(UpdateApplyWorkflowNodeVisaReq)
      returns (UpdateApplyWorkflowNodeVisaRsp) {
    option (api.post) = "/workflow/UpdateApplyWorkflowNodeVisa";
  }

  // 工单节点(申请类)：获取与更新签证信息
  rpc GetApplyWorkflowNodeVisa(GetApplyWorkflowNodeVisaReq)
      returns (GetApplyWorkflowNodeVisaRsp) {
    option (api.post) = "/workflow/GetApplyWorkflowNodeVisa";
  }

  // 工单节点(申请类)：更新拒信信息
  rpc UpdateApplyWorkflowNodeReject(UpdateApplyWorkflowNodeRejectReq)
      returns (UpdateApplyWorkflowNodeRejectRsp) {
    option (api.post) = "/workflow/UpdateApplyWorkflowNodeReject";
  }

  // 工单节点(申请类)：获取拒信信息
  rpc GetApplyWorkflowNodeReject(GetApplyWorkflowNodeRejectReq)
      returns (GetApplyWorkflowNodeRejectRsp) {
    option (api.post) = "/workflow/GetApplyWorkflowNodeReject";
  }

  // 工单节点(申请类)：获取与更新确认信信息
  rpc UpdateApplyWorkflowNodeConfirm(UpdateApplyWorkflowNodeConfirmReq)
      returns (UpdateApplyWorkflowNodeConfirmRsp) {
    option (api.post) = "/workflow/UpdateApplyWorkflowNodeConfirm";
  }

  // 工单节点(申请类)：获取与更新确认信信息
  rpc GetApplyWorkflowNodeConfirm(GetApplyWorkflowNodeConfirmReq)
      returns (GetApplyWorkflowNodeConfirmRsp) {
    option (api.post) = "/workflow/GetApplyWorkflowNodeConfirm";
  }

  // 工单节点(申请类)：更新择导信息
  rpc UpdateApplyWorkflowNodeChooseGuide(UpdateApplyWorkflowNodeChooseGuideReq)
      returns (UpdateApplyWorkflowNodeChooseGuideRsp) {
    option (api.post) = "/workflow/UpdateApplyWorkflowNodeChooseGuide";
  }
  // 工单节点(申请类)：获取择导信息
  rpc GetApplyWorkflowNodeChooseGuide(GetApplyWorkflowNodeChooseGuideReq)
      returns (GetApplyWorkflowNodeChooseGuideRsp) {
    option (api.post) = "/workflow/GetApplyWorkflowNodeChooseGuide";
  }

  // 工单节点(申请类)：更新套磁信息
  rpc UpdateApplyWorkflowNodeClosenessLetter(
      UpdateApplyWorkflowNodeClosenessLetterReq)
      returns (UpdateApplyWorkflowNodeClosenessLetterRsp) {
    option (api.post) = "/workflow/UpdateApplyWorkflowNodeClosenessLetter";
  }
  // 工单节点(申请类)：获取套磁信息
  rpc GetApplyWorkflowNodeClosenessLetter(
      GetApplyWorkflowNodeClosenessLetterReq)
      returns (GetApplyWorkflowNodeClosenessLetterRsp) {
    option (api.post) = "/workflow/GetApplyWorkflowNodeClosenessLetter";
  }

  // 工单管理任务视图列表导出请求
  rpc AddExportWorkflowTaskList(AddExportWorkflowTaskListReq)
      returns (AddExportWorkflowTaskListRsp) {
    option (api.post) = "/workflow/AddExportWorkflowTaskList";
  }

  // 工单管理列表导出请求
  rpc AddExportWorkflowWorkOrderList(AddExportWorkflowWorkOrderListReq)
      returns (AddExportWorkflowWorkOrderListRsp) {
    option (api.post) = "/workflow/AddExportWorkflowWorkOrderList";
  }

  // 工单服务正常完成
  rpc WorkflowServiceDone(WorkflowServiceDoneReq)
      returns (WorkflowServiceDoneRsp) {
    option (api.post) = "/workflow/WorkflowServiceDone";
  }

  // 工单服务异常完成
  rpc WorkflowServiceAbnormalDone(WorkflowServiceAbnormalDoneReq)
      returns (WorkflowServiceAbnormalDoneRsp) {
    option (api.post) = "/workflow/WorkflowServiceAbnormalDone";
  }

  // 工单节点(单项类)：更新单项需求信息
  rpc UpdateApplyWorkflowNodeSingleRequirement(
      UpdateApplyWorkflowNodeSingleRequirementReq)
      returns (UpdateApplyWorkflowNodeSingleRequirementRsp) {
    option (api.post) = "/workflow/UpdateApplyWorkflowNodeSingleRequirement";
  }

  // 工单节点(单项类)：获取单项需求信息
  rpc GetApplyWorkflowNodeSingleRequirement(
      GetApplyWorkflowNodeSingleRequirementReq)
      returns (GetApplyWorkflowNodeSingleRequirementRsp) {
    option (api.post) = "/workflow/GetApplyWorkflowNodeSingleRequirement";
  }

  // 工单节点(单项类)：更新单项结果文件
  rpc UpdateApplyWorkflowNodeSingleFile(UpdateApplyWorkflowNodeSingleFileReq)
      returns (UpdateApplyWorkflowNodeSingleFileRsp) {
    option (api.post) = "/workflow/UpdateApplyWorkflowNodeSingleFile";
  }

  // 工单节点(单项类)：获取项结果文件
  rpc GetApplyWorkflowNodeSingleFile(GetApplyWorkflowNodeSingleFileReq)
      returns (GetApplyWorkflowNodeSingleFileRsp) {
    option (api.post) = "/workflow/GetApplyWorkflowNodeSingleFile";
  }

  // 更新工单内部状态
  rpc UpdateWorkflowInnerStatus(UpdateWorkflowInnerStatusReq)
      returns (UpdateWorkflowInnerStatusRsp) {
    option (api.post) = "/workflow/UpdateWorkflowInnerStatus";
  }

  // 更新工单名称
  rpc UpdateWorkflowName(UpdateWorkflowNameReq)
      returns (UpdateWorkflowNameRsp) {
    option (api.post) = "/workflow/UpdateWorkflowName";
  }

  // 工单节点：关联群聊
  rpc RelationshipWorkflowGroupChat(RelationshipWorkflowGroupChatReq)
      returns (RelationshipWorkflowGroupChatRsp) {
    option (api.post) = "/workflow/RelationshipWorkflowGroupChat";
  }

  // 工单节点：快速建群
  rpc WorkflowCreateGroupChat(WorkflowCreateGroupChatReq)
      returns (WorkflowCreateGroupChatRsp) {
    option (api.post) = "/workflow/WorkflowCreateGroupChat";
  }

  // 工单节点：解绑群聊
  rpc UnbindWorkflowGroupChat(UnbindWorkflowGroupChatReq)
      returns (UnbindWorkflowGroupChatRsp) {
    option (api.post) = "/workflow/UnbindWorkflowGroupChat";
  }

  // 保存文件(群聊+留言板)
  rpc SaveWorkflowFile(SaveWorkflowFileReq) returns (SaveWorkflowFileRsp) {
    option (api.post) = "/workflow/SaveWorkflowFile";
  }

  // 客户标签上的工单信息流展示
  rpc GetCustomerWorkflowList(GetCustomerWorkflowListReq)
      returns (GetCustomerWorkflowListRsp) {
    option (api.post) = "/workflow/GetCustomerWorkflowList";
  }

  // 客户确认节点推送
  rpc WorkflowConfirmPush(WorkflowConfirmPushReq)
      returns (WorkflowConfirmPushRsp) {
    option (api.post) = "/workflow/WorkflowConfirmPush";
  }

  // 工单操作日志
  rpc WorkflowOperationLog(WorkflowOperationLogReq)
      returns (WorkflowOperationLogRsp) {
    option (api.post) = "/workflow/WorkflowOperationLog";
  }

  // -------------------工单节点(辅导类)------------------
  //  公共 - 需求信息修改
  rpc UpdateWorkflowGuidanceDemandInfo(UpdateWorkflowGuidanceDemandInfoReq)
      returns (UpdateWorkflowGuidanceDemandInfoRsp) {
    option (api.post) = "/workflow/UpdateGuidanceDemandInfo";
  }

  // 论文辅导 2. 师资信息修改
  rpc UpdateWorkflowGuidanceTeacherInfo(UpdateWorkflowGuidanceTeacherInfoReq)
      returns (UpdateWorkflowGuidanceTeacherInfoRsp) {
    option (api.post) = "/workflow/UpdateGuidanceTeacherInfo";
  }

  // 论文辅导||课程辅导 更新课程信息
  rpc UpdateWorkflowGuidanceLessonInfo(UpdateWorkflowGuidanceLessonInfoReq)
      returns (UpdateWorkflowGuidanceLessonInfoRsp) {
    option (api.post) = "/workflow/UpdateGuidanceLessonInfo";
  }

  // 论文辅导-稿件
  rpc UpdateWorkflowGuidanceManuscriptInfo(
      UpdateWorkflowGuidanceManuscriptInfoReq)
      returns (UpdateWorkflowGuidanceManuscriptInfoRsp) {
    option (api.post) = "/workflow/UpdateGuidanceManuscriptInfo";
  }

  // 期刊发表 - 结果文件
  rpc UpdateWorkflowGuidanceResultInfo(UpdateWorkflowGuidanceResultInfoReq)
      returns (UpdateWorkflowGuidanceResultInfoRsp) {
    option (api.post) = "/workflow/UpdateGuidanceResultInfo";
  }

  rpc GetWorkflowGuidanceDemandInfo(GetWorkflowGuidanceDemandInfoReq)
      returns (GetWorkflowGuidanceDemandInfoRsp) {
    option (api.post) = "/workflow/GetGuidanceDemandInfo";
  }

  // 论文辅导 2. 师资信息修改
  rpc GetWorkflowGuidanceTeacherInfo(GetWorkflowGuidanceTeacherInfoReq)
      returns (GetWorkflowGuidanceTeacherInfoRsp) {
    option (api.post) = "/workflow/GetGuidanceTeacherInfo";
  }

  // 论文辅导||课程辅导 更新课程信息
  rpc GetWorkflowGuidanceLessonInfo(GetWorkflowGuidanceLessonInfoReq)
      returns (GetWorkflowGuidanceLessonInfoRsp) {
    option (api.post) = "/workflow/GetGuidanceLessonInfo";
  }

  // 论文辅导-稿件
  rpc GetWorkflowGuidanceManuscriptInfo(GetWorkflowGuidanceManuscriptInfoReq)
      returns (GetWorkflowGuidanceManuscriptInfoRsp) {
    option (api.post) = "/workflow/GetGuidanceManuscriptInfo";
  }

  // 期刊发表 - 结果文件
  rpc GetWorkflowGuidanceResultInfo(GetWorkflowGuidanceResultInfoReq)
      returns (GetWorkflowGuidanceResultInfoRsp) {
    option (api.post) = "/workflow/GetGuidanceResultInfo";
  }

  // 非phd申请 - 添加第三方收费并绑定
  rpc AddFundWithWorkflowApply(AddFundWithWorkflowApplyReq)
      returns (AddFundWithWorkflowApplyRsp) {
    option (api.post) = "/workflow/AddFundWithWorkflowApply";
  }
  // 非phd申请 - 绑定第三方收费单
  rpc BindFundWithWorkflowApply(BindFundWithWorkflowApplyReq)
      returns (BindFundWithWorkflowApplyRsp) {
    option (api.post) = "/workflow/BindFundWithWorkflowApply";
  }
  // 非phd申请 - 免除第三方收
  rpc CancelFundWithWorkflowApply(CancelFundWithWorkflowApplyReq)
      returns (CancelFundWithWorkflowApplyRsp) {
    option (api.post) = "/workflow/CancelFundWithWorkflowApply";
  }
  // 工单节点(非phd申请)：获取fund数据
  rpc GetApplyWorkflowFundInfo(GetApplyWorkflowFundInfoReq)
      returns (WorkflowFundEntity) {
    option (api.post) = "/workflow/GetApplyWorkflowFundInfo";
  }

  // 非phd申请 - 添加第三方收费的金额货币相关信息
  rpc UpdateWorkflowApplyFundCurrencyInfo(
      UpdateWorkflowApplyFundCurrencyInfoReq)
      returns (UpdateWorkflowApplyFundCurrencyInfoRsp) {
    option (api.post) = "/workflow/UpdateWorkflowApplyFundCurrencyInfo";
  }
  // 工单节点(单项类)：更新是否转案到业务处理节点
  rpc UpdateSingleWorkflowDispatch(UpdateSingleWorkflowDispatchReq)
      returns (UpdateSingleWorkflowDispatchRsp) {
    option (api.post) = "/workflow/UpdateSingleWorkflowDispatch";
  }

  // 工单节点(单项类)：获取是否转案到业务处理节点
  rpc GetSingleWorkflowDispatch(GetSingleWorkflowDispatchReq)
      returns (GetSingleWorkflowDispatchRsp) {
    option (api.post) = "/workflow/GetSingleWorkflowDispatch";
  }

  // 根据订单ID获取关联的工单列表信息
  rpc GetWorkflowListByOrderId(GetWorkflowListByOrderIdReq)
      returns (GetWorkflowListByOrderIdRsp) {
    option (api.post) = "/workflow/GetWorkflowListByOrderId";
  }

  // 通过订单ID获取派单信息
  rpc GetAttachmentsByOrderId(GetAttachmentsByOrderIdReq)
      returns (GetAttachmentsByOrderIdRsp) {
    option (api.post) = "/workflow/GetAttachmentsByOrderId";
  }

  // 通过申请类工单获取关联的辅导类工单
  rpc GetGuidanceDetailByApply(GetGuidanceDetailByApplyReq)
      returns (GetGuidanceDetailByApplyRsp) {
    option (api.post) = "/workflow/GetGuidanceDetailByApply";
  }

  // 工单-添加第三方申请费 支款单
  rpc AddFinancialRefundWithWorkflowApply(
      AddFinancialRefundWithWorkflowApplyReq)
      returns (AddFinancialRefundWithWorkflowApplyRsp) {
    option (api.post) = "/workflow/AddFinancialRefundWithWorkflowApply";
  }
  // 通过申请类工单获取关联的辅导类工单
  rpc UpdateFinancialRefundWithWorkflowApply(
      UpdateFinancialRefundWithWorkflowApplyReq)
      returns (UpdateFinancialRefundWithWorkflowApplyRsp) {
    option (api.post) = "/workflow/UpdateFinancialRefundWithWorkflowApply";
  }
  // 通过申请类工单获取关联的辅导类工单
  rpc DeleteFinancialRefundWithWorkflowApply(
      DeleteFinancialRefundWithWorkflowApplyReq)
      returns (DeleteFinancialRefundWithWorkflowApplyRsp) {
    option (api.post) = "/workflow/DeleteFinancialRefundWithWorkflowApply";
  }

  // 工单详情ALL IN ONE
  rpc WorkflowDetails(WorkflowDetailsReq) returns (WorkflowDetailsRsp) {
    option (api.post) = "/workflow/WorkflowDetails";
  }

  // 获取工单列表(无权限)
  rpc ListWorkflowWithoutCtx(ListWorkflowWithoutCtxReq)
      returns (ListWorkflowRsp) {
    option (api.post) = "/workflow/ListWorkflowWithoutCtx";
  }

  // 获取角色列表(派单页面)
  rpc GetAllowRoleList(GetAllowRoleListReq) returns (GetAllowRoleListRsp) {
    option (api.post) = "/workflow/GetAllowRoleList";
  }
}

service Udata {
  rpc AddEmployeeUdataPriorityByEmail(UdataPriorityByEmailReq)
      returns (UdataPriorityByEmailRsp) {
    option (api.post) = "/udata/AddEmployeeUdataPriorityByEmail";
  }

  rpc AddEmployeeUdataPriorityByRole(UdataPriorityByRoleReq)
      returns (UdataPriorityByRoleRsp) {
    option (api.post) = "/udata/AddEmployeeUdataPriorityByRole";
  }

  // 获取用户权限
  rpc GetEmployeeUdataPriority(GetEmployeeUdataPriorityReq)
      returns (GetEmployeeUdataPriorityResp) {
    option (api.post) = "/udata/GetEmployeeUdataPriority";
  }

  // 设置Udata管理员
  rpc SetEmployeeAsUdataManager(SetEmployeeAsUdataManagerReq)
      returns (SetEmployeeAsUdataManagerResp) {
    option (api.post) = "/udata/SetEmployeeAsUdataManager";
  }

  // 个人视图 - 销售数据
  rpc GetSalesData(PersonalDataReq) returns (EmployeeSaleRsp) {
    option (api.post) = "/udata/GetSalesData";
  }
  rpc GetGroupSalesData(PersonalDataReq) returns (GroupSalesDataRsp) {
    option (api.post) = "/udata/GetGroupSalesData";
  }
  rpc GetWorkflowData(PersonalDataReq) returns (EmployeeWorkflowRsp) {
    option (api.post) = "/udata/GetWorkflowData";
  }
  rpc GetGroupWorkflowData(PersonalDataReq) returns (GroupWorkflowDataRsp) {
    option (api.post) = "/udata/GetGroupWorkflowData";
  }

  // 获取订单销售数据
  rpc GetOrderSalesInfo(GetOrderSalesInfoReq) returns (GetOrderSalesInfoResp) {
    option (api.post) = "/udata/GetOrderSalesInfo";
  }

  // 获取订单退款数据
  rpc GetOrderRefundInfo(GetOrderRefundInfoReq)
      returns (GetOrderRefundInfoResp) {
    option (api.post) = "/udata/GetOrderRefundInfo";
  }

  // 获取全局视图数据
  rpc GetOverviewData(GetOverviewDataReq) returns (GetOverviewDataResp) {
    option (api.post) = "/udata/GetOverviewData";
  }

  // 获取客户页面数据
  rpc GetCustomerPageData(GetCustomerPageDataReq)
      returns (GetCustomerPageDataResp) {
    option (api.post) = "/udata/GetCustomerPageData";
  }
}