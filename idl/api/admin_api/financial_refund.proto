syntax = "proto3";
package admin_api;
option go_package = "server/cmd/admin_api";

import "hz.proto"; // hz框架注解
import "financial_enum.proto";
import "financial_fund.proto";

message RelationOrderInfo {
  int64 order_id = 1;      // ID
  string order_no = 2;     // 订单号
  string service_name = 3; // 服务项目名称
  string goods_name = 4;   // 商品名称
  string goods_spec = 5;   // 商品规格名称
  string goods_num = 6;    // 商品数量
}
message CustomerSimpleTags {
  int64 id = 1;    // 标签id
  string name = 2; // 标签名称
  string icon = 3; // 标签图标
}

message CustomerWithTags {
  int64 id = 1;                               // 客户id
  string name = 2;                            // 姓名
  repeated CustomerSimpleTags tags = 3;       // 标签
  repeated CustomerSimpleTags smart_tags = 4; // 智能标签
}

message AccountInfo {
  string type = 1; // 账号类型
  string name = 2; // 账号名称
}
// 退款列表返回
message FinancialRefundInfo {
  int64 id = 1;                     // 主键ID
  string refund_no = 2;             // 支款单号
  RelationOrderInfo order_info = 3; // 关联订单
  int64 customer_id = 4;            // 客户id
  string customer_name = 5;         // 客户名称
  string should_amount_other = 6;   // 合同金额
  string real_amount_other = 7;     // 支款总额
  int32 refund_type =
      8; // 支款类型(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
  repeated string service_name = 9;   // 服务项目名称
  string brand_name = 10;             // 品牌
  repeated string business_name = 11; // 业务线
  int64 submit_id = 12;               // 申请人ID
  string submit_name = 13;            // 申请名字
  int64 created_at = 14;              // 创建时间
  string currency = 15;               // 币种
  int32 approve_status = 16; // 状态(1=待审批;2=待支款;3=支款完成;4=审核驳回)
  int64 pass_time = 17;      // 通过时间
  int64 reject_time = 18;    // 驳回时间
  int64 complete_time = 19;  // 支款完成时间
  int64 payment_account_id = 20;          // 支款账户ID
  repeated ImageInfo transaction_no = 21; // 交易单号
  int32 account_type = 22;     // 账户类型 1=支付宝 2=微信 3=中国银行账户
  string fund_no = 23;         // 收款单号
  string contract_amount = 24; // 合同金额
  string account_name = 25;    // 支款账户名称
  string real_amount_rmb = 26; // 实收金额人民币
  string exchange_rate = 27;   // 汇率
  CustomerWithTags customer_info = 28;           // 客户信息
  string refund_reason = 29;                     // 退款原因
  string workflow_name = 30;                     // 工单名称
  string workflow_no = 31;                       // 工单编号
  int64 workflow_id = 32;                        // 工单ID
  repeated ImageInfo refund_agreement = 33;      // 支款协议
  repeated ImageInfo approve_log = 34;           // 审批表
  repeated ImageInfo scholarship_agreement = 35; // 奖学金协议
  repeated ImageInfo visa = 36;                  // 签证
  repeated ImageInfo student_card = 37;          // 学生卡
  repeated ImageInfo tuition_payment_proof = 38; // 全额缴纳学费证明
  int32 refund_receive_account_type = 39;        // 账户类型
  string refund_receive_account = 40;            // 接收账户
  string submit_department = 41;                 // 申请人部门
  int64 approve_by = 42;                         // 审核人ID
  string approve_name = 43;                      // 审核人名字
  string approve_department = 44;                // 审核人部门
  string approve_comment = 45;                   // 审核意见
  int32 user_type = 46;                   // 用户类型（1新客户，2老客户,3其他）
  repeated AccountInfo account_info = 47; // 订单原支付方式
  repeated string order_transaction_no = 48; // 订单原交易单号
  repeated IdNameDept submit_source = 49;    // 订单原提交人
  repeated IdNameDept user_source = 50;      // 客户来源
  repeated IdNameDept order_source = 51;     // 订单来源
  string paid_amount = 52;                   // 实际付款金额
  int64 refund_deadline = 53;                // 支款截止时间
}

// 支款订单列表请求
message FinancialRefundListReq {
  int32 approve_status =
      1;                 // 审批状态(1=待审批;2=待支款;3=支款完成;4=审核驳回);
  string refund_no = 2;  // 支款单号
  string order_no = 3;   // 订单号
  int64 customer_id = 4; // 客户ID
  int64 created_at_start = 5;       // 创建日期开始时间
  int64 created_at_end = 6;         // 创建日期开始时间
  string currency = 7;              // 币种
  repeated int64 submit_id = 8;     // 申请人
  repeated string service_name = 9; // 服务项目名称
  string goods_name = 10;           // 商品名称
  string brand_name = 11;           // 品牌
  string business_name = 12;        // 业务名称
  int32 refund_type =
      13; // 款项类型(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
  int64 payment_account_id = 14;  // 支款账户ID
  string transaction_no = 15;     // 交易单号
  int64 pass_time_start = 16;     // 审核通过开始时间
  int64 pass_time_end = 17;       // 审核通过结束时间
  int64 reject_time_start = 18;   // 审核驳回开始时间
  int64 reject_time_end = 19;     // 审核驳回结束时间
  int64 complete_time_start = 20; // 支款完成开始时间
  int64 complete_time_end = 21;   // 支款完成结束时间
  string order_by = 22;           // 排序字段 created_at desc
  int32 page_num = 23;            // 页数
  int32 page_size = 24;           // 每页几条
  repeated PaymentAccountType payment_account_types = 25; // 订单原收款账户类型
  repeated int32 approve_status_list =
      26;                  // 审批状态(1=待审批;2=待支款;3=支款完成;4=审核驳回);
  int64 workflow_id = 27;  // 工单ID
  string workflow_no = 28; // 工单编号
  int32 payment_account_type = 29; // 退款接收账号类型(1=支付宝 2微信 3银行卡)
  int32 user_type = 30;            // 用户类型（1新客户，2老客户,3其他）
  int32 is_control = 31;           // 权限控制 1控制，其他不传不控制
  repeated int64 user_source = 32; // 客户来源
  repeated int32 user_source_depart = 33;  // 客户来源部门
  repeated int64 order_source = 34;        // 订单来源
  repeated int32 order_source_depart = 35; // 订单来源部门
  repeated int32 submit_depart = 36;       // 申请人部门
  string customer_name = 37;               // 客户姓名
  int64 approve_by = 38;                   // 审核人ID
  repeated int64 payment_account_ids = 39; // 订单原收款账户ID列表
  int64 refund_deadline_start = 40;        // 支款截止时间开始
  int64 refund_deadline_end = 41;          // 支款截止时间结束
}

message FinancialRefundListRsp {
  repeated FinancialRefundInfo items = 1;
  int64 total = 2; // 总数
}

// 审核通过或者驳回
message FinancialRefundStatusUpdateReq {
  int64 id = 1;
  // 1=待审批;2=待支款;3=支款完成;4=审核驳回
  int32 approve_status = 2;
  // 交易单号
  repeated ImageInfo transaction_no = 3;
  // 审核意见
  int32 approve_comment = 4;
  // 支款账号
  int32 financial_account_id = 5;
}

message FinancialRefundUpdateRsp { int64 id = 1; }

// 支款单详情
message FinancialRefundDetailReq {
  int64 id = 1;             // ID主键ID
  string refund_no = 2;     // 支款单号
  int64 order_id = 3;       // 订单ID
  int32 approve_status = 4; // 审批状态(1=待审批;2=待支款;3=支款完成;4=审核驳回)
  int32 refund_type =
      5; //(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
}

// 退款审核记录
message FinancialRefundApproveLog {
  int64 approve_by = 1;          // 审批人
  string approve_comment = 2;    // 审批建议
  int32 status = 3;              // 审批状态 1通过,2驳回,3回退,4完成打款
  int64 created_at = 4;          // 创建时间
  string approve_name = 5;       // 姓名
  string approve_department = 6; // 审批人部门
}

message FinancialRelationFundInfo {
  int64 id = 1;                 // 主键ID
  string fund_no = 2;           // 收款单号
  string real_amount_other = 3; // 已收金额
  string currency = 4;          // 币种
  int32 pay_type = 5;           // 付款方式(1=分期;2=一次性)
  int32 fund_type =
      6; // 款项类型(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
  string contract_no = 9;      // 合同编码
  int64 created_at = 10;       // 创建时间
  string submit_name = 11;     // 提交者姓名
  string real_amount_rmb = 12; // 已收金额人民币
}

// 合同信息
message ImageInfo {
  string url = 1;           // 图片地址
  string name = 2;          // 合同名称
  string thumbnail_url = 3; // 缩略图
}

// 支款款管理详情
message FinancialRefundDetail {
  int64 id = 1;                 // 主键ID
  string refund_no = 2;         // 支款单号
  string real_amount_other = 3; // 支款金额
  int32 refund_type =
      4; // 支款类型(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
  int64 created_at = 5;     // 创建时间
  string currency = 6;      // 币种
  int32 approve_status = 7; // 状态(1=待审批;2=待支款;3=支款完成;4=审核驳回)
  int64 pass_time = 8;      // 通过时间
  int64 reject_time = 9;    // 驳回时间
  int64 complete_time = 10; // 支款完成时间
  repeated ImageInfo transaction_no = 11;        // 交易单号
  int32 refund_receive_account_type = 12;        // 账户类型
  string refund_receive_account = 13;            // 接收账户
  string refund_reason = 14;                     // 支款原因
  string account_name = 15;                      // 支款账户名称
  int64 order_id = 16;                           // 订单ID
  string order_no = 17;                          // 订单号
  int64 customer_id = 18;                        // 客户ID
  string real_amount_rmb = 19;                   // 支款金额人民币
  string exchange_rate = 20;                     // 汇率
  string workflow_name = 21;                     // 工单名称
  string workflow_no = 22;                       // 工单编号
  int64 workflow_id = 23;                        // 工单ID
  repeated ImageInfo refund_agreement = 24;      // 支款协议
  repeated ImageInfo approve_log = 25;           // 审批表
  repeated ImageInfo scholarship_agreement = 26; // 奖学金协议
  repeated ImageInfo visa = 27;                  // 签证
  repeated ImageInfo student_card = 28;          // 学生卡
  repeated ImageInfo tuition_payment_proof = 29; // 全额缴纳学费证明
  int32 user_type = 30;                          // 用户类型（1新客户，2老客户）
  int64 refund_deadline = 31;                    // 支款截止时间
}
// 支款款管理详情
message FinancialRefundDetailRsp {
  FinancialRefundDetail financial_refund_info = 1;          // 本次支款信息
  repeated FinancialFundDetail relation_financial_info = 2; // 关联收款信息
  repeated FinancialRefundApproveLog approve_log = 3;       // 审核记录
  repeated FinancialRefundDetail financial_refund_list = 4; // 已经支款信息
  string refund_total_amount = 5;                           // 已经支款总额
  CustomerWithTags customer_info = 6;                       // 客户信息
}

// 审核信息
message FinancialRefundApproveReq {
  int64 financial_refund_id = 1;         // 支款ID
  string approve_comment = 2;            // 审核信息，回退意见
  int32 status = 3;                      // 审批状态 1通过,2驳回,3回退,4完成打款
  string account_name = 4;               // 账户名称 完成打款时填写
  repeated ImageInfo transaction_no = 5; // 交易截图 完成打款时填写
  int64 order_id = 6;                    // 订单ID自增ID
  int64 payment_account_id = 7;          // 支款账户ID
}

message FinancialRefundApproveRsp {}

message AddExportFinancialRefundListReq {
  map<string, string> download_fields = 1
      [ (api.vd) = "len($) > 0; msg:'下载字段不能为空'" ]; // 下载字段 <必填>
  repeated int32 approve_status_list = 2 [
    (api.vd) = "len($) > 0; msg:'支款状态不能为空'"
  ]; // 状态(1=待审批;2=待支款;3=支款完成;4=审核驳回) <必填>
  repeated int64 created_at = 3;  // 创建时间（单位：毫秒）（[start, end]）
  repeated int64 pass_time = 4;   // 审核通过时间 （单位：毫秒）（[start, end]）
  repeated int64 reject_time = 5; // 审核驳回时间 （单位：毫秒）（[start, end]）
  repeated int64 complete_time = 6; // 支款完成时间 （单位：毫秒）（[start, end]）
  repeated string service_name = 7; // 服务项目名称
  string goods_name = 8;            // 商品名称
  string business_name = 9;         // 业务线
  string brand_name = 10;           // 品牌
  int32 refund_type = 11; // 支款类型(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
  int32 refund_receive_account_type = 12; // 退款接收账户类型 1=支付宝 2=微信 3=银行卡
  // 下载字段顺序 <必填>
  repeated string download_fields_order = 13
      [ (api.vd) = "len($) > 0; msg:'下载字段顺序不能为空'" ];
  int32 user_type = 14;                    // 用户类型（1新客户，2老客户,3其他）
  repeated int32 user_source_depart = 15;  // 客户来源部门
  repeated int32 order_source_depart = 16; // 订单来源部门
  repeated int32 submit_depart = 17;       // 申请人部门
  repeated int64 refund_deadline = 18;        // 支款截止时间（单位：毫秒）（[start, end]）
}

message AddExportFinancialRefundListRsp {}