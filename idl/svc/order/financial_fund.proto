syntax = "proto3";
package order;
option go_package = "server/cmd/order";

import "base.proto";
import "enum.proto";

//  创建收款
message FinancialFundCreateReq {
  string order_no = 1;
  string fund_no = 2;
  int64 customer_id = 3;
  int32 approve_status = 4;
  int32 pay_type = 5;
  int32 fund_type = 6;
  string currency = 7;
  string exchange_rate = 8;
  string real_amount_other = 9;
  string real_amount_rmb = 10;
  int32 discount = 11;
  string discount_rate = 12;
  string should_amount_other = 13;
  string should_amount_rmb = 14;
  int64 approve_by = 15;
  string approve_comment = 16;
  int64 paid_time = 17;
  string contract_no = 18;
  int64 submit_id = 19;
  string urgent_speed = 20;
  string contract_url = 21; // 合同内容
  int64 order_id = 22;      // orderID
  repeated FinancialPaidInfo financial_paid_info = 23;
  string service_name = 24;     // 服务项目名称
  string goods_name = 25;       // 商品名称
  string brand_name = 26;       // 品牌
  string business_name = 27;    // 业务名称
  string goods_specs_name = 28; // 商品规格名称
  string num = 29;              // 商品数量
  int64 goods_id = 30;          // 商品ID
  int32 user_type = 31;         // 用户类型（1新客户，2老客户，3其他）
  string remark = 32;           // 备注
}

// 创建收款返回
message FinancialFundCreateRsp {
  int64 id = 1; // ID
  base.BaseRsp base = 255;
}

// 支付信息
message FinancialPaidListReq {
  // 财务ID
  int64 financial_fund_id = 1;
  int64 order_id = 2;
  int32 fund_type = 3;
}

// 支付信息返回
message FinancialPaidInfo {
  int64 financial_fund_id = 1;  // 关联收款单ID
  int64 payment_account_id = 2; // 收款账户ID
  int32 paid_type = 3;
  string currency = 4;                 // 币种
  string exchange_rate = 5;            // 汇率
  string amount_cny = 6;               // 人民币金额
  string amount_other = 7;             // 其他币种金额
  string images_path = 8;              // 图片
  string account_name = 9;             // 收款账户名称
  repeated string transaction_no = 10; // 交易单号
  int64 id = 11;
}

// 支付信息返回
message FinancialPaidListRsp {
  // 付款信息
  repeated FinancialPaidInfo paid_list = 1;
  base.BaseRsp base = 255;
}

// 收款详情查询
message FinancialFundInfoReq {
  int64 id = 1;        // 自增ID
  string fund_no = 2;  // 收款单号
  int64 order_id = 3;  // 订单ID
  string order_no = 4; // 订单号
  int32 fund_type =
      5; // 款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)
  int64 workflow_id = 6; // 工单ID
  int64 customer_id = 7; // 客户ID
}
// 收款详情返回
message FinancialFundInfoRsp {
  int64 id = 1;
  int64 customer_id = 2;
  int64 order_id = 3;
  string order_no = 4;
  string fund_no = 5;
  string real_amount_other = 6;
  string currency = 7;
  string should_amount_other = 8;
  int64 submit_id = 9; // 提交人ID
  int64 created_at = 10;
  int32 fund_type = 11;
  int32 pay_type = 12;
  string contract_no = 13;
  string exchange_rate = 14;
  int32 approve_status = 15; // 审批状态(1=待审批;2=审批通过;3=驳回审批);
  int32 discount = 16;       // 是否有折扣1 无2有
  string discount_rate = 17; // 折扣率
  string urgent_speed = 18;  // 加急速度
  int64 paid_time = 19;      // 付款时间
  string contract_url = 20;  // 合同地址
  repeated FinancialPaidInfo financial_pai_info = 23; // 交易信息
  string real_amount_rmb = 24;                        // 申请金额人民币
  int64 approve_by = 25;                              // 审核人ID
  string approve_comment = 26;                        // 审批意见
  FinancialCustomerType user_type = 27; // 用户类型（1新客户，2老客户,3其他 ）
  string remark = 28;                   // 备注
  string should_amount_rmb = 29;        // 本期应收rmb
  repeated int64 other_submit_ids = 30; // 订单共同提交人ID
  repeated int64 order_source_ids = 31; // 订单来源IDS

  base.BaseRsp base = 255;
}

// 收款列表查询
message FinancialFundListReq {
  int32 approve_status = 1;         // 审批状态(1=待审批;2=审批通过;3=驳回审批);
  string fund_no = 2;               // 收款单号
  string order_no = 3;              // 订单号
  int64 customer_id = 4;            // 客户ID
  int64 created_at_start = 5;       // 创建日期开始时间
  int64 created_at_end = 6;         // 创建日期结束时间
  int32 fund_type = 7;              // 款项类型
  string contract_no = 8;           // 合同编码
  repeated string service_name = 9; // 服务项目名称
  string goods_name = 10;           // 商品名称
  string brand_name = 11;           // 品牌
  string business_name = 12;        // 业务名称
  int64 pay_type = 13;              // 付款方式(1=分期;2=一次性)
  string currency = 14;             // 币种
  repeated int64 submit_id = 15;    // 提交人
  int64 payment_account_id = 16; // 收款账户ID,已作废，请使用payment_account_ids
  int64 pass_time_start = 17;    // 通过日期开始时间
  int64 pass_time_end = 18;      // 通过日期结束时间
  int64 reject_time_start = 19;  // 驳回日期开始时间
  int64 reject_time_end = 20;    // 驳回日期结束时间
  string order_by = 21;          // 排序字段 created_at desc
  int64 order_id = 22;           // 订单ID
  int32 page_num = 23;           // 页数
  int32 page_size = 24;          // 每页几条
  string real_amount_rmb = 25;   // 申请金额人民币
  repeated int32 approve_status_list =
      26;                          // 审批状态(1=待审批;2=审批通过;3=驳回审批);
  string transaction_no = 27;      // 交易单号
  int32 user_type = 28;            // 用户类型（1新客户，2老客户,3其他）
  repeated int64 staff_ids = 29;   // 员工ids
  string customer_name = 30;       // 客户姓名
  int64 paid_time_start = 31;      // 实际付款日期开始时间
  int64 paid_time_end = 32;        // 实际付款日期结束时间
  repeated int64 user_source = 33; // 客户来源
  repeated int32 user_source_depart = 34;  // 客户来源部门
  repeated int64 order_source = 35;        // 订单来源
  repeated int32 order_source_depart = 36; // 订单来源部门
  repeated int32 order_status =
      37; // 订单状态#1%已下定金（定金）|2%支付待确认（首款）|3%尾款待支付（尾款）|4%支付成功|5%支款订单|6%交易关闭
  repeated int32 submit_source_depart = 38;               // 订单提交人部门
  repeated int64 payment_account_ids = 39;                // 收款账户ID列表
  repeated PaymentAccountType payment_account_types = 40; // 收款账户类型
}
// 收款列表详情返回
message FinancialFundInfo {
  int64 id = 1;                   // ID
  string order_no = 2;            // 订单号
  string fund_no = 3;             // 收款单号
  string real_amount_other = 4;   // 实际收款金额
  string currency = 5;            // 币种
  string should_amount_other = 6; // 应收款金额
  int64 submit_id = 7;            // 提交人
  int64 created_at = 8;           // 创建时间
  int64 customer_id = 10;         // 客户ID
  int64 paid_time = 11;           // 支付时间
  int32 pay_type = 12;            // 付款方式(1=分期;2=一次性)
  int32 fund_type =
      13; // 款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)
  string goods_name = 14;                             // 商品名称
  string goods_spec = 15;                             // 商品规格名称
  string goods_num = 16;                              // 商品数量
  repeated string service_name = 17;                  // 服务项目名称
  string brand_name = 18;                             // 品牌
  repeated string business_name = 19;                 // 业务线
  string contract_no = 20;                            // 合同编码
  int64 pass_time = 21;                               // 通过日期
  int64 reject_time = 22;                             // 驳回日期
  repeated FinancialPaidInfo financial_pai_info = 23; // 交易信息
  int32 ApproveStatus = 24; // 审批状态(1=待审批;2=审批通过;3=驳回审批);
  int64 order_id = 25;
  string exchange_rate = 26;         // 汇率
  int32 discount = 27;               // 折扣 1无 2有
  string discount_rate = 28;         // 折扣率
  string urgent_speed = 29;          // 加急速度 1.00000就是不加急
  int64 approve_by = 30;             // 审核人
  string contract_url = 31;          // 合同信息
  string approve_comment = 32;       // 审批意见
  string real_amount_rmb = 33;       // 申请金额人民币
  int64 workflow_id = 34;            // 工单ID
  string workflow_no = 35;           // 工单编号
  int32 user_type = 36;              // 用户类型（1新客户，2老客户,3其他）
  repeated int64 user_source = 37;   // 客户来源
  repeated int64 order_source = 38;  // 订单来源
  repeated int64 submit_source = 39; // 订单共同提交人ID
  string should_amount_rmb = 40;     // 应收款金额
  int32 order_status =
      41; // 订单状态#1%已下定金（定金）|2%支付待确认（首款）|3%尾款待支付（尾款）|4%支付成功|5%支款订单|6%交易关闭
  string remark = 42; // 备注
}

// 收款列表返回
message FinancialFundListRsp {
  repeated FinancialFundInfo financial_fund_list = 1;
  int64 total = 2; // 总数

  base.BaseRsp base = 255;
}

// 修改收款订单状态
message FinancialFundStatusUpdateReq {
  int64 financial_fund_id = 1;
  int32 status = 2;
  int64 updated_by = 3;
  string approve_comment = 4; // 审批备注
}
// 修改收款单状态返回
message FinancialFundStatusUpdateRsp {
  int64 financial_fund_id = 1;
  int32 status = 2;

  base.BaseRsp base = 255;
}

// 保存收款信息草稿
message FinancialFundDraftReq {
  int64 order_id = 1; // 订单ID
  int32 fund_type =
      2;              // 款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)
  string content = 3; // 内容
  int64 created_by = 4;     // 创建人id
  string contract_info = 5; // 合同草稿内容
  string remark = 6;        // 备注
}

message FinancialFundDraftRsp { base.BaseRsp base = 255; }

// 获取收款信息草稿
message GetFinancialFundDraftReq {
  int64 order_id = 1; // 订单ID
  int32 fund_type =
      2; // 款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)
}

message GetFinancialFundDraftRsp {
  int64 order_id = 1; // 订单ID
  int32 fund_type =
      2;              // 款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)
  string content = 3; // 内容
  string contract_info = 4; // 合同草稿内容
  string remark = 5;        // 备注
  base.BaseRsp base = 255;
}

// 更新收款单
message FinancialFundUpdateReq {
  int64 financial_fund_id = 1;
  string order_no = 2;
  string fund_no = 3;
  int64 customer_id = 4;
  int32 approve_status = 5;
  int32 pay_type = 6;
  int32 fund_type = 7;
  string currency = 8;
  string exchange_rate = 9;
  string real_amount_other = 10;
  string real_amount_rmb = 11;
  int32 discount = 12;
  string discount_rate = 13;
  string should_amount_other = 14;
  string should_amount_rmb = 15;
  int64 approve_by = 16;
  string approve_comment = 17;
  int64 paid_time = 18;
  string contract_no = 19;
  int64 updated_by = 20;
  string urgent_speed = 21;
  string contract_url = 22; // 合同内容
  int64 order_id = 23;      // orderID
  repeated FinancialPaidInfo financial_paid_info = 24;
  string goods_name = 25;       // 商品名称
  string goods_num = 27;        // 商品数量
  string service_name = 28;     // 服务项目名称
  string brand_name = 29;       // 品牌
  string business_name = 30;    // 业务线
  string goods_specs_name = 31; // 商品规格名称
  string num = 32;              // 商品数量
  int64 goods_id = 33;          // 商品ID
  int32 user_type = 34;         // 用户类型（1新客户，2老客户,3其他）
  string remark = 35;           // 备注
}

// 更新收款返回
message FinancialFundUpdateRsp { base.BaseRsp base = 255; }

// 删除财务收款单
message FinancialFundDelReq {
  int64 financial_fund_id = 1;
  string order_no = 2;
  string fund_no = 3;
  int32 fund_type = 4;
  int64 order_id = 5;
}
// 删除收款返回
message FinancialFundDelRsp { base.BaseRsp base = 255; }

// 创建线上收款

message FinancialPaymentCreateReq {
  int64 order_id = 1;                                   // 订单ID
  int64 customer_id = 2;                                // 客户ID
  string customer_name = 3;                             // 客户姓名
  string order_no = 4;                                  // 订单编号
  string service_name = 5;                              // 服务项目名称
  string goods_name = 6;                                // 商品名称
  string brand_name = 7;                                // 品牌
  string business_name = 8;                             // 业务名称
  int64 operator_id = 9;                                // 操作人ID
  string num = 10;                                      // 数量
  int64 brand_id = 11;                                  // 品牌ID
  repeated FinancialPaidInfo payment_account_info = 12; // 支款账户信息
}

message FinancialPaymentCreateRsp {
  repeated string out_trade_no = 1; // 交易单号数组
  base.BaseRsp base = 255;
}

// 更新线上收款状态
message FinancialPaymentUpdateReq {
  int64 id = 1;               // ID
  int32 payment_status = 2;   // 支付状态 1.创建 2=待支付 3=已支付 4=已取消'
  int64 payment_deadline = 3; // 支付过期时间
  int64 payment_time = 4;     // 支付完成时间
}

message FinancialPaymentUpdateRsp {
  int64 id = 1; // 主键ID
  base.BaseRsp base = 255;
}

// 获取线上收款信息
message FinancialPaymentInfoReq {
  string out_trade_no = 1; // 交易单号
}

message FinancialPaymentInfoRsp {
  int64 id = 1;                    // 主键ID
  int64 customer_id = 2;           // 客户ID
  string customer_name = 3;        // 客户姓名
  string order_no = 4;             // 订单编号
  string amount = 5;               // 金额
  string out_trade_no = 6;         // 交易单号
  int64 order_id = 7;              // 订单ID
  string service_name = 8;         // 服务项目名称
  string goods_name = 9;           // 商品名称
  string brand_name = 10;          // 品牌
  string business_name = 11;       // 业务名称
  int64 operator_id = 12;          // 操作人ID
  int64 recipient_account_id = 13; // 接收账户ID
  int32 payment_method = 14;       // 支付方式
  string num = 15;                 // 数量
  int64 brand_id = 16;             // 品牌ID
  int32 payment_status = 17;       //'支付状态 1.创建 2=待支付 3=已支付 4=已取消
  int64 payment_time = 18;         // 支付时间
  int64 payment_deadline = 19;     // 支付最晚时间
  string recipient_account_name = 20; // 接收账户名称

  base.BaseRsp base = 255;
}

// 创建第三方申请费收款
message ThirdFundCreateReq {
  int64 customer_id = 1;        // 客户ID
  string currency = 2;          // 币种
  string exchange_rate = 3;     // 汇率
  string real_amount_other = 4; // 金额
  int64 paid_time = 5;          // 支付时间
  int64 submit_id = 6;          // 提交者ID
  repeated FinancialPaidInfo financial_paid_info = 7;
  string service_name = 8;      // 服务项目名称
  string goods_name = 9;        // 商品名称
  string brand_name = 10;       // 品牌
  string business_name = 11;    // 业务名称
  string goods_specs_name = 12; // 商品规格名称
  string num = 13;              // 商品数量
  int64 workflow_id = 14;       // 工单ID
  string workflow_name = 15;    // 工单名称
  string real_amount_rmb = 16;  // 申请金额人民币
  string workflow_no = 17;      // 工单编号
  int64 order_id = 18;          // 订单ID
  string order_no = 19;         // 订单编号
  int64 goods_id = 20;          // 商品ID
  int32 user_type = 21;         // 用户类型（1新客户，2老客户）
  string remark = 22;           // 备注
}

// 创建第三方申请费返回
message ThirdFundCreateRsp {
  int64 id = 1; // ID
  base.BaseRsp base = 255;
}

// 获取关联收款汇率请求
message RelationExchangeRateReq {
  int64 order_id = 1;    // 订单ID
  string currency = 2;   // 币种
  string order_no = 3;   // 订单编号
  int64 workflow_id = 4; // 工单ID
}

// 获取关联收款汇率返回
message RelationExchangeRateRsp {
  string exchange_rate = 1; // 汇率
  base.BaseRsp base = 255;
}

// 获取工单关联收款信息
message WorkflowFundReq {
  string workflow_no = 1;   // 工单编号
  string workflow_name = 2; //
  int32 page_num = 4;       // 页数
  int32 page_size = 5;      // 每页几条
  int64 customer_id = 6;    // 客户ID
  int64 workflow_id = 7;    // 工单ID
  int32 approve_status = 8; // 审批状态(1=待审批;2=审批通过;3=驳回审批)
  int32 refund_type =
      9; // 款项类型(1=定金;2=订单首期款;3=订单尾款;4=第三方申请费)
}

// 获取工单关联收款信息
message WorkflowFund {
  int64 id = 1;               // 主键ID
  string fund_no = 2;         // 收款单号
  string workflow_name = 3;   // 工单名称
  string workflow_no = 4;     // 工单编号
  string currency = 5;        // 币种
  string real_amount_rmb = 6; // 实付金额
  int64 paid_time = 7;        // 交易时间
}
// 获取工单关联收款信息返回
message WorkflowFundRsp {
  repeated WorkflowFund workflow_fund = 1; // 关联收款信息
  int64 total = 2;                         // 总数
  base.BaseRsp base = 255;
}

// 获取工单关联第三方申请费收款
message ThirdFundByWorkflowReq {
  int64 workflow_id = 1;  // 工单ID
  string workflow_no = 2; // 工单编号
}

// 第三方申请费收款总额
message AmountInfo {
  string currency = 1; // 币种
  string amount = 2;   // 金额
}

message ThirdFundByWorkflowRsp {
  int64 id = 1;                 // 主键ID
  string fund_no = 2;           // 收款单号
  string workflow_name = 3;     // 工单名称
  string workflow_no = 4;       // 工单编号
  string currency = 5;          // 币种
  string real_amount_rmb = 6;   // 实付金额人民币
  int64 paid_time = 7;          // 交易时间
  int64 workflow_id = 8;        // 工单ID
  string real_amount_other = 9; // 实付金额其他币种
  int32 approve_status = 10;    // 审批状态(1=待审批;2=审批通过;3=驳回审批)
  repeated AmountInfo ThirdAmountList = 11; // 第三方申请费收款总额
  base.BaseRsp base = 255;
}

// 更新收款单跟进员工
message UpdateFinancialFundEmployeeReq {
  int64 before = 1;                // 更新前员工ID
  int64 after = 2;                 // 更新后员工ID
  repeated int64 customer_ids = 3; // 客户ids
}

message UpdateFinancialFundEmployeeRsp { base.BaseRsp base = 255; }

/**
 * 编辑已审核通过的收款信息
 * 支持部分字段的重新编辑功能
 */
message FinancialFundApprovedEditReq {
  int64 financial_fund_id = 1;         // 收款单ID（必填）
  int64 paid_time = 2;                 // 实际付款日期
  int32 user_type = 3;                 // 客户类型（1新客户，2老客户，3其他）
  repeated int64 order_source_ids = 4; // 订单来源IDS
  string contract_info = 5;            // 合同文件信息
  repeated int64 other_submit_ids = 6; // 共同提交人ID列表
  int64 updated_by = 7;                // 更新操作人ID（必填）
  string edit_reason = 8;              // 编辑原因（可选，用于审计）
}

/**
 * 编辑已审核通过的收款信息响应
 */
message FinancialFundApprovedEditRsp {
  int64 financial_fund_id = 1; // 收款单ID
  int64 updated_at = 2;
  int64 customer_id = 3;//客户id
  repeated int64 to_delete = 4;// 要删除的跟进人
  repeated int64 to_add = 5;// 要新增的跟进人
  base.BaseRsp base = 255;
}
