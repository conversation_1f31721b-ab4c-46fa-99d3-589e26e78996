syntax = "proto3";
package order;
option go_package = "server/cmd/order";

import "base.proto";

//新增一条支款审核记录
message FinancialRefundApproveCreateReq {
  int64  financial_refund_id = 1;
  string order_no = 2;
  int64  approve_by = 3;
  string approve_comment = 4;
  int32  status = 5;//审批状态 1通过,2驳回,3回退,4完成打款
  int64 order_id=6;//订单ID
}
message FinancialRefundApproveCreateRsp {
  //ID
  int64 id = 1;

  base.BaseRsp base = 255;
}

//支款审核记录
message FinancialRefundApproveInfoReq {
  //支款单ID
  int64 id = 1;
}

message FinancialRefundApproveInfo {
  int64  id =1;
  int64  financial_Refund_id = 2;
  string order_no = 3;
  int64  approve_by = 4;
  string approve_comment = 5;
  int32  status = 6;
  int64  created_at=7;
}

//支款审核记录列表
message FinancialRefundApproveListReq {
  //支款单ID
  int64 financial_refund_id = 1;
  int32 page_num =2 ;
  int32 page_size =3;
}

message FinancialRefundApproveListRsp {
  repeated FinancialRefundApproveInfo financial_refund_approve_info=1;
  int64 total = 2;   //总数

  base.BaseRsp base = 255;
}




