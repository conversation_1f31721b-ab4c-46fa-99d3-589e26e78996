syntax = "proto3";
package order;
option go_package = "server/cmd/order";

import "base.proto";
import "enum.proto";

//  创建支款
message FinancialRefundCreateReq {
  string order_no = 1;          // 订单号
  string fund_no = 2;           // 收款单号
  string refund_no = 3;         // 支款单号
  int64 customer_id = 4;        // 客户ID
  string currency = 5;          // 退款币种
  string exchange_rate = 6;     // 汇率
  string real_amount_other = 7; // 申请退款金额
  string real_amount_rmb = 8;   // 申请退款金额人民币
  int32 refund_type =
      9; // 款项类型(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
  int64 submit_id = 10; // 提交者ID
  int32 refund_receive_account_type =
      11; // 退款接收账号类型(1=支付宝 2微信 3银行卡)
  string refund_receive_account = 12; // 退款接收账号
  string refund_reason = 13;          // 退款原因
  string refund_agreement = 14;       // 支款协议
  string scholarship_agreement = 15;  // 奖学金协议
  string visa = 16;                   // 签证
  string student_card = 17;           // 学生卡
  string tuition_payment_proof = 18;  // 全额缴纳学费证明
  int64 approve_by = 19;              // 更新人ID
  int64 order_id = 20;                // 订单ID
  string approve_log = 21;            // 支款审批记录
  string workflow_no = 22;            // 工单编号
  int64 workflow_id = 23;             // 工单ID
  string workflow_name = 24;          // 关联工单名称
  string goods_name = 25;             // 商品名称
  string goods_specs_name = 26;       // 商品规格名称
  string service_name = 27;           // 服务项目名称
  string brand_name = 28;             // 品牌名称
  string business_name = 29;          // 业务线名称
  string num = 30;                    // 数量
  string contract_amount = 31;        // 合同金额
  int64 goods_id = 32;                // 商品ID
  string university = 33;             // 大学
  string major = 34;                  // 专业
  string level = 35;                  // 学历等级
  int64 enter_time = 36;              // 入学时间
  int32 user_type = 37;               // 用户类型（1新客户，2老客户）
  int64 refund_deadline = 38;         // 支款截止时间
}

message FinancialRefundCreateRsp {
  int64 id = 1; // 主ID
  base.BaseRsp base = 255;
}

// 支款详情
message FinancialRefundInfoReq {
  int64 id = 1;             // ID主键ID
  string refund_no = 2;     // 支款单号
  int64 order_id = 3;       // 订单ID
  int32 approve_status = 4; // 审批状态(1=待审批;2=待支款;3=支款完成;4=审核驳回)
  int32 refund_type =
      5; //(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
}
// 支款详情
message FinancialRefundInfoRsp {
  int64 id = 1;                 // ID主键ID
  string order_no = 2;          // 订单号
  string refund_no = 3;         // 支款单号
  string real_amount_other = 4; // 支款金额
  string currency = 5;          // 币种
  string exchange_rate = 6;     // 汇率
  int64 submit_id = 7;          // 提交者ID
  int64 created_at = 8;         // 创建时间
  int32 refund_type =
      9; // 支款类型(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
  int64 customer_id = 10;                 // 客户ID
  int32 refund_receive_account_type = 11; // 支款接收账户类型
  string refund_receive_account = 12;     // 支款接收账户
  int64 complete_time = 13;               // 支款完成时间
  string refund_reason = 14;              // 支款原因
  string account_name = 15;               // 支款账户名称
  int64 order_id = 16;                    // 订单ID
  int32 approve_status = 17; // 订单状态 1=待审批;2=待支款;3=支款完成;4=审核驳回
  string refund_agreement = 18;      // 支款协议
  string approve_log = 19;           // 支款审批记录
  string real_amount_rmb = 20;       // 申请金额人民币
  string workflow_no = 21;           // 工单编号
  int64 workflow_id = 22;            // 工单ID
  string workflow_name = 23;         // 关联工单名称
  string approve_comment = 24;       // 审核意见
  string scholarship_agreement = 25; // 奖学金协议
  string visa = 26;                  // 签证
  string student_card = 27;          // 学生卡
  string tuition_payment_proof = 28; // 全额缴纳学费证明
  int32 user_type = 29;              // 用户类型（1新客户，2老客户）
  string transaction_no = 30;        // 交易截图
  int64 refund_deadline = 31;        // 支款截止时间

  base.BaseRsp base = 255;
}

// 支款列表
message FinancialRefundListReq {
  int32 approve_status =
      1;                 // 审批状态(1=待审批;2=待支款;3=支款完成;4=审核驳回);
  string refund_no = 2;  // 支款单号
  string order_no = 3;   // 订单号
  int64 customer_id = 4; // 客户ID
  int64 created_at_start = 5;       // 创建日期开始时间
  int64 created_at_end = 6;         // 创建日期开始时间
  string currency = 7;              // 币种
  repeated int64 submit_id = 8;     // 申请人
  repeated string service_name = 9; // 服务项目名称
  string goods_name = 10;           // 商品名称
  string brand_name = 11;           // 品牌
  string business_name = 12;        // 业务名称
  int32 refund_type =
      13; // 款项类型(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
  int64 payment_account_id = 14;                // 支款账户ID
  string transaction_no = 15;                   // 交易单号
  int64 pass_time_start = 16;                   // 审核通过开始时间
  int64 pass_time_end = 17;                     // 审核通过结束时间
  int64 reject_time_start = 18;                 // 审核驳回开始时间
  int64 reject_time_end = 19;                   // 审核驳回结束时间
  int64 complete_time_start = 20;               // 支款完成开始时间
  int64 complete_time_end = 21;                 // 支款完成结束时间
  string order_by = 22;                         // 排序字段 created_at desc
  int32 page_num = 23;                          // 页数
  int32 page_size = 24;                         // 每页几条
  int64 order_id = 25;                          // 订单ID
  string real_amount_rmb = 26;                  // 申请金额人民币
  PaymentAccountType payment_account_type = 27; // 支款账号类型
  repeated int32 approve_status_list =
      28;                  // 审批状态(1=待审批;2=待支款;3=支款完成;4=审核驳回);
  int64 workflow_id = 29;  // 工单ID
  string workflow_no = 30; // 工单编号
  int32 user_type = 31;    // 用户类型（1新客户，2老客户）
  repeated int64 staff_ids = 32;           // 员工ids
  repeated int64 user_source = 33;         // 客户来源
  repeated int32 user_source_depart = 34;  // 客户来源部门
  repeated int64 order_source = 35;        // 订单来源
  repeated int32 order_source_depart = 36; // 订单来源部门
  repeated int32 submit_depart = 37;       // 申请人部门
  string customer_name = 38;               // 客户姓名
  repeated int64 payment_account_ids = 39; // 订单原收款账户ID列表
  repeated PaymentAccountType payment_account_types =
      40;                           // 订单原收款账户类型列表
  int64 approve_by = 41;            // 审批人
  int64 refund_deadline_start = 42; // 支款截止时间开始
  int64 refund_deadline_end = 43;   // 支款截止时间结束
}
message AccountInfo {
  string type = 1; // 账号类型
  string name = 2; // 账号名称
}

// 支款列表详情
message FinancialRefundInfo {
  int64 id = 1;
  int32 approve_status = 2; // 审批状态(1=待审批;2=待支款;3=支款完成;4=审核驳回)
  string order_no = 3;      // 订单号
  string refund_no = 4;     // 支款单号
  string fund_no = 5;       // 收款单号
  int64 submit_id = 6;      // 申请人ID
  int64 customer_id = 7;    // 客户id
  string should_amount_other = 8; // 合同金额
  string real_amount_other = 9;   // 支款金额
  int32 refund_type =
      10; // 支款类型(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
  repeated string service_name = 11;  // 服务项目名称
  string brand_name = 12;             // 品牌
  repeated string business_name = 13; // 业务线
  int64 created_at = 14;              // 创建时间
  string currency = 15;               // 币种
  int64 pass_time = 16;               // 通过时间
  int64 reject_time = 17;             // 驳回时间
  int64 complete_time = 18;           // 支款完成时间
  int64 payment_account_id = 19;      // 支款账户ID
  string transaction_no = 20;         // 交易单号
  int32 account_type = 21;            // 账户类型 1=支付宝 2=微信 3=中国银行账户
                                      // 4=英国银行账户 5=pos机 6=paypal 7=其他
  string goods_name = 22;             // 商品名称
  string goods_specs_name = 23;       // 商品规格名称
  string goods_num = 24;              // 商品数量
  string account_name = 25;           // 支款账户名称
  string contract_amount = 26;        // 合同金额
  int64 order_id = 27;                // 订单ID
  string refund_agreement = 28;       // 支款协议
  string approve_log = 29;            // 审批记录
  string scholarship_agreement = 30;  // 奖学金协议
  string visa = 31;                   // 签证
  string student_card = 32;           // 学生卡
  string tuition_payment_proof = 33;  // 全额缴纳学费证明
  string workflow_name = 34;          // 关联工单名称
  int64 approve_by = 35;              // 审核人ID
  string approve_comment = 36;        // 审核意见
  int32 refund_receive_account_type =
      37; // 退款接收账号类型(1=支付宝 2微信 3银行卡)
  string refund_receive_account = 38;     // 退款接收账号
  string refund_reason = 39;              // 退款原因
  string university = 40;                 // 大学
  string major = 41;                      // 专业
  string level = 42;                      // 等级
  int64 enter_time = 43;                  // 入学时间
  string real_amount_rmb = 44;            // 申请金额人民币
  string exchange_rate = 45;              // 汇率
  string workflow_no = 46;                // 工单编号
  int64 workflow_id = 47;                 // 工单ID
  int32 user_type = 48;                   // 用户类型（1新客户，2老客户,3其他）
  repeated int64 user_source = 49;        // 客户来源
  repeated int64 order_source = 50;       // 订单来源
  repeated int64 submit_source = 51;      // 订单共同提交人
  repeated AccountInfo account_info = 52; // 订单原支付方式
  repeated string order_transaction_no = 53; // 订单原交易单号
  string paid_amount = 54;                   // 实际付款金额
  int64 refund_deadline = 55;                // 支款截止时间
}

message FinancialRefundListRsp {
  repeated FinancialRefundInfo financial_refund_list = 1;
  int64 total = 2; // 总数

  base.BaseRsp base = 255;
}

// 修改支款订单状态
message FinancialRefundStatusUpdateReq {
  int64 financial_refund_id = 1;
  int32 status = 2;
  string account_name = 3;   // 账户名称 完成打款时填写
  string transaction_no = 4; // 交易单号 完成打款时填写
  int64 updated_by = 5;
}

message FinancialRefundStatusUpdateRsp {
  int64 financial_Refund_id = 1;
  int32 status = 2;

  base.BaseRsp base = 255;
}

// 更新支款单
message FinancialRefundUpdateReq {
  int64 financial_refund_id = 1; // 支款单ID
  string order_no = 2;           // 订单号
  string fund_no = 3;            // 收款单号
  string refund_no = 4;          // 支款单号
  int64 customer_id = 5;         // 客户ID
  string currency = 6;           // 退款币种
  string exchange_rate = 7;      // 汇率
  string real_amount_other = 8;  // 申请退款金额
  string real_amount_rmb = 9;    // 申请退款金额人民币
  int32 refund_type =
      10; // 款项类型(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
  int64 submit_id = 11; // 提交者ID
  int32 refund_receive_account_type =
      12; // 退款接收账号类型(1=支付宝 2微信 3银行卡)
  string refund_receive_account = 13; // 退款接收账号
  string refund_reason = 14;          // 退款原因
  string refund_agreement = 15;       // 支款协议
  string scholarship_agreement = 16;  // 奖学金协议
  string visa = 17;                   // 签证
  string student_card = 18;           // 学生卡
  string tuition_payment_proof = 19;  // 全额缴纳学费证明
  int32 approve_status =
      20;                   // 审批状态(1=待审批;2=待支款;3=支款完成;4=审核驳回)
  int64 updated_by = 21;    // 更新人ID
  int32 status = 22;        // 审批状态 1通过,2驳回,3回退,4完成打款
  string account_name = 23; // 账户名称 完成打款时填写
  string transaction_no = 24;      // 交易单号 完成打款时填写
  string approve_comment = 25;     // 审批备注
  int64 order_id = 26;             // 订单ID
  string approve_log = 27;         // 支款审批记录
  string workflow_no = 28;         // 工单编号
  int64 workflow_id = 29;          // 工单ID
  string workflow_name = 30;       // 关联工单名称
  string goods_name = 31;          // 商品名称
  string goods_specs_name = 32;    // 商品规格名称
  string service_name = 33;        // 服务项目名称
  string brand_name = 34;          // 品牌名称
  string business_name = 35;       // 业务线名称
  string num = 36;                 // 数量
  string should_amount_other = 37; // 合同金额
  int64 goods_id = 38;             // 商品ID
  int64 approve_by = 39;           // 审批人
  string university = 40;          // 大学
  string major = 41;               // 专业
  string level = 42;               // 学历等级
  int64 enter_time = 43;           // 入学时间
  int32 user_type = 44;            // 用户类型（1新客户，2老客户）
  int64 payment_account_id = 45;   // 支款账户id  完成打款时填写
  int64 refund_deadline = 46;      // 支款截止时间
}

message FinancialRefundUpdateRsp { base.BaseRsp base = 255; }

// 删除支款单
message FinancialRefundDelReq {
  int64 financial_refund_id = 1; // 支款单ID
}

message FinancialRefundDelRsp { base.BaseRsp base = 255; }

// 关联支款详情
message FinancialRefundAssociate {
  int64 id = 1;                 // ID主键ID
  string order_no = 2;          // 订单号
  string refund_no = 3;         // 支款单号
  string real_amount_other = 4; // 支款金额
  string currency = 5;          // 币种
  string exchange_rate = 6;     // 汇率
  int64 submit_id = 7;          // 提交者ID
  int64 created_at = 8;         // 创建时间
  int32 refund_type =
      9; // 支款类型(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
  int64 customer_id = 10;                 // 客户ID
  int32 refund_receive_account_type = 11; // 支款接收账户类型
  string refund_receive_account = 12;     // 支款接收账户
  int64 complete_time = 13;               // 支款完成时间
  string refund_reason = 14;              // 支款原因
  string refund_agreement = 15;           // 支款协议
  string approve_log = 16;                // 支款审批记录
  string real_amount_rmb = 17;            // 申请金额人民币
}

// 关联支款详情
message FinancialRefundAssociateRsp {
  repeated FinancialRefundAssociate refund_list = 1; // 关联的支款
}

// 关联支款的请求
message FinancialRefundAssociateReq {
  int64 id = 1;        // 本次支款ID
  string order_no = 2; // 订单号
  int64 order_id = 3;  // 订单ID
}

message FinancialRefundSumReq {
  int64 customer_id = 1; // 客户ID
  int32 refund_type =
      2; // 款项类型(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
  int64 order_id = 3;       // 订单ID
  string order_no = 4;      // 订单编号
  int32 approve_status = 5; // 状态(1=待审批;2=待支款;3=支款完成;4=审核驳回)
}
message FinancialRefundSumRsp {
  string amount_total = 1; // 已退款总额
  base.BaseRsp base = 255;
}

// 创建奖学金支款
message ScholarshipRefundCreateReq {
  int64 customer_id = 1;        // 客户ID
  string currency = 2;          // 币种
  string exchange_rate = 3;     // 汇率
  string real_amount_other = 4; // 申请金额
  int64 submit_id = 5;          // 提交者ID
  int32 refund_receive_account_type =
      6;                             // 退款接收账号类型(1=支付宝 2微信 3银行卡)
  string refund_receive_account = 7; // 退款接收账号
  string refund_reason = 8;          // 退款原因
  string refund_agreement = 9;       // 支款协议
  string scholarship_agreement = 10; // 奖学金协议
  string visa = 11;                  // 签证
  string student_card = 12;          // 学生卡
  string tuition_payment_proof = 13; // 全额缴纳学费证明
  int64 workflow_id = 14;            // 工单ID
  string workflow_name = 15;         // 工单名称
  string approve_log = 16;           // 支款审批记录
  string real_amount_rmb = 17;       // 申请金额人民币
  int32 user_type = 18;              // 用户类型（1新客户，2老客户）
}

message ScholarshipRefundCreateRsp {
  int64 id = 1; // 主键ID
  base.BaseRsp base = 255;
}

// 创建第三方申请费
message ThirdRefundCreateReq {
  string order_no = 1;          // 订单号
  string fund_no = 2;           // 收款单号
  string refund_no = 3;         // 支款单号
  int64 customer_id = 4;        // 客户ID
  string currency = 5;          // 退款币种
  string exchange_rate = 6;     // 汇率
  string real_amount_other = 7; // 申请退款金额
  string real_amount_rmb = 8;   // 申请退款金额人民币
  int64 submit_id = 10;         // 提交者ID
  int32 refund_receive_account_type =
      11; // 退款接收账号类型(1=支付宝 2微信 3银行卡)
  string refund_receive_account = 12; // 退款接收账号
  string refund_reason = 13;          // 退款原因
  string refund_agreement = 14;       // 支款协议
  string scholarship_agreement = 15;  // 奖学金协议
  string visa = 16;                   // 签证
  string student_card = 17;           // 学生卡
  string tuition_payment_proof = 18;  // 全额缴纳学费证明
  int64 approve_by = 19;              // 更新人ID
  int64 order_id = 20;                // 订单ID
  string approve_log = 21;            // 支款审批记录
  string workflow_no = 22;            // 工单编号
  int64 workflow_id = 23;             // 工单ID
  string workflow_name = 24;          // 关联工单名称
  string goods_name = 25;             // 商品名称
  string goods_specs_name = 26;       // 商品规格名称
  string service_name = 27;           // 服务项目名称
  string brand_name = 28;             // 品牌名称
  string business_name = 29;          // 业务线名称
  string num = 30;                    // 数量
  string contract_amount = 31;        // 合同金额
  int64 goods_id = 32;                // 商品ID
  int32 user_type = 33;               // 用户类型（1新客户，2老客户）
  int64 refund_deadline = 34;         // 支款截止时间
}

message ThirdRefundCreateRsp {
  int64 id = 1; // 主键ID
  base.BaseRsp base = 255;
}

// 获取第三方申请费
message ThirdRefundReq {
  int64 customer_id = 1;    // 客户ID
  int64 workflow_id = 2;    // 工单ID
  string workflow_no = 3;   // 工单编号
  int32 approve_status = 4; // 1=待审批;2=待支款;3=支款完成;4=审核驳回
  int32 refund_type =
      5; // 款项类型(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
  int32 page_num = 6;  // 页数
  int32 page_size = 7; // 每页几条
}

message ThirdRefund {
  int64 id = 1;                 // 主键ID
  string refund_no = 2;         // 支款单号
  string real_amount_rmb = 3;   // 支款金额
  string real_amount_other = 4; // 支款其他币种金额
  string currency = 5;          // 币种
  int64 workflow_id = 6;        // 工单ID
  string workflow_no = 7;       // 工单编号
  int64 complete_time = 8;      // 支款完成时间
  int32 approve_status = 9;     // 1=待审批;2=待支款;3=支款完成;4=审核驳回
  string exchange_rate = 10;    // 汇率
  int64 refund_deadline = 11;   // 支款截止时间
}

message ThirdRefundRsp {
  repeated ThirdRefund third_refund = 1; // 第三方申请费支款信息
  int64 total = 2;                       // 总数
  base.BaseRsp base = 255;
}

message RefundListByOrderReq {
  repeated int64 order_ids = 1; // orderIds数组
}

message RefundListByOrderInfo {
  int64 id = 1;               // 主键ID
  string real_amount_rmb = 2; // 支款金额
  int32 refund_type =
      3; // 支款类型(1=退定金;2=退服务费;3=奖学金;4=退差价;5=支付违约金;6=第三方申请费)
  int64 order_id = 4; // 订单ID
}

message RefundListByOrderRsp {
  repeated RefundListByOrderInfo refund_list = 1; // 退款列表
  base.BaseRsp base = 255;
}