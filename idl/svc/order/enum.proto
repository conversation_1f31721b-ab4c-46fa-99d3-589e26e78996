syntax = "proto3";
package order;
option go_package = "server/cmd/order";

// 付款方式
enum OrderInstallmentType {
  INSTALLMENT_TYPE_UNSPECIFIED = 0;  // 未指定
  INSTALLMENT_TYPE_FULL_PAYMENT = 1; // 一次性付款（全款）
  INSTALLMENT_TYPE_BY_STAGES = 2;    // 分期付款
}

// 支款类型
enum OrderDisbursementType {
  DISBURSEMENT_TYPE_UNSPECIFIED = 0;        // 未指定
  DISBURSEMENT_TYPE_REFUND_DEPOSIT = 1;     // 退定金
  DISBURSEMENT_TYPE_REFUND_SERVICE = 2;     // 退服务费
  DISBURSEMENT_TYPE_SCHOLARSHIP = 3;        // 奖学金
  DISBURSEMENT_TYPE_REFUND_DIFFERENCE = 4;  // 退差价
  DISBURSEMENT_TYPE_LIQUIDATED_DAMAGES = 5; // 支付违约金
}

// 是否状态
enum StatusYesNo {
  STATUS_UNSPECIFIED = 0; // 未指定
  STATUS_YES = 1;         // 是
  STATUS_NO = 2;          // 否
}

// 订单状态
enum StatusOrder {
  STATUS_ORDER_UNSPECIFIED = 0;  // 未指定
  STATUS_ORDER_DEPOSIT = 1;      // 定金（已下定金）
  STATUS_ORDER_FIRST = 2;        // 首款（支付待确认）
  STATUS_ORDER_FINAL = 3;        // 尾款（尾款待支付）
  STATUS_ORDER_SUCCESS = 4;      // 支付成功
  STATUS_ORDER_DISBURSEMENT = 5; // 支款（支款订单）
  STATUS_ORDER_CLOSE = 6;        // 已关闭（交易关闭）
}

// 审核状态
enum StatusOrderReview {
  STATUS_REVIEW_UNSPECIFIED = 0;  // 未指定
  STATUS_REVIEW_PASS = 1;         // 审核通过
  STATUS_REVIEW_DRAFT = 2;        // 草稿
  STATUS_REVIEW_DRAFT_AUDIT = 3;  // 草稿待审核
  STATUS_REVIEW_REJECT_AUDIT = 4; // 驳回待审核
  STATUS_REVIEW_REJECT = 5;       // 审核驳回
}

// 工单服务状态
enum StatusOrderWorkflow {
  ORDER_WORKFLOW_STATUS_UNKNOWN = 0;    // 未知状态
  ORDER_WORKFLOW_STATUS_PENDING = 1;    // 待接收
  ORDER_WORKFLOW_STATUS_IN_SERVICE = 2; // 服务中
  ORDER_WORKFLOW_STATUS_PAUSED = 3;     // 服务暂停
  ORDER_WORKFLOW_STATUS_COMPLETED = 4;  // 服务完成
  ORDER_WORKFLOW_STATUS_TERMINATED = 5; // 服务终止
}

// 支付状态
enum StatusOrderPay {
  STATUS_PAY_UNSPECIFIED = 0; // 未指定
  STATUS_PAY_PAID = 1;        // 已支付
  STATUS_PAY_PENDING = 2;     // 待支付
  STATUS_PAY_ING = 3;         // 支付中
  STATUS_PAY_FAIL = 4;        // 支付失败
}

// 商品类型
enum OrderGoodsType {
  GOODS_TYPE_UNSPECIFIED = 0; // 未指定
  GOODS_TYPE_NORMAL = 1;      // 普通商品
  GOODS_TYPE_GIFT = 2;        // 赠送商品
}

// 操作类型
enum OrderOperationType {
  ORDER_OPERATION_TYPE_UNSPECIFIED = 0; // 未指定
  ORDER_OPERATION_TYPE_ORDER = 1;       // 订单状态
  ORDER_OPERATION_TYPE_REVIEW = 2;      // 订单小状态
}

// 排序类型
enum OrderByType {
  ORDER_BY_UNSPECIFIED = 0; // 未指定
  ORDER_BY_ASC = 1;         // 正序
  ORDER_BY_DESC = 2;        // 倒序
}

// 尾款支付方式
enum OrderFinalPaymentType {
  ORDER_FINAL_PAYMENT_TYPE_UNKNOWN = 0;     // 未知
  ORDER_FINAL_PAYMENT_TYPE_ONE_TIME = 1;    // 一次性
  ORDER_FINAL_PAYMENT_TYPE_PRODUCT_NUM = 2; // 按照数量
}

enum PaymentAccountType {
  PAYMENT_ACCOUNT_TYPE_UNSPECIFIED = 0;
  PAYMENT_ACCOUNT_TYPE_ALIPAY = 1;          // 支付宝
  PAYMENT_ACCOUNT_TYPE_WECHAT = 2;          // 微信
  PAYMENT_ACCOUNT_TYPE_BANK_ACCOUNT = 3;    // 中国银行账户
  PAYMENT_ACCOUNT_TYPE_UK_BANK_ACCOUNT = 4; // 英国银行账户
  PAYMENT_ACCOUNT_TYPE_POS = 5;             // pos机
  PAYMENT_ACCOUNT_TYPE_PAYPAL = 6;          // paypal
  PAYMENT_ACCOUNT_TYPE_OTHER = 7;           // 其他
}

enum OrderRelationAction {
  OrderRelationActionUnspecified = 0; // 未指定
  OrderRelationActionCreate = 1;      // 1%创建
  OrderRelationActionUpdate = 2;      // 2%编辑
  OrderRelationActionClose = 3;       // 3%关闭
  OrderRelationActionDelete = 4;      // 4%删除
  OrderRelationActionReview = 5;      // 5%审核
  OrderRelationActionSubmission = 6;  // 6%共同提交
  OrderRelationActionSource = 7;      // 7%订单来源
  OrderRelationActionOwn = 8;         // 8%资产归属
  OrderRelationActionWorkflow = 9;    // 9%工单跟进
  OrderRelationActionDispatch = 10;   // 10%派单对象
}

enum FinancialCustomerType {
  FINANCIAL_CUSTOMER_TYPE_UNSPECIFIED = 0;
  FINANCIAL_CUSTOMER_TYPE_NEW = 1;   // 新客户
  FINANCIAL_CUSTOMER_TYPE_OLD = 2;   // 老客户
  FINANCIAL_CUSTOMER_TYPE_OTHER = 3; // 其他
}