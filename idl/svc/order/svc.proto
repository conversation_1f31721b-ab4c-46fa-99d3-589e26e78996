syntax = "proto3";
package order;
option go_package = "server/cmd/order";

import "financial_account.proto";        // 收款账户
import "financial_fund.proto";           //收款单
import "financial_fund_approve.proto";   //收款单审核记录
import "financial_refund.proto";         //支款单
import "financial_refund_approve.proto"; //支款单审核记录
import "financial_operation_log.proto";  //操作记录
import "request.proto";

// 订单服务
service Service {
  // 获取币种列表
  rpc GetCurrencyList(GetCurrencyListReq) returns (GetCurrencyListRsp) {}
  // 获取订单数据
  rpc GetOrderStatic(GetOrderStaticReq) returns (GetOrderStaticRsp) {}
  // 获取订单列表
  rpc GetOrderList(GetOrderListReq) returns (GetOrderListRsp) {}
  // 获取订单列表（app 专用）
  rpc GetOrderListApp(GetOrderListAppReq) returns (GetOrderListAppRsp) {}
  // 通过订单id批量获取订单
  rpc GetOrderInfoByIds(GetOrderInfoByIdsReq) returns (GetOrderInfoByIdsRsp) {}
  // 获取订单详情
  rpc GetOrderInfo(GetOrderInfoReq) returns (GetOrderInfoRsp) {}
  // 保存订单信息（创建/更新）
  rpc SaveOrder(SaveOrderReq) returns (SaveOrderRsp) {}
  // 更新订单状态
  rpc UpdateOrderStatus(UpdateOrderStatusReq) returns (UpdateOrderStatusRsp) {}
  // 奖学金-待发放列表
  rpc GetScholarshipToBeDistributed(GetScholarshipToBeDistributedReq)
      returns (GetScholarshipToBeDistributedRsp) {}
  // 奖学金-待发放总额
  rpc GetScholarshipToBeDistributedAmount(
      GetScholarshipToBeDistributedAmountReq)
      returns (GetScholarshipToBeDistributedAmountRsp) {}
  // 批量更换最后编辑者ID（资产转移）
  rpc BatchUpdateUpdaterId(BatchUpdateUpdaterIdReq)
      returns (BatchUpdateUpdaterIdRsp) {}
  // 资产转移（多用户）
  rpc BatchUpdateOrderOwnId(BatchUpdateOrderOwnIdReq)
      returns (BatchUpdateOrderOwnIdRsp) {}
  // 重制订单工单状态为已完成的工单状态
  rpc ResetCompleteWorkflowStatus(ResetCompleteWorkflowStatusReq)
      returns (ResetCompleteWorkflowStatusRsp) {}
  // 批量更新订单工单状态
  rpc BatchUpdateWorkflowStatus(BatchUpdateWorkflowStatusReq)
      returns (BatchUpdateWorkflowStatusRsp) {}
  // 获取退款高危客户列表
  rpc GetRefundHighRiskCustomers(GetRefundHighRiskCustomersReq)
      returns (GetRefundHighRiskCustomersRsp) {}
  // 获取红线高危客户列表
  rpc GetRedLineRiskCustomers(GetRedLineRiskCustomersReq)
      returns (GetRedLineRiskCustomersRsp) {}
  // 获取新旧客户列表
  rpc GetOldNewCustomers(GetOldNewCustomersReq)
      returns (GetOldNewCustomersRsp) {}
  // 获取工单全部为筛选状态的订单
  rpc GetWorkflowCompleteOrders(GetWorkflowCompleteOrdersReq)
      returns (GetWorkflowCompleteOrdersRsp) {}
  // 获取最新订单操作日志
  rpc GetLatestOrderOperationLogByOrderId(
      GetLatestOrderOperationLogByOrderIdReq)
      returns (GetLatestOrderOperationLogByOrderIdRsp) {}
  // 通过用户ID获取最新订单信息
  rpc GetLatestOrderInfoByCustomerIds(GetLatestOrderInfoByCustomerIdsReq)
      returns (GetLatestOrderInfoByCustomerIdsRsp) {}
  // 通过用户ID获取用户订单数量
  rpc GetOrderCountByCustomerId(GetOrderCountByCustomerIdReq)
      returns (GetOrderCountByCustomerIdRsp) {}
  // 通过用户ID批量获取用户订单数量
  rpc GetOrderCountByCustomerIds(GetOrderCountByCustomerIdsReq)
      returns (GetOrderCountByCustomerIdsRsp) {}
  // 获取订单操作日志列表（通过订单ID）
  rpc GetOrderOperationLogList(GetOrderOperationLogListReq)
      returns (GetOrderOperationLogListRsp) {}
  // 更新订单的操作人
  rpc UpdateOrderRelation(UpdateOrderRelationReq)
      returns (UpdateOrderRelationRsp) {}
}

// 订单商品服务
service GoodsService {
  // 获取订单商品列表（通过订单ID）
  rpc GetOrderGoodsList(GetOrderGoodsListReq) returns (GetOrderGoodsListRsp) {}
}

// 订单操作日志服务
service OperationLogService {
  // 获取订单操作日志列表（通过订单ID）
  //  rpc GetOrderOperationLogList(GetOrderOperationLogListReq) returns
  //  (GetOrderOperationLogListRsp) {}
}

// 财务相关
service FinancialService {
  // 新增财务账户
  rpc AccountCreate(FinancialAccountCreateReq)
      returns (FinancialAccountCreateRsp) {}
  // 财务账户详情
  rpc AccountInfo(FinancialAccountInfoReq) returns (FinancialAccountInfoRsp) {}
  // 财务账户详情
  rpc AccountList(FinancialAccountListReq) returns (FinancialAccountListRsp) {}
  // 财务账户删除
  rpc AccountDelete(FinancialAccountDeleteReq)
      returns (FinancialAccountDeleteRsp) {}
  // 财务账户修改
  rpc AccountUpdate(FinancialAccountUpdateReq)
      returns (FinancialAccountUpdateRsp) {}
  // 新增收款订单
  rpc FundCreate(FinancialFundCreateReq) returns (FinancialFundCreateRsp) {}
  // 收款订单列表
  rpc FundList(FinancialFundListReq) returns (FinancialFundListRsp) {}
  // 收款订单详情
  rpc FundInfo(FinancialFundInfoReq) returns (FinancialFundInfoRsp) {}
  // 收款支付记录
  rpc FundPaidList(FinancialPaidListReq) returns (FinancialPaidListRsp) {}
  // 新增收款审核记录
  rpc FundApproveCreate(FinancialFundApproveCreateReq)
      returns (FinancialFundApproveCreateRsp) {}
  // 修改收款单状态
  rpc FundStatusUpdate(FinancialFundStatusUpdateReq)
      returns (FinancialFundStatusUpdateRsp) {}
  // 收款审核记录详情
  rpc FundApproveInfo(FinancialFundApproveInfoReq)
      returns (FinancialFundApproveInfo) {}
  // 收款审核记录列表
  rpc FundApproveList(FinancialFundApproveListReq)
      returns (FinancialFundApproveListRsp) {}
  // 新增支款订单
  rpc RefundCreate(FinancialRefundCreateReq)
      returns (FinancialRefundCreateRsp) {}
  // 支款订单列表
  rpc RefundList(FinancialRefundListReq) returns (FinancialRefundListRsp) {}
  // 支款订单详情
  rpc RefundInfo(FinancialRefundInfoReq) returns (FinancialRefundInfoRsp) {}
  // 新增支款审核记录
  rpc RefundApproveCreate(FinancialRefundApproveCreateReq)
      returns (FinancialRefundApproveCreateRsp) {}
  // 修改支款单状态
  rpc RefundStatusUpdate(FinancialRefundStatusUpdateReq)
      returns (FinancialRefundStatusUpdateRsp) {}
  // 支款审核记录详情
  rpc RefundApproveInfo(FinancialRefundApproveInfoReq)
      returns (FinancialRefundApproveInfo) {}
  // 支款审核记录列表
  rpc RefundApproveList(FinancialRefundApproveListReq)
      returns (FinancialRefundApproveListRsp) {}
  // 新增操作日志
  rpc RefundOperationLogCreate(FinancialOperationLogCreateReq)
      returns (FinancialOperationLogCreateRsp) {}
  // 操作日志列表
  rpc FinancialOperationLogList(FinancialOperationLogListReq)
      returns (FinancialOperationLogListRsp) {}
  // 保存收款记录草稿
  rpc fundDraftCreate(FinancialFundDraftReq) returns (FinancialFundDraftRsp) {}
  // 获取收款记录草稿
  rpc getFundDraft(GetFinancialFundDraftReq)
      returns (GetFinancialFundDraftRsp) {}
  // 财务订单更新
  rpc FundUpdate(FinancialFundUpdateReq) returns (FinancialFundUpdateRsp) {}
  // 财务订单删除
  rpc FundDel(FinancialFundDelReq) returns (FinancialFundDelRsp) {}
  // 财务支款单更新
  rpc RefundUpdate(FinancialRefundUpdateReq)
      returns (FinancialRefundUpdateRsp) {}
  // 财务支款单删除
  rpc RefundDel(FinancialRefundDelReq) returns (FinancialRefundDelRsp) {}
  // 关联支款列表
  rpc RefundAssociateList(FinancialRefundAssociateReq)
      returns (FinancialRefundAssociateRsp) {}
  // 已发放奖学金总额
  rpc RefundScholarshipAmount(FinancialRefundSumReq)
      returns (FinancialRefundSumRsp) {}
  // 线上收款创建
  rpc AddFinancialPayment(FinancialPaymentCreateReq)
      returns (FinancialPaymentCreateRsp) {}
  // 更新线上收款
  rpc UpdateFinancialPayment(FinancialPaymentUpdateReq)
      returns (FinancialPaymentUpdateRsp) {}
  // 获取线上收款
  rpc FinancialPaymentInfo(FinancialPaymentInfoReq)
      returns (FinancialPaymentInfoRsp) {}
  // 创建第三方申请费收款
  rpc ThirdFundCreate(ThirdFundCreateReq) returns (ThirdFundCreateRsp) {}
  // 奖学金支款申请
  rpc ScholarshipRefundCreate(ScholarshipRefundCreateReq)
      returns (ScholarshipRefundCreateRsp) {}
  // 创建第三方申请费支款
  rpc ThirdRefundCreate(ThirdRefundCreateReq) returns (ThirdRefundCreateRsp) {}
  // 获取关联收款汇率
  rpc GetRelationExchangeRate(RelationExchangeRateReq)
      returns (RelationExchangeRateRsp) {}
  // 获取工单关联收款信息
  rpc GetWorkflowFund(WorkflowFundReq) returns (WorkflowFundRsp) {}
  // 获取第三方申请费支款
  rpc GetThirdRefund(ThirdRefundReq) returns (ThirdRefundRsp) {}
  // 获取工单关联第三方申请费收款
  rpc GetThirdFundByWorkflow(ThirdFundByWorkflowReq)
      returns (ThirdFundByWorkflowRsp) {}
  // 获取订单关联的最近已经支款
  rpc RefundListByOrder(RefundListByOrderReq) returns (RefundListByOrderRsp) {}
  // 更新财务单跟进员工
  rpc UpdateFinancialFundEmployee(UpdateFinancialFundEmployeeReq)
      returns (UpdateFinancialFundEmployeeRsp) {}
  // 编辑已审核通过的收款信息
  rpc EditApprovedFinancialFund(FinancialFundApprovedEditReq)
      returns (FinancialFundApprovedEditRsp) {}
}
